package common

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/segmentio/kafka-go"
)

func TestKafkaWriter(t *testing.T) {
	topic := "token_topic"
	addr := "172.16.17.5:9092"

	kafkaWriter := &kafka.Writer{
		Addr:     kafka.TCP(addr),
		Topic:    topic,
		Balancer: &kafka.LeastBytes{},
	}
	var token SyncToken
	token.ChainIndex = 60
	token.Contract = "0xe0232D625Ea3B94698F0a7DfF702931B704083c9"

	marshal, err := json.Marshal(token)
	if err != nil {
		t.<PERSON>al(err)
	}
	message := kafka.Message{
		Key:   []byte(fmt.Sprintf("%v", time.Now().Unix())),
		Value: marshal,
		Time:  time.Time{},
	}
	err = kafkaWriter.WriteMessages(context.Background(), message)
	if err != nil {
		t.<PERSON>(err)
	}
}

func TestKafkaReader(t *testing.T) {
	topic := "token_topic"
	addr := "172.16.17.5:9092"
	group := "token_group"
	kafkaReader := kafka.NewReader(kafka.ReaderConfig{
		Brokers: []string{addr},
		Topic:   topic,
		GroupID: group,
	})
	ctx := context.Background()
	for {
		msg, err := kafkaReader.ReadMessage(ctx)
		if err != nil {
			continue
		}
		fmt.Println("msg:", string(msg.Value))
		err = kafkaReader.CommitMessages(ctx, msg)
		if err != nil {
			t.Fatal(err)
		}
	}

}
func TestKafkaNewTopic(t *testing.T) {
	topic := "token_topic"
	addr := "172.16.17.5:9092"
	err := CreateTopicIfNotExists(addr, topic)
	if err != nil {
		t.Error(err)
	}
}
