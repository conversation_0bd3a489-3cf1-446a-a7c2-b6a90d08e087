package common

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/segmentio/kafka-go"
)

func CreateTopicIfNotExists(brokerAddr, topic string) error {
	conn, err := kafka.Dial("tcp", brokerAddr)
	if err != nil {
		return err
	}
	defer conn.Close()

	controller, err := conn.Controller()
	if err != nil {
		return err
	}

	// Dial to controller
	ctrlConn, err := kafka.Dial("tcp", fmt.Sprintf("%s:%d", controller.Host, controller.Port))
	if err != nil {
		return err
	}
	defer ctrlConn.Close()

	topicConfigs := []kafka.TopicConfig{
		{
			Topic:             topic,
			NumPartitions:     3,
			ReplicationFactor: 1,
		},
	}

	return ctrlConn.CreateTopics(topicConfigs...)
}

func AddTokenToKafka(kafkaWriter *kafka.Writer, tokenInfo SyncToken) error {
	marshal, err := json.Marshal(tokenInfo)
	if err != nil {
		return err
	}
	message := kafka.Message{
		Key:   []byte(fmt.Sprintf("%v", time.Now().Unix())),
		Value: marshal,
		Time:  time.Time{},
	}
	err = kafkaWriter.WriteMessages(context.Background(), message)
	if err != nil {
		return err
	}
	return nil
}

func AddAccountToKafka(kafkaWriter *kafka.Writer, account Account) error {
	marshal, err := json.Marshal(account)
	if err != nil {
		return err
	}
	message := kafka.Message{
		Key:   []byte(fmt.Sprintf("%v", time.Now().Unix())),
		Value: marshal,
		Time:  time.Time{},
	}
	err = kafkaWriter.WriteMessages(context.Background(), message)
	if err != nil {
		return err
	}
	return nil
}
