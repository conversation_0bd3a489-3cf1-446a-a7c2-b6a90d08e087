package request

// BaseSignPayload 是所有签名请求的通用结构
type BaseSignPayload struct {
	Sign    string `json:"sign" form:"sign"`       // 用户签名
	Message string `json:"message" form:"message"` // 用户签名的消息内容 如注册walletId:"[{\"chainIndex\":60,\"address\":\"******************************************\"}]" UpDataBossId:"{\"oldWalletId\":\"ZX4DHNL3OOTWY7P98Z1663RT\",\"newWalletId\":\"888888\"}"
	Address string `json:"address" form:"address"` // 用户签名地址（ETH）
}

// UserRegister 表示用户注册请求体
type UserRegister struct {
	BaseSignPayload // 嵌入基础签名字段
}

// UpDateBossId 表示修改用户名请求体
type UpDateBossId struct {
	BaseSignPayload // 嵌入基础签名字段
}

// UserAddress 表示用户绑定的链地址信息
type UserAddress struct {
	ChainIndex int64  `json:"chainIndex"` // 链索引
	Address    string `json:"address"`    // 对应链的地址
}

type UpdateWalletIdPayload struct {
	OldWalletId string `json:"oldWalletId"`
	NewWalletId string `json:"newWalletId"`
}
