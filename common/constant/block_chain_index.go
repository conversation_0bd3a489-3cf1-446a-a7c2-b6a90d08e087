package constant

// AllChainIndex 筛选时使用
const AllChainIndex int64 = -1

var ChainIndex2ChainID = map[int64]string{
	EthChainIndex:      "1",
	BscChainIndex:      "56",
	PolChainIndex:      "137",
	ArbChainIndex:      "42161",
	OptimismChainIndex: "10",
	BaseChainIndex:     "8453",
	SolChainIndex:      "501",
	TronChainIndex:     "728126428",
}

const (
	BtcChainIndex      int64 = 0
	EthChainIndex      int64 = 60
	SolChainIndex      int64 = 501
	BscChainIndex      int64 = 20000714
	PolChainIndex      int64 = 966
	BaseChainIndex     int64 = 8453
	ArbChainIndex      int64 = 10042221
	OptimismChainIndex int64 = 10000070
	TronChainIndex     int64 = 195
)

var chainIndex2Name = map[int64]string{
	BtcChainIndex:      "Bitcoin",
	EthChainIndex:      "Ethereum",
	SolChainIndex:      "Solana",
	BscChainIndex:      "BNB Chain",
	PolChainIndex:      "Polygon",
	BaseChainIndex:     "Base",
	ArbChainIndex:      "Arbitrum",
	OptimismChainIndex: "Optimism",
	TronChainIndex:     "Tron",
}

// chainIndex2ChainID maps chain indexes to their corresponding blockchain chain IDs
var chainIndex2ChainID = map[int64]uint64{
	EthChainIndex:      1,     // Ethereum Mainnet
	BscChainIndex:      56,    // BSC Mainnet
	PolChainIndex:      137,   // Polygon Mainnet
	ArbChainIndex:      42161, // Arbitrum One
	OptimismChainIndex: 10,    // Optimism Mainnet
	BaseChainIndex:     8453,  // Base Mainnet
	// Note: Bitcoin, Solana, and Tron don't use EVM-style chain IDs
}

func GetChainName(chainIndex int64) string {
	return chainIndex2Name[chainIndex]
}

// GetChainID returns the blockchain chain ID for the given chain index
// Returns 0 if the chain index is not found or doesn't have a chain ID (e.g., Bitcoin, Solana)
func GetChainID(chainIndex int64) uint64 {
	return chainIndex2ChainID[chainIndex]
}

// GetChainIDWithDefault returns the blockchain chain ID for the given chain index
// If the chain index is not found, returns the provided default chain ID
func GetChainIDWithDefault(chainIndex int64, defaultChainID uint64) uint64 {
	if chainID, exists := chainIndex2ChainID[chainIndex]; exists {
		return chainID
	}
	return defaultChainID
}

func IsValidChainIndex(chainIndex int64) bool {
	_, ok := chainIndex2Name[chainIndex]
	return ok
}

// IsEVMChain returns true if the chain index corresponds to an EVM-compatible chain
func IsEVMChain(chainIndex int64) bool {
	_, ok := chainIndex2ChainID[chainIndex]
	return ok
}
