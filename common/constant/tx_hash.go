package constant

// TransferSigHash = crypto.Keccak256Hash([]byte("Transfer(address,address,uint256)"))
var TransferSigHash = "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef"

// ApprovalSigHash = crypto.Keccak256Hash([]byte("Approval(address,address,uint256)"))
var ApprovalSigHash = "0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925"

// LogTransferSigHash = crypto.Keccak256Hash([]byte("LogTransfer(address,address,address,uint256,uint256,uint256,uint256,uint256))"))
var LogTransferSigHash = "0xe6497e3ee548a3372136af2fcb0696db31fc6cf20260707645068bd3fe97f3c4"

var MessageInSigHash = "0x13d3a5b2d6aaada5c31b5654f99c2ab9587cf9a53ee4b2e25b6c68a8dfaa4472"

// DepositSigHash Deposit (address,uint256)
var DepositSigHash = "0xe1fffcc4923d04b559f4d29a8bfc6cda04eb5b0d3c460751c2402c5c5cc9109c"

var (
	PancakeSwapV3Hash    = "0x19b47279256b2a23a1665c810c8d55a1758940ee09377d4f8d26497a3577dc83"
	SwapSigHash          = "0xd78ad95fa46c994b6551d0da85fc275fe613ce37657fb8d5e3d130840159d822"
	BscUnitSwapV3SigHash = "0x04206ad2b7c0f463bff3dd4f33c5735b0f2957a351e4f79763a4fa9e775dd237"
)

var FilledRelaySigHash = "0x44b559f101f8fbcc8a0ea43fa91a05a729a5ea6e14a7c75aa750374690137208"
var FundsDepositedSigHash = "0x32ed1a409ef04c7b0227189c3a103dc5ac10e775a15b785dcc510201f7c25ad3"
var WithdrawalSigHash = "0x7fcf532c15f0a6db0bd6d0e038bea71d30d808c7d98cb3bf7268a95bf5081b65"
