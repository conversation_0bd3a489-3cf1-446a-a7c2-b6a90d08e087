package constant

const (
	NativeTokenType = "native"
	ERC20TokenType  = "erc20"
	TRC20TokenType  = "trc20"
	SPLTokenType    = "spl"
)

var chainIndex2TokenType = map[int64]string{
	EthChainIndex:      ERC20TokenType,
	BscChainIndex:      ERC20TokenType,
	PolChainIndex:      ERC20TokenType,
	ArbChainIndex:      ERC20TokenType,
	OptimismChainIndex: ERC20TokenType,
	BaseChainIndex:     ERC20TokenType,
	SolChainIndex:      SPLTokenType,
	TronChainIndex:     TRC20TokenType,
}

func GetTokenType(chainIndex int64) string {
	return chainIndex2TokenType[chainIndex]
}
