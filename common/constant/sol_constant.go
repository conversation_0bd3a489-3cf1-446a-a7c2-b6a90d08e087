package constant

const (
	TransferMethod                                   = "Transfer"
	TransferCheckedMethod                            = "TransferChecked"
	CloseAccountMethod                               = "CloseAccount"
	MintToMethod                                     = "MintTo"
	InitializeMintMethod                             = "InitializeMint"
	InstructionCreateAccountMethod                   = "CreateAccount"
	InstructionCreateAccountWithSeedMethod           = "CreateAccountWithSeed"
	InstructionInitializeAccountMethod               = "InitializeAccount"
	InstructionInitializeAccount2Method              = "InitializeAccount2"
	InstructionInitializeAccount3Method              = "InitializeAccount3"
	InstructionWithdrawNonceAccountInstructionMethod = "WithdrawNonceAccount"
	ApproveInstructionMethod                         = "Approval"
	InstructionUnknownMethod                         = "Unknown"
)

const (
	TokenProgramID       = "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"
	SystemProgram        = "********************************"
	WSolProgram          = "So********************************111111112"
	VoteProgram          = "Vote********************************1111111"
	ComputeBudgetProgram = "ComputeBudget111111111111111111111111111111"
	SerumProgram         = "9xQeWvG816bUx9EPjHmaT23yvVM2ZWbrrpZb9PusVFin"
)

const (
	SolLamports = 1e9
	SolDecimals = 9
)

const (
	WorkNumber = 20

	WorkerCount = 8 // worker 数建议为 CPU 核心数的 2~4 倍
)
