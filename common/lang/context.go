package lang

import "context"

const (
	Default = "zh"
)

type localizer<PERSON>ey struct{}

func FromContext(ctx context.Context) string {
	return fromContext(ctx, Default)
}

func fromContext(ctx context.Context, defaultLang string) string {
	v := ctx.Value(localizerKey{})
	if v == nil {
		return defaultLang
	}
	return v.(string)
}

func NewContext(ctx context.Context, language string) context.Context {
	return context.WithValue(ctx, localizerKey{}, language)
}
