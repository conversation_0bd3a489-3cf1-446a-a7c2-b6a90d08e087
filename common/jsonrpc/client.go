package jsonrpc

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// Client 是一个 JSON-RPC 2.0 客户端
type Client struct {
	URL     string
	Timeout time.Duration
	Headers map[string]string
}

// Request 表示 JSON-RPC 2.0 请求
type Request struct {
	JSONRPC string        `json:"jsonrpc"`
	Method  string        `json:"method"`
	Params  []interface{} `json:"params"`
	ID      interface{}   `json:"id"`
}

// Response 表示 JSON-RPC 2.0 响应
type Response struct {
	JSONRPC string          `json:"jsonrpc"`
	Result  json.RawMessage `json:"result,omitempty"`
	Error   *Error          `json:"error,omitempty"`
	ID      interface{}     `json:"id"`
}

// Error 表示 JSON-RPC 2.0 错误
type Error struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// NewClient 创建一个新的 JSON-RPC 2.0 客户端
func NewClient(url string) *Client {
	return &Client{
		URL: url,
		Headers: map[string]string{
			"Content-Type": "application/json",
			"Accept":       "application/json",
		},
	}
}

// SetHeader 设置 HTTP 请求头
func (c *Client) SetHeader(key, value string) *Client {
	c.Headers[key] = value
	return c
}

// SetTimeout 设置请求超时时间
func (c *Client) SetTimeout(timeout time.Duration) *Client {
	c.Timeout = timeout
	return c
}

// Call 发送 JSON-RPC 2.0 请求并返回原始响应
func (c *Client) Call(ctx context.Context, method string, params []interface{}) (json.RawMessage, error) {
	return c.CallWithID(ctx, method, params, nil)
}

// CallWithID 发送带有指定 ID 的 JSON-RPC 2.0 请求并返回原始响应
func (c *Client) CallWithID(ctx context.Context, method string, params []interface{}, id interface{}) (json.RawMessage, error) {
	reqBody := Request{
		JSONRPC: "2.0",
		Method:  method,
		Params:  params,
		ID:      id,
	}

	bodyBytes, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("marshal request failed: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", c.URL, bytes.NewBuffer(bodyBytes))
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	// 设置请求头
	for key, value := range c.Headers {
		req.Header.Set(key, value)
	}

	// 创建带有超时的 HTTP 客户端
	httpClient := &http.Client{
		Timeout: c.Timeout,
	}

	resp, err := httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("send request failed: %w", err)
	}
	defer resp.Body.Close()

	respBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response failed: %w", err)
	}

	var rpcResp Response
	if err := json.Unmarshal(respBytes, &rpcResp); err != nil {
		return nil, fmt.Errorf("unmarshal response failed: %w", err)
	}

	if rpcResp.Error != nil {
		return nil, fmt.Errorf("RPC error: %s (code: %d)", rpcResp.Error.Message, rpcResp.Error.Code)
	}

	return rpcResp.Result, nil
}

// CallFor 发送 JSON-RPC 2.0 请求并将结果解析为指定类型
func (c *Client) CallFor(ctx context.Context, result interface{}, method string, params []interface{}) error {
	rawResult, err := c.Call(ctx, method, params)
	if err != nil {
		return err
	}

	if err := json.Unmarshal(rawResult, result); err != nil {
		return fmt.Errorf("unmarshal result failed: %w", err)
	}

	return nil
}

// BatchCall 发送批量 JSON-RPC 2.0 请求
func (c *Client) BatchCall(ctx context.Context, calls []Request) ([]Response, error) {
	// 确保每个请求都有 JSONRPC 版本
	for i := range calls {
		calls[i].JSONRPC = "2.0"
	}

	bodyBytes, err := json.Marshal(calls)
	if err != nil {
		return nil, fmt.Errorf("marshal batch request failed: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", c.URL, bytes.NewBuffer(bodyBytes))
	if err != nil {
		return nil, fmt.Errorf("create batch request failed: %w", err)
	}

	// 设置请求头
	for key, value := range c.Headers {
		req.Header.Set(key, value)
	}

	// 创建带有超时的 HTTP 客户端
	httpClient := &http.Client{
		Timeout: c.Timeout,
	}

	resp, err := httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("send batch request failed: %w", err)
	}
	defer resp.Body.Close()

	respBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("read batch response failed: %w", err)
	}

	var responses []Response
	if err := json.Unmarshal(respBytes, &responses); err != nil {
		return nil, fmt.Errorf("unmarshal batch response failed: %w", err)
	}

	return responses, nil
}
