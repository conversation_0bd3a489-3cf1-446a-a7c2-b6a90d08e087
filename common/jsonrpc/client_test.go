package jsonrpc

import (
	"context"
	"fmt"
	"github.com/stretchr/testify/assert"
	"testing"
)

var testUrl = "https://still-soft-patina.btc.quiknode.pro/83d9eda3f3e272d94c4bf15b8e8dd51c439f607e/"

var testCli = NewClient(testUrl)

func TestClient_BatchCall(t *testing.T) {
	resps, err := testCli.BatchCall(context.Background(), []Request{
		{
			JSONRPC: "2.0",
			Method:  "bb_getBlock",
			Params: []any{
				"753204",
				map[string]any{"page": 1},
			},
			ID: 1,
		},
		{
			JSONRPC: "2.0",
			Method:  "bb_getBlock",
			Params: []any{
				"753204",
				map[string]any{"page": 2},
			},
			ID: 2,
		},
	})
	assert.NoError(t, err)
	assert.Equal(t, 2, len(resps))
	assert.Equal(t, float64(1), resps[0].ID)
	assert.Equal(t, float64(2), resps[1].ID)
}

func TestClient_Call(t *testing.T) {
	type args struct {
		method string
		params []interface{}
	}
	tests := []struct {
		name    string
		url     string
		args    args
		wantErr bool
	}{
		{
			name: "qn_btc_getblockcount",
			url:  testUrl,
			args: args{
				method: "getblockcount",
				params: nil,
			},
			wantErr: false,
		},
		{
			name: "qn_btc_bb_getAddress",
			url:  testUrl,
			args: args{
				method: "bb_getAddress",
				params: []interface{}{
					"**************************************************************",
					map[string]any{
						"page": 1, "size": 100, "fromHeight": 0, "details": "txids",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "qn_btc_bb_getTx",
			url:  testUrl,
			args: args{
				method: "bb_getTx",
				params: []interface{}{
					"bfceea7b8610846c0d7144d8b59656512a2d05990f2827b6d907db39713bf6c0",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := testCli.Call(context.Background(), tt.args.method, tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("Call() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			fmt.Println(tt.name, string(got))
		})
	}
}

func TestClient_CallFor(t *testing.T) {
	var height int64
	err := testCli.CallFor(context.Background(), &height, "getblockcount", nil)
	assert.NoError(t, err)
	assert.True(t, height > 0)
}
