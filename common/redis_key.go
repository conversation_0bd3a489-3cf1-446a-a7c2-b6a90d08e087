package common

import "fmt"

func GetSync<PERSON><PERSON><PERSON><PERSON>ber<PERSON><PERSON>(chainIndex int64) string {
	return fmt.Sprintf("sync_block:%d", chainIndex)
}

func GetChainIndexRpc<PERSON>ey(chainIndex int64, rpcType string) string {
	return fmt.Sprintf("rpc_list:%d:%s", chainIndex, rpcType)
}

func Get<PERSON>lock<PERSON>hainInfoKey(chainIndex int64) string {
	return fmt.Sprintf("blk:%d", chainIndex)
}

func GetTrackedAddress(chainIndex int64) string {
	return fmt.Sprintf("tracked:%d", chainIndex)
}
func GetSolOwner(account string) string {
	return fmt.Sprintf("solana:owner:%s", account)
}

func GetSyncEvmInternalBlockNumberKey(chainIndex int64) string {
	return fmt.Sprintf("sync_evm_internal_block:%d", chainIndex)
}
