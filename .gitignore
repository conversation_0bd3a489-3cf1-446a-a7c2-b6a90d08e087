# OS
.DS_Store
*.swp
*.swo
*.swl
*.swm
*.swn
.vscode
.idea
*.pyc
*.exe
*.exe~
*.dll
*.so
*.dylib
.dccache

# Build
*.test
.glide/
vendor
build
bin
tools/bin/*
docs/_build
docs/tutorial
docs/node_modules
docs/assets
docs/modules
docs/cosmos-sdk
docs/ethermint
docs/ibc-go
dist
tools-stamp
docs-tools-stamp
proto-tools-stamp
golangci-lint
keyring_test_cosmos
./**/dist
secret.yml
artifacts/*
tmp-swagger-gen
github.com/
# vue/

# Local docker volume mappings
localnet-setup
.testnets

# Testing
coverage.txt
*.out
sim_log_file
tests/**/tmp/*
yarn.lock

# Vagrant
.vagrant/
*.box
*.log
vagrant
log/

# IDE
.idea/
*.iml
*.code-workspace

# Graphviz
dependency-graph.png

# Latex
*.aux
*.out
*.synctex.gz

# Contracts
*.bin
*.abi

# Node.js
**/node_modules

# Embedded OpenAPI files (auto-generated, do not edit manually)
embedded/wallet-openapi.yaml
embedded/admin-openapi.yaml


# OpenZeppelin contracts
contracts/@openzeppelin/*

*.vk
*.pk
secrets
chain_config

*.key
**/*.key

kms/kms

*.conf
**/*.conf

*/key
key/

**/target**
**/debug**

config.json

*.idea

*.csv
*.xlsx

*.json
*.txt

tmp/