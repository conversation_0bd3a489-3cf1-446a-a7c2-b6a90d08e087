# syntax=docker/dockerfile:experimental

FROM golang:1.23 AS builder

LABEL stage=gobuilder

ARG NAME=server


ENV CGO_ENABLED 0
ENV PATH="${PATH}:${GOPATH}/bin"


RUN apt-get update && apt-get -y upgrade && apt-get -y install make gcc git libc-dev openssh-client ca-certificates tzdata protobuf-compiler && rm -rf /var/cache/apt/*


RUN mkdir ~/.ssh
RUN ssh-keyscan gitlab.server.live ************ >> ~/.ssh/known_hosts
RUN git config --global url."********************".insteadOf "https://gitlab.xbit.live"


COPY . /src


WORKDIR /src/${NAME}


RUN go env -w CGO_ENABLED="1"
RUN --mount=type=ssh,id=gilab \
    CGO_ENABLED=1 \
    go build -ldflags "-X main.CodeVersion=$(git describe --tags || echo dev) -X main.GitCommitHash=$(git rev-parse --short HEAD || echo HEAD) -linkmode=external -extldflags=-static" \
    -o main


FROM debian:bullseye-slim
WORKDIR /app
ARG NAME=server
ARG TYPE=api
RUN apt-get update && apt-get -y upgrade && apt-get -y install openssl wget make gcc bash tzdata libc-dev ca-certificates openssh-client && update-ca-certificates && rm -rf /var/cache/apt/*

COPY --from=builder /src/${NAME}/main  /app/
EXPOSE 9000

ENTRYPOINT ["./main"]
