server:
  http:
    addr: 0.0.0.0:8001
    timeout: 15s
  grpc:
    addr: 0.0.0.0:9001
    timeout: 10s
data:
  database:
    driver: postgres
    db_user: root
    db_password: Pd8wCkfBZx1yOA8wCvC7oAlJ
    db_host: ***********
    db_port: 5432
    dry_run: false
    max_idle_conns: 20
    max_open_conns: 10
    conn_max_life_time: 4h
    db_name: wallet
  redis:
    addrs:
      - ***********:6379
    username:
    password: JJYVBLy6Lh31B83y
    read_timeout: 0.2s
    write_timeout: 0.2s
  kafka:
    brokers:
      - ***********:9092