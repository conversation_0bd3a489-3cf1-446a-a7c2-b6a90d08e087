server:
  syncer:
    chain_index: 501

data:
  database:
    driver: postgres
    db_user: root
    db_password: Pd8wCkfBZx1yOA8wCvC7oAlJ
    db_host: ***********
    db_port: 5432
    dry_run: false
    max_idle_conns: 20
    max_open_conns: 10
    conn_max_life_time: 4h
    db_name: wallet
  redis:
    addrs:
      - ***********:6379
    username:
    password: JJYVBLy6Lh31B83y
    read_timeout: 0.2s
    write_timeout: 0.2s
  kafka:
    brokers:
      - ***********:9092
    token_topic: token_topic
    hold_new_token_topic: hold_new_token_topic
