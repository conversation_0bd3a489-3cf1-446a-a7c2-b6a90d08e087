# Swagger OpenAPI Deployment Solution

## Problem Statement

The OpenAPI specification files (`openapi.yaml`) were not being properly packaged into the compiled binary, causing "Not Found" errors when accessing `/q/openapi.yaml` on the server in production environments, while working correctly in local development.

## Root Cause Analysis

1. **External File Dependency**: The original implementation relied on external files (`api/wallet/openapi.yaml` and `api/walletadmin/openapi.yaml`) that were not included in the Docker container or deployment environment.

2. **Build Process Gap**: The build process did not include the `api/` directory in the final binary or deployment package.

3. **Environment Differences**: Local development had access to the source files, but production deployments only had the compiled binary.

## Solution Overview

Implemented a **multi-tier fallback strategy** that ensures OpenAPI files are always available:

### Tier 1: Embedded Actual API Files (Production)
- **Primary Strategy**: Embed the actual OpenAPI files from `api/` directory into the binary using Go's `embed` directive
- **Reliability**: Guarantees availability in all deployment environments
- **Performance**: No file system access required

### Tier 2: External Files (Development)
- **Secondary Strategy**: Fall back to external files for development environments
- **Flexibility**: Allows real-time updates during development
- **Debugging**: Easier to modify and test API specifications

### Tier 3: Embedded UI Files (Legacy Fallback)
- **Tertiary Strategy**: Use embedded UI directory files as last resort
- **Compatibility**: Maintains backward compatibility

## Implementation Details

### 1. Embedded Package Structure
```
embedded/
├── openapi.go              # Go package with embed directives
├── wallet-openapi.yaml     # Embedded wallet API specification
└── admin-openapi.yaml      # Embedded admin API specification
```

### 2. Automatic Sync Process
- **Script**: `scripts/sync-openapi.sh` automatically copies API files to embedded directory
- **Makefile Integration**: Sync runs automatically during `make proto` and `make build`
- **Version Control**: Embedded files are gitignored (auto-generated)

### 3. Smart Fallback Logic
```go
// Strategy 1: Try embedded actual API files first (production)
if len(embeddedContent) > 0 {
    return embeddedContent
}

// Strategy 2: Try external file (development)
if externalFileExists {
    return externalFile
}

// Strategy 3: Fallback to embedded UI files (legacy)
return embeddedUIFile
```

## File Structure Changes

### New Files Added
- `embedded/openapi.go` - Embedded API files package
- `scripts/sync-openapi.sh` - Sync script for embedded files
- `docs/swagger-openapi-deployment.md` - This documentation

### Modified Files
- `internal/server/swagger/swagger.go` - Updated fallback logic with logging
- `internal/server/http.go` - Added production deployment comments
- `internal/server/admin_http.go` - Added production deployment comments
- `Makefile` - Added sync-openapi target and integration
- `.gitignore` - Excluded auto-generated embedded files

## Usage Instructions

### Development Environment
1. **Generate API Files**: `make proto` (automatically syncs embedded files)
2. **Manual Sync**: `make sync-openapi` or `./scripts/sync-openapi.sh`
3. **Build**: `make build` (automatically syncs before building)

### Production Deployment
1. **Build Binary**: The embedded files are automatically included
2. **Docker**: No additional files needed in container
3. **Verification**: Check logs for "Serving OpenAPI from embedded" messages

## Testing

### Comprehensive Test Suite
- **External File Tests**: Verify development environment functionality
- **Embedded Content Tests**: Verify production environment functionality
- **Production Scenario Tests**: Simulate deployment conditions
- **Fallback Logic Tests**: Verify all tiers work correctly

### Running Tests
```bash
# Run all swagger tests
go test ./internal/server/swagger -v

# Test specific scenarios
go test ./internal/server/swagger -run TestProductionDeploymentScenario -v
go test ./internal/server/swagger -run TestEmbeddedOpenAPIContent -v
```

## Logging and Debugging

### Log Messages
- `[Swagger] Serving OpenAPI from embedded wallet API file (size: X bytes)`
- `[Swagger] Serving OpenAPI from embedded admin API file (size: X bytes)`
- `[Swagger] Serving OpenAPI from external file: path/to/file`
- `[Swagger] External OpenAPI file not found: path, error: details`

### Troubleshooting
1. **Check Logs**: Look for Swagger log messages to see which source is being used
2. **Verify Embedded Files**: Ensure `embedded/` directory contains YAML files
3. **Sync Files**: Run `make sync-openapi` if embedded files are outdated
4. **Test Endpoints**: Access `/q/openapi.yaml` and `/q/swagger-ui` to verify functionality

## Benefits

### Production Reliability
- ✅ **Zero External Dependencies**: All files embedded in binary
- ✅ **Consistent Deployment**: Same behavior across all environments
- ✅ **No File System Issues**: No path or permission problems

### Development Flexibility
- ✅ **Real-time Updates**: External files used when available
- ✅ **Easy Debugging**: Clear logging shows which source is used
- ✅ **Backward Compatible**: Existing development workflows unchanged

### Maintenance Efficiency
- ✅ **Automated Sync**: No manual file copying required
- ✅ **Version Control**: Source files remain authoritative
- ✅ **Build Integration**: Seamless integration with existing build process

## API Endpoints

### Wallet API
- **Swagger UI**: `http://localhost:8000/q/swagger-ui`
- **OpenAPI Spec**: `http://localhost:8000/q/openapi.yaml`
- **Source**: `api/wallet/openapi.yaml` → `embedded/wallet-openapi.yaml`

### Admin API
- **Swagger UI**: `http://localhost:8001/q/swagger-ui`
- **OpenAPI Spec**: `http://localhost:8001/q/openapi.yaml`
- **Source**: `api/walletadmin/openapi.yaml` → `embedded/admin-openapi.yaml`

## Future Considerations

1. **Automated Testing**: CI/CD pipeline should run swagger tests
2. **Version Validation**: Consider adding version checks between source and embedded files
3. **Performance Monitoring**: Monitor embedded file sizes and loading times
4. **Documentation Updates**: Keep this document updated with any changes
