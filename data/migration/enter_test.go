package migration

import (
	"testing"
)

func TestData_Migrate(t *testing.T) {
	// dbConfig := config.Mysql{
	// 	GeneralDB: config.GeneralDB{
	// 		Prefix:       "",
	// 		Port:         "3306",
	// 		Config:       "charset=utf8mb4&parseTime=True&loc=Local",
	// 		Dbname:       "block_chain",
	// 		Username:     "root",
	// 		Password:     "root",
	// 		Path:         "localhost",
	// 		Engine:       "",
	// 		LogMode:      "error",
	// 		MaxIdleConns: 50,
	// 		MaxOpenConns: 500,
	// 		Singular:     false,
	// 		LogZap:       false,
	// 	},
	// }
	// db := initialize.GormMysqlByConfig(dbConfig)
	// tables := []any{&model.User{}, &model.UserGasPool{}}
	// err := db.AutoMigrate(tables...)
	// assert.NoError(t, err)
	// oldDBConfig := config.Mysql{
	// 	GeneralDB: config.GeneralDB{
	// 		Prefix:       "",
	// 		Port:         "3306",
	// 		Config:       "charset=utf8mb4&parseTime=True&loc=Local",
	// 		Dbname:       "block_chain",
	// 		Username:     "bybtoken",
	// 		Password:     "T2z84ubaY1rQPGOkmKjqK1KF",
	// 		Path:         "***********",
	// 		Engine:       "",
	// 		LogMode:      "error",
	// 		MaxIdleConns: 50,
	// 		MaxOpenConns: 500,
	// 		Singular:     false,
	// 		LogZap:       false,
	// 	},
	// }
	// oldDB := initialize.GormMysqlByConfig(oldDBConfig)
	// err = NewData(oldDB, db).Migrate(context.Background())
	// assert.NoError(t, err)
	// // clear data
	// for _, table := range tables {
	// 	tabler, ok := table.(schema.Tabler)
	// 	assert.True(t, ok)
	// 	err = db.Exec(fmt.Sprintf("DROP TABLE %s;", tabler.TableName())).Error
	// 	assert.NoError(t, err)
	// }
}
