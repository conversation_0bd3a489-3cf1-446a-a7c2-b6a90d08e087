package migration

import (
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type OldUser struct {
	CorrelationId string
	Username      string `gorm:"column:user_name"`
	Status        int
}

type OldGasPoolBalance struct {
	CorrelationId string
	Deleted       bool
	// 0-正常 1-异常
	Status           int
	TotalBalance     decimal.Decimal `gorm:"type:decimal(32,6)"`
	AvailableBalance decimal.Decimal `gorm:"type:decimal(32,6)"`
	FreezeBalance    decimal.Decimal `gorm:"type:decimal(32,6)"`
}

type UserGasPool struct {
	gorm.Model
	UserID     uint            `gorm:"uniqueIndex;comment:用户ID，关联 wallet 表"`
	Amount     decimal.Decimal `gorm:"type:decimal(20,6);comment:gasPool余额"`
	LockAmount decimal.Decimal `gorm:"type:decimal(20,6);comment:锁定余额"`
	UsedCount  int64           `gorm:"comment:使用次数"`
}

func (UserGasPool) TableName() string {
	return "user_gas_pool"
}
