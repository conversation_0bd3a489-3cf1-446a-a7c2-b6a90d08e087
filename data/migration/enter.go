package migration

import (
	"byd_wallet/model"
	"context"
	"fmt"
	"log"

	"gorm.io/gorm"
)

type Data struct {
	oldDB, db *gorm.DB
}

func NewData(oldDB *gorm.DB, db *gorm.DB) *Data {
	return &Data{oldDB: oldDB, db: db}
}

func (d Data) Migrate(ctx context.Context) error {
	return d.migrateUsersAndGasPoolBalances(ctx)
}

func (d Data) migrateUsersAndGasPoolBalances(ctx context.Context) error {
	var oldUsers []*OldUser
	if err := d.oldDB.WithContext(ctx).Table("b_user_info").Find(&oldUsers).Error; err != nil {
		return err
	}
	for _, oldUser := range oldUsers {
		if oldUser.Username == "" {
			continue
		}
		user := &model.User{
			Username: oldUser.Username,
			Enable:   oldUser.Status == 0,
		}
		if err := d.db.WithContext(ctx).Create(user).Error; err != nil {
			return fmt.Errorf("migrate wallet fail: username=%s, err=%s", oldUser.Username, err.Error())
		}
		var gps []*OldGasPoolBalance
		if err := d.oldDB.WithContext(ctx).Table("b_gas_pool_balance").
			Where("correlation_id=?", oldUser.CorrelationId).
			Where("deleted=0").Find(&gps).Error; err != nil {
			return fmt.Errorf("get OldGasPoolBalance correlation_id=%s, err: %s", oldUser.CorrelationId, err.Error())
		}
		if len(gps) == 0 {
			continue
		}
		if len(gps) > 1 {
			log.Printf("data migration warn: OldGasPoolBalance record greater than 1.correlation_id=%s\n", oldUser.CorrelationId)
		}
		gp := gps[0]
		if err := d.db.WithContext(ctx).Create(&UserGasPool{
			UserID:     user.ID,
			Amount:     gp.AvailableBalance,
			LockAmount: gp.FreezeBalance,
		}).Error; err != nil {
			return fmt.Errorf("migrate UserGasPool fail: username=%s, err=%s", oldUser.Username, err.Error())
		}
	}

	return nil
}
