# BYD Wallet Golang

project base on [Go-Kratos](https://go-kratos.dev/docs/)

## QuickStart
install [Buf CLI](https://buf.build/docs/cli/installation/)

install tools
```shell
make init
```

run projects
```shell
# ~/.bashrc or ~/.zshrc
export GOPATH=$HOME/go
export PATH=$PATH:$GOPATH/bin

kratos run
```

add new api
```shell
kratos add proto api/xx/v1/xx.proto

# generate api proto
make api
```


```shell
# 添加solana 初使token
go run main.go version.go migrator initialize addspltoken -conf ./../../configs/bydwallet
```
more: [Makefile](./Makefile)


## Notes
gorm psql unique composite index
```go
type TableModel struct {
    A string `gorm:"index:,unique,composite:a_b"`
    B string `gorm:"index:,unique,composite:a_b"`
}
```

## http 错误信息国际化
1. 错误定义: `api/wallet/v1/error.proto`
2. 国际化文件定义: `internal/server/middleware/localize`, 
3. 目前只支持中文和英文, 加语言需要改`internal/server/middleware/localize/i18n.go`和增加对应的locale.*.toml文件
4. 只需在`service`层去返回`kratos.Error`
5. 后续如果需要更细的错误信息，再考虑在biz/data层返回`kratos.Error`