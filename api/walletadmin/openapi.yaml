# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: ""
    version: 0.0.1
paths:
    /admin/v1/address/list_address:
        get:
            tags:
                - AddressSrv
            description: 地址列表
            operationId: AddressSrv_ListAddress
            parameters:
                - name: page
                  in: query
                  description: 页码
                  schema:
                    type: string
                - name: page_size
                  in: query
                  description: 页容量
                  schema:
                    type: string
                - name: chain_index
                  in: query
                  description: 链索引(传值-1查询全部链)
                  schema:
                    type: string
                - name: address
                  in: query
                  description: 地址
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.ListAddressReply'
    /admin/v1/admin/login:
        post:
            tags:
                - AdminSrv
            operationId: AdminSrv_Login
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.walletadmin.v1.LoginReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.LoginReply'
    /admin/v1/app-versions:
        get:
            tags:
                - AppVersionService
            description: 版本列表
            operationId: AppVersionService_ListAppVersions
            parameters:
                - name: app_type
                  in: query
                  schema:
                    type: string
                - name: page
                  in: query
                  schema:
                    type: string
                - name: page_size
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.ListAppVersionsReply'
        post:
            tags:
                - AppVersionService
            description: 创建版本
            operationId: AppVersionService_CreateAppVersion
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.walletadmin.v1.CreateAppVersionReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.CreateAppVersionReply'
    /admin/v1/app-versions/published:
        get:
            tags:
                - AppVersionService
            description: 获取已发布版本列表
            operationId: AppVersionService_ListPublishedVersions
            parameters:
                - name: app_type
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.ListPublishedVersionsReply'
    /admin/v1/app-versions/{id}:
        get:
            tags:
                - AppVersionService
            description: 获取版本详情
            operationId: AppVersionService_GetAppVersion
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.AppVersionInfo'
        put:
            tags:
                - AppVersionService
            description: 更新版本
            operationId: AppVersionService_UpdateAppVersion
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.walletadmin.v1.UpdateAppVersionReq'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
        delete:
            tags:
                - AppVersionService
            description: 删除版本
            operationId: AppVersionService_DeleteAppVersion
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/chain/list_chain:
        get:
            tags:
                - ChainSrv
            description: 公链列表
            operationId: ChainSrv_ListChain
            parameters:
                - name: chain_name
                  in: query
                  description: 公链名称
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.ListChainReply'
    /admin/v1/chain/update_chain:
        put:
            tags:
                - ChainSrv
            description: 更新公链记录
            operationId: ChainSrv_UpdateChain
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.walletadmin.v1.UpdateChainReq'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/chain/update_chain_sort:
        patch:
            tags:
                - ChainSrv
            description: 更新公链排序
            operationId: ChainSrv_UpdateChainSort
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.walletadmin.v1.UpdateChainSortReq'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/coin/create_coin_star:
        post:
            tags:
                - CoinSrv
            description: 加入添加货币列表
            operationId: CoinSrv_CreateCoinStar
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.walletadmin.v1.CreateCoinStarReq'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/coin/delete_coin_star:
        delete:
            tags:
                - CoinSrv
            description: 移除添加货币列表
            operationId: CoinSrv_DeleteCoinStar
            parameters:
                - name: id
                  in: query
                  description: 记录ID
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/coin/list_coin:
        get:
            tags:
                - CoinSrv
            description: 币种列表
            operationId: CoinSrv_ListCoin
            parameters:
                - name: page
                  in: query
                  description: 页码
                  schema:
                    type: string
                - name: page_size
                  in: query
                  description: 页容量
                  schema:
                    type: string
                - name: chain_index
                  in: query
                  description: 链索引(传值-1查询全部链)
                  schema:
                    type: string
                - name: symbol
                  in: query
                  description: 币符号
                  schema:
                    type: string
                - name: address
                  in: query
                  description: 币合约地址
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.ListCoinReply'
    /admin/v1/coin/list_coin_star:
        get:
            tags:
                - CoinSrv
            description: 添加货币列表
            operationId: CoinSrv_ListCoinStar
            parameters:
                - name: page
                  in: query
                  description: 页码
                  schema:
                    type: string
                - name: page_size
                  in: query
                  description: 页容量
                  schema:
                    type: string
                - name: chain_index
                  in: query
                  description: 链索引(传值-1查询全部链)
                  schema:
                    type: string
                - name: symbol
                  in: query
                  description: 币符号
                  schema:
                    type: string
                - name: address
                  in: query
                  description: 币合约地址
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.ListCoinStarReply'
    /admin/v1/coin/update_coin:
        put:
            tags:
                - CoinSrv
            description: 更新币种
            operationId: CoinSrv_UpdateCoin
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.walletadmin.v1.UpdateCoinReq'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/coin/update_coin_star_sort:
        patch:
            tags:
                - CoinSrv
            description: 更新添加货币列表排序
            operationId: CoinSrv_UpdateCoinStarSort
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.walletadmin.v1.UpdateCoinStarSortReq'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /admin/v1/dapp:
        get:
            tags:
                - AdminDappSrv
            description: ListDapp 查询dapps
            operationId: AdminDappSrv_ListDapp
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.ListDappReply'
        post:
            tags:
                - AdminDappSrv
            description: CreateDapp 添加DAPP
            operationId: AdminDappSrv_CreateDapp
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.walletadmin.v1.CreateDappReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.CreateDappReply'
    /admin/v1/dapp/category:
        get:
            tags:
                - AdminDappSrv
            description: ListDappCategory 查询dapp分类
            operationId: AdminDappSrv_ListDappCategory
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.ListDappCategoryReply'
        post:
            tags:
                - AdminDappSrv
            description: CreateDappCategory 添加DAPP分类
            operationId: AdminDappSrv_CreateDappCategory
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.walletadmin.v1.CreateDappCategoryReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.CreateDappCategoryReply'
    /admin/v1/dapp/category/navigation:
        get:
            tags:
                - AdminDappSrv
            description: ListDappNavigation 首页导航栏列表
            operationId: AdminDappSrv_ListDappNavigation
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.ListDappNavigationReply'
        put:
            tags:
                - AdminDappSrv
            description: BatchUpdateDappNavigation 批量更新首页导航栏(可用于更新排序)
            operationId: AdminDappSrv_BatchUpdateDappNavigation
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.walletadmin.v1.BatchUpdateDappNavigationReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.BatchUpdateDappNavigationReply'
        post:
            tags:
                - AdminDappSrv
            description: CreateDappNavigation 添加DAPP首页导航栏
            operationId: AdminDappSrv_CreateDappNavigation
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.walletadmin.v1.CreateDappNavigationReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.CreateDappNavigationReply'
    /admin/v1/dapp/category/navigation/sort:
        put:
            tags:
                - AdminDappSrv
            description: SortDappNavigation 首页导航栏排序
            operationId: AdminDappSrv_SortDappNavigation
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.walletadmin.v1.SortReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.SortReply'
    /admin/v1/dapp/category/navigation/{id}:
        delete:
            tags:
                - AdminDappSrv
            description: DeleteDappNavigation 删除首页导航栏
            operationId: AdminDappSrv_DeleteDappNavigation
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.DeleteDappNavigationReply'
    /admin/v1/dapp/category/{id}:
        put:
            tags:
                - AdminDappSrv
            description: UpdateDappCategory 更新DAPP分类
            operationId: AdminDappSrv_UpdateDappCategory
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.walletadmin.v1.UpdateDappCategoryReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.UpdateDappCategoryReply'
        delete:
            tags:
                - AdminDappSrv
            description: DeleteDappCategoryRel 删除分类
            operationId: AdminDappSrv_DeleteDappCategory
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.DeleteDappCategoryReply'
    /admin/v1/dapp/category/{id}/rel:
        get:
            tags:
                - AdminDappSrv
            description: ListDappCategoryRel 查询分类dapp
            operationId: AdminDappSrv_ListDappCategoryRel
            parameters:
                - name: id
                  in: path
                  description: 分类id
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.ListDappCategoryRelReply'
        post:
            tags:
                - AdminDappSrv
            description: CreateDappCategoryRel 添加分类dapp
            operationId: AdminDappSrv_CreateDappCategoryRel
            parameters:
                - name: id
                  in: path
                  description: 分类id
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.walletadmin.v1.CreateDappCategoryRelReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.CreateDappCategoryRelReply'
        delete:
            tags:
                - AdminDappSrv
            description: DeleteDappCategoryRel 删除分类dapp
            operationId: AdminDappSrv_DeleteDappCategoryRel
            parameters:
                - name: id
                  in: path
                  description: 分类id
                  required: true
                  schema:
                    type: string
                - name: dapp_id
                  in: query
                  description: dapp id
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.DeleteDappCategoryRelReply'
    /admin/v1/dapp/index:
        get:
            tags:
                - AdminDappSrv
            description: ListDappIndex 获取dapp首页列表
            operationId: AdminDappSrv_ListDappIndex
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.ListDappIndexReply'
        put:
            tags:
                - AdminDappSrv
            description: BatchUpdateDappIndex 批量更新dapp首页（可用于更新排序）
            operationId: AdminDappSrv_BatchUpdateDappIndex
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.walletadmin.v1.BatchUpdateDappIndexReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.BatchUpdateDappIndexReply'
        post:
            tags:
                - AdminDappSrv
            description: CreateDappIndex 添加DAPP首页
            operationId: AdminDappSrv_CreateDappIndex
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.walletadmin.v1.CreateDappIndexReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.CreateDappIndexReply'
    /admin/v1/dapp/index/sort:
        put:
            tags:
                - AdminDappSrv
            description: SortDappIndex dapp首页排序
            operationId: AdminDappSrv_SortDappIndex
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.walletadmin.v1.SortReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.SortReply'
    /admin/v1/dapp/index/{id}:
        delete:
            tags:
                - AdminDappSrv
            description: DeleteDappIndex 删除dapp首页
            operationId: AdminDappSrv_DeleteDappIndex
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.DeleteDappIndexReply'
    /admin/v1/dapp/topic:
        get:
            tags:
                - AdminDappSrv
            description: ListDappTopic 查询dapp专题
            operationId: AdminDappSrv_ListDappTopic
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.ListDappTopicReply'
        post:
            tags:
                - AdminDappSrv
            description: CreateDappTopic 添加DAPP专题
            operationId: AdminDappSrv_CreateDappTopic
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.walletadmin.v1.CreateDappTopicReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.CreateDappTopicReply'
    /admin/v1/dapp/topic/{id}:
        put:
            tags:
                - AdminDappSrv
            description: UpdateDappTopic 更新DAPP专题
            operationId: AdminDappSrv_UpdateDappTopic
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.walletadmin.v1.UpdateDappTopicReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.UpdateDappTopicReply'
        delete:
            tags:
                - AdminDappSrv
            description: DeleteDappTopic 删除dapp专题
            operationId: AdminDappSrv_DeleteDappTopic
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.DeleteDappTopicReply'
    /admin/v1/dapp/topic/{id}/rel:
        get:
            tags:
                - AdminDappSrv
            description: ListDappTopicRel 查询专题dapp
            operationId: AdminDappSrv_ListDappTopicRel
            parameters:
                - name: id
                  in: path
                  description: 专题id
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.ListDappTopicRelReply'
        post:
            tags:
                - AdminDappSrv
            description: CreateDappTopicRel 添加专题dapp
            operationId: AdminDappSrv_CreateDappTopicRel
            parameters:
                - name: id
                  in: path
                  description: 专题id
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.walletadmin.v1.CreateDappTopicRelReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.CreateDappTopicRelReply'
        delete:
            tags:
                - AdminDappSrv
            description: DeleteDappTopicRel 删除专题dapp
            operationId: AdminDappSrv_DeleteDappTopicRel
            parameters:
                - name: id
                  in: path
                  description: 专题id
                  required: true
                  schema:
                    type: string
                - name: dapp_id
                  in: query
                  description: dapp id
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.DeleteDappTopicRelReply'
    /admin/v1/dapp/{id}:
        put:
            tags:
                - AdminDappSrv
            description: UpdateDapp 更新DAPP
            operationId: AdminDappSrv_UpdateDapp
            parameters:
                - name: id
                  in: path
                  description: dapp主键id
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.walletadmin.v1.UpdateDappReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.UpdateDappReply'
        delete:
            tags:
                - AdminDappSrv
            description: DeleteDapp 删除DAPP
            operationId: AdminDappSrv_DeleteDapp
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.DeleteDappReply'
    /admin/v1/presign:
        post:
            tags:
                - FileSrv
            description: |-
                GeneratePresignedRequest 生成预签名的请求
                 相关文档: https://docs.aws.amazon.com/AmazonS3/latest/API/s3_example_s3_Scenario_PresignedUrl_section.html
            operationId: FileSrv_GeneratePresignedRequest
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.walletadmin.v1.GeneratePresignedRequestReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.GeneratePresignedRequestReply'
    /admin/v1/swap/channel:
        get:
            tags:
                - SwapService
            description: 查询兑换渠道
            operationId: SwapService_ListSwapChannel
            parameters:
                - name: page
                  in: query
                  description: 页码 从1开始
                  schema:
                    type: string
                - name: page_size
                  in: query
                  description: 每页展示条数
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.ListSwapChannelReply'
    /admin/v1/swap/channel/{id}:
        put:
            tags:
                - SwapService
            description: 更新兑换渠道
            operationId: SwapService_UpdateSwapChannel
            parameters:
                - name: id
                  in: path
                  description: 记录id
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.walletadmin.v1.UpdateSwapChannelRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.UpdateSwapChannelReply'
    /admin/v1/swap/record:
        get:
            tags:
                - SwapService
            description: 查询兑换记录列表
            operationId: SwapService_ListSwapRecord
            parameters:
                - name: page
                  in: query
                  description: 页码 从1开始
                  schema:
                    type: string
                - name: page_size
                  in: query
                  description: 每页展示条数
                  schema:
                    type: string
                - name: address
                  in: query
                  description: 交易币种合约地址
                  schema:
                    type: string
                - name: channel_id
                  in: query
                  description: 兑换渠道id, 不传查询所有
                  schema:
                    type: string
                - name: status
                  in: query
                  description: |-
                    兑换状态
                     pending(处理中), wait_kyc(等待KYC验证), wait_refund(等待退款), refund_complete(退款完成)
                     wait_deposit_send_fail(充币失败), wait_for_information(异常订单-等待处理)
                     终态： success(兑换成功), fail(兑换失败)，timeout(超时-未在指定时间内充币)
                  schema:
                    type: string
                - name: from_address
                  in: query
                  description: 发起账户地址
                  schema:
                    type: string
                - name: symbol
                  in: query
                  description: 交易币种名称
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.ListSwapRecordReply'
    /admin/v1/swap/record/{id}:
        get:
            tags:
                - SwapService
            description: 查询兑换记录
            operationId: SwapService_GetSwapRecord
            parameters:
                - name: id
                  in: path
                  description: 兑换记录id
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.SwapRecord'
    /admin/v1/swap/token:
        get:
            tags:
                - SwapService
            description: 查询兑换的币种列表
            operationId: SwapService_ListToken
            parameters:
                - name: chain_index
                  in: query
                  description: 公链索引，传-1 获取所有
                  schema:
                    type: string
                - name: symbol
                  in: query
                  description: 简称
                  schema:
                    type: string
                - name: address
                  in: query
                  description: 合约号
                  schema:
                    type: string
                - name: channel_id
                  in: query
                  description: 兑换渠道id(商户id)
                  schema:
                    type: string
                - name: page
                  in: query
                  description: 页码 从1开始
                  schema:
                    type: string
                - name: page_size
                  in: query
                  description: 每页展示条数
                  schema:
                    type: string
                - name: native
                  in: query
                  description: 是否是原生代币
                  schema:
                    type: boolean
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.ListTokenReply'
    /admin/v1/swap/token/hot:
        get:
            tags:
                - SwapService
            description: 查询热门代币
            operationId: SwapService_ListHotToken
            parameters:
                - name: chain_index
                  in: query
                  description: 公链索引，传-1 获取所有
                  schema:
                    type: string
                - name: symbol
                  in: query
                  description: 币种
                  schema:
                    type: string
                - name: address
                  in: query
                  description: 合约地址
                  schema:
                    type: string
                - name: page
                  in: query
                  description: 页码 从1开始
                  schema:
                    type: string
                - name: page_size
                  in: query
                  description: 每页展示条数
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.ListHotTokenReply'
        post:
            tags:
                - SwapService
            description: 增加热门代币
            operationId: SwapService_CreateHotToken
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.walletadmin.v1.CreateHotTokenRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.CreateHotTokenReply'
    /admin/v1/swap/token/hot/sort:
        put:
            tags:
                - SwapService
            description: 热门代币排序
            operationId: SwapService_SortHotToken
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.walletadmin.v1.CommonSortReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.CommonSortReply'
    /admin/v1/swap/token/hot/{id}:
        delete:
            tags:
                - SwapService
            description: 删除热门代币
            operationId: SwapService_DeleteHotToken
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.DeleteHotTokenReply'
    /admin/v1/swap/token/{id}:
        put:
            tags:
                - SwapService
            description: 更新兑换币种
            operationId: SwapService_UpdateToken
            parameters:
                - name: id
                  in: path
                  description: 记录id
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.walletadmin.v1.UpdateTokenRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.UpdateTokenReply'
    /admin/v1/tx/list_tx:
        get:
            tags:
                - TxSrv
            description: 交易列表
            operationId: TxSrv_ListTx
            parameters:
                - name: page
                  in: query
                  description: 页码
                  schema:
                    type: string
                - name: page_size
                  in: query
                  description: 页容量
                  schema:
                    type: string
                - name: chain_index
                  in: query
                  description: 链索引(默认bitcoin)
                  schema:
                    type: string
                - name: from_address
                  in: query
                  description: 转出地址
                  schema:
                    type: string
                - name: to_address
                  in: query
                  description: 转入地址
                  schema:
                    type: string
                - name: tx_hash
                  in: query
                  description: 交易哈希
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.ListTxReply'
    /admin/v1/user/list_user:
        get:
            tags:
                - UserSrv
            description: 用户列表
            operationId: UserSrv_ListUser
            parameters:
                - name: page
                  in: query
                  description: 页码
                  schema:
                    type: string
                - name: page_size
                  in: query
                  description: 页容量
                  schema:
                    type: string
                - name: id
                  in: query
                  description: 用户ID
                  schema:
                    type: string
                - name: boss_id
                  in: query
                  description: BOSS ID
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.ListUserReply'
components:
    schemas:
        api.walletadmin.v1.Address:
            type: object
            properties:
                id:
                    type: string
                    description: 地址ID
                boss_id:
                    type: string
                    description: BOSS ID
                chain_index:
                    type: string
                    description: 链索引
                address:
                    type: string
                    description: 地址
                created_at:
                    type: string
                    description: 创建时间(时间戳,单位秒)
                chain_name:
                    type: string
                    description: 链名称
        api.walletadmin.v1.AppVersionI18N:
            type: object
            properties:
                language:
                    type: string
                description:
                    type: string
            description: 多语言描述
        api.walletadmin.v1.AppVersionInfo:
            type: object
            properties:
                id:
                    type: string
                version:
                    type: string
                app_type:
                    type: string
                download_url:
                    type: string
                force_update:
                    type: boolean
                i18n_descriptions:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.AppVersionI18N'
                created_at:
                    type: string
                updated_at:
                    type: string
                reminder_type:
                    type: integer
                    format: enum
                min_compatible_version:
                    type: string
                official_download_url:
                    type: string
            description: 版本信息
        api.walletadmin.v1.BatchUpdateDappIndexReply:
            type: object
            properties: {}
        api.walletadmin.v1.BatchUpdateDappIndexReq:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.UpdatedDappIndex'
        api.walletadmin.v1.BatchUpdateDappNavigationReply:
            type: object
            properties: {}
        api.walletadmin.v1.BatchUpdateDappNavigationReq:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.DappNavigation'
            description: UpdateDappNavigationSort 批量更新导航栏排序请求
        api.walletadmin.v1.BlockchainNetwork:
            type: object
            properties:
                id:
                    type: string
                name:
                    type: string
                    description: 名称
                symbol:
                    type: string
                    description: 矿币
                sort_order:
                    type: string
                    description: 序号
                blockchain_url:
                    type: string
                    description: 网络图标
                token_url:
                    type: string
                    description: 矿币图标
                address:
                    type: string
                    description: 授权合约地址
            description: BlockchainNetwork 区块链网络
        api.walletadmin.v1.Chain:
            type: object
            properties:
                id:
                    type: string
                    description: 记录ID
                chain_name:
                    type: string
                    description: 链名称
                chain_index:
                    type: string
                    description: 链索引
                symbol:
                    type: string
                    description: 币符号
                decimals:
                    type: string
                    description: 精度位数
                chain_type:
                    type: string
                    description: 链类型
                currency_block:
                    type: string
                    description: 当前同步块高
                is_syncing:
                    type: boolean
                    description: 是否正在同步
                is_display:
                    type: boolean
                    description: 是否展示
                blockchain_url:
                    type: string
                    description: 网络图标
                token_url:
                    type: string
                    description: 币图标
                chain_id:
                    type: string
                    description: 网络ID
                sort_order:
                    type: string
                    description: 排序
        api.walletadmin.v1.Coin:
            type: object
            properties:
                id:
                    type: string
                    description: 记录ID
                name:
                    type: string
                    description: 币名称
                symbol:
                    type: string
                    description: 币符号
                chain_index:
                    type: string
                    description: 链索引
                decimals:
                    type: string
                    description: 精度位数
                address:
                    type: string
                    description: 币合约地址
                created_at:
                    type: string
                    description: 创建时间(时间戳,单位秒)
                chain_name:
                    type: string
                    description: 链名称
                logo_url:
                    type: string
                    description: 币图标
                is_display:
                    type: boolean
                    description: 是否展示
                chain_id:
                    type: string
                    description: 网络ID
        api.walletadmin.v1.CoinStar:
            type: object
            properties:
                id:
                    type: string
                    description: 记录ID
                created_at:
                    type: string
                    description: 创建时间(时间戳,单位秒)
                name:
                    type: string
                    description: 币名称
                symbol:
                    type: string
                    description: 币符号
                chain_index:
                    type: string
                    description: 链索引
                decimals:
                    type: string
                    description: 精度位数
                address:
                    type: string
                    description: 币合约地址
                chain_name:
                    type: string
                    description: 链名称
                logo_url:
                    type: string
                    description: 币图标
                sort_order:
                    type: string
                    description: 排序
        api.walletadmin.v1.CommonSortReply:
            type: object
            properties: {}
        api.walletadmin.v1.CommonSortReq:
            type: object
            properties:
                id:
                    type: string
                    description: 记录ID
                sort_type:
                    type: integer
                    description: 排序类型
                    format: enum
            description: 排序请求
        api.walletadmin.v1.CreateAppVersionReply:
            type: object
            properties: {}
            description: 创建版本响应
        api.walletadmin.v1.CreateAppVersionReq:
            type: object
            properties:
                version:
                    type: string
                app_type:
                    type: string
                download_url:
                    type: string
                force_update:
                    type: boolean
                i18n_descriptions:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.AppVersionI18N'
                reminder_type:
                    type: integer
                    format: enum
                min_compatible_version:
                    type: string
                official_download_url:
                    type: string
            description: 创建版本请求
        api.walletadmin.v1.CreateCoinStarReq:
            type: object
            properties:
                coin_id:
                    type: string
                    description: 币种ID
        api.walletadmin.v1.CreateDappCategoryRelReply:
            type: object
            properties: {}
        api.walletadmin.v1.CreateDappCategoryRelReq:
            type: object
            properties:
                id:
                    type: string
                    description: 分类id
                dapp_id:
                    type: string
                    description: dapp id
        api.walletadmin.v1.CreateDappCategoryReply:
            type: object
            properties: {}
        api.walletadmin.v1.CreateDappCategoryReq:
            type: object
            properties:
                show:
                    type: boolean
                    description: 是否展示，默认传true
                i18ns:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.DappCategoryI18N'
                    description: 分类多语言
        api.walletadmin.v1.CreateDappIndexReply:
            type: object
            properties: {}
        api.walletadmin.v1.CreateDappIndexReq:
            type: object
            properties:
                owner_type:
                    type: string
                    description: |-
                        专题: dapp_topic
                         分类: dapp_category
                owner_id:
                    type: string
                    description: 专题或者分类的主键id
        api.walletadmin.v1.CreateDappNavigationReply:
            type: object
            properties: {}
        api.walletadmin.v1.CreateDappNavigationReq:
            type: object
            properties:
                dapp_category_id:
                    type: string
                    description: 分类id
                show:
                    type: boolean
                    description: 是否展示，默认true
        api.walletadmin.v1.CreateDappReply:
            type: object
            properties: {}
        api.walletadmin.v1.CreateDappReq:
            type: object
            properties:
                dapp:
                    $ref: '#/components/schemas/api.walletadmin.v1.Dapp'
        api.walletadmin.v1.CreateDappTopicRelReply:
            type: object
            properties: {}
        api.walletadmin.v1.CreateDappTopicRelReq:
            type: object
            properties:
                id:
                    type: string
                    description: 专题id
                dapp_id:
                    type: string
                    description: dapp id
                sort_order:
                    type: string
                    description: 序号
            description: CreateDappTopicRel 请求消息
        api.walletadmin.v1.CreateDappTopicReply:
            type: object
            properties: {}
        api.walletadmin.v1.CreateDappTopicReq:
            type: object
            properties:
                data_topic:
                    allOf:
                        - $ref: '#/components/schemas/api.walletadmin.v1.DappTopic'
                    description: 专题
        api.walletadmin.v1.CreateHotTokenReply:
            type: object
            properties: {}
        api.walletadmin.v1.CreateHotTokenRequest:
            type: object
            properties:
                swappable_token_id:
                    type: string
                    description: 通过搜索出来的数据获取
                is_all:
                    type: boolean
                    description: 是否是全部
        api.walletadmin.v1.Dapp:
            type: object
            properties:
                id:
                    type: string
                    description: 主键id
                logo:
                    type: string
                    description: dapp图标
                link:
                    type: string
                    description: dapp内容链接
                tags:
                    type: string
                    description: 标签
                hot:
                    type: boolean
                    description: 是否热门
                show:
                    type: boolean
                    description: 是否展示
                i18ns:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.DappI18N'
                    description: 多语言
                networks:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.BlockchainNetwork'
                    description: 支持的网络
                created_at:
                    type: string
                    description: 添加时间 Unix时间戳
        api.walletadmin.v1.DappCategory:
            type: object
            properties:
                id:
                    type: string
                show:
                    type: boolean
                    description: 是否展示，默认true
                i18ns:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.DappCategoryI18N'
                    description: 分类多语言
                app_count:
                    type: string
                    description: app数量
                hot_count:
                    type: string
                    description: 热门数量
                navigation:
                    type: boolean
                    description: 是否推荐到首页导航
        api.walletadmin.v1.DappCategoryI18N:
            type: object
            properties:
                name:
                    type: string
                    description: 分类名称
                summary:
                    type: string
                    description: 分类摘要
                language:
                    type: string
                    description: 语言缩写 参考 https://zh.wikipedia.org/wiki/ISO_639-1
        api.walletadmin.v1.DappI18N:
            type: object
            properties:
                name:
                    type: string
                    description: dapp名称
                summary:
                    type: string
                    description: 摘要
                language:
                    type: string
                    description: 语言缩写 参考 https://zh.wikipedia.org/wiki/ISO_639-1
        api.walletadmin.v1.DappIndex:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.Dapp'
                id:
                    type: string
                sort_order:
                    type: string
                    description: 序号
                name:
                    type: string
                    description: 分类或者专题名称
                owner_type:
                    type: string
                    description: |-
                        专题: dapp_topic
                         分类: dapp_category
        api.walletadmin.v1.DappNavigation:
            type: object
            properties:
                id:
                    type: string
                show:
                    type: boolean
                    description: 是否推荐到首页导航
                i18ns:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.DappCategoryI18N'
                    description: 分类多语言
                app_count:
                    type: string
                    description: app数量
                hot_count:
                    type: string
                    description: 热门数量
                sort_order:
                    type: string
                    description: 排序序号
        api.walletadmin.v1.DappTopic:
            type: object
            properties:
                show:
                    type: boolean
                    description: 是否展示，默认true
                background_url:
                    type: string
                    description: 背景图url
                i18ns:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.DappTopicI18N'
                    description: 专题多语言
        api.walletadmin.v1.DappTopicI18N:
            type: object
            properties:
                name:
                    type: string
                    description: 专题名称
                summary:
                    type: string
                    description: 简介
                language:
                    type: string
                    description: 语言缩写 参考 https://zh.wikipedia.org/wiki/ISO_639-1
                title:
                    type: string
                    description: 大标题
                top_title:
                    type: string
                    description: 上小标题
                bottom_title:
                    type: string
                    description: 下小标题
            description: DappTopicI18N dapp专题多语言
        api.walletadmin.v1.DappTopicInfo:
            type: object
            properties:
                id:
                    type: string
                show:
                    type: boolean
                    description: 是否展示，默认true
                background_url:
                    type: string
                    description: 背景图url
                i18ns:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.DappTopicI18N'
                    description: 专题多语言
                app_count:
                    type: string
                    description: app数量
                created_at:
                    type: string
                    description: 添加时间 Unix时间戳
            description: DappTopicInfo DAPP专题信息（用于查询返回）
        api.walletadmin.v1.DeleteDappCategoryRelReply:
            type: object
            properties: {}
        api.walletadmin.v1.DeleteDappCategoryReply:
            type: object
            properties: {}
        api.walletadmin.v1.DeleteDappIndexReply:
            type: object
            properties: {}
        api.walletadmin.v1.DeleteDappNavigationReply:
            type: object
            properties: {}
        api.walletadmin.v1.DeleteDappReply:
            type: object
            properties: {}
        api.walletadmin.v1.DeleteDappTopicRelReply:
            type: object
            properties: {}
        api.walletadmin.v1.DeleteDappTopicReply:
            type: object
            properties: {}
        api.walletadmin.v1.DeleteHotTokenReply:
            type: object
            properties: {}
        api.walletadmin.v1.GeneratePresignedRequestReply:
            type: object
            properties:
                url:
                    type: string
                method:
                    type: string
                    description: '请求方法: GET/POST/PUT...'
                headers:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.Header'
                    description: 请求头
        api.walletadmin.v1.GeneratePresignedRequestReq:
            type: object
            properties:
                biz_type:
                    type: string
                    description: 业务类型(dapp_logo,token_logo)
                file_suffix:
                    type: string
                    description: 文件后缀名：jpg,png...
        api.walletadmin.v1.Header:
            type: object
            properties:
                key:
                    type: string
                value:
                    type: array
                    items:
                        type: string
        api.walletadmin.v1.ListAddressReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.Address'
                total_count:
                    type: string
                    description: 总记录数
        api.walletadmin.v1.ListAppVersionsReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.AppVersionInfo'
                total_count:
                    type: string
            description: 版本列表响应
        api.walletadmin.v1.ListChainReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.Chain'
        api.walletadmin.v1.ListCoinReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.Coin'
                total_count:
                    type: string
                    description: 总记录数
        api.walletadmin.v1.ListCoinStarReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.CoinStar'
                total_count:
                    type: string
                    description: 总记录数
        api.walletadmin.v1.ListDappCategoryRelReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.Dapp'
        api.walletadmin.v1.ListDappCategoryReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.DappCategory'
        api.walletadmin.v1.ListDappIndexReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.DappIndex'
        api.walletadmin.v1.ListDappNavigationReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.DappNavigation'
        api.walletadmin.v1.ListDappReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.Dapp'
        api.walletadmin.v1.ListDappTopicRelReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.Dapp'
        api.walletadmin.v1.ListDappTopicReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.DappTopicInfo'
        api.walletadmin.v1.ListHotTokenReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.SwappableToken'
                total_count:
                    type: string
                    description: 总条数
        api.walletadmin.v1.ListPublishedVersionsReply:
            type: object
            properties:
                versions:
                    type: array
                    items:
                        type: string
            description: 获取已发布版本列表响应
        api.walletadmin.v1.ListSwapChannelReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.SwapChannel'
                total_count:
                    type: string
                    description: 总条数
        api.walletadmin.v1.ListSwapRecordReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.SwapRecord'
                total_count:
                    type: string
                    description: 总条数
        api.walletadmin.v1.ListTokenReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.SwappableToken'
                total_count:
                    type: string
                    description: 总条数
        api.walletadmin.v1.ListTxReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.Tx'
                total_count:
                    type: string
                    description: 总记录数
        api.walletadmin.v1.ListUserReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.User'
                total_count:
                    type: string
                    description: 总记录数
        api.walletadmin.v1.LoginReply:
            type: object
            properties:
                token:
                    type: string
        api.walletadmin.v1.LoginReq:
            type: object
            properties:
                username:
                    type: string
                password:
                    type: string
        api.walletadmin.v1.SortReply:
            type: object
            properties: {}
        api.walletadmin.v1.SortReq:
            type: object
            properties:
                current_sort:
                    type: string
                    description: 当前排序(来自列表的sort_order字段值)
                target_sort:
                    type: string
                    description: 更新后排序
                id:
                    type: string
                    description: 记录ID
            description: SortReq 排序请求
        api.walletadmin.v1.SwapChannel:
            type: object
            properties:
                id:
                    type: string
                    description: 记录ID
                name:
                    type: string
                    description: 平台名称
                enable:
                    type: boolean
                    description: 是否可用
        api.walletadmin.v1.SwapDetail:
            type: object
            properties:
                chain_index:
                    type: string
                    description: 链索引
                explorer_url:
                    type: string
                    description: 区块浏览器url
                hash:
                    type: string
                    description: 交易hash
                status:
                    type: string
                    description: 交易状态
                collected:
                    type: boolean
                    description: 是否收录
        api.walletadmin.v1.SwapRecord:
            type: object
            properties:
                id:
                    type: string
                    description: 兑换记录id
                swapped_at:
                    type: string
                    description: 兑换时间(unix时间戳)
                status:
                    type: string
                    description: |-
                        兑换状态
                         pending(处理中), wait_kyc(等待KYC验证), wait_refund(等待退款), refund_complete(退款完成)
                         wait_deposit_send_fail(充币失败), wait_for_information(异常订单-等待处理)
                         终态： success(兑换成功), fail(兑换失败)，timeout(超时-未在指定时间内充币)
                from:
                    $ref: '#/components/schemas/api.walletadmin.v1.SwapToken'
                to:
                    $ref: '#/components/schemas/api.walletadmin.v1.SwapToken'
                gas_fee:
                    type: string
                    description: 用户兑换消耗的GasFee
                fee_rate:
                    type: string
                    description: 兑换手续费率
                hash:
                    type: string
                    description: 用户发起的交易hash
                approval_hash:
                    type: string
                    description: 授权hash
                height:
                    type: string
                    description: 区块高度
                dex:
                    type: string
                    description: 兑换平台名称
                dex_logo:
                    type: string
                    description: 兑换平台logo url
                swap_price:
                    type: string
                    description: 兑换价格
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.SwapDetail'
                    description: 兑换详情列表
                finished_at:
                    type: string
                    description: 结束时间(unix时间戳)
        api.walletadmin.v1.SwapToken:
            type: object
            properties:
                token_address:
                    type: string
                    description: 代币合约地址
                address:
                    type: string
                    description: 用户地址
                chain_index:
                    type: string
                    description: 链
                amount:
                    type: string
                    description: 数量
                token_logo:
                    type: string
                    description: token logo url
                symbol:
                    type: string
                    description: 币种
                decimals:
                    type: string
                    description: 精度
        api.walletadmin.v1.SwappableToken:
            type: object
            properties:
                id:
                    type: string
                    description: 记录ID
                name:
                    type: string
                    description: 币名称
                symbol:
                    type: string
                    description: 币符号
                chain_index:
                    type: string
                    description: 链名称
                decimals:
                    type: string
                    description: 精度位数
                address:
                    type: string
                    description: 币合约地址
                created_at:
                    type: string
                    description: 创建时间(时间戳,单位秒)
                chain_name:
                    type: string
                    description: 链名称
                logo_url:
                    type: string
                    description: 币图标
                display:
                    type: boolean
                    description: 是否展示
                chain_id:
                    type: string
                    description: 网络ID
                enable:
                    type: boolean
                    description: 是否已上架
                channels:
                    type: array
                    items:
                        type: string
                    description: 商户平台
                is_all:
                    type: boolean
                    description: 是否是所有
        api.walletadmin.v1.Tx:
            type: object
            properties:
                id:
                    type: string
                    description: 记录ID
                name:
                    type: string
                    description: 币名称
                symbol:
                    type: string
                    description: 币符号
                chain_index:
                    type: string
                    description: 链索引
                decimals:
                    type: string
                    description: 精度位数
                token_address:
                    type: string
                    description: 币合约地址
                tx_time:
                    type: string
                    description: 交易(时间戳,单位秒)
                chain_name:
                    type: string
                    description: 链名称
                from_address:
                    type: string
                    description: 转出地址
                to_address:
                    type: string
                    description: 转入地址
                status:
                    type: string
                    description: 交易状态(success,fail)
                value:
                    type: string
                    description: 交易金额
                fee:
                    type: string
                    description: 交易手续费
                method:
                    type: string
                    description: 交易类型
        api.walletadmin.v1.UpdateAppVersionReq:
            type: object
            properties:
                id:
                    type: string
                i18n_descriptions:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.AppVersionI18N'
            description: 更新版本请求
        api.walletadmin.v1.UpdateChainReq:
            type: object
            properties:
                blockchain_url:
                    type: string
                    description: 网络图标
                token_url:
                    type: string
                    description: 币图标
                is_display:
                    type: boolean
                    description: 是否展示
                id:
                    type: string
                    description: 记录ID
        api.walletadmin.v1.UpdateChainSortReq:
            type: object
            properties:
                current_sort:
                    type: string
                    description: 当前排序(来自列表的sort_order字段值)
                target_sort:
                    type: string
                    description: 更新后排序(数值越大,排序越靠前)
                id:
                    type: string
                    description: 记录ID
        api.walletadmin.v1.UpdateCoinReq:
            type: object
            properties:
                id:
                    type: string
                    description: 记录ID
                logo_url:
                    type: string
                    description: 币图标
                is_display:
                    type: boolean
                    description: 是否展示
        api.walletadmin.v1.UpdateCoinStarSortReq:
            type: object
            properties:
                id:
                    type: string
                    description: 记录ID
                current_sort:
                    type: string
                    description: 当前排序(来自列表的sort_order字段值)
                target_sort:
                    type: string
                    description: 添加排序(数值越大,排序越靠前)
        api.walletadmin.v1.UpdateDappCategoryReply:
            type: object
            properties: {}
        api.walletadmin.v1.UpdateDappCategoryReq:
            type: object
            properties:
                id:
                    type: string
                show:
                    type: boolean
                    description: 是否展示，默认true
                i18ns:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.DappCategoryI18N'
                    description: 分类多语言
        api.walletadmin.v1.UpdateDappReply:
            type: object
            properties: {}
        api.walletadmin.v1.UpdateDappReq:
            type: object
            properties:
                id:
                    type: string
                    description: dapp主键id
                dapp:
                    allOf:
                        - $ref: '#/components/schemas/api.walletadmin.v1.Dapp'
                    description: dapp
        api.walletadmin.v1.UpdateDappTopicReply:
            type: object
            properties: {}
        api.walletadmin.v1.UpdateDappTopicReq:
            type: object
            properties:
                id:
                    type: string
                show:
                    type: boolean
                    description: 是否展示，默认true
                background_url:
                    type: string
                    description: 背景图url
                i18ns:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.DappTopicI18N'
                    description: 专题多语言
            description: UpdateDappTopic 更新DAPP专题请求
        api.walletadmin.v1.UpdateSwapChannelReply:
            type: object
            properties: {}
        api.walletadmin.v1.UpdateSwapChannelRequest:
            type: object
            properties:
                id:
                    type: string
                    description: 记录id
                enable:
                    type: boolean
                    description: 是否关闭通道
        api.walletadmin.v1.UpdateTokenReply:
            type: object
            properties: {}
        api.walletadmin.v1.UpdateTokenRequest:
            type: object
            properties:
                id:
                    type: string
                    description: 记录id
                display:
                    type: boolean
                    description: 是否展示
        api.walletadmin.v1.UpdatedDappIndex:
            type: object
            properties:
                id:
                    type: string
                sort_order:
                    type: string
                    description: 序号
        api.walletadmin.v1.User:
            type: object
            properties:
                id:
                    type: string
                    description: 用户ID
                boss_id:
                    type: string
                    description: BOSS ID
                created_at:
                    type: string
                    description: 创建时间(时间戳,单位秒)
                enable:
                    type: boolean
                    description: 是否可用
tags:
    - name: AddressSrv
    - name: AdminDappSrv
    - name: AdminSrv
    - name: AppVersionService
    - name: ChainSrv
    - name: CoinSrv
    - name: FileSrv
    - name: SwapService
    - name: TxSrv
    - name: UserSrv
