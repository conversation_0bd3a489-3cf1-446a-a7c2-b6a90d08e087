// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: api/walletadmin/v1/address.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AddressSrv_ListAddress_FullMethodName = "/api.walletadmin.v1.AddressSrv/ListAddress"
)

// AddressSrvClient is the client API for AddressSrv service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AddressSrvClient interface {
	// 地址列表
	ListAddress(ctx context.Context, in *ListAddressReq, opts ...grpc.CallOption) (*ListAddressReply, error)
}

type addressSrvClient struct {
	cc grpc.ClientConnInterface
}

func NewAddressSrvClient(cc grpc.ClientConnInterface) AddressSrvClient {
	return &addressSrvClient{cc}
}

func (c *addressSrvClient) ListAddress(ctx context.Context, in *ListAddressReq, opts ...grpc.CallOption) (*ListAddressReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAddressReply)
	err := c.cc.Invoke(ctx, AddressSrv_ListAddress_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AddressSrvServer is the server API for AddressSrv service.
// All implementations must embed UnimplementedAddressSrvServer
// for forward compatibility.
type AddressSrvServer interface {
	// 地址列表
	ListAddress(context.Context, *ListAddressReq) (*ListAddressReply, error)
	mustEmbedUnimplementedAddressSrvServer()
}

// UnimplementedAddressSrvServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAddressSrvServer struct{}

func (UnimplementedAddressSrvServer) ListAddress(context.Context, *ListAddressReq) (*ListAddressReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAddress not implemented")
}
func (UnimplementedAddressSrvServer) mustEmbedUnimplementedAddressSrvServer() {}
func (UnimplementedAddressSrvServer) testEmbeddedByValue()                    {}

// UnsafeAddressSrvServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AddressSrvServer will
// result in compilation errors.
type UnsafeAddressSrvServer interface {
	mustEmbedUnimplementedAddressSrvServer()
}

func RegisterAddressSrvServer(s grpc.ServiceRegistrar, srv AddressSrvServer) {
	// If the following call pancis, it indicates UnimplementedAddressSrvServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AddressSrv_ServiceDesc, srv)
}

func _AddressSrv_ListAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAddressReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AddressSrvServer).ListAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AddressSrv_ListAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AddressSrvServer).ListAddress(ctx, req.(*ListAddressReq))
	}
	return interceptor(ctx, in, info, handler)
}

// AddressSrv_ServiceDesc is the grpc.ServiceDesc for AddressSrv service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AddressSrv_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.walletadmin.v1.AddressSrv",
	HandlerType: (*AddressSrvServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListAddress",
			Handler:    _AddressSrv_ListAddress_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/walletadmin/v1/address.proto",
}
