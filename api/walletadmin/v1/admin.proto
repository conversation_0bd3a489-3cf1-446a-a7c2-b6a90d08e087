syntax = "proto3";

package api.walletadmin.v1;

option go_package = "byd_wallet/api/walletadmin/v1;v1";
option java_multiple_files = true;
option java_package = "api.walletadmin.v1";

import "google/api/annotations.proto";
import "buf/validate/validate.proto";

service AdminSrv {
	rpc Login(LoginReq) returns (LoginReply) {
		option (google.api.http) = {
			post: "/admin/v1/admin/login"
			body: "*"
		};
	}
}

message LoginReq {
	string username = 1 [(buf.validate.field).string.min_len = 1];
	string password = 2 [(buf.validate.field).string.min_len = 1];
}

message LoginReply {
	string token = 1;
}