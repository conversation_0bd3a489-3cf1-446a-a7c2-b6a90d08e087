// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: api/walletadmin/v1/coin.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	CoinSrv_ListCoin_FullMethodName           = "/api.walletadmin.v1.CoinSrv/ListCoin"
	CoinSrv_UpdateCoin_FullMethodName         = "/api.walletadmin.v1.CoinSrv/UpdateCoin"
	CoinSrv_CreateCoinStar_FullMethodName     = "/api.walletadmin.v1.CoinSrv/CreateCoinStar"
	CoinSrv_ListCoinStar_FullMethodName       = "/api.walletadmin.v1.CoinSrv/ListCoinStar"
	CoinSrv_DeleteCoinStar_FullMethodName     = "/api.walletadmin.v1.CoinSrv/DeleteCoinStar"
	CoinSrv_UpdateCoinStarSort_FullMethodName = "/api.walletadmin.v1.CoinSrv/UpdateCoinStarSort"
)

// CoinSrvClient is the client API for CoinSrv service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CoinSrvClient interface {
	// 币种列表
	ListCoin(ctx context.Context, in *ListCoinReq, opts ...grpc.CallOption) (*ListCoinReply, error)
	// 更新币种
	UpdateCoin(ctx context.Context, in *UpdateCoinReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 加入添加货币列表
	CreateCoinStar(ctx context.Context, in *CreateCoinStarReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 添加货币列表
	ListCoinStar(ctx context.Context, in *ListCoinStarReq, opts ...grpc.CallOption) (*ListCoinStarReply, error)
	// 移除添加货币列表
	DeleteCoinStar(ctx context.Context, in *DeleteCoinStarReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 更新添加货币列表排序
	UpdateCoinStarSort(ctx context.Context, in *UpdateCoinStarSortReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type coinSrvClient struct {
	cc grpc.ClientConnInterface
}

func NewCoinSrvClient(cc grpc.ClientConnInterface) CoinSrvClient {
	return &coinSrvClient{cc}
}

func (c *coinSrvClient) ListCoin(ctx context.Context, in *ListCoinReq, opts ...grpc.CallOption) (*ListCoinReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListCoinReply)
	err := c.cc.Invoke(ctx, CoinSrv_ListCoin_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *coinSrvClient) UpdateCoin(ctx context.Context, in *UpdateCoinReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, CoinSrv_UpdateCoin_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *coinSrvClient) CreateCoinStar(ctx context.Context, in *CreateCoinStarReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, CoinSrv_CreateCoinStar_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *coinSrvClient) ListCoinStar(ctx context.Context, in *ListCoinStarReq, opts ...grpc.CallOption) (*ListCoinStarReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListCoinStarReply)
	err := c.cc.Invoke(ctx, CoinSrv_ListCoinStar_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *coinSrvClient) DeleteCoinStar(ctx context.Context, in *DeleteCoinStarReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, CoinSrv_DeleteCoinStar_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *coinSrvClient) UpdateCoinStarSort(ctx context.Context, in *UpdateCoinStarSortReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, CoinSrv_UpdateCoinStarSort_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CoinSrvServer is the server API for CoinSrv service.
// All implementations must embed UnimplementedCoinSrvServer
// for forward compatibility.
type CoinSrvServer interface {
	// 币种列表
	ListCoin(context.Context, *ListCoinReq) (*ListCoinReply, error)
	// 更新币种
	UpdateCoin(context.Context, *UpdateCoinReq) (*emptypb.Empty, error)
	// 加入添加货币列表
	CreateCoinStar(context.Context, *CreateCoinStarReq) (*emptypb.Empty, error)
	// 添加货币列表
	ListCoinStar(context.Context, *ListCoinStarReq) (*ListCoinStarReply, error)
	// 移除添加货币列表
	DeleteCoinStar(context.Context, *DeleteCoinStarReq) (*emptypb.Empty, error)
	// 更新添加货币列表排序
	UpdateCoinStarSort(context.Context, *UpdateCoinStarSortReq) (*emptypb.Empty, error)
	mustEmbedUnimplementedCoinSrvServer()
}

// UnimplementedCoinSrvServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedCoinSrvServer struct{}

func (UnimplementedCoinSrvServer) ListCoin(context.Context, *ListCoinReq) (*ListCoinReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCoin not implemented")
}
func (UnimplementedCoinSrvServer) UpdateCoin(context.Context, *UpdateCoinReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCoin not implemented")
}
func (UnimplementedCoinSrvServer) CreateCoinStar(context.Context, *CreateCoinStarReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCoinStar not implemented")
}
func (UnimplementedCoinSrvServer) ListCoinStar(context.Context, *ListCoinStarReq) (*ListCoinStarReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCoinStar not implemented")
}
func (UnimplementedCoinSrvServer) DeleteCoinStar(context.Context, *DeleteCoinStarReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCoinStar not implemented")
}
func (UnimplementedCoinSrvServer) UpdateCoinStarSort(context.Context, *UpdateCoinStarSortReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCoinStarSort not implemented")
}
func (UnimplementedCoinSrvServer) mustEmbedUnimplementedCoinSrvServer() {}
func (UnimplementedCoinSrvServer) testEmbeddedByValue()                 {}

// UnsafeCoinSrvServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CoinSrvServer will
// result in compilation errors.
type UnsafeCoinSrvServer interface {
	mustEmbedUnimplementedCoinSrvServer()
}

func RegisterCoinSrvServer(s grpc.ServiceRegistrar, srv CoinSrvServer) {
	// If the following call pancis, it indicates UnimplementedCoinSrvServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&CoinSrv_ServiceDesc, srv)
}

func _CoinSrv_ListCoin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCoinReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CoinSrvServer).ListCoin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CoinSrv_ListCoin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CoinSrvServer).ListCoin(ctx, req.(*ListCoinReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CoinSrv_UpdateCoin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCoinReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CoinSrvServer).UpdateCoin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CoinSrv_UpdateCoin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CoinSrvServer).UpdateCoin(ctx, req.(*UpdateCoinReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CoinSrv_CreateCoinStar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCoinStarReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CoinSrvServer).CreateCoinStar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CoinSrv_CreateCoinStar_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CoinSrvServer).CreateCoinStar(ctx, req.(*CreateCoinStarReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CoinSrv_ListCoinStar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCoinStarReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CoinSrvServer).ListCoinStar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CoinSrv_ListCoinStar_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CoinSrvServer).ListCoinStar(ctx, req.(*ListCoinStarReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CoinSrv_DeleteCoinStar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteCoinStarReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CoinSrvServer).DeleteCoinStar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CoinSrv_DeleteCoinStar_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CoinSrvServer).DeleteCoinStar(ctx, req.(*DeleteCoinStarReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CoinSrv_UpdateCoinStarSort_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCoinStarSortReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CoinSrvServer).UpdateCoinStarSort(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CoinSrv_UpdateCoinStarSort_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CoinSrvServer).UpdateCoinStarSort(ctx, req.(*UpdateCoinStarSortReq))
	}
	return interceptor(ctx, in, info, handler)
}

// CoinSrv_ServiceDesc is the grpc.ServiceDesc for CoinSrv service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CoinSrv_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.walletadmin.v1.CoinSrv",
	HandlerType: (*CoinSrvServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListCoin",
			Handler:    _CoinSrv_ListCoin_Handler,
		},
		{
			MethodName: "UpdateCoin",
			Handler:    _CoinSrv_UpdateCoin_Handler,
		},
		{
			MethodName: "CreateCoinStar",
			Handler:    _CoinSrv_CreateCoinStar_Handler,
		},
		{
			MethodName: "ListCoinStar",
			Handler:    _CoinSrv_ListCoinStar_Handler,
		},
		{
			MethodName: "DeleteCoinStar",
			Handler:    _CoinSrv_DeleteCoinStar_Handler,
		},
		{
			MethodName: "UpdateCoinStarSort",
			Handler:    _CoinSrv_UpdateCoinStarSort_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/walletadmin/v1/coin.proto",
}
