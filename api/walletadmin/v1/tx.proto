syntax = "proto3";

package api.walletadmin.v1;

option go_package = "byd_wallet/api/walletadmin/v1;v1";
option java_multiple_files = true;
option java_package = "api.walletadmin.v1";

import "google/api/annotations.proto";
import "buf/validate/validate.proto";

service TxSrv {
	// 交易列表
	rpc ListTx (ListTxReq) returns (ListTxReply) {
		option (google.api.http) = {
			get: "/admin/v1/tx/list_tx"
		};
	};
}

message ListTxReq {
	// 页码
	int64 page = 1 [(buf.validate.field).int64.gt = 0];
	// 页容量
	int64 page_size = 2 [(buf.validate.field).int64 = {gt: 0,lte:100}];
	// 链索引(默认bitcoin)
	int64 chain_index = 3 [(buf.validate.field).int64.gt = -1];
	// 转出地址
	optional string from_address = 4;
	// 转入地址
	optional string to_address = 5;
	// 交易哈希
	optional string tx_hash = 6;
}

message Tx {
	// 记录ID
	uint64 id = 1;
	// 币名称
	string name = 2;
	// 币符号
	string symbol = 3;
	// 链索引
	int64 chain_index = 4;
	// 精度位数
	int64 decimals = 5;
	// 币合约地址
	string token_address = 6;
	// 交易(时间戳,单位秒)
	int64 tx_time = 7;
	// 链名称
	string chain_name = 8;
	// 转出地址
	string from_address = 9;
	// 转入地址
	string to_address = 10;
	// 交易状态(success,fail)
	string status = 11;
	// 交易金额
	string value = 12;
	// 交易手续费
	string fee = 13;
	// 交易类型
	string method = 14;
}

message ListTxReply {
	repeated Tx list = 1;
	// 总记录数
	int64 total_count = 2;
}