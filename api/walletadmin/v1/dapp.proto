syntax = "proto3";

package api.walletadmin.v1;

import "buf/validate/validate.proto";
import "google/api/annotations.proto";

option go_package = "byd_wallet/api/walletadmin/v1;v1";

service AdminDappSrv {
  // CreateDapp 添加DAPP
  rpc CreateDapp(CreateDappReq) returns (CreateDappReply) {
    option (google.api.http) = {
      post: "/admin/v1/dapp"
      body: "*"
    };
  }
  // DeleteDapp 删除DAPP
  rpc DeleteDapp(DeleteDappReq) returns (DeleteDappReply) {
    option (google.api.http) = {delete: "/admin/v1/dapp/{id}"};
  }
  // UpdateDapp 更新DAPP
  rpc UpdateDapp(UpdateDappReq) returns (UpdateDappReply) {
    option (google.api.http) = {
      put: "/admin/v1/dapp/{id}"
      body: "*"
    };
  }
  // ListDapp 查询dapps
  rpc ListDapp(ListDappReq) returns (ListDappReply) {
    option (google.api.http) = {get: "/admin/v1/dapp"};
  }
  // CreateDappCategory 添加DAPP分类
  rpc CreateDappCategory(CreateDappCategoryReq) returns (CreateDappCategoryReply) {
    option (google.api.http) = {
      post: "/admin/v1/dapp/category"
      body: "*"
    };
  }
  // UpdateDappCategory 更新DAPP分类
  rpc UpdateDappCategory(UpdateDappCategoryReq) returns (UpdateDappCategoryReply) {
    option (google.api.http) = {
      put: "/admin/v1/dapp/category/{id}"
      body: "*"
    };
  }
  // ListDappCategory 查询dapp分类
  rpc ListDappCategory(ListDappCategoryReq) returns (ListDappCategoryReply) {
    option (google.api.http) = {get: "/admin/v1/dapp/category"};
  }
  // DeleteDappCategoryRel 删除分类
  rpc DeleteDappCategory(DeleteDappCategoryReq) returns (DeleteDappCategoryReply) {
    option (google.api.http) = {
      delete: "/admin/v1/dapp/category/{id}"
    };
  }
  // CreateDappCategoryRel 添加分类dapp
  rpc CreateDappCategoryRel(CreateDappCategoryRelReq) returns (CreateDappCategoryRelReply) {
    option (google.api.http) = {
      post: "/admin/v1/dapp/category/{id}/rel"
      body: "*"
    };
  }
  // DeleteDappCategoryRel 删除分类dapp
  rpc DeleteDappCategoryRel(DeleteDappCategoryRelReq) returns (DeleteDappCategoryRelReply) {
    option (google.api.http) = {
      delete: "/admin/v1/dapp/category/{id}/rel"
    };
  }
  // ListDappCategoryRel 查询分类dapp
  rpc ListDappCategoryRel(ListDappCategoryRelReq) returns (ListDappCategoryRelReply) {
    option (google.api.http) = {
      get: "/admin/v1/dapp/category/{id}/rel"
    };
  }
  // CreateDappTopic 添加DAPP专题
  rpc CreateDappTopic(CreateDappTopicReq) returns (CreateDappTopicReply) {
    option (google.api.http) = {
      post: "/admin/v1/dapp/topic"
      body: "*"
    };
  }
  // DeleteDappTopic 删除dapp专题
  rpc DeleteDappTopic(DeleteDappTopicReq) returns (DeleteDappTopicReply) {
    option (google.api.http) = {
      delete: "/admin/v1/dapp/topic/{id}"
    };
  }
  // UpdateDappTopic 更新DAPP专题
  rpc UpdateDappTopic(UpdateDappTopicReq) returns (UpdateDappTopicReply) {
    option (google.api.http) = {
      put: "/admin/v1/dapp/topic/{id}"
      body: "*"
    };
  }
  // ListDappTopic 查询dapp专题
  rpc ListDappTopic(ListDappTopicReq) returns (ListDappTopicReply) {
    option (google.api.http) = {get: "/admin/v1/dapp/topic"};
  }
  // CreateDappNavigation 添加DAPP首页导航栏
  rpc CreateDappNavigation(CreateDappNavigationReq) returns (CreateDappNavigationReply) {
    option (google.api.http) = {
      post: "/admin/v1/dapp/category/navigation"
      body: "*"
    };
  }
  // DeleteDappNavigation 删除首页导航栏
  rpc DeleteDappNavigation(DeleteDappNavigationReq) returns (DeleteDappNavigationReply) {
    option (google.api.http) = {delete: "/admin/v1/dapp/category/navigation/{id}"};
  }
  // ListDappNavigation 首页导航栏列表
  rpc ListDappNavigation(ListDappNavigationReq) returns (ListDappNavigationReply) {
    option (google.api.http) = {get: "/admin/v1/dapp/category/navigation"};
  }
  // BatchUpdateDappNavigation 批量更新首页导航栏(可用于更新排序)
  rpc BatchUpdateDappNavigation(BatchUpdateDappNavigationReq) returns (BatchUpdateDappNavigationReply) {
    option (google.api.http) = {
      put: "/admin/v1/dapp/category/navigation"
      body: "*"
    };
  }
  // SortDappNavigation 首页导航栏排序
  rpc SortDappNavigation(SortReq) returns (SortReply) {
    option (google.api.http) = {
      put: "/admin/v1/dapp/category/navigation/sort"
      body: "*"
    };
  }
  // CreateDappIndex 添加DAPP首页
  rpc CreateDappIndex(CreateDappIndexReq) returns (CreateDappIndexReply) {
    option (google.api.http) = {
      post: "/admin/v1/dapp/index"
      body: "*"
    };
  }
  // ListDappIndex 获取dapp首页列表
  rpc ListDappIndex(ListDappIndexReq) returns (ListDappIndexReply) {
    option (google.api.http) = {get: "/admin/v1/dapp/index"};
  }
  // DeleteDappIndex 删除dapp首页
  rpc DeleteDappIndex(DeleteDappIndexReq) returns (DeleteDappIndexReply) {
    option (google.api.http) = {delete: "/admin/v1/dapp/index/{id}"};
  }
  // BatchUpdateDappIndex 批量更新dapp首页（可用于更新排序）
  rpc BatchUpdateDappIndex(BatchUpdateDappIndexReq) returns (BatchUpdateDappIndexReply) {
    option (google.api.http) = {
      put: "/admin/v1/dapp/index"
      body: "*"
    };
  }
  // SortDappIndex dapp首页排序
  rpc SortDappIndex(SortReq) returns (SortReply) {
    option (google.api.http) = {
      put: "/admin/v1/dapp/index/sort"
      body: "*"
    };
  }
  // CreateDappTopicRel 添加专题dapp
  rpc CreateDappTopicRel(CreateDappTopicRelReq) returns (CreateDappTopicRelReply) {
    option (google.api.http) = {
      post: "/admin/v1/dapp/topic/{id}/rel"
      body: "*"
    };
  }
  // DeleteDappTopicRel 删除专题dapp
  rpc DeleteDappTopicRel(DeleteDappTopicRelReq) returns (DeleteDappTopicRelReply) {
    option (google.api.http) = {
      delete: "/admin/v1/dapp/topic/{id}/rel"
    };
  }
  // ListDappTopicRel 查询专题dapp
  rpc ListDappTopicRel(ListDappTopicRelReq) returns (ListDappTopicRelReply) {
    option (google.api.http) = {
      get: "/admin/v1/dapp/topic/{id}/rel"
    };
  }
}

message DeleteDappTopicReq {
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
}

message DeleteDappTopicReply {}

message ListDappCategoryRelReq {
  // 分类id
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
}

message ListDappCategoryRelReply {
  repeated Dapp list = 1;
}

message DeleteDappCategoryRelReq {
  // 分类id
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
  // dapp id
  uint64 dapp_id = 2 [(buf.validate.field).uint64.gt = 0];
}

message DeleteDappCategoryRelReply {}

message CreateDappCategoryRelReq {
  // 分类id
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
  // dapp id
  uint64 dapp_id = 2 [(buf.validate.field).uint64.gt = 0];
}

message CreateDappCategoryRelReply {}

message CreateDappIndexReq {
  // 专题: dapp_topic
  // 分类: dapp_category
  string owner_type = 1;
  // 专题或者分类的主键id
  uint64 owner_id = 2;
}

message CreateDappIndexReply {}

message ListDappIndexReq {}

message ListDappIndexReply {
  repeated DappIndex list = 1;
}

message DeleteDappIndexReq {
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
}

message DeleteDappIndexReply {}

message DappIndex {
  repeated Dapp list = 1;
  uint64 id = 2;
  // 序号
  int64 sort_order = 3;
  // 分类或者专题名称
  string name = 4;
  // 专题: dapp_topic
  // 分类: dapp_category
  string owner_type = 5;
}

message UpdatedDappIndex {
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
  // 序号
  uint64 sort_order = 2;
}

message BatchUpdateDappIndexReq {
  repeated UpdatedDappIndex list = 1;
}

message BatchUpdateDappIndexReply {}

message CreateDappNavigationReq {
  // 分类id
  uint64 dapp_category_id = 1 [(buf.validate.field).uint64.gt = 0];
  // 是否展示，默认true
  bool show = 2;
}

message CreateDappNavigationReply {}

message DeleteDappNavigationReq {
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
}

message DeleteDappNavigationReply {}

message ListDappNavigationReq {}

message ListDappNavigationReply {
  repeated DappNavigation list = 1;
}

message DappNavigation {
  uint64 id = 1;
  // 是否推荐到首页导航
  bool show = 2;
  // 分类多语言
  repeated DappCategoryI18N i18ns = 3;
  // app数量
  int64 app_count = 4;
  // 热门数量
  int64 hot_count = 5;
  // 排序序号
  int64 sort_order = 6;
}

message CreateDappTopicReq {
  // 专题
  DappTopic data_topic = 1;
}

message CreateDappTopicReply {}

message DappTopic {
  // 是否展示，默认true
  bool show = 1;
  // 背景图url
  string background_url = 2 [(buf.validate.field).string.min_len = 1];
  // 专题多语言
  repeated DappTopicI18N i18ns = 3 [(buf.validate.field).repeated.min_items = 1];
}

// DappTopicI18N dapp专题多语言
message DappTopicI18N {
  // 专题名称
  string name = 1;
  // 简介
  string summary = 2 [(buf.validate.field).string.min_len = 1];
  // 语言缩写 参考 https://zh.wikipedia.org/wiki/ISO_639-1
  string language = 3 [(buf.validate.field).string.min_len = 1];
  // 大标题
  string title = 4 [(buf.validate.field).string.min_len = 1];
  // 上小标题
  string top_title = 5;
  // 下小标题
  string bottom_title = 6;
}

message DappTopicRel {
  uint64 dapp_id = 1 [(buf.validate.field).uint64.gt = 0];
  // 序号
  int64 sort_order = 2;
}

message ListDappCategoryReq {}

message ListDappCategoryReply {
  repeated DappCategory list = 1;
}

message UpdateDappCategoryReq {
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
  // 是否展示，默认true
  bool show = 2;
  // 分类多语言
  repeated DappCategoryI18N i18ns = 3 [(buf.validate.field).repeated.min_items = 1];
}

message UpdateDappCategoryReply {}

message DeleteDappCategoryReq {
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
}

message DeleteDappCategoryReply {}

message DappCategory {
  uint64 id = 1;
  // 是否展示，默认true
  bool show = 2;
  // 分类多语言
  repeated DappCategoryI18N i18ns = 3;
  // app数量
  int64 app_count = 4;
  // 热门数量
  int64 hot_count = 5;
  // 是否推荐到首页导航
  bool navigation = 6;
}

message DappCategoryRel {
  uint64 dapp_id = 1 [(buf.validate.field).uint64.gt = 0];
  int64 sort_order = 2;
}

message DappCategoryI18N {
  // 分类名称
  string name = 1 [(buf.validate.field).string.min_len = 1];
  // 分类摘要
  string summary = 2 [(buf.validate.field).string.min_len = 1];
  // 语言缩写 参考 https://zh.wikipedia.org/wiki/ISO_639-1
  string language = 3 [(buf.validate.field).string.min_len = 1];
}

message CreateDappCategoryReq {
  // 是否展示，默认传true
  bool show = 1;
  // 分类多语言
  repeated DappCategoryI18N i18ns = 2 [(buf.validate.field).repeated.min_items = 1];
}

message CreateDappCategoryReply {}

message ListDappReq {}

message ListDappReply {
  repeated Dapp list = 1;
}

message UpdateDappReq {
  // dapp主键id
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
  // dapp
  Dapp dapp = 2 [(buf.validate.field).required = true];
}

message UpdateDappReply {}

message DeleteDappReq {
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
}

message DeleteDappReply {}

message CreateDappReq {
  Dapp dapp = 1 [(buf.validate.field).required = true];
}

message Dapp {
  // 主键id
  uint64 id = 1;
  // dapp图标
  string logo = 2 [(buf.validate.field).string.uri = true];
  // dapp内容链接
  string link = 3 [(buf.validate.field).string.uri = true];
  // 标签
  string tags = 4;
  // 是否热门
  bool hot = 5;
  // 是否展示
  bool show = 6;
  // 多语言
  repeated DappI18N i18ns = 7 [(buf.validate.field).repeated.min_items = 1];
  // 支持的网络
  repeated BlockchainNetwork networks = 8 [(buf.validate.field).repeated.min_items = 1];
  // 添加时间 Unix时间戳
  int64 created_at = 9;
}

message DappI18N {
  // dapp名称
  string name = 1 [(buf.validate.field).string.min_len = 1];
  // 摘要
  string summary = 2 [(buf.validate.field).string.min_len = 1];
  // 语言缩写 参考 https://zh.wikipedia.org/wiki/ISO_639-1
  string language = 3 [(buf.validate.field).string.min_len = 1];
}

message CreateDappReply {}

// BlockchainNetwork 区块链网络
message BlockchainNetwork {
  uint64 id = 1;
  // 名称
  string name = 2;
  // 矿币
  string symbol = 3;
  // 序号
  int64 sort_order = 4;
  // 网络图标
  string blockchain_url = 5;
  // 矿币图标
  string token_url = 6;
  // 授权合约地址
  string address = 7;
}

// UpdateDappTopic 更新DAPP专题请求
message UpdateDappTopicReq {
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
  // 是否展示，默认true
  bool show = 2;
  // 背景图url
  string background_url = 3 [(buf.validate.field).string.min_len = 1];
  // 专题多语言
  repeated DappTopicI18N i18ns = 4 [(buf.validate.field).repeated.min_items = 1];
}

message UpdateDappTopicReply {}

// ListDappTopic 查询DAPP专题请求
message ListDappTopicReq {}

message ListDappTopicReply {
  repeated DappTopicInfo list = 1;
}

// DappTopicInfo DAPP专题信息（用于查询返回）
message DappTopicInfo {
  uint64 id = 1;
  // 是否展示，默认true
  bool show = 2;
  // 背景图url
  string background_url = 3;
  // 专题多语言
  repeated DappTopicI18N i18ns = 4;
  // app数量
  int64 app_count = 5;
  // 添加时间 Unix时间戳
  int64 created_at = 6;
}

// CreateDappTopicRel 请求消息
message CreateDappTopicRelReq {
  // 专题id
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
  // dapp id
  uint64 dapp_id = 2 [(buf.validate.field).uint64.gt = 0];
  // 序号
  int64 sort_order = 3;
}

message CreateDappTopicRelReply {}

// DeleteDappTopicRel 请求消息
message DeleteDappTopicRelReq {
  // 专题id
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
  // dapp id
  uint64 dapp_id = 2 [(buf.validate.field).uint64.gt = 0];
}

message DeleteDappTopicRelReply {}

// ListDappTopicRel 请求消息
message ListDappTopicRelReq {
  // 专题id
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
}

message ListDappTopicRelReply {
  repeated Dapp list = 1;
}

// UpdateDappNavigationSort 批量更新导航栏排序请求
message BatchUpdateDappNavigationReq {
  repeated DappNavigation list = 1 [(buf.validate.field).repeated.min_items = 2];
}

message BatchUpdateDappNavigationReply {}

// SortReq 排序请求
message SortReq {
  // 当前排序(来自列表的sort_order字段值)
  int64 current_sort = 1;
  // 更新后排序
  int64 target_sort = 2;
  // 记录ID
  uint64 id = 3;
}

message SortReply {}