// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: api/walletadmin/v1/file.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	FileSrv_GeneratePresignedRequest_FullMethodName = "/api.walletadmin.v1.FileSrv/GeneratePresignedRequest"
)

// FileSrvClient is the client API for FileSrv service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FileSrvClient interface {
	// GeneratePresignedRequest 生成预签名的请求
	// 相关文档: https://docs.aws.amazon.com/AmazonS3/latest/API/s3_example_s3_Scenario_PresignedUrl_section.html
	GeneratePresignedRequest(ctx context.Context, in *GeneratePresignedRequestReq, opts ...grpc.CallOption) (*GeneratePresignedRequestReply, error)
}

type fileSrvClient struct {
	cc grpc.ClientConnInterface
}

func NewFileSrvClient(cc grpc.ClientConnInterface) FileSrvClient {
	return &fileSrvClient{cc}
}

func (c *fileSrvClient) GeneratePresignedRequest(ctx context.Context, in *GeneratePresignedRequestReq, opts ...grpc.CallOption) (*GeneratePresignedRequestReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GeneratePresignedRequestReply)
	err := c.cc.Invoke(ctx, FileSrv_GeneratePresignedRequest_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FileSrvServer is the server API for FileSrv service.
// All implementations must embed UnimplementedFileSrvServer
// for forward compatibility.
type FileSrvServer interface {
	// GeneratePresignedRequest 生成预签名的请求
	// 相关文档: https://docs.aws.amazon.com/AmazonS3/latest/API/s3_example_s3_Scenario_PresignedUrl_section.html
	GeneratePresignedRequest(context.Context, *GeneratePresignedRequestReq) (*GeneratePresignedRequestReply, error)
	mustEmbedUnimplementedFileSrvServer()
}

// UnimplementedFileSrvServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedFileSrvServer struct{}

func (UnimplementedFileSrvServer) GeneratePresignedRequest(context.Context, *GeneratePresignedRequestReq) (*GeneratePresignedRequestReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GeneratePresignedRequest not implemented")
}
func (UnimplementedFileSrvServer) mustEmbedUnimplementedFileSrvServer() {}
func (UnimplementedFileSrvServer) testEmbeddedByValue()                 {}

// UnsafeFileSrvServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FileSrvServer will
// result in compilation errors.
type UnsafeFileSrvServer interface {
	mustEmbedUnimplementedFileSrvServer()
}

func RegisterFileSrvServer(s grpc.ServiceRegistrar, srv FileSrvServer) {
	// If the following call pancis, it indicates UnimplementedFileSrvServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&FileSrv_ServiceDesc, srv)
}

func _FileSrv_GeneratePresignedRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GeneratePresignedRequestReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileSrvServer).GeneratePresignedRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FileSrv_GeneratePresignedRequest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileSrvServer).GeneratePresignedRequest(ctx, req.(*GeneratePresignedRequestReq))
	}
	return interceptor(ctx, in, info, handler)
}

// FileSrv_ServiceDesc is the grpc.ServiceDesc for FileSrv service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FileSrv_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.walletadmin.v1.FileSrv",
	HandlerType: (*FileSrvServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GeneratePresignedRequest",
			Handler:    _FileSrv_GeneratePresignedRequest_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/walletadmin/v1/file.proto",
}
