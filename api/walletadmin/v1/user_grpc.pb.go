// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: api/walletadmin/v1/user.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	UserSrv_ListUser_FullMethodName = "/api.walletadmin.v1.UserSrv/ListUser"
)

// UserSrvClient is the client API for UserSrv service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserSrvClient interface {
	// 用户列表
	ListUser(ctx context.Context, in *ListUserReq, opts ...grpc.CallOption) (*ListUserReply, error)
}

type userSrvClient struct {
	cc grpc.ClientConnInterface
}

func NewUserSrvClient(cc grpc.ClientConnInterface) UserSrvClient {
	return &userSrvClient{cc}
}

func (c *userSrvClient) ListUser(ctx context.Context, in *ListUserReq, opts ...grpc.CallOption) (*ListUserReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListUserReply)
	err := c.cc.Invoke(ctx, UserSrv_ListUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserSrvServer is the server API for UserSrv service.
// All implementations must embed UnimplementedUserSrvServer
// for forward compatibility.
type UserSrvServer interface {
	// 用户列表
	ListUser(context.Context, *ListUserReq) (*ListUserReply, error)
	mustEmbedUnimplementedUserSrvServer()
}

// UnimplementedUserSrvServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedUserSrvServer struct{}

func (UnimplementedUserSrvServer) ListUser(context.Context, *ListUserReq) (*ListUserReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUser not implemented")
}
func (UnimplementedUserSrvServer) mustEmbedUnimplementedUserSrvServer() {}
func (UnimplementedUserSrvServer) testEmbeddedByValue()                 {}

// UnsafeUserSrvServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserSrvServer will
// result in compilation errors.
type UnsafeUserSrvServer interface {
	mustEmbedUnimplementedUserSrvServer()
}

func RegisterUserSrvServer(s grpc.ServiceRegistrar, srv UserSrvServer) {
	// If the following call pancis, it indicates UnimplementedUserSrvServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&UserSrv_ServiceDesc, srv)
}

func _UserSrv_ListUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserSrvServer).ListUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserSrv_ListUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserSrvServer).ListUser(ctx, req.(*ListUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

// UserSrv_ServiceDesc is the grpc.ServiceDesc for UserSrv service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserSrv_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.walletadmin.v1.UserSrv",
	HandlerType: (*UserSrvServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListUser",
			Handler:    _UserSrv_ListUser_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/walletadmin/v1/user.proto",
}
