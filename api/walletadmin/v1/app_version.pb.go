// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: api/walletadmin/v1/app_version.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReminderType int32

const (
	ReminderType_REMINDER_TYPE_UNSPECIFIED ReminderType = 0 // 未指定
	ReminderType_REMINDER_TYPE_EVERY_OPEN  ReminderType = 1 // 每次打开
	ReminderType_REMINDER_TYPE_DAILY       ReminderType = 2 // 每日
	ReminderType_REMINDER_TYPE_WEEKLY      ReminderType = 3 // 每周
	ReminderType_REMINDER_TYPE_NEVER       ReminderType = 4 // 从不
)

// Enum value maps for ReminderType.
var (
	ReminderType_name = map[int32]string{
		0: "REMINDER_TYPE_UNSPECIFIED",
		1: "REMINDER_TYPE_EVERY_OPEN",
		2: "REMINDER_TYPE_DAILY",
		3: "REMINDER_TYPE_WEEKLY",
		4: "REMINDER_TYPE_NEVER",
	}
	ReminderType_value = map[string]int32{
		"REMINDER_TYPE_UNSPECIFIED": 0,
		"REMINDER_TYPE_EVERY_OPEN":  1,
		"REMINDER_TYPE_DAILY":       2,
		"REMINDER_TYPE_WEEKLY":      3,
		"REMINDER_TYPE_NEVER":       4,
	}
)

func (x ReminderType) Enum() *ReminderType {
	p := new(ReminderType)
	*p = x
	return p
}

func (x ReminderType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReminderType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_walletadmin_v1_app_version_proto_enumTypes[0].Descriptor()
}

func (ReminderType) Type() protoreflect.EnumType {
	return &file_api_walletadmin_v1_app_version_proto_enumTypes[0]
}

func (x ReminderType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReminderType.Descriptor instead.
func (ReminderType) EnumDescriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_app_version_proto_rawDescGZIP(), []int{0}
}

// 多语言描述
type AppVersionI18N struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Language    string `protobuf:"bytes,1,opt,name=language,proto3" json:"language,omitempty"`       // 语言代码(zh,en,ja,es)
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"` // 版本描述
}

func (x *AppVersionI18N) Reset() {
	*x = AppVersionI18N{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppVersionI18N) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppVersionI18N) ProtoMessage() {}

func (x *AppVersionI18N) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppVersionI18N.ProtoReflect.Descriptor instead.
func (*AppVersionI18N) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_app_version_proto_rawDescGZIP(), []int{0}
}

func (x *AppVersionI18N) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *AppVersionI18N) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// 创建版本请求
type CreateAppVersionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version              string            `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`                                                                     // 版本号
	AppType              string            `protobuf:"bytes,2,opt,name=app_type,json=appType,proto3" json:"app_type,omitempty"`                                                      // APP类型
	DownloadUrl          string            `protobuf:"bytes,3,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`                                          // 下载地址
	ForceUpdate          bool              `protobuf:"varint,4,opt,name=force_update,json=forceUpdate,proto3" json:"force_update,omitempty"`                                         // 强制更新
	I18NDescriptions     []*AppVersionI18N `protobuf:"bytes,5,rep,name=i18n_descriptions,json=i18nDescriptions,proto3" json:"i18n_descriptions,omitempty"`                           // 多语言描述
	ReminderType         ReminderType      `protobuf:"varint,6,opt,name=reminder_type,json=reminderType,proto3,enum=api.walletadmin.v1.ReminderType" json:"reminder_type,omitempty"` // 提醒类型 (0:每次打开 1:每日 2:每周 3:从不)
	MinCompatibleVersion string            `protobuf:"bytes,7,opt,name=min_compatible_version,json=minCompatibleVersion,proto3" json:"min_compatible_version,omitempty"`             // 最低兼容版本
	OfficialDownloadUrl  string            `protobuf:"bytes,8,opt,name=official_download_url,json=officialDownloadUrl,proto3" json:"official_download_url,omitempty"`                // 官方下载地址
}

func (x *CreateAppVersionReq) Reset() {
	*x = CreateAppVersionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAppVersionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAppVersionReq) ProtoMessage() {}

func (x *CreateAppVersionReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAppVersionReq.ProtoReflect.Descriptor instead.
func (*CreateAppVersionReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_app_version_proto_rawDescGZIP(), []int{1}
}

func (x *CreateAppVersionReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *CreateAppVersionReq) GetAppType() string {
	if x != nil {
		return x.AppType
	}
	return ""
}

func (x *CreateAppVersionReq) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

func (x *CreateAppVersionReq) GetForceUpdate() bool {
	if x != nil {
		return x.ForceUpdate
	}
	return false
}

func (x *CreateAppVersionReq) GetI18NDescriptions() []*AppVersionI18N {
	if x != nil {
		return x.I18NDescriptions
	}
	return nil
}

func (x *CreateAppVersionReq) GetReminderType() ReminderType {
	if x != nil {
		return x.ReminderType
	}
	return ReminderType_REMINDER_TYPE_UNSPECIFIED
}

func (x *CreateAppVersionReq) GetMinCompatibleVersion() string {
	if x != nil {
		return x.MinCompatibleVersion
	}
	return ""
}

func (x *CreateAppVersionReq) GetOfficialDownloadUrl() string {
	if x != nil {
		return x.OfficialDownloadUrl
	}
	return ""
}

// 创建版本响应
type CreateAppVersionReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CreateAppVersionReply) Reset() {
	*x = CreateAppVersionReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAppVersionReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAppVersionReply) ProtoMessage() {}

func (x *CreateAppVersionReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAppVersionReply.ProtoReflect.Descriptor instead.
func (*CreateAppVersionReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_app_version_proto_rawDescGZIP(), []int{2}
}

// 更新版本请求
type UpdateAppVersionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               uint64            `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                    // 版本ID
	I18NDescriptions []*AppVersionI18N `protobuf:"bytes,2,rep,name=i18n_descriptions,json=i18nDescriptions,proto3" json:"i18n_descriptions,omitempty"` // 多语言描述
}

func (x *UpdateAppVersionReq) Reset() {
	*x = UpdateAppVersionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppVersionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppVersionReq) ProtoMessage() {}

func (x *UpdateAppVersionReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppVersionReq.ProtoReflect.Descriptor instead.
func (*UpdateAppVersionReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_app_version_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateAppVersionReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateAppVersionReq) GetI18NDescriptions() []*AppVersionI18N {
	if x != nil {
		return x.I18NDescriptions
	}
	return nil
}

// 删除版本请求
type DeleteAppVersionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` // 版本ID
}

func (x *DeleteAppVersionReq) Reset() {
	*x = DeleteAppVersionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAppVersionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAppVersionReq) ProtoMessage() {}

func (x *DeleteAppVersionReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAppVersionReq.ProtoReflect.Descriptor instead.
func (*DeleteAppVersionReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_app_version_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteAppVersionReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 版本列表请求
type ListAppVersionsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppType  string `protobuf:"bytes,1,opt,name=app_type,json=appType,proto3" json:"app_type,omitempty"`     // APP类型筛选
	Page     int64  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`                         // 页码
	PageSize int64  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"` // 每页数量
}

func (x *ListAppVersionsReq) Reset() {
	*x = ListAppVersionsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppVersionsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppVersionsReq) ProtoMessage() {}

func (x *ListAppVersionsReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppVersionsReq.ProtoReflect.Descriptor instead.
func (*ListAppVersionsReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_app_version_proto_rawDescGZIP(), []int{5}
}

func (x *ListAppVersionsReq) GetAppType() string {
	if x != nil {
		return x.AppType
	}
	return ""
}

func (x *ListAppVersionsReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListAppVersionsReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// 版本列表响应
type ListAppVersionsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List       []*AppVersionInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`                                // 版本列表
	TotalCount int64             `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"` // 总数
}

func (x *ListAppVersionsReply) Reset() {
	*x = ListAppVersionsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppVersionsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppVersionsReply) ProtoMessage() {}

func (x *ListAppVersionsReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppVersionsReply.ProtoReflect.Descriptor instead.
func (*ListAppVersionsReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_app_version_proto_rawDescGZIP(), []int{6}
}

func (x *ListAppVersionsReply) GetList() []*AppVersionInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListAppVersionsReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

// 获取版本详情请求
type GetAppVersionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` // 版本ID
}

func (x *GetAppVersionReq) Reset() {
	*x = GetAppVersionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppVersionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppVersionReq) ProtoMessage() {}

func (x *GetAppVersionReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppVersionReq.ProtoReflect.Descriptor instead.
func (*GetAppVersionReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_app_version_proto_rawDescGZIP(), []int{7}
}

func (x *GetAppVersionReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 获取已发布版本列表请求
type ListPublishedVersionsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppType string `protobuf:"bytes,1,opt,name=app_type,json=appType,proto3" json:"app_type,omitempty"` // APP类型筛选
}

func (x *ListPublishedVersionsReq) Reset() {
	*x = ListPublishedVersionsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPublishedVersionsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPublishedVersionsReq) ProtoMessage() {}

func (x *ListPublishedVersionsReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPublishedVersionsReq.ProtoReflect.Descriptor instead.
func (*ListPublishedVersionsReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_app_version_proto_rawDescGZIP(), []int{8}
}

func (x *ListPublishedVersionsReq) GetAppType() string {
	if x != nil {
		return x.AppType
	}
	return ""
}

// 获取已发布版本列表响应
type ListPublishedVersionsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []string `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"` // 版本号列表
}

func (x *ListPublishedVersionsReply) Reset() {
	*x = ListPublishedVersionsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPublishedVersionsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPublishedVersionsReply) ProtoMessage() {}

func (x *ListPublishedVersionsReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPublishedVersionsReply.ProtoReflect.Descriptor instead.
func (*ListPublishedVersionsReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_app_version_proto_rawDescGZIP(), []int{9}
}

func (x *ListPublishedVersionsReply) GetList() []string {
	if x != nil {
		return x.List
	}
	return nil
}

// 版本信息
type AppVersionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                   uint64            `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                                              // 版本ID
	Version              string            `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`                                                                     // 版本号
	AppType              string            `protobuf:"bytes,3,opt,name=app_type,json=appType,proto3" json:"app_type,omitempty"`                                                      // APP类型
	DownloadUrl          string            `protobuf:"bytes,4,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`                                          // 下载地址
	ForceUpdate          bool              `protobuf:"varint,5,opt,name=force_update,json=forceUpdate,proto3" json:"force_update,omitempty"`                                         // 强制更新
	I18NDescriptions     []*AppVersionI18N `protobuf:"bytes,6,rep,name=i18n_descriptions,json=i18nDescriptions,proto3" json:"i18n_descriptions,omitempty"`                           // 多语言描述
	CreatedAt            int64             `protobuf:"varint,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                                               // 创建时间
	UpdatedAt            int64             `protobuf:"varint,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                                               // 更新时间
	ReminderType         ReminderType      `protobuf:"varint,9,opt,name=reminder_type,json=reminderType,proto3,enum=api.walletadmin.v1.ReminderType" json:"reminder_type,omitempty"` // 提醒类型
	MinCompatibleVersion string            `protobuf:"bytes,10,opt,name=min_compatible_version,json=minCompatibleVersion,proto3" json:"min_compatible_version,omitempty"`            // 最低兼容版本
	OfficialDownloadUrl  string            `protobuf:"bytes,11,opt,name=official_download_url,json=officialDownloadUrl,proto3" json:"official_download_url,omitempty"`               // 官方下载地址
}

func (x *AppVersionInfo) Reset() {
	*x = AppVersionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppVersionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppVersionInfo) ProtoMessage() {}

func (x *AppVersionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppVersionInfo.ProtoReflect.Descriptor instead.
func (*AppVersionInfo) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_app_version_proto_rawDescGZIP(), []int{10}
}

func (x *AppVersionInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AppVersionInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *AppVersionInfo) GetAppType() string {
	if x != nil {
		return x.AppType
	}
	return ""
}

func (x *AppVersionInfo) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

func (x *AppVersionInfo) GetForceUpdate() bool {
	if x != nil {
		return x.ForceUpdate
	}
	return false
}

func (x *AppVersionInfo) GetI18NDescriptions() []*AppVersionI18N {
	if x != nil {
		return x.I18NDescriptions
	}
	return nil
}

func (x *AppVersionInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *AppVersionInfo) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *AppVersionInfo) GetReminderType() ReminderType {
	if x != nil {
		return x.ReminderType
	}
	return ReminderType_REMINDER_TYPE_UNSPECIFIED
}

func (x *AppVersionInfo) GetMinCompatibleVersion() string {
	if x != nil {
		return x.MinCompatibleVersion
	}
	return ""
}

func (x *AppVersionInfo) GetOfficialDownloadUrl() string {
	if x != nil {
		return x.OfficialDownloadUrl
	}
	return ""
}

var File_api_walletadmin_v1_app_version_proto protoreflect.FileDescriptor

var file_api_walletadmin_v1_app_version_proto_rawDesc = []byte{
	0x0a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c,
	0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x62, 0x75, 0x66, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x4e, 0x0a, 0x0e, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x49, 0x31, 0x38, 0x4e, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0xa4, 0x03, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x21, 0x0a, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a,
	0x08, 0x61, 0x70, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x70, 0x70, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x5f, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x66, 0x6f, 0x72, 0x63,
	0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x4f, 0x0a, 0x11, 0x69, 0x31, 0x38, 0x6e, 0x5f,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x49, 0x31, 0x38, 0x4e, 0x52, 0x10, 0x69, 0x31, 0x38, 0x6e, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x45, 0x0a, 0x0d, 0x72, 0x65, 0x6d, 0x69,
	0x6e, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0c, 0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x34, 0x0a, 0x16, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x74, 0x69, 0x62, 0x6c,
	0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x14, 0x6d, 0x69, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x15, 0x6f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61,
	0x6c, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x6f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x44, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x22, 0x17, 0x0a, 0x15, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x7f, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xba, 0x48, 0x04, 0x32, 0x02, 0x20, 0x00, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x4f, 0x0a, 0x11, 0x69, 0x31, 0x38, 0x6e, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x31, 0x38,
	0x4e, 0x52, 0x10, 0x69, 0x31, 0x38, 0x6e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x22, 0x2e, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x70, 0x70,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xba, 0x48, 0x04, 0x32, 0x02, 0x20, 0x00, 0x52,
	0x02, 0x69, 0x64, 0x22, 0x74, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xba, 0x48, 0x04, 0x22, 0x02, 0x28, 0x01, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x12, 0x26, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x09, 0xba, 0x48, 0x06, 0x22, 0x04, 0x18, 0x64, 0x28, 0x01, 0x52,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x6f, 0x0a, 0x14, 0x4c, 0x69, 0x73,
	0x74, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x36, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x2b, 0x0a, 0x10, 0x47, 0x65,
	0x74, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x17,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xba, 0x48, 0x04, 0x32,
	0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x35, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x54, 0x79, 0x70, 0x65, 0x22, 0x30,
	0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x22, 0xdb, 0x03, 0x0a, 0x0e, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a,
	0x08, 0x61, 0x70, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x70, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x66,
	0x6f, 0x72, 0x63, 0x65, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0b, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x4f,
	0x0a, 0x11, 0x69, 0x31, 0x38, 0x6e, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x31, 0x38, 0x4e, 0x52, 0x10, 0x69,
	0x31, 0x38, 0x6e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x45, 0x0a,
	0x0d, 0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x6d, 0x69, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x74, 0x69,
	0x62, 0x6c, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x15, 0x6f, 0x66,
	0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x6f, 0x66, 0x66, 0x69, 0x63,
	0x69, 0x61, 0x6c, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x2a, 0x97,
	0x01, 0x0a, 0x0c, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1d, 0x0a, 0x19, 0x52, 0x45, 0x4d, 0x49, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1c,
	0x0a, 0x18, 0x52, 0x45, 0x4d, 0x49, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x45, 0x56, 0x45, 0x52, 0x59, 0x5f, 0x4f, 0x50, 0x45, 0x4e, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13,
	0x52, 0x45, 0x4d, 0x49, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x41,
	0x49, 0x4c, 0x59, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x52, 0x45, 0x4d, 0x49, 0x4e, 0x44, 0x45,
	0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x57, 0x45, 0x45, 0x4b, 0x4c, 0x59, 0x10, 0x03, 0x12,
	0x17, 0x0a, 0x13, 0x52, 0x45, 0x4d, 0x49, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x4e, 0x45, 0x56, 0x45, 0x52, 0x10, 0x04, 0x32, 0xc1, 0x06, 0x0a, 0x11, 0x41, 0x70, 0x70,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x89,
	0x01, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41,
	0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x29, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x3a,
	0x01, 0x2a, 0x22, 0x16, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70,
	0x70, 0x2d, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x7b, 0x0a, 0x10, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x27,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x3a, 0x01, 0x2a, 0x1a, 0x1b, 0x2f, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2d, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x78, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x23, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x1d, 0x2a, 0x1b, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x76, 0x31, 0x2f,
	0x61, 0x70, 0x70, 0x2d, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x69, 0x64,
	0x7d, 0x12, 0x83, 0x01, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c,
	0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41,
	0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x28, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x12,
	0x16, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2d, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x7e, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x41, 0x70,
	0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x22,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e,
	0x66, 0x6f, 0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x12, 0x1b, 0x2f, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2d, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0xa2, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x75, 0x62, 0x6c, 0x69,
	0x73, 0x68, 0x65, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x1a,
	0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x65, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x3a, 0x01, 0x2a, 0x22, 0x20, 0x2f, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2d, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x42, 0xb5, 0x01, 0x0a,
	0x16, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x42, 0x0f, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x20, 0x62, 0x79, 0x64, 0x5f,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0xa2, 0x02, 0x03, 0x41,
	0x57, 0x58, 0xaa, 0x02, 0x12, 0x41, 0x70, 0x69, 0x2e, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x56, 0x31, 0xca, 0x02, 0x12, 0x41, 0x70, 0x69, 0x5c, 0x57, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5c, 0x56, 0x31, 0xe2, 0x02, 0x1e, 0x41,
	0x70, 0x69, 0x5c, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5c, 0x56,
	0x31, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0xea, 0x02, 0x14,
	0x41, 0x70, 0x69, 0x3a, 0x3a, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x3a, 0x3a, 0x56, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_walletadmin_v1_app_version_proto_rawDescOnce sync.Once
	file_api_walletadmin_v1_app_version_proto_rawDescData = file_api_walletadmin_v1_app_version_proto_rawDesc
)

func file_api_walletadmin_v1_app_version_proto_rawDescGZIP() []byte {
	file_api_walletadmin_v1_app_version_proto_rawDescOnce.Do(func() {
		file_api_walletadmin_v1_app_version_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_walletadmin_v1_app_version_proto_rawDescData)
	})
	return file_api_walletadmin_v1_app_version_proto_rawDescData
}

var file_api_walletadmin_v1_app_version_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_walletadmin_v1_app_version_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_api_walletadmin_v1_app_version_proto_goTypes = []any{
	(ReminderType)(0),                  // 0: api.walletadmin.v1.ReminderType
	(*AppVersionI18N)(nil),             // 1: api.walletadmin.v1.AppVersionI18N
	(*CreateAppVersionReq)(nil),        // 2: api.walletadmin.v1.CreateAppVersionReq
	(*CreateAppVersionReply)(nil),      // 3: api.walletadmin.v1.CreateAppVersionReply
	(*UpdateAppVersionReq)(nil),        // 4: api.walletadmin.v1.UpdateAppVersionReq
	(*DeleteAppVersionReq)(nil),        // 5: api.walletadmin.v1.DeleteAppVersionReq
	(*ListAppVersionsReq)(nil),         // 6: api.walletadmin.v1.ListAppVersionsReq
	(*ListAppVersionsReply)(nil),       // 7: api.walletadmin.v1.ListAppVersionsReply
	(*GetAppVersionReq)(nil),           // 8: api.walletadmin.v1.GetAppVersionReq
	(*ListPublishedVersionsReq)(nil),   // 9: api.walletadmin.v1.ListPublishedVersionsReq
	(*ListPublishedVersionsReply)(nil), // 10: api.walletadmin.v1.ListPublishedVersionsReply
	(*AppVersionInfo)(nil),             // 11: api.walletadmin.v1.AppVersionInfo
	(*emptypb.Empty)(nil),              // 12: google.protobuf.Empty
}
var file_api_walletadmin_v1_app_version_proto_depIdxs = []int32{
	1,  // 0: api.walletadmin.v1.CreateAppVersionReq.i18n_descriptions:type_name -> api.walletadmin.v1.AppVersionI18N
	0,  // 1: api.walletadmin.v1.CreateAppVersionReq.reminder_type:type_name -> api.walletadmin.v1.ReminderType
	1,  // 2: api.walletadmin.v1.UpdateAppVersionReq.i18n_descriptions:type_name -> api.walletadmin.v1.AppVersionI18N
	11, // 3: api.walletadmin.v1.ListAppVersionsReply.list:type_name -> api.walletadmin.v1.AppVersionInfo
	1,  // 4: api.walletadmin.v1.AppVersionInfo.i18n_descriptions:type_name -> api.walletadmin.v1.AppVersionI18N
	0,  // 5: api.walletadmin.v1.AppVersionInfo.reminder_type:type_name -> api.walletadmin.v1.ReminderType
	2,  // 6: api.walletadmin.v1.AppVersionService.CreateAppVersion:input_type -> api.walletadmin.v1.CreateAppVersionReq
	4,  // 7: api.walletadmin.v1.AppVersionService.UpdateAppVersion:input_type -> api.walletadmin.v1.UpdateAppVersionReq
	5,  // 8: api.walletadmin.v1.AppVersionService.DeleteAppVersion:input_type -> api.walletadmin.v1.DeleteAppVersionReq
	6,  // 9: api.walletadmin.v1.AppVersionService.ListAppVersions:input_type -> api.walletadmin.v1.ListAppVersionsReq
	8,  // 10: api.walletadmin.v1.AppVersionService.GetAppVersion:input_type -> api.walletadmin.v1.GetAppVersionReq
	9,  // 11: api.walletadmin.v1.AppVersionService.ListPublishedVersions:input_type -> api.walletadmin.v1.ListPublishedVersionsReq
	3,  // 12: api.walletadmin.v1.AppVersionService.CreateAppVersion:output_type -> api.walletadmin.v1.CreateAppVersionReply
	12, // 13: api.walletadmin.v1.AppVersionService.UpdateAppVersion:output_type -> google.protobuf.Empty
	12, // 14: api.walletadmin.v1.AppVersionService.DeleteAppVersion:output_type -> google.protobuf.Empty
	7,  // 15: api.walletadmin.v1.AppVersionService.ListAppVersions:output_type -> api.walletadmin.v1.ListAppVersionsReply
	11, // 16: api.walletadmin.v1.AppVersionService.GetAppVersion:output_type -> api.walletadmin.v1.AppVersionInfo
	10, // 17: api.walletadmin.v1.AppVersionService.ListPublishedVersions:output_type -> api.walletadmin.v1.ListPublishedVersionsReply
	12, // [12:18] is the sub-list for method output_type
	6,  // [6:12] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_api_walletadmin_v1_app_version_proto_init() }
func file_api_walletadmin_v1_app_version_proto_init() {
	if File_api_walletadmin_v1_app_version_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_walletadmin_v1_app_version_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*AppVersionI18N); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_app_version_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*CreateAppVersionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_app_version_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*CreateAppVersionReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_app_version_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateAppVersionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_app_version_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteAppVersionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_app_version_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*ListAppVersionsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_app_version_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*ListAppVersionsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_app_version_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*GetAppVersionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_app_version_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*ListPublishedVersionsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_app_version_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*ListPublishedVersionsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_app_version_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*AppVersionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_walletadmin_v1_app_version_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_walletadmin_v1_app_version_proto_goTypes,
		DependencyIndexes: file_api_walletadmin_v1_app_version_proto_depIdxs,
		EnumInfos:         file_api_walletadmin_v1_app_version_proto_enumTypes,
		MessageInfos:      file_api_walletadmin_v1_app_version_proto_msgTypes,
	}.Build()
	File_api_walletadmin_v1_app_version_proto = out.File
	file_api_walletadmin_v1_app_version_proto_rawDesc = nil
	file_api_walletadmin_v1_app_version_proto_goTypes = nil
	file_api_walletadmin_v1_app_version_proto_depIdxs = nil
}
