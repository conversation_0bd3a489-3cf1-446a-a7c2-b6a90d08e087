syntax = "proto3";

package api.walletadmin.v1;

option go_package = "byd_wallet/api/walletadmin/v1;v1";
option java_multiple_files = true;
option java_package = "api.walletadmin.v1";

import "google/api/annotations.proto";
import "buf/validate/validate.proto";
import "google/protobuf/empty.proto";

service ChainSrv {
	// 公链列表
	rpc ListChain(ListChainReq) returns (ListChainReply) {
		option (google.api.http) = {
			get: "/admin/v1/chain/list_chain"
		};
	}
	// 更新公链记录
	rpc UpdateChain(UpdateChainReq) returns (google.protobuf.Empty) {
		option (google.api.http) = {
			put: "/admin/v1/chain/update_chain"
			body: "*"
		};
	}
	// 更新公链排序
	rpc UpdateChainSort(UpdateChainSortReq) returns (google.protobuf.Empty) {
		option (google.api.http) = {
			patch: "/admin/v1/chain/update_chain_sort"
			body: "*"
		};
	}
}

message UpdateChainSortReq {
	// 当前排序(来自列表的sort_order字段值)
	int64 current_sort = 1;
	// 更新后排序(数值越大,排序越靠前)
	int64 target_sort = 2;
	// 记录ID
	uint64 id = 3 [(buf.validate.field).uint64.gt = 0];
}

message UpdateChainReq {
	// 网络图标
	string blockchain_url = 1 [(buf.validate.field).string.uri = true];
	// 币图标
	string token_url = 2 [(buf.validate.field).string.uri = true];
	// 是否展示
	bool is_display = 3;
	// 记录ID
	uint64 id = 4 [(buf.validate.field).uint64.gt = 0];
}

message ListChainReq {
	// 公链名称
	optional string chain_name = 3;
}

message Chain {
	// 记录ID
	uint64 id = 1;
	// 链名称
	string chain_name = 2;
	// 链索引
	int64 chain_index = 3;
	// 币符号
	string symbol = 4;
	// 精度位数
	int64 decimals = 5;
	// 链类型
	string chain_type = 6;
	// 当前同步块高
	int64 currency_block = 7;
	// 是否正在同步
	bool is_syncing = 8;
	// 是否展示
	bool is_display = 9;
	// 网络图标
	string blockchain_url = 10;
	// 币图标
	string token_url = 11;
	// 网络ID
	string chain_id = 12;
	// 排序
	int64 sort_order = 13;
}

message ListChainReply {
	repeated Chain list = 1;
}