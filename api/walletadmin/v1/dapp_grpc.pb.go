// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: api/walletadmin/v1/dapp.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AdminDappSrv_CreateDapp_FullMethodName                = "/api.walletadmin.v1.AdminDappSrv/CreateDapp"
	AdminDappSrv_DeleteDapp_FullMethodName                = "/api.walletadmin.v1.AdminDappSrv/DeleteDapp"
	AdminDappSrv_UpdateDapp_FullMethodName                = "/api.walletadmin.v1.AdminDappSrv/UpdateDapp"
	AdminDappSrv_ListDapp_FullMethodName                  = "/api.walletadmin.v1.AdminDappSrv/ListDapp"
	AdminDappSrv_CreateDappCategory_FullMethodName        = "/api.walletadmin.v1.AdminDappSrv/CreateDappCategory"
	AdminDappSrv_UpdateDappCategory_FullMethodName        = "/api.walletadmin.v1.AdminDappSrv/UpdateDappCategory"
	AdminDappSrv_ListDappCategory_FullMethodName          = "/api.walletadmin.v1.AdminDappSrv/ListDappCategory"
	AdminDappSrv_DeleteDappCategory_FullMethodName        = "/api.walletadmin.v1.AdminDappSrv/DeleteDappCategory"
	AdminDappSrv_CreateDappCategoryRel_FullMethodName     = "/api.walletadmin.v1.AdminDappSrv/CreateDappCategoryRel"
	AdminDappSrv_DeleteDappCategoryRel_FullMethodName     = "/api.walletadmin.v1.AdminDappSrv/DeleteDappCategoryRel"
	AdminDappSrv_ListDappCategoryRel_FullMethodName       = "/api.walletadmin.v1.AdminDappSrv/ListDappCategoryRel"
	AdminDappSrv_CreateDappTopic_FullMethodName           = "/api.walletadmin.v1.AdminDappSrv/CreateDappTopic"
	AdminDappSrv_DeleteDappTopic_FullMethodName           = "/api.walletadmin.v1.AdminDappSrv/DeleteDappTopic"
	AdminDappSrv_UpdateDappTopic_FullMethodName           = "/api.walletadmin.v1.AdminDappSrv/UpdateDappTopic"
	AdminDappSrv_ListDappTopic_FullMethodName             = "/api.walletadmin.v1.AdminDappSrv/ListDappTopic"
	AdminDappSrv_CreateDappNavigation_FullMethodName      = "/api.walletadmin.v1.AdminDappSrv/CreateDappNavigation"
	AdminDappSrv_DeleteDappNavigation_FullMethodName      = "/api.walletadmin.v1.AdminDappSrv/DeleteDappNavigation"
	AdminDappSrv_ListDappNavigation_FullMethodName        = "/api.walletadmin.v1.AdminDappSrv/ListDappNavigation"
	AdminDappSrv_BatchUpdateDappNavigation_FullMethodName = "/api.walletadmin.v1.AdminDappSrv/BatchUpdateDappNavigation"
	AdminDappSrv_SortDappNavigation_FullMethodName        = "/api.walletadmin.v1.AdminDappSrv/SortDappNavigation"
	AdminDappSrv_CreateDappIndex_FullMethodName           = "/api.walletadmin.v1.AdminDappSrv/CreateDappIndex"
	AdminDappSrv_ListDappIndex_FullMethodName             = "/api.walletadmin.v1.AdminDappSrv/ListDappIndex"
	AdminDappSrv_DeleteDappIndex_FullMethodName           = "/api.walletadmin.v1.AdminDappSrv/DeleteDappIndex"
	AdminDappSrv_BatchUpdateDappIndex_FullMethodName      = "/api.walletadmin.v1.AdminDappSrv/BatchUpdateDappIndex"
	AdminDappSrv_SortDappIndex_FullMethodName             = "/api.walletadmin.v1.AdminDappSrv/SortDappIndex"
	AdminDappSrv_CreateDappTopicRel_FullMethodName        = "/api.walletadmin.v1.AdminDappSrv/CreateDappTopicRel"
	AdminDappSrv_DeleteDappTopicRel_FullMethodName        = "/api.walletadmin.v1.AdminDappSrv/DeleteDappTopicRel"
	AdminDappSrv_ListDappTopicRel_FullMethodName          = "/api.walletadmin.v1.AdminDappSrv/ListDappTopicRel"
)

// AdminDappSrvClient is the client API for AdminDappSrv service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AdminDappSrvClient interface {
	// CreateDapp 添加DAPP
	CreateDapp(ctx context.Context, in *CreateDappReq, opts ...grpc.CallOption) (*CreateDappReply, error)
	// DeleteDapp 删除DAPP
	DeleteDapp(ctx context.Context, in *DeleteDappReq, opts ...grpc.CallOption) (*DeleteDappReply, error)
	// UpdateDapp 更新DAPP
	UpdateDapp(ctx context.Context, in *UpdateDappReq, opts ...grpc.CallOption) (*UpdateDappReply, error)
	// ListDapp 查询dapps
	ListDapp(ctx context.Context, in *ListDappReq, opts ...grpc.CallOption) (*ListDappReply, error)
	// CreateDappCategory 添加DAPP分类
	CreateDappCategory(ctx context.Context, in *CreateDappCategoryReq, opts ...grpc.CallOption) (*CreateDappCategoryReply, error)
	// UpdateDappCategory 更新DAPP分类
	UpdateDappCategory(ctx context.Context, in *UpdateDappCategoryReq, opts ...grpc.CallOption) (*UpdateDappCategoryReply, error)
	// ListDappCategory 查询dapp分类
	ListDappCategory(ctx context.Context, in *ListDappCategoryReq, opts ...grpc.CallOption) (*ListDappCategoryReply, error)
	// DeleteDappCategoryRel 删除分类
	DeleteDappCategory(ctx context.Context, in *DeleteDappCategoryReq, opts ...grpc.CallOption) (*DeleteDappCategoryReply, error)
	// CreateDappCategoryRel 添加分类dapp
	CreateDappCategoryRel(ctx context.Context, in *CreateDappCategoryRelReq, opts ...grpc.CallOption) (*CreateDappCategoryRelReply, error)
	// DeleteDappCategoryRel 删除分类dapp
	DeleteDappCategoryRel(ctx context.Context, in *DeleteDappCategoryRelReq, opts ...grpc.CallOption) (*DeleteDappCategoryRelReply, error)
	// ListDappCategoryRel 查询分类dapp
	ListDappCategoryRel(ctx context.Context, in *ListDappCategoryRelReq, opts ...grpc.CallOption) (*ListDappCategoryRelReply, error)
	// CreateDappTopic 添加DAPP专题
	CreateDappTopic(ctx context.Context, in *CreateDappTopicReq, opts ...grpc.CallOption) (*CreateDappTopicReply, error)
	// DeleteDappTopic 删除dapp专题
	DeleteDappTopic(ctx context.Context, in *DeleteDappTopicReq, opts ...grpc.CallOption) (*DeleteDappTopicReply, error)
	// UpdateDappTopic 更新DAPP专题
	UpdateDappTopic(ctx context.Context, in *UpdateDappTopicReq, opts ...grpc.CallOption) (*UpdateDappTopicReply, error)
	// ListDappTopic 查询dapp专题
	ListDappTopic(ctx context.Context, in *ListDappTopicReq, opts ...grpc.CallOption) (*ListDappTopicReply, error)
	// CreateDappNavigation 添加DAPP首页导航栏
	CreateDappNavigation(ctx context.Context, in *CreateDappNavigationReq, opts ...grpc.CallOption) (*CreateDappNavigationReply, error)
	// DeleteDappNavigation 删除首页导航栏
	DeleteDappNavigation(ctx context.Context, in *DeleteDappNavigationReq, opts ...grpc.CallOption) (*DeleteDappNavigationReply, error)
	// ListDappNavigation 首页导航栏列表
	ListDappNavigation(ctx context.Context, in *ListDappNavigationReq, opts ...grpc.CallOption) (*ListDappNavigationReply, error)
	// BatchUpdateDappNavigation 批量更新首页导航栏(可用于更新排序)
	BatchUpdateDappNavigation(ctx context.Context, in *BatchUpdateDappNavigationReq, opts ...grpc.CallOption) (*BatchUpdateDappNavigationReply, error)
	// SortDappNavigation 首页导航栏排序
	SortDappNavigation(ctx context.Context, in *SortReq, opts ...grpc.CallOption) (*SortReply, error)
	// CreateDappIndex 添加DAPP首页
	CreateDappIndex(ctx context.Context, in *CreateDappIndexReq, opts ...grpc.CallOption) (*CreateDappIndexReply, error)
	// ListDappIndex 获取dapp首页列表
	ListDappIndex(ctx context.Context, in *ListDappIndexReq, opts ...grpc.CallOption) (*ListDappIndexReply, error)
	// DeleteDappIndex 删除dapp首页
	DeleteDappIndex(ctx context.Context, in *DeleteDappIndexReq, opts ...grpc.CallOption) (*DeleteDappIndexReply, error)
	// BatchUpdateDappIndex 批量更新dapp首页（可用于更新排序）
	BatchUpdateDappIndex(ctx context.Context, in *BatchUpdateDappIndexReq, opts ...grpc.CallOption) (*BatchUpdateDappIndexReply, error)
	// SortDappIndex dapp首页排序
	SortDappIndex(ctx context.Context, in *SortReq, opts ...grpc.CallOption) (*SortReply, error)
	// CreateDappTopicRel 添加专题dapp
	CreateDappTopicRel(ctx context.Context, in *CreateDappTopicRelReq, opts ...grpc.CallOption) (*CreateDappTopicRelReply, error)
	// DeleteDappTopicRel 删除专题dapp
	DeleteDappTopicRel(ctx context.Context, in *DeleteDappTopicRelReq, opts ...grpc.CallOption) (*DeleteDappTopicRelReply, error)
	// ListDappTopicRel 查询专题dapp
	ListDappTopicRel(ctx context.Context, in *ListDappTopicRelReq, opts ...grpc.CallOption) (*ListDappTopicRelReply, error)
}

type adminDappSrvClient struct {
	cc grpc.ClientConnInterface
}

func NewAdminDappSrvClient(cc grpc.ClientConnInterface) AdminDappSrvClient {
	return &adminDappSrvClient{cc}
}

func (c *adminDappSrvClient) CreateDapp(ctx context.Context, in *CreateDappReq, opts ...grpc.CallOption) (*CreateDappReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateDappReply)
	err := c.cc.Invoke(ctx, AdminDappSrv_CreateDapp_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminDappSrvClient) DeleteDapp(ctx context.Context, in *DeleteDappReq, opts ...grpc.CallOption) (*DeleteDappReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteDappReply)
	err := c.cc.Invoke(ctx, AdminDappSrv_DeleteDapp_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminDappSrvClient) UpdateDapp(ctx context.Context, in *UpdateDappReq, opts ...grpc.CallOption) (*UpdateDappReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateDappReply)
	err := c.cc.Invoke(ctx, AdminDappSrv_UpdateDapp_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminDappSrvClient) ListDapp(ctx context.Context, in *ListDappReq, opts ...grpc.CallOption) (*ListDappReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListDappReply)
	err := c.cc.Invoke(ctx, AdminDappSrv_ListDapp_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminDappSrvClient) CreateDappCategory(ctx context.Context, in *CreateDappCategoryReq, opts ...grpc.CallOption) (*CreateDappCategoryReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateDappCategoryReply)
	err := c.cc.Invoke(ctx, AdminDappSrv_CreateDappCategory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminDappSrvClient) UpdateDappCategory(ctx context.Context, in *UpdateDappCategoryReq, opts ...grpc.CallOption) (*UpdateDappCategoryReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateDappCategoryReply)
	err := c.cc.Invoke(ctx, AdminDappSrv_UpdateDappCategory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminDappSrvClient) ListDappCategory(ctx context.Context, in *ListDappCategoryReq, opts ...grpc.CallOption) (*ListDappCategoryReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListDappCategoryReply)
	err := c.cc.Invoke(ctx, AdminDappSrv_ListDappCategory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminDappSrvClient) DeleteDappCategory(ctx context.Context, in *DeleteDappCategoryReq, opts ...grpc.CallOption) (*DeleteDappCategoryReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteDappCategoryReply)
	err := c.cc.Invoke(ctx, AdminDappSrv_DeleteDappCategory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminDappSrvClient) CreateDappCategoryRel(ctx context.Context, in *CreateDappCategoryRelReq, opts ...grpc.CallOption) (*CreateDappCategoryRelReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateDappCategoryRelReply)
	err := c.cc.Invoke(ctx, AdminDappSrv_CreateDappCategoryRel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminDappSrvClient) DeleteDappCategoryRel(ctx context.Context, in *DeleteDappCategoryRelReq, opts ...grpc.CallOption) (*DeleteDappCategoryRelReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteDappCategoryRelReply)
	err := c.cc.Invoke(ctx, AdminDappSrv_DeleteDappCategoryRel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminDappSrvClient) ListDappCategoryRel(ctx context.Context, in *ListDappCategoryRelReq, opts ...grpc.CallOption) (*ListDappCategoryRelReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListDappCategoryRelReply)
	err := c.cc.Invoke(ctx, AdminDappSrv_ListDappCategoryRel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminDappSrvClient) CreateDappTopic(ctx context.Context, in *CreateDappTopicReq, opts ...grpc.CallOption) (*CreateDappTopicReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateDappTopicReply)
	err := c.cc.Invoke(ctx, AdminDappSrv_CreateDappTopic_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminDappSrvClient) DeleteDappTopic(ctx context.Context, in *DeleteDappTopicReq, opts ...grpc.CallOption) (*DeleteDappTopicReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteDappTopicReply)
	err := c.cc.Invoke(ctx, AdminDappSrv_DeleteDappTopic_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminDappSrvClient) UpdateDappTopic(ctx context.Context, in *UpdateDappTopicReq, opts ...grpc.CallOption) (*UpdateDappTopicReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateDappTopicReply)
	err := c.cc.Invoke(ctx, AdminDappSrv_UpdateDappTopic_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminDappSrvClient) ListDappTopic(ctx context.Context, in *ListDappTopicReq, opts ...grpc.CallOption) (*ListDappTopicReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListDappTopicReply)
	err := c.cc.Invoke(ctx, AdminDappSrv_ListDappTopic_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminDappSrvClient) CreateDappNavigation(ctx context.Context, in *CreateDappNavigationReq, opts ...grpc.CallOption) (*CreateDappNavigationReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateDappNavigationReply)
	err := c.cc.Invoke(ctx, AdminDappSrv_CreateDappNavigation_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminDappSrvClient) DeleteDappNavigation(ctx context.Context, in *DeleteDappNavigationReq, opts ...grpc.CallOption) (*DeleteDappNavigationReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteDappNavigationReply)
	err := c.cc.Invoke(ctx, AdminDappSrv_DeleteDappNavigation_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminDappSrvClient) ListDappNavigation(ctx context.Context, in *ListDappNavigationReq, opts ...grpc.CallOption) (*ListDappNavigationReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListDappNavigationReply)
	err := c.cc.Invoke(ctx, AdminDappSrv_ListDappNavigation_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminDappSrvClient) BatchUpdateDappNavigation(ctx context.Context, in *BatchUpdateDappNavigationReq, opts ...grpc.CallOption) (*BatchUpdateDappNavigationReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchUpdateDappNavigationReply)
	err := c.cc.Invoke(ctx, AdminDappSrv_BatchUpdateDappNavigation_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminDappSrvClient) SortDappNavigation(ctx context.Context, in *SortReq, opts ...grpc.CallOption) (*SortReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SortReply)
	err := c.cc.Invoke(ctx, AdminDappSrv_SortDappNavigation_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminDappSrvClient) CreateDappIndex(ctx context.Context, in *CreateDappIndexReq, opts ...grpc.CallOption) (*CreateDappIndexReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateDappIndexReply)
	err := c.cc.Invoke(ctx, AdminDappSrv_CreateDappIndex_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminDappSrvClient) ListDappIndex(ctx context.Context, in *ListDappIndexReq, opts ...grpc.CallOption) (*ListDappIndexReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListDappIndexReply)
	err := c.cc.Invoke(ctx, AdminDappSrv_ListDappIndex_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminDappSrvClient) DeleteDappIndex(ctx context.Context, in *DeleteDappIndexReq, opts ...grpc.CallOption) (*DeleteDappIndexReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteDappIndexReply)
	err := c.cc.Invoke(ctx, AdminDappSrv_DeleteDappIndex_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminDappSrvClient) BatchUpdateDappIndex(ctx context.Context, in *BatchUpdateDappIndexReq, opts ...grpc.CallOption) (*BatchUpdateDappIndexReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchUpdateDappIndexReply)
	err := c.cc.Invoke(ctx, AdminDappSrv_BatchUpdateDappIndex_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminDappSrvClient) SortDappIndex(ctx context.Context, in *SortReq, opts ...grpc.CallOption) (*SortReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SortReply)
	err := c.cc.Invoke(ctx, AdminDappSrv_SortDappIndex_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminDappSrvClient) CreateDappTopicRel(ctx context.Context, in *CreateDappTopicRelReq, opts ...grpc.CallOption) (*CreateDappTopicRelReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateDappTopicRelReply)
	err := c.cc.Invoke(ctx, AdminDappSrv_CreateDappTopicRel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminDappSrvClient) DeleteDappTopicRel(ctx context.Context, in *DeleteDappTopicRelReq, opts ...grpc.CallOption) (*DeleteDappTopicRelReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteDappTopicRelReply)
	err := c.cc.Invoke(ctx, AdminDappSrv_DeleteDappTopicRel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminDappSrvClient) ListDappTopicRel(ctx context.Context, in *ListDappTopicRelReq, opts ...grpc.CallOption) (*ListDappTopicRelReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListDappTopicRelReply)
	err := c.cc.Invoke(ctx, AdminDappSrv_ListDappTopicRel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AdminDappSrvServer is the server API for AdminDappSrv service.
// All implementations must embed UnimplementedAdminDappSrvServer
// for forward compatibility.
type AdminDappSrvServer interface {
	// CreateDapp 添加DAPP
	CreateDapp(context.Context, *CreateDappReq) (*CreateDappReply, error)
	// DeleteDapp 删除DAPP
	DeleteDapp(context.Context, *DeleteDappReq) (*DeleteDappReply, error)
	// UpdateDapp 更新DAPP
	UpdateDapp(context.Context, *UpdateDappReq) (*UpdateDappReply, error)
	// ListDapp 查询dapps
	ListDapp(context.Context, *ListDappReq) (*ListDappReply, error)
	// CreateDappCategory 添加DAPP分类
	CreateDappCategory(context.Context, *CreateDappCategoryReq) (*CreateDappCategoryReply, error)
	// UpdateDappCategory 更新DAPP分类
	UpdateDappCategory(context.Context, *UpdateDappCategoryReq) (*UpdateDappCategoryReply, error)
	// ListDappCategory 查询dapp分类
	ListDappCategory(context.Context, *ListDappCategoryReq) (*ListDappCategoryReply, error)
	// DeleteDappCategoryRel 删除分类
	DeleteDappCategory(context.Context, *DeleteDappCategoryReq) (*DeleteDappCategoryReply, error)
	// CreateDappCategoryRel 添加分类dapp
	CreateDappCategoryRel(context.Context, *CreateDappCategoryRelReq) (*CreateDappCategoryRelReply, error)
	// DeleteDappCategoryRel 删除分类dapp
	DeleteDappCategoryRel(context.Context, *DeleteDappCategoryRelReq) (*DeleteDappCategoryRelReply, error)
	// ListDappCategoryRel 查询分类dapp
	ListDappCategoryRel(context.Context, *ListDappCategoryRelReq) (*ListDappCategoryRelReply, error)
	// CreateDappTopic 添加DAPP专题
	CreateDappTopic(context.Context, *CreateDappTopicReq) (*CreateDappTopicReply, error)
	// DeleteDappTopic 删除dapp专题
	DeleteDappTopic(context.Context, *DeleteDappTopicReq) (*DeleteDappTopicReply, error)
	// UpdateDappTopic 更新DAPP专题
	UpdateDappTopic(context.Context, *UpdateDappTopicReq) (*UpdateDappTopicReply, error)
	// ListDappTopic 查询dapp专题
	ListDappTopic(context.Context, *ListDappTopicReq) (*ListDappTopicReply, error)
	// CreateDappNavigation 添加DAPP首页导航栏
	CreateDappNavigation(context.Context, *CreateDappNavigationReq) (*CreateDappNavigationReply, error)
	// DeleteDappNavigation 删除首页导航栏
	DeleteDappNavigation(context.Context, *DeleteDappNavigationReq) (*DeleteDappNavigationReply, error)
	// ListDappNavigation 首页导航栏列表
	ListDappNavigation(context.Context, *ListDappNavigationReq) (*ListDappNavigationReply, error)
	// BatchUpdateDappNavigation 批量更新首页导航栏(可用于更新排序)
	BatchUpdateDappNavigation(context.Context, *BatchUpdateDappNavigationReq) (*BatchUpdateDappNavigationReply, error)
	// SortDappNavigation 首页导航栏排序
	SortDappNavigation(context.Context, *SortReq) (*SortReply, error)
	// CreateDappIndex 添加DAPP首页
	CreateDappIndex(context.Context, *CreateDappIndexReq) (*CreateDappIndexReply, error)
	// ListDappIndex 获取dapp首页列表
	ListDappIndex(context.Context, *ListDappIndexReq) (*ListDappIndexReply, error)
	// DeleteDappIndex 删除dapp首页
	DeleteDappIndex(context.Context, *DeleteDappIndexReq) (*DeleteDappIndexReply, error)
	// BatchUpdateDappIndex 批量更新dapp首页（可用于更新排序）
	BatchUpdateDappIndex(context.Context, *BatchUpdateDappIndexReq) (*BatchUpdateDappIndexReply, error)
	// SortDappIndex dapp首页排序
	SortDappIndex(context.Context, *SortReq) (*SortReply, error)
	// CreateDappTopicRel 添加专题dapp
	CreateDappTopicRel(context.Context, *CreateDappTopicRelReq) (*CreateDappTopicRelReply, error)
	// DeleteDappTopicRel 删除专题dapp
	DeleteDappTopicRel(context.Context, *DeleteDappTopicRelReq) (*DeleteDappTopicRelReply, error)
	// ListDappTopicRel 查询专题dapp
	ListDappTopicRel(context.Context, *ListDappTopicRelReq) (*ListDappTopicRelReply, error)
	mustEmbedUnimplementedAdminDappSrvServer()
}

// UnimplementedAdminDappSrvServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAdminDappSrvServer struct{}

func (UnimplementedAdminDappSrvServer) CreateDapp(context.Context, *CreateDappReq) (*CreateDappReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDapp not implemented")
}
func (UnimplementedAdminDappSrvServer) DeleteDapp(context.Context, *DeleteDappReq) (*DeleteDappReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDapp not implemented")
}
func (UnimplementedAdminDappSrvServer) UpdateDapp(context.Context, *UpdateDappReq) (*UpdateDappReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDapp not implemented")
}
func (UnimplementedAdminDappSrvServer) ListDapp(context.Context, *ListDappReq) (*ListDappReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDapp not implemented")
}
func (UnimplementedAdminDappSrvServer) CreateDappCategory(context.Context, *CreateDappCategoryReq) (*CreateDappCategoryReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDappCategory not implemented")
}
func (UnimplementedAdminDappSrvServer) UpdateDappCategory(context.Context, *UpdateDappCategoryReq) (*UpdateDappCategoryReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDappCategory not implemented")
}
func (UnimplementedAdminDappSrvServer) ListDappCategory(context.Context, *ListDappCategoryReq) (*ListDappCategoryReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDappCategory not implemented")
}
func (UnimplementedAdminDappSrvServer) DeleteDappCategory(context.Context, *DeleteDappCategoryReq) (*DeleteDappCategoryReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDappCategory not implemented")
}
func (UnimplementedAdminDappSrvServer) CreateDappCategoryRel(context.Context, *CreateDappCategoryRelReq) (*CreateDappCategoryRelReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDappCategoryRel not implemented")
}
func (UnimplementedAdminDappSrvServer) DeleteDappCategoryRel(context.Context, *DeleteDappCategoryRelReq) (*DeleteDappCategoryRelReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDappCategoryRel not implemented")
}
func (UnimplementedAdminDappSrvServer) ListDappCategoryRel(context.Context, *ListDappCategoryRelReq) (*ListDappCategoryRelReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDappCategoryRel not implemented")
}
func (UnimplementedAdminDappSrvServer) CreateDappTopic(context.Context, *CreateDappTopicReq) (*CreateDappTopicReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDappTopic not implemented")
}
func (UnimplementedAdminDappSrvServer) DeleteDappTopic(context.Context, *DeleteDappTopicReq) (*DeleteDappTopicReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDappTopic not implemented")
}
func (UnimplementedAdminDappSrvServer) UpdateDappTopic(context.Context, *UpdateDappTopicReq) (*UpdateDappTopicReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDappTopic not implemented")
}
func (UnimplementedAdminDappSrvServer) ListDappTopic(context.Context, *ListDappTopicReq) (*ListDappTopicReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDappTopic not implemented")
}
func (UnimplementedAdminDappSrvServer) CreateDappNavigation(context.Context, *CreateDappNavigationReq) (*CreateDappNavigationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDappNavigation not implemented")
}
func (UnimplementedAdminDappSrvServer) DeleteDappNavigation(context.Context, *DeleteDappNavigationReq) (*DeleteDappNavigationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDappNavigation not implemented")
}
func (UnimplementedAdminDappSrvServer) ListDappNavigation(context.Context, *ListDappNavigationReq) (*ListDappNavigationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDappNavigation not implemented")
}
func (UnimplementedAdminDappSrvServer) BatchUpdateDappNavigation(context.Context, *BatchUpdateDappNavigationReq) (*BatchUpdateDappNavigationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchUpdateDappNavigation not implemented")
}
func (UnimplementedAdminDappSrvServer) SortDappNavigation(context.Context, *SortReq) (*SortReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortDappNavigation not implemented")
}
func (UnimplementedAdminDappSrvServer) CreateDappIndex(context.Context, *CreateDappIndexReq) (*CreateDappIndexReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDappIndex not implemented")
}
func (UnimplementedAdminDappSrvServer) ListDappIndex(context.Context, *ListDappIndexReq) (*ListDappIndexReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDappIndex not implemented")
}
func (UnimplementedAdminDappSrvServer) DeleteDappIndex(context.Context, *DeleteDappIndexReq) (*DeleteDappIndexReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDappIndex not implemented")
}
func (UnimplementedAdminDappSrvServer) BatchUpdateDappIndex(context.Context, *BatchUpdateDappIndexReq) (*BatchUpdateDappIndexReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchUpdateDappIndex not implemented")
}
func (UnimplementedAdminDappSrvServer) SortDappIndex(context.Context, *SortReq) (*SortReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortDappIndex not implemented")
}
func (UnimplementedAdminDappSrvServer) CreateDappTopicRel(context.Context, *CreateDappTopicRelReq) (*CreateDappTopicRelReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDappTopicRel not implemented")
}
func (UnimplementedAdminDappSrvServer) DeleteDappTopicRel(context.Context, *DeleteDappTopicRelReq) (*DeleteDappTopicRelReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDappTopicRel not implemented")
}
func (UnimplementedAdminDappSrvServer) ListDappTopicRel(context.Context, *ListDappTopicRelReq) (*ListDappTopicRelReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDappTopicRel not implemented")
}
func (UnimplementedAdminDappSrvServer) mustEmbedUnimplementedAdminDappSrvServer() {}
func (UnimplementedAdminDappSrvServer) testEmbeddedByValue()                      {}

// UnsafeAdminDappSrvServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AdminDappSrvServer will
// result in compilation errors.
type UnsafeAdminDappSrvServer interface {
	mustEmbedUnimplementedAdminDappSrvServer()
}

func RegisterAdminDappSrvServer(s grpc.ServiceRegistrar, srv AdminDappSrvServer) {
	// If the following call pancis, it indicates UnimplementedAdminDappSrvServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AdminDappSrv_ServiceDesc, srv)
}

func _AdminDappSrv_CreateDapp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDappReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminDappSrvServer).CreateDapp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminDappSrv_CreateDapp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminDappSrvServer).CreateDapp(ctx, req.(*CreateDappReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminDappSrv_DeleteDapp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDappReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminDappSrvServer).DeleteDapp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminDappSrv_DeleteDapp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminDappSrvServer).DeleteDapp(ctx, req.(*DeleteDappReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminDappSrv_UpdateDapp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDappReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminDappSrvServer).UpdateDapp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminDappSrv_UpdateDapp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminDappSrvServer).UpdateDapp(ctx, req.(*UpdateDappReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminDappSrv_ListDapp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDappReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminDappSrvServer).ListDapp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminDappSrv_ListDapp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminDappSrvServer).ListDapp(ctx, req.(*ListDappReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminDappSrv_CreateDappCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDappCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminDappSrvServer).CreateDappCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminDappSrv_CreateDappCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminDappSrvServer).CreateDappCategory(ctx, req.(*CreateDappCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminDappSrv_UpdateDappCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDappCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminDappSrvServer).UpdateDappCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminDappSrv_UpdateDappCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminDappSrvServer).UpdateDappCategory(ctx, req.(*UpdateDappCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminDappSrv_ListDappCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDappCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminDappSrvServer).ListDappCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminDappSrv_ListDappCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminDappSrvServer).ListDappCategory(ctx, req.(*ListDappCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminDappSrv_DeleteDappCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDappCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminDappSrvServer).DeleteDappCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminDappSrv_DeleteDappCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminDappSrvServer).DeleteDappCategory(ctx, req.(*DeleteDappCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminDappSrv_CreateDappCategoryRel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDappCategoryRelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminDappSrvServer).CreateDappCategoryRel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminDappSrv_CreateDappCategoryRel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminDappSrvServer).CreateDappCategoryRel(ctx, req.(*CreateDappCategoryRelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminDappSrv_DeleteDappCategoryRel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDappCategoryRelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminDappSrvServer).DeleteDappCategoryRel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminDappSrv_DeleteDappCategoryRel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminDappSrvServer).DeleteDappCategoryRel(ctx, req.(*DeleteDappCategoryRelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminDappSrv_ListDappCategoryRel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDappCategoryRelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminDappSrvServer).ListDappCategoryRel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminDappSrv_ListDappCategoryRel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminDappSrvServer).ListDappCategoryRel(ctx, req.(*ListDappCategoryRelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminDappSrv_CreateDappTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDappTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminDappSrvServer).CreateDappTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminDappSrv_CreateDappTopic_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminDappSrvServer).CreateDappTopic(ctx, req.(*CreateDappTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminDappSrv_DeleteDappTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDappTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminDappSrvServer).DeleteDappTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminDappSrv_DeleteDappTopic_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminDappSrvServer).DeleteDappTopic(ctx, req.(*DeleteDappTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminDappSrv_UpdateDappTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDappTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminDappSrvServer).UpdateDappTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminDappSrv_UpdateDappTopic_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminDappSrvServer).UpdateDappTopic(ctx, req.(*UpdateDappTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminDappSrv_ListDappTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDappTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminDappSrvServer).ListDappTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminDappSrv_ListDappTopic_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminDappSrvServer).ListDappTopic(ctx, req.(*ListDappTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminDappSrv_CreateDappNavigation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDappNavigationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminDappSrvServer).CreateDappNavigation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminDappSrv_CreateDappNavigation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminDappSrvServer).CreateDappNavigation(ctx, req.(*CreateDappNavigationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminDappSrv_DeleteDappNavigation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDappNavigationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminDappSrvServer).DeleteDappNavigation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminDappSrv_DeleteDappNavigation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminDappSrvServer).DeleteDappNavigation(ctx, req.(*DeleteDappNavigationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminDappSrv_ListDappNavigation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDappNavigationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminDappSrvServer).ListDappNavigation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminDappSrv_ListDappNavigation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminDappSrvServer).ListDappNavigation(ctx, req.(*ListDappNavigationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminDappSrv_BatchUpdateDappNavigation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpdateDappNavigationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminDappSrvServer).BatchUpdateDappNavigation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminDappSrv_BatchUpdateDappNavigation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminDappSrvServer).BatchUpdateDappNavigation(ctx, req.(*BatchUpdateDappNavigationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminDappSrv_SortDappNavigation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminDappSrvServer).SortDappNavigation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminDappSrv_SortDappNavigation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminDappSrvServer).SortDappNavigation(ctx, req.(*SortReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminDappSrv_CreateDappIndex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDappIndexReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminDappSrvServer).CreateDappIndex(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminDappSrv_CreateDappIndex_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminDappSrvServer).CreateDappIndex(ctx, req.(*CreateDappIndexReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminDappSrv_ListDappIndex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDappIndexReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminDappSrvServer).ListDappIndex(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminDappSrv_ListDappIndex_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminDappSrvServer).ListDappIndex(ctx, req.(*ListDappIndexReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminDappSrv_DeleteDappIndex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDappIndexReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminDappSrvServer).DeleteDappIndex(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminDappSrv_DeleteDappIndex_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminDappSrvServer).DeleteDappIndex(ctx, req.(*DeleteDappIndexReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminDappSrv_BatchUpdateDappIndex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpdateDappIndexReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminDappSrvServer).BatchUpdateDappIndex(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminDappSrv_BatchUpdateDappIndex_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminDappSrvServer).BatchUpdateDappIndex(ctx, req.(*BatchUpdateDappIndexReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminDappSrv_SortDappIndex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminDappSrvServer).SortDappIndex(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminDappSrv_SortDappIndex_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminDappSrvServer).SortDappIndex(ctx, req.(*SortReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminDappSrv_CreateDappTopicRel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDappTopicRelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminDappSrvServer).CreateDappTopicRel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminDappSrv_CreateDappTopicRel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminDappSrvServer).CreateDappTopicRel(ctx, req.(*CreateDappTopicRelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminDappSrv_DeleteDappTopicRel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDappTopicRelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminDappSrvServer).DeleteDappTopicRel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminDappSrv_DeleteDappTopicRel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminDappSrvServer).DeleteDappTopicRel(ctx, req.(*DeleteDappTopicRelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminDappSrv_ListDappTopicRel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDappTopicRelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminDappSrvServer).ListDappTopicRel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminDappSrv_ListDappTopicRel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminDappSrvServer).ListDappTopicRel(ctx, req.(*ListDappTopicRelReq))
	}
	return interceptor(ctx, in, info, handler)
}

// AdminDappSrv_ServiceDesc is the grpc.ServiceDesc for AdminDappSrv service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AdminDappSrv_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.walletadmin.v1.AdminDappSrv",
	HandlerType: (*AdminDappSrvServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateDapp",
			Handler:    _AdminDappSrv_CreateDapp_Handler,
		},
		{
			MethodName: "DeleteDapp",
			Handler:    _AdminDappSrv_DeleteDapp_Handler,
		},
		{
			MethodName: "UpdateDapp",
			Handler:    _AdminDappSrv_UpdateDapp_Handler,
		},
		{
			MethodName: "ListDapp",
			Handler:    _AdminDappSrv_ListDapp_Handler,
		},
		{
			MethodName: "CreateDappCategory",
			Handler:    _AdminDappSrv_CreateDappCategory_Handler,
		},
		{
			MethodName: "UpdateDappCategory",
			Handler:    _AdminDappSrv_UpdateDappCategory_Handler,
		},
		{
			MethodName: "ListDappCategory",
			Handler:    _AdminDappSrv_ListDappCategory_Handler,
		},
		{
			MethodName: "DeleteDappCategory",
			Handler:    _AdminDappSrv_DeleteDappCategory_Handler,
		},
		{
			MethodName: "CreateDappCategoryRel",
			Handler:    _AdminDappSrv_CreateDappCategoryRel_Handler,
		},
		{
			MethodName: "DeleteDappCategoryRel",
			Handler:    _AdminDappSrv_DeleteDappCategoryRel_Handler,
		},
		{
			MethodName: "ListDappCategoryRel",
			Handler:    _AdminDappSrv_ListDappCategoryRel_Handler,
		},
		{
			MethodName: "CreateDappTopic",
			Handler:    _AdminDappSrv_CreateDappTopic_Handler,
		},
		{
			MethodName: "DeleteDappTopic",
			Handler:    _AdminDappSrv_DeleteDappTopic_Handler,
		},
		{
			MethodName: "UpdateDappTopic",
			Handler:    _AdminDappSrv_UpdateDappTopic_Handler,
		},
		{
			MethodName: "ListDappTopic",
			Handler:    _AdminDappSrv_ListDappTopic_Handler,
		},
		{
			MethodName: "CreateDappNavigation",
			Handler:    _AdminDappSrv_CreateDappNavigation_Handler,
		},
		{
			MethodName: "DeleteDappNavigation",
			Handler:    _AdminDappSrv_DeleteDappNavigation_Handler,
		},
		{
			MethodName: "ListDappNavigation",
			Handler:    _AdminDappSrv_ListDappNavigation_Handler,
		},
		{
			MethodName: "BatchUpdateDappNavigation",
			Handler:    _AdminDappSrv_BatchUpdateDappNavigation_Handler,
		},
		{
			MethodName: "SortDappNavigation",
			Handler:    _AdminDappSrv_SortDappNavigation_Handler,
		},
		{
			MethodName: "CreateDappIndex",
			Handler:    _AdminDappSrv_CreateDappIndex_Handler,
		},
		{
			MethodName: "ListDappIndex",
			Handler:    _AdminDappSrv_ListDappIndex_Handler,
		},
		{
			MethodName: "DeleteDappIndex",
			Handler:    _AdminDappSrv_DeleteDappIndex_Handler,
		},
		{
			MethodName: "BatchUpdateDappIndex",
			Handler:    _AdminDappSrv_BatchUpdateDappIndex_Handler,
		},
		{
			MethodName: "SortDappIndex",
			Handler:    _AdminDappSrv_SortDappIndex_Handler,
		},
		{
			MethodName: "CreateDappTopicRel",
			Handler:    _AdminDappSrv_CreateDappTopicRel_Handler,
		},
		{
			MethodName: "DeleteDappTopicRel",
			Handler:    _AdminDappSrv_DeleteDappTopicRel_Handler,
		},
		{
			MethodName: "ListDappTopicRel",
			Handler:    _AdminDappSrv_ListDappTopicRel_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/walletadmin/v1/dapp.proto",
}
