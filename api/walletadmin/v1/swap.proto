syntax = "proto3";

package api.walletadmin.v1;

import "buf/validate/validate.proto";
import "google/api/annotations.proto";
import "api/walletadmin/v1/sort.proto";

option go_package = "byd_wallet/api/walletadmin/v1;v1";

service SwapService {
  // 查询兑换渠道
  rpc ListSwapChannel (ListSwapChannelRequest) returns (ListSwapChannelReply) {
    option (google.api.http) = {
      get: "/admin/v1/swap/channel"
    };
  }
  // 更新兑换渠道
  rpc UpdateSwapChannel (UpdateSwapChannelRequest) returns (UpdateSwapChannelReply) {
    option (google.api.http) = {
      put: "/admin/v1/swap/channel/{id}"
      body: "*"
    };
  }
  // 查询兑换的币种列表
  rpc ListToken (ListTokenRequest) returns (ListTokenReply) {
    option (google.api.http) = {
      get: "/admin/v1/swap/token"
    };
  }
  // 更新兑换币种
  rpc UpdateToken (UpdateTokenRequest) returns (UpdateTokenReply) {
    option (google.api.http) = {
      put: "/admin/v1/swap/token/{id}"
      body: "*"
    };
  }
  // 查询热门代币
  rpc ListHotToken (ListHotTokenRequest) returns (ListHotTokenReply) {
    option (google.api.http) = {
      get: "/admin/v1/swap/token/hot"
    };
  }
  // 增加热门代币
  rpc CreateHotToken (CreateHotTokenRequest) returns (CreateHotTokenReply) {
    option (google.api.http) = {
      post: "/admin/v1/swap/token/hot"
      body: "*"
    };
  }
  // 删除热门代币
  rpc DeleteHotToken (DeleteHotTokenRequest) returns (DeleteHotTokenReply) {
    option (google.api.http) = {
      delete: "/admin/v1/swap/token/hot/{id}"
    };
  }
  // 热门代币排序
  rpc SortHotToken (CommonSortReq) returns (CommonSortReply) {
    option (google.api.http) = {
      put: "/admin/v1/swap/token/hot/sort"
      body: "*"
    };
  }
  // 查询兑换记录
  rpc GetSwapRecord (GetSwapRecordRequest) returns (SwapRecord) {
    option (google.api.http) = {
      get: "/admin/v1/swap/record/{id}"
    };
  }
  // 查询兑换记录列表
  rpc ListSwapRecord (ListSwapRecordRequest) returns (ListSwapRecordReply) {
    option (google.api.http) = {
      get: "/admin/v1/swap/record"
    };
  }
}

message SwapChannel {
  // 记录ID
  uint64 id = 1;
  // 平台名称
  string name = 2;
  // 是否可用
  bool enable = 3;
}

message SwappableToken {
  // 记录ID
  uint64 id = 1;
  // 币名称
  string name = 2;
  // 币符号
  string symbol = 3;
  // 链名称
  int64 chain_index = 4;
  // 精度位数
  int64 decimals = 5;
  // 币合约地址
  string address = 6;
  // 创建时间(时间戳,单位秒)
  int64 created_at = 7;
  // 链名称
  string chain_name = 8;
  // 币图标
  string logo_url = 9;
  // 是否展示
  bool display = 10;
  // 网络ID
  string chain_id = 11;
  // 是否已上架
  bool enable = 12;
  // 商户平台
  repeated string channels = 13;
  // 是否是所有
  bool is_all = 14;
}

message SwapRecord {
  // 兑换记录id
  uint64 id = 1;
  // 兑换时间(unix时间戳)
  int64 swapped_at = 2;
  // 兑换状态
  // pending(处理中), wait_kyc(等待KYC验证), wait_refund(等待退款), refund_complete(退款完成)
  // wait_deposit_send_fail(充币失败), wait_for_information(异常订单-等待处理)
  // 终态： success(兑换成功), fail(兑换失败)，timeout(超时-未在指定时间内充币)
  string status = 3;
  SwapToken from = 4;
  SwapToken to = 5;
  // 用户兑换消耗的GasFee
  string gas_fee = 6;
  // 兑换手续费率
  string fee_rate = 7;
  // 用户发起的交易hash
  string hash = 8;
  // 授权hash
  string approval_hash = 9;
  // 区块高度
  int64 height = 10;
  // 兑换平台名称
  string dex = 11;
  // 兑换平台logo url
  string dex_logo = 12;
  // 兑换价格
  string swap_price = 13;
  // 兑换详情列表
  repeated SwapDetail details = 14;
  // 结束时间(unix时间戳)
  int64 finished_at = 15;
}

message SwapToken {
  // 代币合约地址
  string token_address = 1;
  // 用户地址
  string address = 2;
  // 链
  int64 chain_index = 3;
  // 数量
  string amount = 4;
  // token logo url
  string token_logo = 5;
  // 币种
  string symbol = 6;
  // 精度
  string decimals = 7;
}

message SwapDetail {
  // 链索引
  int64 chain_index = 1;
  // 区块浏览器url
  string explorer_url = 2;
  // 交易hash
  string hash = 3;
  // 交易状态
  string status = 4;
  // 是否收录
  bool collected = 5;
}

message ListSwapChannelRequest {
  // 页码 从1开始
  int64 page = 1 [(buf.validate.field).int64.gt = 0];
  // 每页展示条数
  int64 page_size = 2 [(buf.validate.field).int64 = {
    gt: 0
    lte: 100
  }];
}

message ListSwapChannelReply {
  repeated SwapChannel list = 1;
  // 总条数
  int64 total_count = 2;
}

message UpdateSwapChannelRequest {
  // 记录id
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
  // 是否关闭通道
  bool enable = 2;
}

message UpdateSwapChannelReply {}

message ListTokenRequest {
  // 公链索引，传-1 获取所有
  int64 chain_index = 1;
  // 简称
  string symbol = 2;
  // 合约号
  string address = 3;
  // 兑换渠道id(商户id)
  uint64 channel_id = 4;
  // 页码 从1开始
  int64 page = 5 [(buf.validate.field).int64.gt = 0];
  // 每页展示条数
  int64 page_size = 6 [(buf.validate.field).int64 = {
    gt: 0
    lte: 100
  }];
  // 是否是原生代币
  bool native = 7;
}

message ListTokenReply {
  repeated SwappableToken list = 1;
  // 总条数
  int64 total_count = 2;
}

message UpdateTokenRequest {
  // 记录id
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
  // 是否展示
  bool display = 2;
}

message UpdateTokenReply {}

message ListHotTokenRequest {
  // 公链索引，传-1 获取所有
  int64 chain_index = 1;
  // 币种
  string symbol = 2;
  // 合约地址
  string address = 3;
  // 页码 从1开始
  int64 page = 4 [(buf.validate.field).int64.gt = 0];
  // 每页展示条数
  int64 page_size = 5 [(buf.validate.field).int64 = {
    gt: 0
    lte: 100
  }];
}

message ListHotTokenReply {
  repeated SwappableToken list = 1;
  // 总条数
  int64 total_count = 2;
}

message CreateHotTokenRequest {
  // 通过搜索出来的数据获取
  int64 swappable_token_id = 1;
  // 是否是全部
  bool is_all = 2;
}

message CreateHotTokenReply {}

message DeleteHotTokenRequest {
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
}

message DeleteHotTokenReply {}

message GetSwapRecordRequest {
  // 兑换记录id
  uint64 id = 1;
}

message ListSwapRecordRequest {
  // 页码 从1开始
  int64 page = 1 [(buf.validate.field).int64.gt = 0];
  // 每页展示条数
  int64 page_size = 2 [(buf.validate.field).int64 = {
    gt: 0
    lte: 100
  }];
  // 交易币种合约地址
  string address = 3;
  // 兑换渠道id, 不传查询所有
  uint64 channel_id = 4;
  // 兑换状态
  // pending(处理中), wait_kyc(等待KYC验证), wait_refund(等待退款), refund_complete(退款完成)
  // wait_deposit_send_fail(充币失败), wait_for_information(异常订单-等待处理)
  // 终态： success(兑换成功), fail(兑换失败)，timeout(超时-未在指定时间内充币)
  string status = 5;
  // 发起账户地址
  string from_address = 6;
  // 交易币种名称
  string symbol = 7;
}

message ListSwapRecordReply {
  repeated SwapRecord list = 1;
  // 总条数
  int64 total_count = 2;
}

