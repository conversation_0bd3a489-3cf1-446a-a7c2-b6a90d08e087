syntax = "proto3";

package api.walletadmin.v1;

option go_package = "byd_wallet/api/walletadmin/v1;v1";
option java_multiple_files = true;
option java_package = "api.walletadmin.v1";

import "google/api/annotations.proto";
import "buf/validate/validate.proto";
import "google/protobuf/empty.proto";


service CoinSrv {
	// 币种列表
	rpc ListCoin(ListCoinReq) returns (ListCoinReply) {
		option (google.api.http) = {
			get: "/admin/v1/coin/list_coin"
		};
	};
	// 更新币种
	rpc UpdateCoin(UpdateCoinReq) returns (google.protobuf.Empty) {
		option (google.api.http) = {
			put: "/admin/v1/coin/update_coin"
			body: "*"
		};
	}
	// 加入添加货币列表
	rpc CreateCoinStar(CreateCoinStarReq) returns (google.protobuf.Empty) {
		option (google.api.http) = {
			post: "/admin/v1/coin/create_coin_star"
			body: "*"
		};
	}
	// 添加货币列表
	rpc ListCoinStar(ListCoinStarReq) returns (ListCoinStarReply) {
		option (google.api.http) = {
			get: "/admin/v1/coin/list_coin_star"
		};
	}
	// 移除添加货币列表
	rpc DeleteCoinStar(DeleteCoinStarReq) returns (google.protobuf.Empty) {
		option (google.api.http) = {
			delete: "/admin/v1/coin/delete_coin_star"
		};
	}
	// 更新添加货币列表排序
	rpc UpdateCoinStarSort(UpdateCoinStarSortReq) returns (google.protobuf.Empty) {
		option (google.api.http) = {
			patch: "/admin/v1/coin/update_coin_star_sort"
			body: "*"
		};
	}
}

message UpdateCoinStarSortReq {
	// 记录ID
	uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
	// 当前排序(来自列表的sort_order字段值)
	int64 current_sort = 2;
	// 添加排序(数值越大,排序越靠前)
	int64 target_sort = 3;
}

message ListCoinStarReq {
	// 页码
	int64 page = 1 [(buf.validate.field).int64.gt = 0];
	// 页容量
	int64 page_size = 2 [(buf.validate.field).int64 = {gt: 0,lte:100}];
	// 链索引(传值-1查询全部链)
	int64 chain_index = 3;
	// 币符号
	optional string symbol = 4;
	// 币合约地址
	optional string address = 5;
}

message CoinStar {
	// 记录ID
	uint64 id = 1;
	// 创建时间(时间戳,单位秒)
	int64 created_at = 2;
	// 币名称
	string name = 3;
	// 币符号
	string symbol = 4;
	// 链索引
	int64 chain_index = 5;
	// 精度位数
	int64 decimals = 6;
	// 币合约地址
	string address = 7;
	// 链名称
	string chain_name = 8;
	// 币图标
	string logo_url = 9;
	// 排序
	int64 sort_order = 10;
}

message ListCoinStarReply {
	repeated CoinStar list = 1;
	// 总记录数
	int64 total_count = 2;
}

message DeleteCoinStarReq {
	// 记录ID
	uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
}

message CreateCoinStarReq {
	// 币种ID
	uint64 coin_id = 1 [(buf.validate.field).uint64.gt = 0];
}


message UpdateCoinReq {
	// 记录ID
	uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
	// 币图标
	string logo_url = 2 [(buf.validate.field).string.uri = true];
	// 是否展示
	bool is_display = 3;
}

message ListCoinReq {
	// 页码
	int64 page = 1 [(buf.validate.field).int64.gt = 0];
	// 页容量
	int64 page_size = 2 [(buf.validate.field).int64 = {gt: 0,lte:100}];
	// 链索引(传值-1查询全部链)
	int64 chain_index = 3;
	// 币符号
	optional string symbol = 4;
	// 币合约地址
	optional string address = 5;
}

message Coin {
	// 记录ID
	uint64 id = 1;
	// 币名称
	string name = 2;
	// 币符号
	string symbol = 3;
	// 链索引
	int64 chain_index = 4;
	// 精度位数
	int64 decimals = 5;
	// 币合约地址
	string address = 6;
	// 创建时间(时间戳,单位秒)
	int64 created_at = 7;
	// 链名称
	string chain_name = 8;
	// 币图标
	string logo_url = 9;
	// 是否展示
	bool is_display = 10;
	// 网络ID
	string chain_id = 11;
}

message ListCoinReply {
	repeated Coin list = 1;
	// 总记录数
	int64 total_count = 2;
}