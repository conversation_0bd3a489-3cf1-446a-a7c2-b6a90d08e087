// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: api/walletadmin/v1/tx.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ListTxReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 页码
	Page int64 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	// 页容量
	PageSize int64 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 链索引(默认bitcoin)
	ChainIndex int64 `protobuf:"varint,3,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 转出地址
	FromAddress *string `protobuf:"bytes,4,opt,name=from_address,json=fromAddress,proto3,oneof" json:"from_address,omitempty"`
	// 转入地址
	ToAddress *string `protobuf:"bytes,5,opt,name=to_address,json=toAddress,proto3,oneof" json:"to_address,omitempty"`
	// 交易哈希
	TxHash *string `protobuf:"bytes,6,opt,name=tx_hash,json=txHash,proto3,oneof" json:"tx_hash,omitempty"`
}

func (x *ListTxReq) Reset() {
	*x = ListTxReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_tx_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTxReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTxReq) ProtoMessage() {}

func (x *ListTxReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_tx_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTxReq.ProtoReflect.Descriptor instead.
func (*ListTxReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_tx_proto_rawDescGZIP(), []int{0}
}

func (x *ListTxReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListTxReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListTxReq) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *ListTxReq) GetFromAddress() string {
	if x != nil && x.FromAddress != nil {
		return *x.FromAddress
	}
	return ""
}

func (x *ListTxReq) GetToAddress() string {
	if x != nil && x.ToAddress != nil {
		return *x.ToAddress
	}
	return ""
}

func (x *ListTxReq) GetTxHash() string {
	if x != nil && x.TxHash != nil {
		return *x.TxHash
	}
	return ""
}

type Tx struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 记录ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 币名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 币符号
	Symbol string `protobuf:"bytes,3,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,4,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 精度位数
	Decimals int64 `protobuf:"varint,5,opt,name=decimals,proto3" json:"decimals,omitempty"`
	// 币合约地址
	TokenAddress string `protobuf:"bytes,6,opt,name=token_address,json=tokenAddress,proto3" json:"token_address,omitempty"`
	// 交易(时间戳,单位秒)
	TxTime int64 `protobuf:"varint,7,opt,name=tx_time,json=txTime,proto3" json:"tx_time,omitempty"`
	// 链名称
	ChainName string `protobuf:"bytes,8,opt,name=chain_name,json=chainName,proto3" json:"chain_name,omitempty"`
	// 转出地址
	FromAddress string `protobuf:"bytes,9,opt,name=from_address,json=fromAddress,proto3" json:"from_address,omitempty"`
	// 转入地址
	ToAddress string `protobuf:"bytes,10,opt,name=to_address,json=toAddress,proto3" json:"to_address,omitempty"`
	// 交易状态(success,fail)
	Status string `protobuf:"bytes,11,opt,name=status,proto3" json:"status,omitempty"`
	// 交易金额
	Value string `protobuf:"bytes,12,opt,name=value,proto3" json:"value,omitempty"`
	// 交易手续费
	Fee string `protobuf:"bytes,13,opt,name=fee,proto3" json:"fee,omitempty"`
	// 交易类型
	Method string `protobuf:"bytes,14,opt,name=method,proto3" json:"method,omitempty"`
}

func (x *Tx) Reset() {
	*x = Tx{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_tx_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Tx) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Tx) ProtoMessage() {}

func (x *Tx) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_tx_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Tx.ProtoReflect.Descriptor instead.
func (*Tx) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_tx_proto_rawDescGZIP(), []int{1}
}

func (x *Tx) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Tx) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Tx) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *Tx) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *Tx) GetDecimals() int64 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *Tx) GetTokenAddress() string {
	if x != nil {
		return x.TokenAddress
	}
	return ""
}

func (x *Tx) GetTxTime() int64 {
	if x != nil {
		return x.TxTime
	}
	return 0
}

func (x *Tx) GetChainName() string {
	if x != nil {
		return x.ChainName
	}
	return ""
}

func (x *Tx) GetFromAddress() string {
	if x != nil {
		return x.FromAddress
	}
	return ""
}

func (x *Tx) GetToAddress() string {
	if x != nil {
		return x.ToAddress
	}
	return ""
}

func (x *Tx) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Tx) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *Tx) GetFee() string {
	if x != nil {
		return x.Fee
	}
	return ""
}

func (x *Tx) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

type ListTxReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*Tx `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 总记录数
	TotalCount int64 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *ListTxReply) Reset() {
	*x = ListTxReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_tx_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTxReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTxReply) ProtoMessage() {}

func (x *ListTxReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_tx_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTxReply.ProtoReflect.Descriptor instead.
func (*ListTxReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_tx_proto_rawDescGZIP(), []int{2}
}

func (x *ListTxReply) GetList() []*Tx {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListTxReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

var File_api_walletadmin_v1_tx_proto protoreflect.FileDescriptor

var file_api_walletadmin_v1_tx_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x78, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76,
	0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1b, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x99, 0x02, 0x0a,
	0x09, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x78, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xba, 0x48, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x26, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x09, 0xba, 0x48, 0x06, 0x22,
	0x04, 0x18, 0x64, 0x20, 0x00, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12,
	0x31, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x10, 0xba, 0x48, 0x0d, 0x22, 0x0b, 0x20, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x64,
	0x65, 0x78, 0x12, 0x26, 0x0a, 0x0c, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0b, 0x66, 0x72, 0x6f, 0x6d,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x74, 0x6f,
	0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01,
	0x52, 0x09, 0x74, 0x6f, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x88, 0x01, 0x01, 0x12, 0x1c,
	0x0a, 0x07, 0x74, 0x78, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x02, 0x52, 0x06, 0x74, 0x78, 0x48, 0x61, 0x73, 0x68, 0x88, 0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d,
	0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x0d, 0x0a,
	0x0b, 0x5f, 0x74, 0x6f, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x0a, 0x0a, 0x08,
	0x5f, 0x74, 0x78, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x22, 0xf4, 0x02, 0x0a, 0x02, 0x54, 0x78, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1a, 0x0a, 0x08,
	0x64, 0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x64, 0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x17, 0x0a,
	0x07, 0x74, 0x78, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x74, 0x78, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x72, 0x6f,
	0x6d, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x5f, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x6f,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x66, 0x65, 0x65, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x66, 0x65, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x22,
	0x5a, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x78, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2a,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x78, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x32, 0x6f, 0x0a, 0x05, 0x54,
	0x78, 0x53, 0x72, 0x76, 0x12, 0x66, 0x0a, 0x06, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x78, 0x12, 0x1d,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x78, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x78, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1c,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x12, 0x14, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x76,
	0x31, 0x2f, 0x74, 0x78, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x74, 0x78, 0x42, 0xad, 0x01, 0x0a,
	0x16, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x42, 0x07, 0x54, 0x78, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x50, 0x01, 0x5a, 0x20, 0x62, 0x79, 0x64, 0x5f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x76,
	0x31, 0x3b, 0x76, 0x31, 0xa2, 0x02, 0x03, 0x41, 0x57, 0x58, 0xaa, 0x02, 0x12, 0x41, 0x70, 0x69,
	0x2e, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x56, 0x31, 0xca,
	0x02, 0x12, 0x41, 0x70, 0x69, 0x5c, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x5c, 0x56, 0x31, 0xe2, 0x02, 0x1e, 0x41, 0x70, 0x69, 0x5c, 0x57, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5c, 0x56, 0x31, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0xea, 0x02, 0x14, 0x41, 0x70, 0x69, 0x3a, 0x3a, 0x57, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x3a, 0x3a, 0x56, 0x31, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_walletadmin_v1_tx_proto_rawDescOnce sync.Once
	file_api_walletadmin_v1_tx_proto_rawDescData = file_api_walletadmin_v1_tx_proto_rawDesc
)

func file_api_walletadmin_v1_tx_proto_rawDescGZIP() []byte {
	file_api_walletadmin_v1_tx_proto_rawDescOnce.Do(func() {
		file_api_walletadmin_v1_tx_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_walletadmin_v1_tx_proto_rawDescData)
	})
	return file_api_walletadmin_v1_tx_proto_rawDescData
}

var file_api_walletadmin_v1_tx_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_walletadmin_v1_tx_proto_goTypes = []any{
	(*ListTxReq)(nil),   // 0: api.walletadmin.v1.ListTxReq
	(*Tx)(nil),          // 1: api.walletadmin.v1.Tx
	(*ListTxReply)(nil), // 2: api.walletadmin.v1.ListTxReply
}
var file_api_walletadmin_v1_tx_proto_depIdxs = []int32{
	1, // 0: api.walletadmin.v1.ListTxReply.list:type_name -> api.walletadmin.v1.Tx
	0, // 1: api.walletadmin.v1.TxSrv.ListTx:input_type -> api.walletadmin.v1.ListTxReq
	2, // 2: api.walletadmin.v1.TxSrv.ListTx:output_type -> api.walletadmin.v1.ListTxReply
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_walletadmin_v1_tx_proto_init() }
func file_api_walletadmin_v1_tx_proto_init() {
	if File_api_walletadmin_v1_tx_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_walletadmin_v1_tx_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*ListTxReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_tx_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*Tx); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_tx_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*ListTxReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_walletadmin_v1_tx_proto_msgTypes[0].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_walletadmin_v1_tx_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_walletadmin_v1_tx_proto_goTypes,
		DependencyIndexes: file_api_walletadmin_v1_tx_proto_depIdxs,
		MessageInfos:      file_api_walletadmin_v1_tx_proto_msgTypes,
	}.Build()
	File_api_walletadmin_v1_tx_proto = out.File
	file_api_walletadmin_v1_tx_proto_rawDesc = nil
	file_api_walletadmin_v1_tx_proto_goTypes = nil
	file_api_walletadmin_v1_tx_proto_depIdxs = nil
}
