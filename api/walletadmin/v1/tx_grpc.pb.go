// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: api/walletadmin/v1/tx.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	TxSrv_ListTx_FullMethodName = "/api.walletadmin.v1.TxSrv/ListTx"
)

// TxSrvClient is the client API for TxSrv service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TxSrvClient interface {
	// 交易列表
	ListTx(ctx context.Context, in *ListTxReq, opts ...grpc.CallOption) (*ListTxReply, error)
}

type txSrvClient struct {
	cc grpc.ClientConnInterface
}

func NewTxSrvClient(cc grpc.ClientConnInterface) TxSrvClient {
	return &txSrvClient{cc}
}

func (c *txSrvClient) ListTx(ctx context.Context, in *ListTxReq, opts ...grpc.CallOption) (*ListTxReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListTxReply)
	err := c.cc.Invoke(ctx, TxSrv_ListTx_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TxSrvServer is the server API for TxSrv service.
// All implementations must embed UnimplementedTxSrvServer
// for forward compatibility.
type TxSrvServer interface {
	// 交易列表
	ListTx(context.Context, *ListTxReq) (*ListTxReply, error)
	mustEmbedUnimplementedTxSrvServer()
}

// UnimplementedTxSrvServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTxSrvServer struct{}

func (UnimplementedTxSrvServer) ListTx(context.Context, *ListTxReq) (*ListTxReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTx not implemented")
}
func (UnimplementedTxSrvServer) mustEmbedUnimplementedTxSrvServer() {}
func (UnimplementedTxSrvServer) testEmbeddedByValue()               {}

// UnsafeTxSrvServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TxSrvServer will
// result in compilation errors.
type UnsafeTxSrvServer interface {
	mustEmbedUnimplementedTxSrvServer()
}

func RegisterTxSrvServer(s grpc.ServiceRegistrar, srv TxSrvServer) {
	// If the following call pancis, it indicates UnimplementedTxSrvServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&TxSrv_ServiceDesc, srv)
}

func _TxSrv_ListTx_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTxReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TxSrvServer).ListTx(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TxSrv_ListTx_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TxSrvServer).ListTx(ctx, req.(*ListTxReq))
	}
	return interceptor(ctx, in, info, handler)
}

// TxSrv_ServiceDesc is the grpc.ServiceDesc for TxSrv service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TxSrv_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.walletadmin.v1.TxSrv",
	HandlerType: (*TxSrvServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListTx",
			Handler:    _TxSrv_ListTx_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/walletadmin/v1/tx.proto",
}
