// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: api/walletadmin/v1/chain.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ChainSrv_ListChain_FullMethodName       = "/api.walletadmin.v1.ChainSrv/ListChain"
	ChainSrv_UpdateChain_FullMethodName     = "/api.walletadmin.v1.ChainSrv/UpdateChain"
	ChainSrv_UpdateChainSort_FullMethodName = "/api.walletadmin.v1.ChainSrv/UpdateChainSort"
)

// ChainSrvClient is the client API for ChainSrv service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ChainSrvClient interface {
	// 公链列表
	ListChain(ctx context.Context, in *ListChainReq, opts ...grpc.CallOption) (*ListChainReply, error)
	// 更新公链记录
	UpdateChain(ctx context.Context, in *UpdateChainReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 更新公链排序
	UpdateChainSort(ctx context.Context, in *UpdateChainSortReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type chainSrvClient struct {
	cc grpc.ClientConnInterface
}

func NewChainSrvClient(cc grpc.ClientConnInterface) ChainSrvClient {
	return &chainSrvClient{cc}
}

func (c *chainSrvClient) ListChain(ctx context.Context, in *ListChainReq, opts ...grpc.CallOption) (*ListChainReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListChainReply)
	err := c.cc.Invoke(ctx, ChainSrv_ListChain_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chainSrvClient) UpdateChain(ctx context.Context, in *UpdateChainReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ChainSrv_UpdateChain_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chainSrvClient) UpdateChainSort(ctx context.Context, in *UpdateChainSortReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ChainSrv_UpdateChainSort_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChainSrvServer is the server API for ChainSrv service.
// All implementations must embed UnimplementedChainSrvServer
// for forward compatibility.
type ChainSrvServer interface {
	// 公链列表
	ListChain(context.Context, *ListChainReq) (*ListChainReply, error)
	// 更新公链记录
	UpdateChain(context.Context, *UpdateChainReq) (*emptypb.Empty, error)
	// 更新公链排序
	UpdateChainSort(context.Context, *UpdateChainSortReq) (*emptypb.Empty, error)
	mustEmbedUnimplementedChainSrvServer()
}

// UnimplementedChainSrvServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedChainSrvServer struct{}

func (UnimplementedChainSrvServer) ListChain(context.Context, *ListChainReq) (*ListChainReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListChain not implemented")
}
func (UnimplementedChainSrvServer) UpdateChain(context.Context, *UpdateChainReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateChain not implemented")
}
func (UnimplementedChainSrvServer) UpdateChainSort(context.Context, *UpdateChainSortReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateChainSort not implemented")
}
func (UnimplementedChainSrvServer) mustEmbedUnimplementedChainSrvServer() {}
func (UnimplementedChainSrvServer) testEmbeddedByValue()                  {}

// UnsafeChainSrvServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ChainSrvServer will
// result in compilation errors.
type UnsafeChainSrvServer interface {
	mustEmbedUnimplementedChainSrvServer()
}

func RegisterChainSrvServer(s grpc.ServiceRegistrar, srv ChainSrvServer) {
	// If the following call pancis, it indicates UnimplementedChainSrvServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ChainSrv_ServiceDesc, srv)
}

func _ChainSrv_ListChain_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListChainReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChainSrvServer).ListChain(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChainSrv_ListChain_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChainSrvServer).ListChain(ctx, req.(*ListChainReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChainSrv_UpdateChain_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateChainReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChainSrvServer).UpdateChain(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChainSrv_UpdateChain_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChainSrvServer).UpdateChain(ctx, req.(*UpdateChainReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChainSrv_UpdateChainSort_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateChainSortReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChainSrvServer).UpdateChainSort(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChainSrv_UpdateChainSort_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChainSrvServer).UpdateChainSort(ctx, req.(*UpdateChainSortReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ChainSrv_ServiceDesc is the grpc.ServiceDesc for ChainSrv service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ChainSrv_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.walletadmin.v1.ChainSrv",
	HandlerType: (*ChainSrvServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListChain",
			Handler:    _ChainSrv_ListChain_Handler,
		},
		{
			MethodName: "UpdateChain",
			Handler:    _ChainSrv_UpdateChain_Handler,
		},
		{
			MethodName: "UpdateChainSort",
			Handler:    _ChainSrv_UpdateChainSort_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/walletadmin/v1/chain.proto",
}
