// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: api/walletadmin/v1/swap.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	SwapService_ListSwapChannel_FullMethodName   = "/api.walletadmin.v1.SwapService/ListSwapChannel"
	SwapService_UpdateSwapChannel_FullMethodName = "/api.walletadmin.v1.SwapService/UpdateSwapChannel"
	SwapService_ListToken_FullMethodName         = "/api.walletadmin.v1.SwapService/ListToken"
	SwapService_UpdateToken_FullMethodName       = "/api.walletadmin.v1.SwapService/UpdateToken"
	SwapService_ListHotToken_FullMethodName      = "/api.walletadmin.v1.SwapService/ListHotToken"
	SwapService_CreateHotToken_FullMethodName    = "/api.walletadmin.v1.SwapService/CreateHotToken"
	SwapService_DeleteHotToken_FullMethodName    = "/api.walletadmin.v1.SwapService/DeleteHotToken"
	SwapService_SortHotToken_FullMethodName      = "/api.walletadmin.v1.SwapService/SortHotToken"
	SwapService_GetSwapRecord_FullMethodName     = "/api.walletadmin.v1.SwapService/GetSwapRecord"
	SwapService_ListSwapRecord_FullMethodName    = "/api.walletadmin.v1.SwapService/ListSwapRecord"
)

// SwapServiceClient is the client API for SwapService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SwapServiceClient interface {
	// 查询兑换渠道
	ListSwapChannel(ctx context.Context, in *ListSwapChannelRequest, opts ...grpc.CallOption) (*ListSwapChannelReply, error)
	// 更新兑换渠道
	UpdateSwapChannel(ctx context.Context, in *UpdateSwapChannelRequest, opts ...grpc.CallOption) (*UpdateSwapChannelReply, error)
	// 查询兑换的币种列表
	ListToken(ctx context.Context, in *ListTokenRequest, opts ...grpc.CallOption) (*ListTokenReply, error)
	// 更新兑换币种
	UpdateToken(ctx context.Context, in *UpdateTokenRequest, opts ...grpc.CallOption) (*UpdateTokenReply, error)
	// 查询热门代币
	ListHotToken(ctx context.Context, in *ListHotTokenRequest, opts ...grpc.CallOption) (*ListHotTokenReply, error)
	// 增加热门代币
	CreateHotToken(ctx context.Context, in *CreateHotTokenRequest, opts ...grpc.CallOption) (*CreateHotTokenReply, error)
	// 删除热门代币
	DeleteHotToken(ctx context.Context, in *DeleteHotTokenRequest, opts ...grpc.CallOption) (*DeleteHotTokenReply, error)
	// 热门代币排序
	SortHotToken(ctx context.Context, in *CommonSortReq, opts ...grpc.CallOption) (*CommonSortReply, error)
	// 查询兑换记录
	GetSwapRecord(ctx context.Context, in *GetSwapRecordRequest, opts ...grpc.CallOption) (*SwapRecord, error)
	// 查询兑换记录列表
	ListSwapRecord(ctx context.Context, in *ListSwapRecordRequest, opts ...grpc.CallOption) (*ListSwapRecordReply, error)
}

type swapServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSwapServiceClient(cc grpc.ClientConnInterface) SwapServiceClient {
	return &swapServiceClient{cc}
}

func (c *swapServiceClient) ListSwapChannel(ctx context.Context, in *ListSwapChannelRequest, opts ...grpc.CallOption) (*ListSwapChannelReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListSwapChannelReply)
	err := c.cc.Invoke(ctx, SwapService_ListSwapChannel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *swapServiceClient) UpdateSwapChannel(ctx context.Context, in *UpdateSwapChannelRequest, opts ...grpc.CallOption) (*UpdateSwapChannelReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateSwapChannelReply)
	err := c.cc.Invoke(ctx, SwapService_UpdateSwapChannel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *swapServiceClient) ListToken(ctx context.Context, in *ListTokenRequest, opts ...grpc.CallOption) (*ListTokenReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListTokenReply)
	err := c.cc.Invoke(ctx, SwapService_ListToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *swapServiceClient) UpdateToken(ctx context.Context, in *UpdateTokenRequest, opts ...grpc.CallOption) (*UpdateTokenReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateTokenReply)
	err := c.cc.Invoke(ctx, SwapService_UpdateToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *swapServiceClient) ListHotToken(ctx context.Context, in *ListHotTokenRequest, opts ...grpc.CallOption) (*ListHotTokenReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListHotTokenReply)
	err := c.cc.Invoke(ctx, SwapService_ListHotToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *swapServiceClient) CreateHotToken(ctx context.Context, in *CreateHotTokenRequest, opts ...grpc.CallOption) (*CreateHotTokenReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateHotTokenReply)
	err := c.cc.Invoke(ctx, SwapService_CreateHotToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *swapServiceClient) DeleteHotToken(ctx context.Context, in *DeleteHotTokenRequest, opts ...grpc.CallOption) (*DeleteHotTokenReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteHotTokenReply)
	err := c.cc.Invoke(ctx, SwapService_DeleteHotToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *swapServiceClient) SortHotToken(ctx context.Context, in *CommonSortReq, opts ...grpc.CallOption) (*CommonSortReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonSortReply)
	err := c.cc.Invoke(ctx, SwapService_SortHotToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *swapServiceClient) GetSwapRecord(ctx context.Context, in *GetSwapRecordRequest, opts ...grpc.CallOption) (*SwapRecord, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SwapRecord)
	err := c.cc.Invoke(ctx, SwapService_GetSwapRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *swapServiceClient) ListSwapRecord(ctx context.Context, in *ListSwapRecordRequest, opts ...grpc.CallOption) (*ListSwapRecordReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListSwapRecordReply)
	err := c.cc.Invoke(ctx, SwapService_ListSwapRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SwapServiceServer is the server API for SwapService service.
// All implementations must embed UnimplementedSwapServiceServer
// for forward compatibility.
type SwapServiceServer interface {
	// 查询兑换渠道
	ListSwapChannel(context.Context, *ListSwapChannelRequest) (*ListSwapChannelReply, error)
	// 更新兑换渠道
	UpdateSwapChannel(context.Context, *UpdateSwapChannelRequest) (*UpdateSwapChannelReply, error)
	// 查询兑换的币种列表
	ListToken(context.Context, *ListTokenRequest) (*ListTokenReply, error)
	// 更新兑换币种
	UpdateToken(context.Context, *UpdateTokenRequest) (*UpdateTokenReply, error)
	// 查询热门代币
	ListHotToken(context.Context, *ListHotTokenRequest) (*ListHotTokenReply, error)
	// 增加热门代币
	CreateHotToken(context.Context, *CreateHotTokenRequest) (*CreateHotTokenReply, error)
	// 删除热门代币
	DeleteHotToken(context.Context, *DeleteHotTokenRequest) (*DeleteHotTokenReply, error)
	// 热门代币排序
	SortHotToken(context.Context, *CommonSortReq) (*CommonSortReply, error)
	// 查询兑换记录
	GetSwapRecord(context.Context, *GetSwapRecordRequest) (*SwapRecord, error)
	// 查询兑换记录列表
	ListSwapRecord(context.Context, *ListSwapRecordRequest) (*ListSwapRecordReply, error)
	mustEmbedUnimplementedSwapServiceServer()
}

// UnimplementedSwapServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSwapServiceServer struct{}

func (UnimplementedSwapServiceServer) ListSwapChannel(context.Context, *ListSwapChannelRequest) (*ListSwapChannelReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSwapChannel not implemented")
}
func (UnimplementedSwapServiceServer) UpdateSwapChannel(context.Context, *UpdateSwapChannelRequest) (*UpdateSwapChannelReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSwapChannel not implemented")
}
func (UnimplementedSwapServiceServer) ListToken(context.Context, *ListTokenRequest) (*ListTokenReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListToken not implemented")
}
func (UnimplementedSwapServiceServer) UpdateToken(context.Context, *UpdateTokenRequest) (*UpdateTokenReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateToken not implemented")
}
func (UnimplementedSwapServiceServer) ListHotToken(context.Context, *ListHotTokenRequest) (*ListHotTokenReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListHotToken not implemented")
}
func (UnimplementedSwapServiceServer) CreateHotToken(context.Context, *CreateHotTokenRequest) (*CreateHotTokenReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateHotToken not implemented")
}
func (UnimplementedSwapServiceServer) DeleteHotToken(context.Context, *DeleteHotTokenRequest) (*DeleteHotTokenReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteHotToken not implemented")
}
func (UnimplementedSwapServiceServer) SortHotToken(context.Context, *CommonSortReq) (*CommonSortReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortHotToken not implemented")
}
func (UnimplementedSwapServiceServer) GetSwapRecord(context.Context, *GetSwapRecordRequest) (*SwapRecord, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSwapRecord not implemented")
}
func (UnimplementedSwapServiceServer) ListSwapRecord(context.Context, *ListSwapRecordRequest) (*ListSwapRecordReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSwapRecord not implemented")
}
func (UnimplementedSwapServiceServer) mustEmbedUnimplementedSwapServiceServer() {}
func (UnimplementedSwapServiceServer) testEmbeddedByValue()                     {}

// UnsafeSwapServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SwapServiceServer will
// result in compilation errors.
type UnsafeSwapServiceServer interface {
	mustEmbedUnimplementedSwapServiceServer()
}

func RegisterSwapServiceServer(s grpc.ServiceRegistrar, srv SwapServiceServer) {
	// If the following call pancis, it indicates UnimplementedSwapServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&SwapService_ServiceDesc, srv)
}

func _SwapService_ListSwapChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSwapChannelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SwapServiceServer).ListSwapChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SwapService_ListSwapChannel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SwapServiceServer).ListSwapChannel(ctx, req.(*ListSwapChannelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SwapService_UpdateSwapChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSwapChannelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SwapServiceServer).UpdateSwapChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SwapService_UpdateSwapChannel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SwapServiceServer).UpdateSwapChannel(ctx, req.(*UpdateSwapChannelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SwapService_ListToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SwapServiceServer).ListToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SwapService_ListToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SwapServiceServer).ListToken(ctx, req.(*ListTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SwapService_UpdateToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SwapServiceServer).UpdateToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SwapService_UpdateToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SwapServiceServer).UpdateToken(ctx, req.(*UpdateTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SwapService_ListHotToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListHotTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SwapServiceServer).ListHotToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SwapService_ListHotToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SwapServiceServer).ListHotToken(ctx, req.(*ListHotTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SwapService_CreateHotToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateHotTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SwapServiceServer).CreateHotToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SwapService_CreateHotToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SwapServiceServer).CreateHotToken(ctx, req.(*CreateHotTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SwapService_DeleteHotToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteHotTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SwapServiceServer).DeleteHotToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SwapService_DeleteHotToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SwapServiceServer).DeleteHotToken(ctx, req.(*DeleteHotTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SwapService_SortHotToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonSortReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SwapServiceServer).SortHotToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SwapService_SortHotToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SwapServiceServer).SortHotToken(ctx, req.(*CommonSortReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SwapService_GetSwapRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSwapRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SwapServiceServer).GetSwapRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SwapService_GetSwapRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SwapServiceServer).GetSwapRecord(ctx, req.(*GetSwapRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SwapService_ListSwapRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSwapRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SwapServiceServer).ListSwapRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SwapService_ListSwapRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SwapServiceServer).ListSwapRecord(ctx, req.(*ListSwapRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SwapService_ServiceDesc is the grpc.ServiceDesc for SwapService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SwapService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.walletadmin.v1.SwapService",
	HandlerType: (*SwapServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListSwapChannel",
			Handler:    _SwapService_ListSwapChannel_Handler,
		},
		{
			MethodName: "UpdateSwapChannel",
			Handler:    _SwapService_UpdateSwapChannel_Handler,
		},
		{
			MethodName: "ListToken",
			Handler:    _SwapService_ListToken_Handler,
		},
		{
			MethodName: "UpdateToken",
			Handler:    _SwapService_UpdateToken_Handler,
		},
		{
			MethodName: "ListHotToken",
			Handler:    _SwapService_ListHotToken_Handler,
		},
		{
			MethodName: "CreateHotToken",
			Handler:    _SwapService_CreateHotToken_Handler,
		},
		{
			MethodName: "DeleteHotToken",
			Handler:    _SwapService_DeleteHotToken_Handler,
		},
		{
			MethodName: "SortHotToken",
			Handler:    _SwapService_SortHotToken_Handler,
		},
		{
			MethodName: "GetSwapRecord",
			Handler:    _SwapService_GetSwapRecord_Handler,
		},
		{
			MethodName: "ListSwapRecord",
			Handler:    _SwapService_ListSwapRecord_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/walletadmin/v1/swap.proto",
}
