syntax = "proto3";

package api.walletadmin.v1;

import "buf/validate/validate.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";

option go_package = "byd_wallet/api/walletadmin/v1;v1";

service AppVersionService {
  // 创建版本
  rpc CreateAppVersion(CreateAppVersionReq) returns (CreateAppVersionReply) {
    option (google.api.http) = {
      post: "/admin/v1/app-versions"
      body: "*"
    };
  }

  // 更新版本
  rpc UpdateAppVersion(UpdateAppVersionReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/admin/v1/app-versions/{id}"
      body: "*"
    };
  }

  // 删除版本
  rpc DeleteAppVersion(DeleteAppVersionReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/admin/v1/app-versions/{id}"};
  }

  // 版本列表
  rpc ListAppVersions(ListAppVersionsReq) returns (ListAppVersionsReply) {
    option (google.api.http) = {get: "/admin/v1/app-versions"};
  }

  // 获取版本详情
  rpc GetAppVersion(GetAppVersionReq) returns (AppVersionInfo) {
    option (google.api.http) = {get: "/admin/v1/app-versions/{id}"};
  }

  // 获取已发布版本列表
  rpc ListPublishedVersions(ListPublishedVersionsReq) returns (ListPublishedVersionsReply) {
    option (google.api.http) = {get: "/admin/v1/app-versions/published"};
  }
}

enum ReminderType {
  REMINDER_TYPE_UNSPECIFIED = 0; // 未指定
  REMINDER_TYPE_EVERY_OPEN = 1; // 每次打开
  REMINDER_TYPE_DAILY = 2; // 每日
  REMINDER_TYPE_WEEKLY = 3; // 每周
  REMINDER_TYPE_NEVER = 4; // 从不
}

// 多语言描述
message AppVersionI18N {
  string language = 1; // 语言代码(zh,en,ja,es)
  string description = 2; // 版本描述
}

// 创建版本请求
message CreateAppVersionReq {
  string version = 1 [(buf.validate.field).string.min_len = 1]; // 版本号
  string app_type = 2 [(buf.validate.field).string.min_len = 1]; // APP类型
  string download_url = 3; // 下载地址
  bool force_update = 4; // 强制更新
  repeated AppVersionI18N i18n_descriptions = 5; // 多语言描述
	ReminderType reminder_type = 6; // 提醒类型 (0:每次打开 1:每日 2:每周 3:从不)
  string min_compatible_version = 7; // 最低兼容版本
  string official_download_url = 8; // 官方下载地址
}

// 创建版本响应
message CreateAppVersionReply {}

// 更新版本请求
message UpdateAppVersionReq {
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0]; // 版本ID
  repeated AppVersionI18N i18n_descriptions = 2; // 多语言描述
}

// 删除版本请求
message DeleteAppVersionReq {
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0]; // 版本ID
}

// 版本列表请求
message ListAppVersionsReq {
  string app_type = 1; // APP类型筛选
  int64 page = 2 [(buf.validate.field).int32.gte = 1]; // 页码
  int64 page_size = 3 [
    (buf.validate.field).int32.gte = 1,
    (buf.validate.field).int32.lte = 100
  ]; // 每页数量
}

// 版本列表响应
message ListAppVersionsReply {
  repeated AppVersionInfo list = 1; // 版本列表
  int64 total_count = 2; // 总数
}

// 获取版本详情请求
message GetAppVersionReq {
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0]; // 版本ID
}

// 获取已发布版本列表请求
message ListPublishedVersionsReq {
  string app_type = 1; // APP类型筛选
}

// 获取已发布版本列表响应
message ListPublishedVersionsReply {
  repeated string versions = 1; // 版本号列表
}

// 版本信息
message AppVersionInfo {
  uint64 id = 1; // 版本ID
  string version = 2; // 版本号
  string app_type = 3; // APP类型
  string download_url = 4; // 下载地址
  bool force_update = 5; // 强制更新
  repeated AppVersionI18N i18n_descriptions = 6; // 多语言描述
  int64 created_at = 7; // 创建时间
  int64 updated_at = 8; // 更新时间
  ReminderType reminder_type = 9; // 提醒类型
  string min_compatible_version = 10; // 最低兼容版本
  string official_download_url = 11; // 官方下载地址
}
