// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: api/walletadmin/v1/app_version.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AppVersionService_CreateAppVersion_FullMethodName      = "/api.walletadmin.v1.AppVersionService/CreateAppVersion"
	AppVersionService_UpdateAppVersion_FullMethodName      = "/api.walletadmin.v1.AppVersionService/UpdateAppVersion"
	AppVersionService_DeleteAppVersion_FullMethodName      = "/api.walletadmin.v1.AppVersionService/DeleteAppVersion"
	AppVersionService_ListAppVersions_FullMethodName       = "/api.walletadmin.v1.AppVersionService/ListAppVersions"
	AppVersionService_GetAppVersion_FullMethodName         = "/api.walletadmin.v1.AppVersionService/GetAppVersion"
	AppVersionService_ListPublishedVersions_FullMethodName = "/api.walletadmin.v1.AppVersionService/ListPublishedVersions"
)

// AppVersionServiceClient is the client API for AppVersionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AppVersionServiceClient interface {
	// 创建版本
	CreateAppVersion(ctx context.Context, in *CreateAppVersionReq, opts ...grpc.CallOption) (*CreateAppVersionReply, error)
	// 更新版本
	UpdateAppVersion(ctx context.Context, in *UpdateAppVersionReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 删除版本
	DeleteAppVersion(ctx context.Context, in *DeleteAppVersionReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 版本列表
	ListAppVersions(ctx context.Context, in *ListAppVersionsReq, opts ...grpc.CallOption) (*ListAppVersionsReply, error)
	// 获取版本详情
	GetAppVersion(ctx context.Context, in *GetAppVersionReq, opts ...grpc.CallOption) (*AppVersionInfo, error)
	// 获取已发布版本列表
	ListPublishedVersions(ctx context.Context, in *ListPublishedVersionsReq, opts ...grpc.CallOption) (*ListPublishedVersionsReply, error)
}

type appVersionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAppVersionServiceClient(cc grpc.ClientConnInterface) AppVersionServiceClient {
	return &appVersionServiceClient{cc}
}

func (c *appVersionServiceClient) CreateAppVersion(ctx context.Context, in *CreateAppVersionReq, opts ...grpc.CallOption) (*CreateAppVersionReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateAppVersionReply)
	err := c.cc.Invoke(ctx, AppVersionService_CreateAppVersion_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appVersionServiceClient) UpdateAppVersion(ctx context.Context, in *UpdateAppVersionReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, AppVersionService_UpdateAppVersion_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appVersionServiceClient) DeleteAppVersion(ctx context.Context, in *DeleteAppVersionReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, AppVersionService_DeleteAppVersion_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appVersionServiceClient) ListAppVersions(ctx context.Context, in *ListAppVersionsReq, opts ...grpc.CallOption) (*ListAppVersionsReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAppVersionsReply)
	err := c.cc.Invoke(ctx, AppVersionService_ListAppVersions_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appVersionServiceClient) GetAppVersion(ctx context.Context, in *GetAppVersionReq, opts ...grpc.CallOption) (*AppVersionInfo, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AppVersionInfo)
	err := c.cc.Invoke(ctx, AppVersionService_GetAppVersion_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appVersionServiceClient) ListPublishedVersions(ctx context.Context, in *ListPublishedVersionsReq, opts ...grpc.CallOption) (*ListPublishedVersionsReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListPublishedVersionsReply)
	err := c.cc.Invoke(ctx, AppVersionService_ListPublishedVersions_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AppVersionServiceServer is the server API for AppVersionService service.
// All implementations must embed UnimplementedAppVersionServiceServer
// for forward compatibility.
type AppVersionServiceServer interface {
	// 创建版本
	CreateAppVersion(context.Context, *CreateAppVersionReq) (*CreateAppVersionReply, error)
	// 更新版本
	UpdateAppVersion(context.Context, *UpdateAppVersionReq) (*emptypb.Empty, error)
	// 删除版本
	DeleteAppVersion(context.Context, *DeleteAppVersionReq) (*emptypb.Empty, error)
	// 版本列表
	ListAppVersions(context.Context, *ListAppVersionsReq) (*ListAppVersionsReply, error)
	// 获取版本详情
	GetAppVersion(context.Context, *GetAppVersionReq) (*AppVersionInfo, error)
	// 获取已发布版本列表
	ListPublishedVersions(context.Context, *ListPublishedVersionsReq) (*ListPublishedVersionsReply, error)
	mustEmbedUnimplementedAppVersionServiceServer()
}

// UnimplementedAppVersionServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAppVersionServiceServer struct{}

func (UnimplementedAppVersionServiceServer) CreateAppVersion(context.Context, *CreateAppVersionReq) (*CreateAppVersionReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAppVersion not implemented")
}
func (UnimplementedAppVersionServiceServer) UpdateAppVersion(context.Context, *UpdateAppVersionReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAppVersion not implemented")
}
func (UnimplementedAppVersionServiceServer) DeleteAppVersion(context.Context, *DeleteAppVersionReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAppVersion not implemented")
}
func (UnimplementedAppVersionServiceServer) ListAppVersions(context.Context, *ListAppVersionsReq) (*ListAppVersionsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAppVersions not implemented")
}
func (UnimplementedAppVersionServiceServer) GetAppVersion(context.Context, *GetAppVersionReq) (*AppVersionInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppVersion not implemented")
}
func (UnimplementedAppVersionServiceServer) ListPublishedVersions(context.Context, *ListPublishedVersionsReq) (*ListPublishedVersionsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPublishedVersions not implemented")
}
func (UnimplementedAppVersionServiceServer) mustEmbedUnimplementedAppVersionServiceServer() {}
func (UnimplementedAppVersionServiceServer) testEmbeddedByValue()                           {}

// UnsafeAppVersionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AppVersionServiceServer will
// result in compilation errors.
type UnsafeAppVersionServiceServer interface {
	mustEmbedUnimplementedAppVersionServiceServer()
}

func RegisterAppVersionServiceServer(s grpc.ServiceRegistrar, srv AppVersionServiceServer) {
	// If the following call pancis, it indicates UnimplementedAppVersionServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AppVersionService_ServiceDesc, srv)
}

func _AppVersionService_CreateAppVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAppVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppVersionServiceServer).CreateAppVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppVersionService_CreateAppVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppVersionServiceServer).CreateAppVersion(ctx, req.(*CreateAppVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppVersionService_UpdateAppVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAppVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppVersionServiceServer).UpdateAppVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppVersionService_UpdateAppVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppVersionServiceServer).UpdateAppVersion(ctx, req.(*UpdateAppVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppVersionService_DeleteAppVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAppVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppVersionServiceServer).DeleteAppVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppVersionService_DeleteAppVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppVersionServiceServer).DeleteAppVersion(ctx, req.(*DeleteAppVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppVersionService_ListAppVersions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAppVersionsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppVersionServiceServer).ListAppVersions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppVersionService_ListAppVersions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppVersionServiceServer).ListAppVersions(ctx, req.(*ListAppVersionsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppVersionService_GetAppVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAppVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppVersionServiceServer).GetAppVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppVersionService_GetAppVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppVersionServiceServer).GetAppVersion(ctx, req.(*GetAppVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppVersionService_ListPublishedVersions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPublishedVersionsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppVersionServiceServer).ListPublishedVersions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppVersionService_ListPublishedVersions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppVersionServiceServer).ListPublishedVersions(ctx, req.(*ListPublishedVersionsReq))
	}
	return interceptor(ctx, in, info, handler)
}

// AppVersionService_ServiceDesc is the grpc.ServiceDesc for AppVersionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AppVersionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.walletadmin.v1.AppVersionService",
	HandlerType: (*AppVersionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateAppVersion",
			Handler:    _AppVersionService_CreateAppVersion_Handler,
		},
		{
			MethodName: "UpdateAppVersion",
			Handler:    _AppVersionService_UpdateAppVersion_Handler,
		},
		{
			MethodName: "DeleteAppVersion",
			Handler:    _AppVersionService_DeleteAppVersion_Handler,
		},
		{
			MethodName: "ListAppVersions",
			Handler:    _AppVersionService_ListAppVersions_Handler,
		},
		{
			MethodName: "GetAppVersion",
			Handler:    _AppVersionService_GetAppVersion_Handler,
		},
		{
			MethodName: "ListPublishedVersions",
			Handler:    _AppVersionService_ListPublishedVersions_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/walletadmin/v1/app_version.proto",
}
