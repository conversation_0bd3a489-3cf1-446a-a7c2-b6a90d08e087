// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: api/walletadmin/v1/admin.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AdminSrv_Login_FullMethodName = "/api.walletadmin.v1.AdminSrv/Login"
)

// AdminSrvClient is the client API for AdminSrv service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AdminSrvClient interface {
	Login(ctx context.Context, in *LoginReq, opts ...grpc.CallOption) (*LoginReply, error)
}

type adminSrvClient struct {
	cc grpc.ClientConnInterface
}

func NewAdminSrvClient(cc grpc.ClientConnInterface) AdminSrvClient {
	return &adminSrvClient{cc}
}

func (c *adminSrvClient) Login(ctx context.Context, in *LoginReq, opts ...grpc.CallOption) (*LoginReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LoginReply)
	err := c.cc.Invoke(ctx, AdminSrv_Login_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AdminSrvServer is the server API for AdminSrv service.
// All implementations must embed UnimplementedAdminSrvServer
// for forward compatibility.
type AdminSrvServer interface {
	Login(context.Context, *LoginReq) (*LoginReply, error)
	mustEmbedUnimplementedAdminSrvServer()
}

// UnimplementedAdminSrvServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAdminSrvServer struct{}

func (UnimplementedAdminSrvServer) Login(context.Context, *LoginReq) (*LoginReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Login not implemented")
}
func (UnimplementedAdminSrvServer) mustEmbedUnimplementedAdminSrvServer() {}
func (UnimplementedAdminSrvServer) testEmbeddedByValue()                  {}

// UnsafeAdminSrvServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AdminSrvServer will
// result in compilation errors.
type UnsafeAdminSrvServer interface {
	mustEmbedUnimplementedAdminSrvServer()
}

func RegisterAdminSrvServer(s grpc.ServiceRegistrar, srv AdminSrvServer) {
	// If the following call pancis, it indicates UnimplementedAdminSrvServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AdminSrv_ServiceDesc, srv)
}

func _AdminSrv_Login_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LoginReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminSrvServer).Login(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminSrv_Login_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminSrvServer).Login(ctx, req.(*LoginReq))
	}
	return interceptor(ctx, in, info, handler)
}

// AdminSrv_ServiceDesc is the grpc.ServiceDesc for AdminSrv service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AdminSrv_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.walletadmin.v1.AdminSrv",
	HandlerType: (*AdminSrvServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Login",
			Handler:    _AdminSrv_Login_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/walletadmin/v1/admin.proto",
}
