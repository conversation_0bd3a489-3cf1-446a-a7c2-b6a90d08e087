// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: api/walletadmin/v1/sort.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 排序类型
type SortType int32

const (
	// 向上排序
	SortType_UP SortType = 0
	// 向下排序
	SortType_DOWN SortType = 1
	// 置顶
	SortType_TOP SortType = 2
)

// Enum value maps for SortType.
var (
	SortType_name = map[int32]string{
		0: "UP",
		1: "DOWN",
		2: "TOP",
	}
	SortType_value = map[string]int32{
		"UP":   0,
		"DOWN": 1,
		"TOP":  2,
	}
)

func (x SortType) Enum() *SortType {
	p := new(SortType)
	*p = x
	return p
}

func (x SortType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SortType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_walletadmin_v1_sort_proto_enumTypes[0].Descriptor()
}

func (SortType) Type() protoreflect.EnumType {
	return &file_api_walletadmin_v1_sort_proto_enumTypes[0]
}

func (x SortType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SortType.Descriptor instead.
func (SortType) EnumDescriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_sort_proto_rawDescGZIP(), []int{0}
}

// 排序请求
type CommonSortReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 记录ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 排序类型
	SortType SortType `protobuf:"varint,2,opt,name=sort_type,json=sortType,proto3,enum=api.walletadmin.v1.SortType" json:"sort_type,omitempty"`
}

func (x *CommonSortReq) Reset() {
	*x = CommonSortReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_sort_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonSortReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonSortReq) ProtoMessage() {}

func (x *CommonSortReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_sort_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonSortReq.ProtoReflect.Descriptor instead.
func (*CommonSortReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_sort_proto_rawDescGZIP(), []int{0}
}

func (x *CommonSortReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CommonSortReq) GetSortType() SortType {
	if x != nil {
		return x.SortType
	}
	return SortType_UP
}

type CommonSortReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CommonSortReply) Reset() {
	*x = CommonSortReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_sort_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonSortReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonSortReply) ProtoMessage() {}

func (x *CommonSortReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_sort_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonSortReply.ProtoReflect.Descriptor instead.
func (*CommonSortReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_sort_proto_rawDescGZIP(), []int{1}
}

var File_api_walletadmin_v1_sort_proto protoreflect.FileDescriptor

var file_api_walletadmin_v1_sort_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x6f, 0x72, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x12, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2e, 0x76, 0x31, 0x22, 0x5a, 0x0a, 0x0d, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x6f, 0x72,
	0x74, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x39, 0x0a, 0x09, 0x73, 0x6f, 0x72, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x73, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22,
	0x11, 0x0a, 0x0f, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x2a, 0x25, 0x0a, 0x08, 0x53, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x06,
	0x0a, 0x02, 0x55, 0x50, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x44, 0x4f, 0x57, 0x4e, 0x10, 0x01,
	0x12, 0x07, 0x0a, 0x03, 0x54, 0x4f, 0x50, 0x10, 0x02, 0x42, 0xaf, 0x01, 0x0a, 0x16, 0x63, 0x6f,
	0x6d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2e, 0x76, 0x31, 0x42, 0x09, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50,
	0x01, 0x5a, 0x20, 0x62, 0x79, 0x64, 0x5f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x76, 0x31,
	0x3b, 0x76, 0x31, 0xa2, 0x02, 0x03, 0x41, 0x57, 0x58, 0xaa, 0x02, 0x12, 0x41, 0x70, 0x69, 0x2e,
	0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x56, 0x31, 0xca, 0x02,
	0x12, 0x41, 0x70, 0x69, 0x5c, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x5c, 0x56, 0x31, 0xe2, 0x02, 0x1e, 0x41, 0x70, 0x69, 0x5c, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5c, 0x56, 0x31, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0xea, 0x02, 0x14, 0x41, 0x70, 0x69, 0x3a, 0x3a, 0x57, 0x61, 0x6c, 0x6c,
	0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x3a, 0x3a, 0x56, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_walletadmin_v1_sort_proto_rawDescOnce sync.Once
	file_api_walletadmin_v1_sort_proto_rawDescData = file_api_walletadmin_v1_sort_proto_rawDesc
)

func file_api_walletadmin_v1_sort_proto_rawDescGZIP() []byte {
	file_api_walletadmin_v1_sort_proto_rawDescOnce.Do(func() {
		file_api_walletadmin_v1_sort_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_walletadmin_v1_sort_proto_rawDescData)
	})
	return file_api_walletadmin_v1_sort_proto_rawDescData
}

var file_api_walletadmin_v1_sort_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_walletadmin_v1_sort_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_walletadmin_v1_sort_proto_goTypes = []any{
	(SortType)(0),           // 0: api.walletadmin.v1.SortType
	(*CommonSortReq)(nil),   // 1: api.walletadmin.v1.CommonSortReq
	(*CommonSortReply)(nil), // 2: api.walletadmin.v1.CommonSortReply
}
var file_api_walletadmin_v1_sort_proto_depIdxs = []int32{
	0, // 0: api.walletadmin.v1.CommonSortReq.sort_type:type_name -> api.walletadmin.v1.SortType
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_walletadmin_v1_sort_proto_init() }
func file_api_walletadmin_v1_sort_proto_init() {
	if File_api_walletadmin_v1_sort_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_walletadmin_v1_sort_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*CommonSortReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_sort_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*CommonSortReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_walletadmin_v1_sort_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_walletadmin_v1_sort_proto_goTypes,
		DependencyIndexes: file_api_walletadmin_v1_sort_proto_depIdxs,
		EnumInfos:         file_api_walletadmin_v1_sort_proto_enumTypes,
		MessageInfos:      file_api_walletadmin_v1_sort_proto_msgTypes,
	}.Build()
	File_api_walletadmin_v1_sort_proto = out.File
	file_api_walletadmin_v1_sort_proto_rawDesc = nil
	file_api_walletadmin_v1_sort_proto_goTypes = nil
	file_api_walletadmin_v1_sort_proto_depIdxs = nil
}
