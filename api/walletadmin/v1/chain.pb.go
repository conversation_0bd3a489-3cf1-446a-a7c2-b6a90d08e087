// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: api/walletadmin/v1/chain.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UpdateChainSortReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 当前排序(来自列表的sort_order字段值)
	CurrentSort int64 `protobuf:"varint,1,opt,name=current_sort,json=currentSort,proto3" json:"current_sort,omitempty"`
	// 更新后排序(数值越大,排序越靠前)
	TargetSort int64 `protobuf:"varint,2,opt,name=target_sort,json=targetSort,proto3" json:"target_sort,omitempty"`
	// 记录ID
	Id uint64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *UpdateChainSortReq) Reset() {
	*x = UpdateChainSortReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_chain_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateChainSortReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateChainSortReq) ProtoMessage() {}

func (x *UpdateChainSortReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_chain_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateChainSortReq.ProtoReflect.Descriptor instead.
func (*UpdateChainSortReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_chain_proto_rawDescGZIP(), []int{0}
}

func (x *UpdateChainSortReq) GetCurrentSort() int64 {
	if x != nil {
		return x.CurrentSort
	}
	return 0
}

func (x *UpdateChainSortReq) GetTargetSort() int64 {
	if x != nil {
		return x.TargetSort
	}
	return 0
}

func (x *UpdateChainSortReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type UpdateChainReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 网络图标
	BlockchainUrl string `protobuf:"bytes,1,opt,name=blockchain_url,json=blockchainUrl,proto3" json:"blockchain_url,omitempty"`
	// 币图标
	TokenUrl string `protobuf:"bytes,2,opt,name=token_url,json=tokenUrl,proto3" json:"token_url,omitempty"`
	// 是否展示
	IsDisplay bool `protobuf:"varint,3,opt,name=is_display,json=isDisplay,proto3" json:"is_display,omitempty"`
	// 记录ID
	Id uint64 `protobuf:"varint,4,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *UpdateChainReq) Reset() {
	*x = UpdateChainReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_chain_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateChainReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateChainReq) ProtoMessage() {}

func (x *UpdateChainReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_chain_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateChainReq.ProtoReflect.Descriptor instead.
func (*UpdateChainReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_chain_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateChainReq) GetBlockchainUrl() string {
	if x != nil {
		return x.BlockchainUrl
	}
	return ""
}

func (x *UpdateChainReq) GetTokenUrl() string {
	if x != nil {
		return x.TokenUrl
	}
	return ""
}

func (x *UpdateChainReq) GetIsDisplay() bool {
	if x != nil {
		return x.IsDisplay
	}
	return false
}

func (x *UpdateChainReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type ListChainReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 公链名称
	ChainName *string `protobuf:"bytes,3,opt,name=chain_name,json=chainName,proto3,oneof" json:"chain_name,omitempty"`
}

func (x *ListChainReq) Reset() {
	*x = ListChainReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_chain_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListChainReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListChainReq) ProtoMessage() {}

func (x *ListChainReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_chain_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListChainReq.ProtoReflect.Descriptor instead.
func (*ListChainReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_chain_proto_rawDescGZIP(), []int{2}
}

func (x *ListChainReq) GetChainName() string {
	if x != nil && x.ChainName != nil {
		return *x.ChainName
	}
	return ""
}

type Chain struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 记录ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 链名称
	ChainName string `protobuf:"bytes,2,opt,name=chain_name,json=chainName,proto3" json:"chain_name,omitempty"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,3,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 币符号
	Symbol string `protobuf:"bytes,4,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// 精度位数
	Decimals int64 `protobuf:"varint,5,opt,name=decimals,proto3" json:"decimals,omitempty"`
	// 链类型
	ChainType string `protobuf:"bytes,6,opt,name=chain_type,json=chainType,proto3" json:"chain_type,omitempty"`
	// 当前同步块高
	CurrencyBlock int64 `protobuf:"varint,7,opt,name=currency_block,json=currencyBlock,proto3" json:"currency_block,omitempty"`
	// 是否正在同步
	IsSyncing bool `protobuf:"varint,8,opt,name=is_syncing,json=isSyncing,proto3" json:"is_syncing,omitempty"`
	// 是否展示
	IsDisplay bool `protobuf:"varint,9,opt,name=is_display,json=isDisplay,proto3" json:"is_display,omitempty"`
	// 网络图标
	BlockchainUrl string `protobuf:"bytes,10,opt,name=blockchain_url,json=blockchainUrl,proto3" json:"blockchain_url,omitempty"`
	// 币图标
	TokenUrl string `protobuf:"bytes,11,opt,name=token_url,json=tokenUrl,proto3" json:"token_url,omitempty"`
	// 网络ID
	ChainId string `protobuf:"bytes,12,opt,name=chain_id,json=chainId,proto3" json:"chain_id,omitempty"`
	// 排序
	SortOrder int64 `protobuf:"varint,13,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order,omitempty"`
}

func (x *Chain) Reset() {
	*x = Chain{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_chain_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Chain) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Chain) ProtoMessage() {}

func (x *Chain) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_chain_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Chain.ProtoReflect.Descriptor instead.
func (*Chain) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_chain_proto_rawDescGZIP(), []int{3}
}

func (x *Chain) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Chain) GetChainName() string {
	if x != nil {
		return x.ChainName
	}
	return ""
}

func (x *Chain) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *Chain) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *Chain) GetDecimals() int64 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *Chain) GetChainType() string {
	if x != nil {
		return x.ChainType
	}
	return ""
}

func (x *Chain) GetCurrencyBlock() int64 {
	if x != nil {
		return x.CurrencyBlock
	}
	return 0
}

func (x *Chain) GetIsSyncing() bool {
	if x != nil {
		return x.IsSyncing
	}
	return false
}

func (x *Chain) GetIsDisplay() bool {
	if x != nil {
		return x.IsDisplay
	}
	return false
}

func (x *Chain) GetBlockchainUrl() string {
	if x != nil {
		return x.BlockchainUrl
	}
	return ""
}

func (x *Chain) GetTokenUrl() string {
	if x != nil {
		return x.TokenUrl
	}
	return ""
}

func (x *Chain) GetChainId() string {
	if x != nil {
		return x.ChainId
	}
	return ""
}

func (x *Chain) GetSortOrder() int64 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

type ListChainReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*Chain `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListChainReply) Reset() {
	*x = ListChainReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_chain_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListChainReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListChainReply) ProtoMessage() {}

func (x *ListChainReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_chain_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListChainReply.ProtoReflect.Descriptor instead.
func (*ListChainReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_chain_proto_rawDescGZIP(), []int{4}
}

func (x *ListChainReply) GetList() []*Chain {
	if x != nil {
		return x.List
	}
	return nil
}

var File_api_walletadmin_v1_chain_proto protoreflect.FileDescriptor

var file_api_walletadmin_v1_chain_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x12, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1b, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x71, 0x0a, 0x12,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x53, 0x6f, 0x72, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x6f,
	0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x53, 0x6f, 0x72, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x73, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x53, 0x6f, 0x72, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x04, 0x42, 0x07, 0xba, 0x48, 0x04, 0x32, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22,
	0xa0, 0x01, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52,
	0x65, 0x71, 0x12, 0x2f, 0x0a, 0x0e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05, 0x72,
	0x03, 0x88, 0x01, 0x01, 0x52, 0x0d, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x55, 0x72, 0x6c, 0x12, 0x25, 0x0a, 0x09, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05, 0x72, 0x03, 0x88, 0x01, 0x01,
	0x52, 0x08, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73,
	0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09,
	0x69, 0x73, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xba, 0x48, 0x04, 0x32, 0x02, 0x20, 0x00, 0x52, 0x02,
	0x69, 0x64, 0x22, 0x41, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52,
	0x65, 0x71, 0x12, 0x22, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x4e,
	0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x8d, 0x03, 0x0a, 0x05, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x63, 0x69, 0x6d,
	0x61, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x65, 0x63, 0x69, 0x6d,
	0x61, 0x6c, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x63, 0x79, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f,
	0x73, 0x79, 0x6e, 0x63, 0x69, 0x6e, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69,
	0x73, 0x53, 0x79, 0x6e, 0x63, 0x69, 0x6e, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73,
	0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x12, 0x25, 0x0a, 0x0e, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x1b,
	0x0a, 0x09, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x6f, 0x72, 0x74, 0x5f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x6f, 0x72, 0x74,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x22, 0x3f, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x68, 0x61,
	0x69, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2d, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c,
	0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x69, 0x6e,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x32, 0xf6, 0x02, 0x0a, 0x08, 0x43, 0x68, 0x61, 0x69, 0x6e,
	0x53, 0x72, 0x76, 0x12, 0x75, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x68, 0x61, 0x69, 0x6e,
	0x12, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52,
	0x65, 0x71, 0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x68, 0x61, 0x69,
	0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x22, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x12, 0x1a,
	0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2f,
	0x6c, 0x69, 0x73, 0x74, 0x5f, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x72, 0x0a, 0x0b, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x3a, 0x01, 0x2a,
	0x1a, 0x1c, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x7f,
	0x0a, 0x0f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x53, 0x6f, 0x72,
	0x74, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61,
	0x69, 0x6e, 0x53, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26, 0x3a, 0x01, 0x2a, 0x32, 0x21, 0x2f, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2f, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x73, 0x6f, 0x72, 0x74, 0x42,
	0xb0, 0x01, 0x0a, 0x16, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c,
	0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x42, 0x0a, 0x43, 0x68, 0x61, 0x69,
	0x6e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x20, 0x62, 0x79, 0x64, 0x5f, 0x77, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0xa2, 0x02, 0x03, 0x41, 0x57, 0x58,
	0xaa, 0x02, 0x12, 0x41, 0x70, 0x69, 0x2e, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x2e, 0x56, 0x31, 0xca, 0x02, 0x12, 0x41, 0x70, 0x69, 0x5c, 0x57, 0x61, 0x6c, 0x6c,
	0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5c, 0x56, 0x31, 0xe2, 0x02, 0x1e, 0x41, 0x70, 0x69,
	0x5c, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5c, 0x56, 0x31, 0x5c,
	0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0xea, 0x02, 0x14, 0x41, 0x70,
	0x69, 0x3a, 0x3a, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x3a, 0x3a,
	0x56, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_walletadmin_v1_chain_proto_rawDescOnce sync.Once
	file_api_walletadmin_v1_chain_proto_rawDescData = file_api_walletadmin_v1_chain_proto_rawDesc
)

func file_api_walletadmin_v1_chain_proto_rawDescGZIP() []byte {
	file_api_walletadmin_v1_chain_proto_rawDescOnce.Do(func() {
		file_api_walletadmin_v1_chain_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_walletadmin_v1_chain_proto_rawDescData)
	})
	return file_api_walletadmin_v1_chain_proto_rawDescData
}

var file_api_walletadmin_v1_chain_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_walletadmin_v1_chain_proto_goTypes = []any{
	(*UpdateChainSortReq)(nil), // 0: api.walletadmin.v1.UpdateChainSortReq
	(*UpdateChainReq)(nil),     // 1: api.walletadmin.v1.UpdateChainReq
	(*ListChainReq)(nil),       // 2: api.walletadmin.v1.ListChainReq
	(*Chain)(nil),              // 3: api.walletadmin.v1.Chain
	(*ListChainReply)(nil),     // 4: api.walletadmin.v1.ListChainReply
	(*emptypb.Empty)(nil),      // 5: google.protobuf.Empty
}
var file_api_walletadmin_v1_chain_proto_depIdxs = []int32{
	3, // 0: api.walletadmin.v1.ListChainReply.list:type_name -> api.walletadmin.v1.Chain
	2, // 1: api.walletadmin.v1.ChainSrv.ListChain:input_type -> api.walletadmin.v1.ListChainReq
	1, // 2: api.walletadmin.v1.ChainSrv.UpdateChain:input_type -> api.walletadmin.v1.UpdateChainReq
	0, // 3: api.walletadmin.v1.ChainSrv.UpdateChainSort:input_type -> api.walletadmin.v1.UpdateChainSortReq
	4, // 4: api.walletadmin.v1.ChainSrv.ListChain:output_type -> api.walletadmin.v1.ListChainReply
	5, // 5: api.walletadmin.v1.ChainSrv.UpdateChain:output_type -> google.protobuf.Empty
	5, // 6: api.walletadmin.v1.ChainSrv.UpdateChainSort:output_type -> google.protobuf.Empty
	4, // [4:7] is the sub-list for method output_type
	1, // [1:4] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_walletadmin_v1_chain_proto_init() }
func file_api_walletadmin_v1_chain_proto_init() {
	if File_api_walletadmin_v1_chain_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_walletadmin_v1_chain_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateChainSortReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_chain_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateChainReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_chain_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*ListChainReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_chain_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*Chain); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_chain_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*ListChainReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_walletadmin_v1_chain_proto_msgTypes[2].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_walletadmin_v1_chain_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_walletadmin_v1_chain_proto_goTypes,
		DependencyIndexes: file_api_walletadmin_v1_chain_proto_depIdxs,
		MessageInfos:      file_api_walletadmin_v1_chain_proto_msgTypes,
	}.Build()
	File_api_walletadmin_v1_chain_proto = out.File
	file_api_walletadmin_v1_chain_proto_rawDesc = nil
	file_api_walletadmin_v1_chain_proto_goTypes = nil
	file_api_walletadmin_v1_chain_proto_depIdxs = nil
}
