// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.0
// - protoc             (unknown)
// source: api/walletadmin/v1/dapp.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationAdminDappSrvBatchUpdateDappIndex = "/api.walletadmin.v1.AdminDappSrv/BatchUpdateDappIndex"
const OperationAdminDappSrvBatchUpdateDappNavigation = "/api.walletadmin.v1.AdminDappSrv/BatchUpdateDappNavigation"
const OperationAdminDappSrvCreateDapp = "/api.walletadmin.v1.AdminDappSrv/CreateDapp"
const OperationAdminDappSrvCreateDappCategory = "/api.walletadmin.v1.AdminDappSrv/CreateDappCategory"
const OperationAdminDappSrvCreateDappCategoryRel = "/api.walletadmin.v1.AdminDappSrv/CreateDappCategoryRel"
const OperationAdminDappSrvCreateDappIndex = "/api.walletadmin.v1.AdminDappSrv/CreateDappIndex"
const OperationAdminDappSrvCreateDappNavigation = "/api.walletadmin.v1.AdminDappSrv/CreateDappNavigation"
const OperationAdminDappSrvCreateDappTopic = "/api.walletadmin.v1.AdminDappSrv/CreateDappTopic"
const OperationAdminDappSrvCreateDappTopicRel = "/api.walletadmin.v1.AdminDappSrv/CreateDappTopicRel"
const OperationAdminDappSrvDeleteDapp = "/api.walletadmin.v1.AdminDappSrv/DeleteDapp"
const OperationAdminDappSrvDeleteDappCategory = "/api.walletadmin.v1.AdminDappSrv/DeleteDappCategory"
const OperationAdminDappSrvDeleteDappCategoryRel = "/api.walletadmin.v1.AdminDappSrv/DeleteDappCategoryRel"
const OperationAdminDappSrvDeleteDappIndex = "/api.walletadmin.v1.AdminDappSrv/DeleteDappIndex"
const OperationAdminDappSrvDeleteDappNavigation = "/api.walletadmin.v1.AdminDappSrv/DeleteDappNavigation"
const OperationAdminDappSrvDeleteDappTopic = "/api.walletadmin.v1.AdminDappSrv/DeleteDappTopic"
const OperationAdminDappSrvDeleteDappTopicRel = "/api.walletadmin.v1.AdminDappSrv/DeleteDappTopicRel"
const OperationAdminDappSrvListDapp = "/api.walletadmin.v1.AdminDappSrv/ListDapp"
const OperationAdminDappSrvListDappCategory = "/api.walletadmin.v1.AdminDappSrv/ListDappCategory"
const OperationAdminDappSrvListDappCategoryRel = "/api.walletadmin.v1.AdminDappSrv/ListDappCategoryRel"
const OperationAdminDappSrvListDappIndex = "/api.walletadmin.v1.AdminDappSrv/ListDappIndex"
const OperationAdminDappSrvListDappNavigation = "/api.walletadmin.v1.AdminDappSrv/ListDappNavigation"
const OperationAdminDappSrvListDappTopic = "/api.walletadmin.v1.AdminDappSrv/ListDappTopic"
const OperationAdminDappSrvListDappTopicRel = "/api.walletadmin.v1.AdminDappSrv/ListDappTopicRel"
const OperationAdminDappSrvSortDappIndex = "/api.walletadmin.v1.AdminDappSrv/SortDappIndex"
const OperationAdminDappSrvSortDappNavigation = "/api.walletadmin.v1.AdminDappSrv/SortDappNavigation"
const OperationAdminDappSrvUpdateDapp = "/api.walletadmin.v1.AdminDappSrv/UpdateDapp"
const OperationAdminDappSrvUpdateDappCategory = "/api.walletadmin.v1.AdminDappSrv/UpdateDappCategory"
const OperationAdminDappSrvUpdateDappTopic = "/api.walletadmin.v1.AdminDappSrv/UpdateDappTopic"

type AdminDappSrvHTTPServer interface {
	// BatchUpdateDappIndex BatchUpdateDappIndex 批量更新dapp首页（可用于更新排序）
	BatchUpdateDappIndex(context.Context, *BatchUpdateDappIndexReq) (*BatchUpdateDappIndexReply, error)
	// BatchUpdateDappNavigation BatchUpdateDappNavigation 批量更新首页导航栏(可用于更新排序)
	BatchUpdateDappNavigation(context.Context, *BatchUpdateDappNavigationReq) (*BatchUpdateDappNavigationReply, error)
	// CreateDapp CreateDapp 添加DAPP
	CreateDapp(context.Context, *CreateDappReq) (*CreateDappReply, error)
	// CreateDappCategory CreateDappCategory 添加DAPP分类
	CreateDappCategory(context.Context, *CreateDappCategoryReq) (*CreateDappCategoryReply, error)
	// CreateDappCategoryRel CreateDappCategoryRel 添加分类dapp
	CreateDappCategoryRel(context.Context, *CreateDappCategoryRelReq) (*CreateDappCategoryRelReply, error)
	// CreateDappIndex CreateDappIndex 添加DAPP首页
	CreateDappIndex(context.Context, *CreateDappIndexReq) (*CreateDappIndexReply, error)
	// CreateDappNavigation CreateDappNavigation 添加DAPP首页导航栏
	CreateDappNavigation(context.Context, *CreateDappNavigationReq) (*CreateDappNavigationReply, error)
	// CreateDappTopic CreateDappTopic 添加DAPP专题
	CreateDappTopic(context.Context, *CreateDappTopicReq) (*CreateDappTopicReply, error)
	// CreateDappTopicRel CreateDappTopicRel 添加专题dapp
	CreateDappTopicRel(context.Context, *CreateDappTopicRelReq) (*CreateDappTopicRelReply, error)
	// DeleteDapp DeleteDapp 删除DAPP
	DeleteDapp(context.Context, *DeleteDappReq) (*DeleteDappReply, error)
	// DeleteDappCategory DeleteDappCategoryRel 删除分类
	DeleteDappCategory(context.Context, *DeleteDappCategoryReq) (*DeleteDappCategoryReply, error)
	// DeleteDappCategoryRel DeleteDappCategoryRel 删除分类dapp
	DeleteDappCategoryRel(context.Context, *DeleteDappCategoryRelReq) (*DeleteDappCategoryRelReply, error)
	// DeleteDappIndex DeleteDappIndex 删除dapp首页
	DeleteDappIndex(context.Context, *DeleteDappIndexReq) (*DeleteDappIndexReply, error)
	// DeleteDappNavigation DeleteDappNavigation 删除首页导航栏
	DeleteDappNavigation(context.Context, *DeleteDappNavigationReq) (*DeleteDappNavigationReply, error)
	// DeleteDappTopic DeleteDappTopic 删除dapp专题
	DeleteDappTopic(context.Context, *DeleteDappTopicReq) (*DeleteDappTopicReply, error)
	// DeleteDappTopicRel DeleteDappTopicRel 删除专题dapp
	DeleteDappTopicRel(context.Context, *DeleteDappTopicRelReq) (*DeleteDappTopicRelReply, error)
	// ListDapp ListDapp 查询dapps
	ListDapp(context.Context, *ListDappReq) (*ListDappReply, error)
	// ListDappCategory ListDappCategory 查询dapp分类
	ListDappCategory(context.Context, *ListDappCategoryReq) (*ListDappCategoryReply, error)
	// ListDappCategoryRel ListDappCategoryRel 查询分类dapp
	ListDappCategoryRel(context.Context, *ListDappCategoryRelReq) (*ListDappCategoryRelReply, error)
	// ListDappIndex ListDappIndex 获取dapp首页列表
	ListDappIndex(context.Context, *ListDappIndexReq) (*ListDappIndexReply, error)
	// ListDappNavigation ListDappNavigation 首页导航栏列表
	ListDappNavigation(context.Context, *ListDappNavigationReq) (*ListDappNavigationReply, error)
	// ListDappTopic ListDappTopic 查询dapp专题
	ListDappTopic(context.Context, *ListDappTopicReq) (*ListDappTopicReply, error)
	// ListDappTopicRel ListDappTopicRel 查询专题dapp
	ListDappTopicRel(context.Context, *ListDappTopicRelReq) (*ListDappTopicRelReply, error)
	// SortDappIndex SortDappIndex dapp首页排序
	SortDappIndex(context.Context, *SortReq) (*SortReply, error)
	// SortDappNavigation SortDappNavigation 首页导航栏排序
	SortDappNavigation(context.Context, *SortReq) (*SortReply, error)
	// UpdateDapp UpdateDapp 更新DAPP
	UpdateDapp(context.Context, *UpdateDappReq) (*UpdateDappReply, error)
	// UpdateDappCategory UpdateDappCategory 更新DAPP分类
	UpdateDappCategory(context.Context, *UpdateDappCategoryReq) (*UpdateDappCategoryReply, error)
	// UpdateDappTopic UpdateDappTopic 更新DAPP专题
	UpdateDappTopic(context.Context, *UpdateDappTopicReq) (*UpdateDappTopicReply, error)
}

func RegisterAdminDappSrvHTTPServer(s *http.Server, srv AdminDappSrvHTTPServer) {
	r := s.Route("/")
	r.POST("/admin/v1/dapp", _AdminDappSrv_CreateDapp0_HTTP_Handler(srv))
	r.DELETE("/admin/v1/dapp/{id}", _AdminDappSrv_DeleteDapp0_HTTP_Handler(srv))
	r.PUT("/admin/v1/dapp/{id}", _AdminDappSrv_UpdateDapp0_HTTP_Handler(srv))
	r.GET("/admin/v1/dapp", _AdminDappSrv_ListDapp0_HTTP_Handler(srv))
	r.POST("/admin/v1/dapp/category", _AdminDappSrv_CreateDappCategory0_HTTP_Handler(srv))
	r.PUT("/admin/v1/dapp/category/{id}", _AdminDappSrv_UpdateDappCategory0_HTTP_Handler(srv))
	r.GET("/admin/v1/dapp/category", _AdminDappSrv_ListDappCategory0_HTTP_Handler(srv))
	r.DELETE("/admin/v1/dapp/category/{id}", _AdminDappSrv_DeleteDappCategory0_HTTP_Handler(srv))
	r.POST("/admin/v1/dapp/category/{id}/rel", _AdminDappSrv_CreateDappCategoryRel0_HTTP_Handler(srv))
	r.DELETE("/admin/v1/dapp/category/{id}/rel", _AdminDappSrv_DeleteDappCategoryRel0_HTTP_Handler(srv))
	r.GET("/admin/v1/dapp/category/{id}/rel", _AdminDappSrv_ListDappCategoryRel0_HTTP_Handler(srv))
	r.POST("/admin/v1/dapp/topic", _AdminDappSrv_CreateDappTopic0_HTTP_Handler(srv))
	r.DELETE("/admin/v1/dapp/topic/{id}", _AdminDappSrv_DeleteDappTopic0_HTTP_Handler(srv))
	r.PUT("/admin/v1/dapp/topic/{id}", _AdminDappSrv_UpdateDappTopic0_HTTP_Handler(srv))
	r.GET("/admin/v1/dapp/topic", _AdminDappSrv_ListDappTopic0_HTTP_Handler(srv))
	r.POST("/admin/v1/dapp/category/navigation", _AdminDappSrv_CreateDappNavigation0_HTTP_Handler(srv))
	r.DELETE("/admin/v1/dapp/category/navigation/{id}", _AdminDappSrv_DeleteDappNavigation0_HTTP_Handler(srv))
	r.GET("/admin/v1/dapp/category/navigation", _AdminDappSrv_ListDappNavigation0_HTTP_Handler(srv))
	r.PUT("/admin/v1/dapp/category/navigation", _AdminDappSrv_BatchUpdateDappNavigation0_HTTP_Handler(srv))
	r.PUT("/admin/v1/dapp/category/navigation/sort", _AdminDappSrv_SortDappNavigation0_HTTP_Handler(srv))
	r.POST("/admin/v1/dapp/index", _AdminDappSrv_CreateDappIndex0_HTTP_Handler(srv))
	r.GET("/admin/v1/dapp/index", _AdminDappSrv_ListDappIndex0_HTTP_Handler(srv))
	r.DELETE("/admin/v1/dapp/index/{id}", _AdminDappSrv_DeleteDappIndex0_HTTP_Handler(srv))
	r.PUT("/admin/v1/dapp/index", _AdminDappSrv_BatchUpdateDappIndex0_HTTP_Handler(srv))
	r.PUT("/admin/v1/dapp/index/sort", _AdminDappSrv_SortDappIndex0_HTTP_Handler(srv))
	r.POST("/admin/v1/dapp/topic/{id}/rel", _AdminDappSrv_CreateDappTopicRel0_HTTP_Handler(srv))
	r.DELETE("/admin/v1/dapp/topic/{id}/rel", _AdminDappSrv_DeleteDappTopicRel0_HTTP_Handler(srv))
	r.GET("/admin/v1/dapp/topic/{id}/rel", _AdminDappSrv_ListDappTopicRel0_HTTP_Handler(srv))
}

func _AdminDappSrv_CreateDapp0_HTTP_Handler(srv AdminDappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateDappReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDappSrvCreateDapp)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateDapp(ctx, req.(*CreateDappReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateDappReply)
		return ctx.Result(200, reply)
	}
}

func _AdminDappSrv_DeleteDapp0_HTTP_Handler(srv AdminDappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteDappReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDappSrvDeleteDapp)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteDapp(ctx, req.(*DeleteDappReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteDappReply)
		return ctx.Result(200, reply)
	}
}

func _AdminDappSrv_UpdateDapp0_HTTP_Handler(srv AdminDappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateDappReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDappSrvUpdateDapp)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateDapp(ctx, req.(*UpdateDappReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateDappReply)
		return ctx.Result(200, reply)
	}
}

func _AdminDappSrv_ListDapp0_HTTP_Handler(srv AdminDappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListDappReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDappSrvListDapp)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListDapp(ctx, req.(*ListDappReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListDappReply)
		return ctx.Result(200, reply)
	}
}

func _AdminDappSrv_CreateDappCategory0_HTTP_Handler(srv AdminDappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateDappCategoryReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDappSrvCreateDappCategory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateDappCategory(ctx, req.(*CreateDappCategoryReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateDappCategoryReply)
		return ctx.Result(200, reply)
	}
}

func _AdminDappSrv_UpdateDappCategory0_HTTP_Handler(srv AdminDappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateDappCategoryReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDappSrvUpdateDappCategory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateDappCategory(ctx, req.(*UpdateDappCategoryReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateDappCategoryReply)
		return ctx.Result(200, reply)
	}
}

func _AdminDappSrv_ListDappCategory0_HTTP_Handler(srv AdminDappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListDappCategoryReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDappSrvListDappCategory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListDappCategory(ctx, req.(*ListDappCategoryReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListDappCategoryReply)
		return ctx.Result(200, reply)
	}
}

func _AdminDappSrv_DeleteDappCategory0_HTTP_Handler(srv AdminDappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteDappCategoryReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDappSrvDeleteDappCategory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteDappCategory(ctx, req.(*DeleteDappCategoryReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteDappCategoryReply)
		return ctx.Result(200, reply)
	}
}

func _AdminDappSrv_CreateDappCategoryRel0_HTTP_Handler(srv AdminDappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateDappCategoryRelReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDappSrvCreateDappCategoryRel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateDappCategoryRel(ctx, req.(*CreateDappCategoryRelReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateDappCategoryRelReply)
		return ctx.Result(200, reply)
	}
}

func _AdminDappSrv_DeleteDappCategoryRel0_HTTP_Handler(srv AdminDappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteDappCategoryRelReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDappSrvDeleteDappCategoryRel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteDappCategoryRel(ctx, req.(*DeleteDappCategoryRelReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteDappCategoryRelReply)
		return ctx.Result(200, reply)
	}
}

func _AdminDappSrv_ListDappCategoryRel0_HTTP_Handler(srv AdminDappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListDappCategoryRelReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDappSrvListDappCategoryRel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListDappCategoryRel(ctx, req.(*ListDappCategoryRelReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListDappCategoryRelReply)
		return ctx.Result(200, reply)
	}
}

func _AdminDappSrv_CreateDappTopic0_HTTP_Handler(srv AdminDappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateDappTopicReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDappSrvCreateDappTopic)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateDappTopic(ctx, req.(*CreateDappTopicReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateDappTopicReply)
		return ctx.Result(200, reply)
	}
}

func _AdminDappSrv_DeleteDappTopic0_HTTP_Handler(srv AdminDappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteDappTopicReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDappSrvDeleteDappTopic)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteDappTopic(ctx, req.(*DeleteDappTopicReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteDappTopicReply)
		return ctx.Result(200, reply)
	}
}

func _AdminDappSrv_UpdateDappTopic0_HTTP_Handler(srv AdminDappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateDappTopicReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDappSrvUpdateDappTopic)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateDappTopic(ctx, req.(*UpdateDappTopicReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateDappTopicReply)
		return ctx.Result(200, reply)
	}
}

func _AdminDappSrv_ListDappTopic0_HTTP_Handler(srv AdminDappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListDappTopicReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDappSrvListDappTopic)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListDappTopic(ctx, req.(*ListDappTopicReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListDappTopicReply)
		return ctx.Result(200, reply)
	}
}

func _AdminDappSrv_CreateDappNavigation0_HTTP_Handler(srv AdminDappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateDappNavigationReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDappSrvCreateDappNavigation)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateDappNavigation(ctx, req.(*CreateDappNavigationReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateDappNavigationReply)
		return ctx.Result(200, reply)
	}
}

func _AdminDappSrv_DeleteDappNavigation0_HTTP_Handler(srv AdminDappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteDappNavigationReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDappSrvDeleteDappNavigation)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteDappNavigation(ctx, req.(*DeleteDappNavigationReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteDappNavigationReply)
		return ctx.Result(200, reply)
	}
}

func _AdminDappSrv_ListDappNavigation0_HTTP_Handler(srv AdminDappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListDappNavigationReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDappSrvListDappNavigation)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListDappNavigation(ctx, req.(*ListDappNavigationReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListDappNavigationReply)
		return ctx.Result(200, reply)
	}
}

func _AdminDappSrv_BatchUpdateDappNavigation0_HTTP_Handler(srv AdminDappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in BatchUpdateDappNavigationReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDappSrvBatchUpdateDappNavigation)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BatchUpdateDappNavigation(ctx, req.(*BatchUpdateDappNavigationReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*BatchUpdateDappNavigationReply)
		return ctx.Result(200, reply)
	}
}

func _AdminDappSrv_SortDappNavigation0_HTTP_Handler(srv AdminDappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SortReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDappSrvSortDappNavigation)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SortDappNavigation(ctx, req.(*SortReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SortReply)
		return ctx.Result(200, reply)
	}
}

func _AdminDappSrv_CreateDappIndex0_HTTP_Handler(srv AdminDappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateDappIndexReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDappSrvCreateDappIndex)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateDappIndex(ctx, req.(*CreateDappIndexReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateDappIndexReply)
		return ctx.Result(200, reply)
	}
}

func _AdminDappSrv_ListDappIndex0_HTTP_Handler(srv AdminDappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListDappIndexReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDappSrvListDappIndex)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListDappIndex(ctx, req.(*ListDappIndexReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListDappIndexReply)
		return ctx.Result(200, reply)
	}
}

func _AdminDappSrv_DeleteDappIndex0_HTTP_Handler(srv AdminDappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteDappIndexReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDappSrvDeleteDappIndex)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteDappIndex(ctx, req.(*DeleteDappIndexReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteDappIndexReply)
		return ctx.Result(200, reply)
	}
}

func _AdminDappSrv_BatchUpdateDappIndex0_HTTP_Handler(srv AdminDappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in BatchUpdateDappIndexReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDappSrvBatchUpdateDappIndex)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BatchUpdateDappIndex(ctx, req.(*BatchUpdateDappIndexReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*BatchUpdateDappIndexReply)
		return ctx.Result(200, reply)
	}
}

func _AdminDappSrv_SortDappIndex0_HTTP_Handler(srv AdminDappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SortReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDappSrvSortDappIndex)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SortDappIndex(ctx, req.(*SortReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SortReply)
		return ctx.Result(200, reply)
	}
}

func _AdminDappSrv_CreateDappTopicRel0_HTTP_Handler(srv AdminDappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateDappTopicRelReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDappSrvCreateDappTopicRel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateDappTopicRel(ctx, req.(*CreateDappTopicRelReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateDappTopicRelReply)
		return ctx.Result(200, reply)
	}
}

func _AdminDappSrv_DeleteDappTopicRel0_HTTP_Handler(srv AdminDappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteDappTopicRelReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDappSrvDeleteDappTopicRel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteDappTopicRel(ctx, req.(*DeleteDappTopicRelReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteDappTopicRelReply)
		return ctx.Result(200, reply)
	}
}

func _AdminDappSrv_ListDappTopicRel0_HTTP_Handler(srv AdminDappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListDappTopicRelReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminDappSrvListDappTopicRel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListDappTopicRel(ctx, req.(*ListDappTopicRelReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListDappTopicRelReply)
		return ctx.Result(200, reply)
	}
}

type AdminDappSrvHTTPClient interface {
	BatchUpdateDappIndex(ctx context.Context, req *BatchUpdateDappIndexReq, opts ...http.CallOption) (rsp *BatchUpdateDappIndexReply, err error)
	BatchUpdateDappNavigation(ctx context.Context, req *BatchUpdateDappNavigationReq, opts ...http.CallOption) (rsp *BatchUpdateDappNavigationReply, err error)
	CreateDapp(ctx context.Context, req *CreateDappReq, opts ...http.CallOption) (rsp *CreateDappReply, err error)
	CreateDappCategory(ctx context.Context, req *CreateDappCategoryReq, opts ...http.CallOption) (rsp *CreateDappCategoryReply, err error)
	CreateDappCategoryRel(ctx context.Context, req *CreateDappCategoryRelReq, opts ...http.CallOption) (rsp *CreateDappCategoryRelReply, err error)
	CreateDappIndex(ctx context.Context, req *CreateDappIndexReq, opts ...http.CallOption) (rsp *CreateDappIndexReply, err error)
	CreateDappNavigation(ctx context.Context, req *CreateDappNavigationReq, opts ...http.CallOption) (rsp *CreateDappNavigationReply, err error)
	CreateDappTopic(ctx context.Context, req *CreateDappTopicReq, opts ...http.CallOption) (rsp *CreateDappTopicReply, err error)
	CreateDappTopicRel(ctx context.Context, req *CreateDappTopicRelReq, opts ...http.CallOption) (rsp *CreateDappTopicRelReply, err error)
	DeleteDapp(ctx context.Context, req *DeleteDappReq, opts ...http.CallOption) (rsp *DeleteDappReply, err error)
	DeleteDappCategory(ctx context.Context, req *DeleteDappCategoryReq, opts ...http.CallOption) (rsp *DeleteDappCategoryReply, err error)
	DeleteDappCategoryRel(ctx context.Context, req *DeleteDappCategoryRelReq, opts ...http.CallOption) (rsp *DeleteDappCategoryRelReply, err error)
	DeleteDappIndex(ctx context.Context, req *DeleteDappIndexReq, opts ...http.CallOption) (rsp *DeleteDappIndexReply, err error)
	DeleteDappNavigation(ctx context.Context, req *DeleteDappNavigationReq, opts ...http.CallOption) (rsp *DeleteDappNavigationReply, err error)
	DeleteDappTopic(ctx context.Context, req *DeleteDappTopicReq, opts ...http.CallOption) (rsp *DeleteDappTopicReply, err error)
	DeleteDappTopicRel(ctx context.Context, req *DeleteDappTopicRelReq, opts ...http.CallOption) (rsp *DeleteDappTopicRelReply, err error)
	ListDapp(ctx context.Context, req *ListDappReq, opts ...http.CallOption) (rsp *ListDappReply, err error)
	ListDappCategory(ctx context.Context, req *ListDappCategoryReq, opts ...http.CallOption) (rsp *ListDappCategoryReply, err error)
	ListDappCategoryRel(ctx context.Context, req *ListDappCategoryRelReq, opts ...http.CallOption) (rsp *ListDappCategoryRelReply, err error)
	ListDappIndex(ctx context.Context, req *ListDappIndexReq, opts ...http.CallOption) (rsp *ListDappIndexReply, err error)
	ListDappNavigation(ctx context.Context, req *ListDappNavigationReq, opts ...http.CallOption) (rsp *ListDappNavigationReply, err error)
	ListDappTopic(ctx context.Context, req *ListDappTopicReq, opts ...http.CallOption) (rsp *ListDappTopicReply, err error)
	ListDappTopicRel(ctx context.Context, req *ListDappTopicRelReq, opts ...http.CallOption) (rsp *ListDappTopicRelReply, err error)
	SortDappIndex(ctx context.Context, req *SortReq, opts ...http.CallOption) (rsp *SortReply, err error)
	SortDappNavigation(ctx context.Context, req *SortReq, opts ...http.CallOption) (rsp *SortReply, err error)
	UpdateDapp(ctx context.Context, req *UpdateDappReq, opts ...http.CallOption) (rsp *UpdateDappReply, err error)
	UpdateDappCategory(ctx context.Context, req *UpdateDappCategoryReq, opts ...http.CallOption) (rsp *UpdateDappCategoryReply, err error)
	UpdateDappTopic(ctx context.Context, req *UpdateDappTopicReq, opts ...http.CallOption) (rsp *UpdateDappTopicReply, err error)
}

type AdminDappSrvHTTPClientImpl struct {
	cc *http.Client
}

func NewAdminDappSrvHTTPClient(client *http.Client) AdminDappSrvHTTPClient {
	return &AdminDappSrvHTTPClientImpl{client}
}

func (c *AdminDappSrvHTTPClientImpl) BatchUpdateDappIndex(ctx context.Context, in *BatchUpdateDappIndexReq, opts ...http.CallOption) (*BatchUpdateDappIndexReply, error) {
	var out BatchUpdateDappIndexReply
	pattern := "/admin/v1/dapp/index"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminDappSrvBatchUpdateDappIndex))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminDappSrvHTTPClientImpl) BatchUpdateDappNavigation(ctx context.Context, in *BatchUpdateDappNavigationReq, opts ...http.CallOption) (*BatchUpdateDappNavigationReply, error) {
	var out BatchUpdateDappNavigationReply
	pattern := "/admin/v1/dapp/category/navigation"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminDappSrvBatchUpdateDappNavigation))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminDappSrvHTTPClientImpl) CreateDapp(ctx context.Context, in *CreateDappReq, opts ...http.CallOption) (*CreateDappReply, error) {
	var out CreateDappReply
	pattern := "/admin/v1/dapp"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminDappSrvCreateDapp))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminDappSrvHTTPClientImpl) CreateDappCategory(ctx context.Context, in *CreateDappCategoryReq, opts ...http.CallOption) (*CreateDappCategoryReply, error) {
	var out CreateDappCategoryReply
	pattern := "/admin/v1/dapp/category"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminDappSrvCreateDappCategory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminDappSrvHTTPClientImpl) CreateDappCategoryRel(ctx context.Context, in *CreateDappCategoryRelReq, opts ...http.CallOption) (*CreateDappCategoryRelReply, error) {
	var out CreateDappCategoryRelReply
	pattern := "/admin/v1/dapp/category/{id}/rel"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminDappSrvCreateDappCategoryRel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminDappSrvHTTPClientImpl) CreateDappIndex(ctx context.Context, in *CreateDappIndexReq, opts ...http.CallOption) (*CreateDappIndexReply, error) {
	var out CreateDappIndexReply
	pattern := "/admin/v1/dapp/index"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminDappSrvCreateDappIndex))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminDappSrvHTTPClientImpl) CreateDappNavigation(ctx context.Context, in *CreateDappNavigationReq, opts ...http.CallOption) (*CreateDappNavigationReply, error) {
	var out CreateDappNavigationReply
	pattern := "/admin/v1/dapp/category/navigation"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminDappSrvCreateDappNavigation))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminDappSrvHTTPClientImpl) CreateDappTopic(ctx context.Context, in *CreateDappTopicReq, opts ...http.CallOption) (*CreateDappTopicReply, error) {
	var out CreateDappTopicReply
	pattern := "/admin/v1/dapp/topic"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminDappSrvCreateDappTopic))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminDappSrvHTTPClientImpl) CreateDappTopicRel(ctx context.Context, in *CreateDappTopicRelReq, opts ...http.CallOption) (*CreateDappTopicRelReply, error) {
	var out CreateDappTopicRelReply
	pattern := "/admin/v1/dapp/topic/{id}/rel"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminDappSrvCreateDappTopicRel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminDappSrvHTTPClientImpl) DeleteDapp(ctx context.Context, in *DeleteDappReq, opts ...http.CallOption) (*DeleteDappReply, error) {
	var out DeleteDappReply
	pattern := "/admin/v1/dapp/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminDappSrvDeleteDapp))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminDappSrvHTTPClientImpl) DeleteDappCategory(ctx context.Context, in *DeleteDappCategoryReq, opts ...http.CallOption) (*DeleteDappCategoryReply, error) {
	var out DeleteDappCategoryReply
	pattern := "/admin/v1/dapp/category/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminDappSrvDeleteDappCategory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminDappSrvHTTPClientImpl) DeleteDappCategoryRel(ctx context.Context, in *DeleteDappCategoryRelReq, opts ...http.CallOption) (*DeleteDappCategoryRelReply, error) {
	var out DeleteDappCategoryRelReply
	pattern := "/admin/v1/dapp/category/{id}/rel"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminDappSrvDeleteDappCategoryRel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminDappSrvHTTPClientImpl) DeleteDappIndex(ctx context.Context, in *DeleteDappIndexReq, opts ...http.CallOption) (*DeleteDappIndexReply, error) {
	var out DeleteDappIndexReply
	pattern := "/admin/v1/dapp/index/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminDappSrvDeleteDappIndex))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminDappSrvHTTPClientImpl) DeleteDappNavigation(ctx context.Context, in *DeleteDappNavigationReq, opts ...http.CallOption) (*DeleteDappNavigationReply, error) {
	var out DeleteDappNavigationReply
	pattern := "/admin/v1/dapp/category/navigation/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminDappSrvDeleteDappNavigation))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminDappSrvHTTPClientImpl) DeleteDappTopic(ctx context.Context, in *DeleteDappTopicReq, opts ...http.CallOption) (*DeleteDappTopicReply, error) {
	var out DeleteDappTopicReply
	pattern := "/admin/v1/dapp/topic/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminDappSrvDeleteDappTopic))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminDappSrvHTTPClientImpl) DeleteDappTopicRel(ctx context.Context, in *DeleteDappTopicRelReq, opts ...http.CallOption) (*DeleteDappTopicRelReply, error) {
	var out DeleteDappTopicRelReply
	pattern := "/admin/v1/dapp/topic/{id}/rel"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminDappSrvDeleteDappTopicRel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminDappSrvHTTPClientImpl) ListDapp(ctx context.Context, in *ListDappReq, opts ...http.CallOption) (*ListDappReply, error) {
	var out ListDappReply
	pattern := "/admin/v1/dapp"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminDappSrvListDapp))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminDappSrvHTTPClientImpl) ListDappCategory(ctx context.Context, in *ListDappCategoryReq, opts ...http.CallOption) (*ListDappCategoryReply, error) {
	var out ListDappCategoryReply
	pattern := "/admin/v1/dapp/category"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminDappSrvListDappCategory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminDappSrvHTTPClientImpl) ListDappCategoryRel(ctx context.Context, in *ListDappCategoryRelReq, opts ...http.CallOption) (*ListDappCategoryRelReply, error) {
	var out ListDappCategoryRelReply
	pattern := "/admin/v1/dapp/category/{id}/rel"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminDappSrvListDappCategoryRel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminDappSrvHTTPClientImpl) ListDappIndex(ctx context.Context, in *ListDappIndexReq, opts ...http.CallOption) (*ListDappIndexReply, error) {
	var out ListDappIndexReply
	pattern := "/admin/v1/dapp/index"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminDappSrvListDappIndex))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminDappSrvHTTPClientImpl) ListDappNavigation(ctx context.Context, in *ListDappNavigationReq, opts ...http.CallOption) (*ListDappNavigationReply, error) {
	var out ListDappNavigationReply
	pattern := "/admin/v1/dapp/category/navigation"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminDappSrvListDappNavigation))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminDappSrvHTTPClientImpl) ListDappTopic(ctx context.Context, in *ListDappTopicReq, opts ...http.CallOption) (*ListDappTopicReply, error) {
	var out ListDappTopicReply
	pattern := "/admin/v1/dapp/topic"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminDappSrvListDappTopic))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminDappSrvHTTPClientImpl) ListDappTopicRel(ctx context.Context, in *ListDappTopicRelReq, opts ...http.CallOption) (*ListDappTopicRelReply, error) {
	var out ListDappTopicRelReply
	pattern := "/admin/v1/dapp/topic/{id}/rel"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminDappSrvListDappTopicRel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminDappSrvHTTPClientImpl) SortDappIndex(ctx context.Context, in *SortReq, opts ...http.CallOption) (*SortReply, error) {
	var out SortReply
	pattern := "/admin/v1/dapp/index/sort"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminDappSrvSortDappIndex))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminDappSrvHTTPClientImpl) SortDappNavigation(ctx context.Context, in *SortReq, opts ...http.CallOption) (*SortReply, error) {
	var out SortReply
	pattern := "/admin/v1/dapp/category/navigation/sort"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminDappSrvSortDappNavigation))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminDappSrvHTTPClientImpl) UpdateDapp(ctx context.Context, in *UpdateDappReq, opts ...http.CallOption) (*UpdateDappReply, error) {
	var out UpdateDappReply
	pattern := "/admin/v1/dapp/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminDappSrvUpdateDapp))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminDappSrvHTTPClientImpl) UpdateDappCategory(ctx context.Context, in *UpdateDappCategoryReq, opts ...http.CallOption) (*UpdateDappCategoryReply, error) {
	var out UpdateDappCategoryReply
	pattern := "/admin/v1/dapp/category/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminDappSrvUpdateDappCategory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminDappSrvHTTPClientImpl) UpdateDappTopic(ctx context.Context, in *UpdateDappTopicReq, opts ...http.CallOption) (*UpdateDappTopicReply, error) {
	var out UpdateDappTopicReply
	pattern := "/admin/v1/dapp/topic/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminDappSrvUpdateDappTopic))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
