// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.0
// - protoc             (unknown)
// source: api/walletadmin/v1/address.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationAddressSrvListAddress = "/api.walletadmin.v1.AddressSrv/ListAddress"

type AddressSrvHTTPServer interface {
	// ListAddress 地址列表
	ListAddress(context.Context, *ListAddressReq) (*ListAddressReply, error)
}

func RegisterAddressSrvHTTPServer(s *http.Server, srv AddressSrvHTTPServer) {
	r := s.Route("/")
	r.GET("/admin/v1/address/list_address", _AddressSrv_ListAddress0_HTTP_Handler(srv))
}

func _AddressSrv_ListAddress0_HTTP_Handler(srv AddressSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListAddressReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAddressSrvListAddress)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListAddress(ctx, req.(*ListAddressReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListAddressReply)
		return ctx.Result(200, reply)
	}
}

type AddressSrvHTTPClient interface {
	ListAddress(ctx context.Context, req *ListAddressReq, opts ...http.CallOption) (rsp *ListAddressReply, err error)
}

type AddressSrvHTTPClientImpl struct {
	cc *http.Client
}

func NewAddressSrvHTTPClient(client *http.Client) AddressSrvHTTPClient {
	return &AddressSrvHTTPClientImpl{client}
}

func (c *AddressSrvHTTPClientImpl) ListAddress(ctx context.Context, in *ListAddressReq, opts ...http.CallOption) (*ListAddressReply, error) {
	var out ListAddressReply
	pattern := "/admin/v1/address/list_address"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAddressSrvListAddress))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
