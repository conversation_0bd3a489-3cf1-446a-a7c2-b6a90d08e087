// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: api/walletadmin/v1/swap.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SwapChannel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 记录ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 平台名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 是否可用
	Enable bool `protobuf:"varint,3,opt,name=enable,proto3" json:"enable,omitempty"`
}

func (x *SwapChannel) Reset() {
	*x = SwapChannel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_swap_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwapChannel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwapChannel) ProtoMessage() {}

func (x *SwapChannel) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwapChannel.ProtoReflect.Descriptor instead.
func (*SwapChannel) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{0}
}

func (x *SwapChannel) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SwapChannel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SwapChannel) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type SwappableToken struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 记录ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 币名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 币符号
	Symbol string `protobuf:"bytes,3,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// 链名称
	ChainIndex int64 `protobuf:"varint,4,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 精度位数
	Decimals int64 `protobuf:"varint,5,opt,name=decimals,proto3" json:"decimals,omitempty"`
	// 币合约地址
	Address string `protobuf:"bytes,6,opt,name=address,proto3" json:"address,omitempty"`
	// 创建时间(时间戳,单位秒)
	CreatedAt int64 `protobuf:"varint,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// 链名称
	ChainName string `protobuf:"bytes,8,opt,name=chain_name,json=chainName,proto3" json:"chain_name,omitempty"`
	// 币图标
	LogoUrl string `protobuf:"bytes,9,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	// 是否展示
	Display bool `protobuf:"varint,10,opt,name=display,proto3" json:"display,omitempty"`
	// 网络ID
	ChainId string `protobuf:"bytes,11,opt,name=chain_id,json=chainId,proto3" json:"chain_id,omitempty"`
	// 是否已上架
	Enable bool `protobuf:"varint,12,opt,name=enable,proto3" json:"enable,omitempty"`
	// 商户平台
	Channels []string `protobuf:"bytes,13,rep,name=channels,proto3" json:"channels,omitempty"`
	// 是否是所有
	IsAll bool `protobuf:"varint,14,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
}

func (x *SwappableToken) Reset() {
	*x = SwappableToken{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_swap_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwappableToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwappableToken) ProtoMessage() {}

func (x *SwappableToken) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwappableToken.ProtoReflect.Descriptor instead.
func (*SwappableToken) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{1}
}

func (x *SwappableToken) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SwappableToken) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SwappableToken) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *SwappableToken) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *SwappableToken) GetDecimals() int64 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *SwappableToken) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *SwappableToken) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *SwappableToken) GetChainName() string {
	if x != nil {
		return x.ChainName
	}
	return ""
}

func (x *SwappableToken) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *SwappableToken) GetDisplay() bool {
	if x != nil {
		return x.Display
	}
	return false
}

func (x *SwappableToken) GetChainId() string {
	if x != nil {
		return x.ChainId
	}
	return ""
}

func (x *SwappableToken) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *SwappableToken) GetChannels() []string {
	if x != nil {
		return x.Channels
	}
	return nil
}

func (x *SwappableToken) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

type SwapRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 兑换记录id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 兑换时间(unix时间戳)
	SwappedAt int64 `protobuf:"varint,2,opt,name=swapped_at,json=swappedAt,proto3" json:"swapped_at,omitempty"`
	// 兑换状态
	// pending(处理中), wait_kyc(等待KYC验证), wait_refund(等待退款), refund_complete(退款完成)
	// wait_deposit_send_fail(充币失败), wait_for_information(异常订单-等待处理)
	// 终态： success(兑换成功), fail(兑换失败)，timeout(超时-未在指定时间内充币)
	Status string     `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	From   *SwapToken `protobuf:"bytes,4,opt,name=from,proto3" json:"from,omitempty"`
	To     *SwapToken `protobuf:"bytes,5,opt,name=to,proto3" json:"to,omitempty"`
	// 用户兑换消耗的GasFee
	GasFee string `protobuf:"bytes,6,opt,name=gas_fee,json=gasFee,proto3" json:"gas_fee,omitempty"`
	// 兑换手续费率
	FeeRate string `protobuf:"bytes,7,opt,name=fee_rate,json=feeRate,proto3" json:"fee_rate,omitempty"`
	// 用户发起的交易hash
	Hash string `protobuf:"bytes,8,opt,name=hash,proto3" json:"hash,omitempty"`
	// 授权hash
	ApprovalHash string `protobuf:"bytes,9,opt,name=approval_hash,json=approvalHash,proto3" json:"approval_hash,omitempty"`
	// 区块高度
	Height int64 `protobuf:"varint,10,opt,name=height,proto3" json:"height,omitempty"`
	// 兑换平台名称
	Dex string `protobuf:"bytes,11,opt,name=dex,proto3" json:"dex,omitempty"`
	// 兑换平台logo url
	DexLogo string `protobuf:"bytes,12,opt,name=dex_logo,json=dexLogo,proto3" json:"dex_logo,omitempty"`
	// 兑换价格
	SwapPrice string `protobuf:"bytes,13,opt,name=swap_price,json=swapPrice,proto3" json:"swap_price,omitempty"`
	// 兑换详情列表
	Details []*SwapDetail `protobuf:"bytes,14,rep,name=details,proto3" json:"details,omitempty"`
	// 结束时间(unix时间戳)
	FinishedAt int64 `protobuf:"varint,15,opt,name=finished_at,json=finishedAt,proto3" json:"finished_at,omitempty"`
}

func (x *SwapRecord) Reset() {
	*x = SwapRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_swap_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwapRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwapRecord) ProtoMessage() {}

func (x *SwapRecord) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwapRecord.ProtoReflect.Descriptor instead.
func (*SwapRecord) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{2}
}

func (x *SwapRecord) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SwapRecord) GetSwappedAt() int64 {
	if x != nil {
		return x.SwappedAt
	}
	return 0
}

func (x *SwapRecord) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *SwapRecord) GetFrom() *SwapToken {
	if x != nil {
		return x.From
	}
	return nil
}

func (x *SwapRecord) GetTo() *SwapToken {
	if x != nil {
		return x.To
	}
	return nil
}

func (x *SwapRecord) GetGasFee() string {
	if x != nil {
		return x.GasFee
	}
	return ""
}

func (x *SwapRecord) GetFeeRate() string {
	if x != nil {
		return x.FeeRate
	}
	return ""
}

func (x *SwapRecord) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *SwapRecord) GetApprovalHash() string {
	if x != nil {
		return x.ApprovalHash
	}
	return ""
}

func (x *SwapRecord) GetHeight() int64 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *SwapRecord) GetDex() string {
	if x != nil {
		return x.Dex
	}
	return ""
}

func (x *SwapRecord) GetDexLogo() string {
	if x != nil {
		return x.DexLogo
	}
	return ""
}

func (x *SwapRecord) GetSwapPrice() string {
	if x != nil {
		return x.SwapPrice
	}
	return ""
}

func (x *SwapRecord) GetDetails() []*SwapDetail {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *SwapRecord) GetFinishedAt() int64 {
	if x != nil {
		return x.FinishedAt
	}
	return 0
}

type SwapToken struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 代币合约地址
	TokenAddress string `protobuf:"bytes,1,opt,name=token_address,json=tokenAddress,proto3" json:"token_address,omitempty"`
	// 用户地址
	Address string `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	// 链
	ChainIndex int64 `protobuf:"varint,3,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 数量
	Amount string `protobuf:"bytes,4,opt,name=amount,proto3" json:"amount,omitempty"`
	// token logo url
	TokenLogo string `protobuf:"bytes,5,opt,name=token_logo,json=tokenLogo,proto3" json:"token_logo,omitempty"`
	// 币种
	Symbol string `protobuf:"bytes,6,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// 精度
	Decimals string `protobuf:"bytes,7,opt,name=decimals,proto3" json:"decimals,omitempty"`
}

func (x *SwapToken) Reset() {
	*x = SwapToken{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_swap_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwapToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwapToken) ProtoMessage() {}

func (x *SwapToken) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwapToken.ProtoReflect.Descriptor instead.
func (*SwapToken) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{3}
}

func (x *SwapToken) GetTokenAddress() string {
	if x != nil {
		return x.TokenAddress
	}
	return ""
}

func (x *SwapToken) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *SwapToken) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *SwapToken) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *SwapToken) GetTokenLogo() string {
	if x != nil {
		return x.TokenLogo
	}
	return ""
}

func (x *SwapToken) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *SwapToken) GetDecimals() string {
	if x != nil {
		return x.Decimals
	}
	return ""
}

type SwapDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 链索引
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 区块浏览器url
	ExplorerUrl string `protobuf:"bytes,2,opt,name=explorer_url,json=explorerUrl,proto3" json:"explorer_url,omitempty"`
	// 交易hash
	Hash string `protobuf:"bytes,3,opt,name=hash,proto3" json:"hash,omitempty"`
	// 交易状态
	Status string `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	// 是否收录
	Collected bool `protobuf:"varint,5,opt,name=collected,proto3" json:"collected,omitempty"`
}

func (x *SwapDetail) Reset() {
	*x = SwapDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_swap_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwapDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwapDetail) ProtoMessage() {}

func (x *SwapDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwapDetail.ProtoReflect.Descriptor instead.
func (*SwapDetail) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{4}
}

func (x *SwapDetail) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *SwapDetail) GetExplorerUrl() string {
	if x != nil {
		return x.ExplorerUrl
	}
	return ""
}

func (x *SwapDetail) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *SwapDetail) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *SwapDetail) GetCollected() bool {
	if x != nil {
		return x.Collected
	}
	return false
}

type ListSwapChannelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 页码 从1开始
	Page int64 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	// 每页展示条数
	PageSize int64 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
}

func (x *ListSwapChannelRequest) Reset() {
	*x = ListSwapChannelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_swap_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSwapChannelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSwapChannelRequest) ProtoMessage() {}

func (x *ListSwapChannelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSwapChannelRequest.ProtoReflect.Descriptor instead.
func (*ListSwapChannelRequest) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{5}
}

func (x *ListSwapChannelRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListSwapChannelRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ListSwapChannelReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*SwapChannel `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 总条数
	TotalCount int64 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *ListSwapChannelReply) Reset() {
	*x = ListSwapChannelReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_swap_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSwapChannelReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSwapChannelReply) ProtoMessage() {}

func (x *ListSwapChannelReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSwapChannelReply.ProtoReflect.Descriptor instead.
func (*ListSwapChannelReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{6}
}

func (x *ListSwapChannelReply) GetList() []*SwapChannel {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListSwapChannelReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type UpdateSwapChannelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 记录id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 是否关闭通道
	Enable bool `protobuf:"varint,2,opt,name=enable,proto3" json:"enable,omitempty"`
}

func (x *UpdateSwapChannelRequest) Reset() {
	*x = UpdateSwapChannelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_swap_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSwapChannelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSwapChannelRequest) ProtoMessage() {}

func (x *UpdateSwapChannelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSwapChannelRequest.ProtoReflect.Descriptor instead.
func (*UpdateSwapChannelRequest) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateSwapChannelRequest) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateSwapChannelRequest) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type UpdateSwapChannelReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateSwapChannelReply) Reset() {
	*x = UpdateSwapChannelReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_swap_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSwapChannelReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSwapChannelReply) ProtoMessage() {}

func (x *UpdateSwapChannelReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSwapChannelReply.ProtoReflect.Descriptor instead.
func (*UpdateSwapChannelReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{8}
}

type ListTokenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 公链索引，传-1 获取所有
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 简称
	Symbol string `protobuf:"bytes,2,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// 合约号
	Address string `protobuf:"bytes,3,opt,name=address,proto3" json:"address,omitempty"`
	// 兑换渠道id(商户id)
	ChannelId uint64 `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	// 页码 从1开始
	Page int64 `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	// 每页展示条数
	PageSize int64 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 是否是原生代币
	Native bool `protobuf:"varint,7,opt,name=native,proto3" json:"native,omitempty"`
}

func (x *ListTokenRequest) Reset() {
	*x = ListTokenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_swap_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTokenRequest) ProtoMessage() {}

func (x *ListTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTokenRequest.ProtoReflect.Descriptor instead.
func (*ListTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{9}
}

func (x *ListTokenRequest) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *ListTokenRequest) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *ListTokenRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *ListTokenRequest) GetChannelId() uint64 {
	if x != nil {
		return x.ChannelId
	}
	return 0
}

func (x *ListTokenRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListTokenRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListTokenRequest) GetNative() bool {
	if x != nil {
		return x.Native
	}
	return false
}

type ListTokenReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*SwappableToken `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 总条数
	TotalCount int64 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *ListTokenReply) Reset() {
	*x = ListTokenReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_swap_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTokenReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTokenReply) ProtoMessage() {}

func (x *ListTokenReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTokenReply.ProtoReflect.Descriptor instead.
func (*ListTokenReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{10}
}

func (x *ListTokenReply) GetList() []*SwappableToken {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListTokenReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type UpdateTokenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 记录id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 是否展示
	Display bool `protobuf:"varint,2,opt,name=display,proto3" json:"display,omitempty"`
}

func (x *UpdateTokenRequest) Reset() {
	*x = UpdateTokenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_swap_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTokenRequest) ProtoMessage() {}

func (x *UpdateTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTokenRequest.ProtoReflect.Descriptor instead.
func (*UpdateTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{11}
}

func (x *UpdateTokenRequest) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateTokenRequest) GetDisplay() bool {
	if x != nil {
		return x.Display
	}
	return false
}

type UpdateTokenReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateTokenReply) Reset() {
	*x = UpdateTokenReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_swap_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTokenReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTokenReply) ProtoMessage() {}

func (x *UpdateTokenReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTokenReply.ProtoReflect.Descriptor instead.
func (*UpdateTokenReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{12}
}

type ListHotTokenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 公链索引，传-1 获取所有
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 币种
	Symbol string `protobuf:"bytes,2,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// 合约地址
	Address string `protobuf:"bytes,3,opt,name=address,proto3" json:"address,omitempty"`
	// 页码 从1开始
	Page int64 `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	// 每页展示条数
	PageSize int64 `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
}

func (x *ListHotTokenRequest) Reset() {
	*x = ListHotTokenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_swap_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListHotTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHotTokenRequest) ProtoMessage() {}

func (x *ListHotTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHotTokenRequest.ProtoReflect.Descriptor instead.
func (*ListHotTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{13}
}

func (x *ListHotTokenRequest) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *ListHotTokenRequest) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *ListHotTokenRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *ListHotTokenRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListHotTokenRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ListHotTokenReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*SwappableToken `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 总条数
	TotalCount int64 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *ListHotTokenReply) Reset() {
	*x = ListHotTokenReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_swap_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListHotTokenReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHotTokenReply) ProtoMessage() {}

func (x *ListHotTokenReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHotTokenReply.ProtoReflect.Descriptor instead.
func (*ListHotTokenReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{14}
}

func (x *ListHotTokenReply) GetList() []*SwappableToken {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListHotTokenReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type CreateHotTokenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 通过搜索出来的数据获取
	SwappableTokenId int64 `protobuf:"varint,1,opt,name=swappable_token_id,json=swappableTokenId,proto3" json:"swappable_token_id,omitempty"`
	// 是否是全部
	IsAll bool `protobuf:"varint,2,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
}

func (x *CreateHotTokenRequest) Reset() {
	*x = CreateHotTokenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_swap_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateHotTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateHotTokenRequest) ProtoMessage() {}

func (x *CreateHotTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateHotTokenRequest.ProtoReflect.Descriptor instead.
func (*CreateHotTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{15}
}

func (x *CreateHotTokenRequest) GetSwappableTokenId() int64 {
	if x != nil {
		return x.SwappableTokenId
	}
	return 0
}

func (x *CreateHotTokenRequest) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

type CreateHotTokenReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CreateHotTokenReply) Reset() {
	*x = CreateHotTokenReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_swap_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateHotTokenReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateHotTokenReply) ProtoMessage() {}

func (x *CreateHotTokenReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateHotTokenReply.ProtoReflect.Descriptor instead.
func (*CreateHotTokenReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{16}
}

type DeleteHotTokenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteHotTokenRequest) Reset() {
	*x = DeleteHotTokenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_swap_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteHotTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteHotTokenRequest) ProtoMessage() {}

func (x *DeleteHotTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteHotTokenRequest.ProtoReflect.Descriptor instead.
func (*DeleteHotTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{17}
}

func (x *DeleteHotTokenRequest) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DeleteHotTokenReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteHotTokenReply) Reset() {
	*x = DeleteHotTokenReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_swap_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteHotTokenReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteHotTokenReply) ProtoMessage() {}

func (x *DeleteHotTokenReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteHotTokenReply.ProtoReflect.Descriptor instead.
func (*DeleteHotTokenReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{18}
}

type GetSwapRecordRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 兑换记录id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetSwapRecordRequest) Reset() {
	*x = GetSwapRecordRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_swap_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSwapRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSwapRecordRequest) ProtoMessage() {}

func (x *GetSwapRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSwapRecordRequest.ProtoReflect.Descriptor instead.
func (*GetSwapRecordRequest) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{19}
}

func (x *GetSwapRecordRequest) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type ListSwapRecordRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 页码 从1开始
	Page int64 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	// 每页展示条数
	PageSize int64 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 交易币种合约地址
	Address string `protobuf:"bytes,3,opt,name=address,proto3" json:"address,omitempty"`
	// 兑换渠道id, 不传查询所有
	ChannelId uint64 `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	// 兑换状态
	// pending(处理中), wait_kyc(等待KYC验证), wait_refund(等待退款), refund_complete(退款完成)
	// wait_deposit_send_fail(充币失败), wait_for_information(异常订单-等待处理)
	// 终态： success(兑换成功), fail(兑换失败)，timeout(超时-未在指定时间内充币)
	Status string `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`
	// 发起账户地址
	FromAddress string `protobuf:"bytes,6,opt,name=from_address,json=fromAddress,proto3" json:"from_address,omitempty"`
	// 交易币种名称
	Symbol string `protobuf:"bytes,7,opt,name=symbol,proto3" json:"symbol,omitempty"`
}

func (x *ListSwapRecordRequest) Reset() {
	*x = ListSwapRecordRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_swap_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSwapRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSwapRecordRequest) ProtoMessage() {}

func (x *ListSwapRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSwapRecordRequest.ProtoReflect.Descriptor instead.
func (*ListSwapRecordRequest) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{20}
}

func (x *ListSwapRecordRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListSwapRecordRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListSwapRecordRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *ListSwapRecordRequest) GetChannelId() uint64 {
	if x != nil {
		return x.ChannelId
	}
	return 0
}

func (x *ListSwapRecordRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ListSwapRecordRequest) GetFromAddress() string {
	if x != nil {
		return x.FromAddress
	}
	return ""
}

func (x *ListSwapRecordRequest) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

type ListSwapRecordReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*SwapRecord `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 总条数
	TotalCount int64 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *ListSwapRecordReply) Reset() {
	*x = ListSwapRecordReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_walletadmin_v1_swap_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSwapRecordReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSwapRecordReply) ProtoMessage() {}

func (x *ListSwapRecordReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSwapRecordReply.ProtoReflect.Descriptor instead.
func (*ListSwapRecordReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{21}
}

func (x *ListSwapRecordReply) GetList() []*SwapRecord {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListSwapRecordReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

var File_api_walletadmin_v1_swap_proto protoreflect.FileDescriptor

var file_api_walletadmin_v1_swap_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x77, 0x61, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x12, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d,
	0x61, 0x70, 0x69, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x6f, 0x72, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x49, 0x0a,
	0x0b, 0x53, 0x77, 0x61, 0x70, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0xfc, 0x02, 0x0a, 0x0e, 0x53, 0x77, 0x61,
	0x70, 0x70, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68,
	0x61, 0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x63, 0x69,
	0x6d, 0x61, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x65, 0x63, 0x69,
	0x6d, 0x61, 0x6c, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6c, 0x6f, 0x67, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73,
	0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73,
	0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x05, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x22, 0xe1, 0x03, 0x0a, 0x0a, 0x53, 0x77, 0x61, 0x70,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x77, 0x61, 0x70, 0x70, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x77, 0x61, 0x70,
	0x70, 0x65, 0x64, 0x41, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x31, 0x0a,
	0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x77, 0x61, 0x70, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d,
	0x12, 0x2d, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x77, 0x61, 0x70, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x02, 0x74, 0x6f, 0x12,
	0x17, 0x0a, 0x07, 0x67, 0x61, 0x73, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x67, 0x61, 0x73, 0x46, 0x65, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x65, 0x65, 0x5f,
	0x72, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x65, 0x65, 0x52,
	0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x61, 0x6c, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x48, 0x61, 0x73, 0x68, 0x12, 0x16, 0x0a, 0x06,
	0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x68, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x65, 0x78, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x64, 0x65, 0x78, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x65, 0x78, 0x5f, 0x6c, 0x6f,
	0x67, 0x6f, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x65, 0x78, 0x4c, 0x6f, 0x67,
	0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x77, 0x61, 0x70, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x77, 0x61, 0x70, 0x50, 0x72, 0x69, 0x63, 0x65,
	0x12, 0x38, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x77, 0x61, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69,
	0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x41, 0x74, 0x22, 0xd6, 0x01, 0x0a, 0x09,
	0x53, 0x77, 0x61, 0x70, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x6c, 0x6f, 0x67, 0x6f, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x4c, 0x6f, 0x67, 0x6f,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x63, 0x69,
	0x6d, 0x61, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x63, 0x69,
	0x6d, 0x61, 0x6c, 0x73, 0x22, 0x9a, 0x01, 0x0a, 0x0a, 0x53, 0x77, 0x61, 0x70, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x64,
	0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49,
	0x6e, 0x64, 0x65, 0x78, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x72,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x78, 0x70, 0x6c,
	0x6f, 0x72, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x22, 0x5d, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x77, 0x61, 0x70, 0x43, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xba, 0x48, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x26, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x09, 0xba, 0x48, 0x06,
	0x22, 0x04, 0x18, 0x64, 0x20, 0x00, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x22, 0x6c, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x77, 0x61, 0x70, 0x43, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x33, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x77, 0x61, 0x70,
	0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x1f, 0x0a,
	0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x4b,
	0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x77, 0x61, 0x70, 0x43, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xba, 0x48, 0x04, 0x32, 0x02, 0x20, 0x00, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x18, 0x0a, 0x16, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x77, 0x61, 0x70, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0xe1, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x68,
	0x61, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d,
	0x62, 0x6f, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xba, 0x48, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x26, 0x0a, 0x09, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x09, 0xba, 0x48,
	0x06, 0x22, 0x04, 0x18, 0x64, 0x20, 0x00, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x06, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x22, 0x69, 0x0a, 0x0e, 0x4c, 0x69, 0x73,
	0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x36, 0x0a, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x77, 0x61, 0x70, 0x70, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0x47, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xba, 0x48, 0x04, 0x32, 0x02, 0x20, 0x00, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x22, 0x12, 0x0a,
	0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0xad, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x6f, 0x74, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79,
	0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62,
	0x6f, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1b, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xba, 0x48, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x26, 0x0a, 0x09, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x09, 0xba, 0x48,
	0x06, 0x22, 0x04, 0x18, 0x64, 0x20, 0x00, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x22, 0x6c, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x6f, 0x74, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x36, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x77, 0x61, 0x70, 0x70, 0x61,
	0x62, 0x6c, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x1f,
	0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22,
	0x5c, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x48, 0x6f, 0x74, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x77, 0x61, 0x70,
	0x70, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x73, 0x77, 0x61, 0x70, 0x70, 0x61, 0x62, 0x6c, 0x65, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x22, 0x15, 0x0a,
	0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x48, 0x6f, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x30, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x48, 0x6f,
	0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xba, 0x48, 0x04, 0x32, 0x02,
	0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x15, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x48, 0x6f, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x26, 0x0a,
	0x14, 0x47, 0x65, 0x74, 0x53, 0x77, 0x61, 0x70, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x02, 0x69, 0x64, 0x22, 0xe8, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x77,
	0x61, 0x70, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1b, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xba,
	0x48, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x26, 0x0a, 0x09,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x09, 0xba, 0x48, 0x06, 0x22, 0x04, 0x18, 0x64, 0x20, 0x00, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x72, 0x6f,
	0x6d, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62,
	0x6f, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c,
	0x22, 0x6a, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x77, 0x61, 0x70, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x32, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c,
	0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x77, 0x61, 0x70, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x32, 0xcd, 0x0a, 0x0a,
	0x0b, 0x53, 0x77, 0x61, 0x70, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x87, 0x01, 0x0a,
	0x0f, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x77, 0x61, 0x70, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x12, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x77, 0x61, 0x70, 0x43, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x77, 0x61, 0x70, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x12, 0x16,
	0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x77, 0x61, 0x70, 0x2f, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x95, 0x01, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x77, 0x61, 0x70, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x2c, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x77, 0x61, 0x70, 0x43, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x77, 0x61, 0x70, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x3a, 0x01,
	0x2a, 0x1a, 0x1b, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x77, 0x61,
	0x70, 0x2f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x73,
	0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x24, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x12, 0x14, 0x2f,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x77, 0x61, 0x70, 0x2f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x81, 0x01, 0x0a, 0x0b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x3a, 0x01, 0x2a, 0x1a, 0x19, 0x2f, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x77, 0x61, 0x70, 0x2f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x80, 0x01, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74,
	0x48, 0x6f, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x48, 0x6f, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x6f, 0x74, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a,
	0x12, 0x18, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x77, 0x61, 0x70,
	0x2f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x2f, 0x68, 0x6f, 0x74, 0x12, 0x89, 0x01, 0x0a, 0x0e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x48, 0x6f, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x29, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x48, 0x6f, 0x74, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x48, 0x6f, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x3a, 0x01, 0x2a, 0x22, 0x18, 0x2f, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x77, 0x61, 0x70, 0x2f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x2f, 0x68, 0x6f, 0x74, 0x12, 0x8b, 0x01, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x48, 0x6f, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x48, 0x6f, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x48, 0x6f, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x25, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x2a, 0x1d, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x77, 0x61, 0x70, 0x2f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x2f, 0x68, 0x6f, 0x74, 0x2f,
	0x7b, 0x69, 0x64, 0x7d, 0x12, 0x80, 0x01, 0x0a, 0x0c, 0x53, 0x6f, 0x72, 0x74, 0x48, 0x6f, 0x74,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c,
	0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x53, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x28, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x22, 0x3a, 0x01, 0x2a, 0x1a, 0x1d, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x77, 0x61, 0x70, 0x2f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x2f, 0x68,
	0x6f, 0x74, 0x2f, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x7d, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x53, 0x77,
	0x61, 0x70, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x53, 0x77, 0x61, 0x70, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x77, 0x61, 0x70, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x22, 0x22, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x12, 0x1a, 0x2f, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x77, 0x61, 0x70, 0x2f, 0x72, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x83, 0x01, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x53,
	0x77, 0x61, 0x70, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x77, 0x61, 0x70, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x77,
	0x61, 0x70, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1d, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x17, 0x12, 0x15, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x77, 0x61, 0x70, 0x2f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x42, 0xaf, 0x01, 0x0a,
	0x16, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x42, 0x09, 0x53, 0x77, 0x61, 0x70, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x50, 0x01, 0x5a, 0x20, 0x62, 0x79, 0x64, 0x5f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0xa2, 0x02, 0x03, 0x41, 0x57, 0x58, 0xaa, 0x02, 0x12, 0x41,
	0x70, 0x69, 0x2e, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x56,
	0x31, 0xca, 0x02, 0x12, 0x41, 0x70, 0x69, 0x5c, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x5c, 0x56, 0x31, 0xe2, 0x02, 0x1e, 0x41, 0x70, 0x69, 0x5c, 0x57, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5c, 0x56, 0x31, 0x5c, 0x47, 0x50, 0x42, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0xea, 0x02, 0x14, 0x41, 0x70, 0x69, 0x3a, 0x3a, 0x57,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x3a, 0x3a, 0x56, 0x31, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_walletadmin_v1_swap_proto_rawDescOnce sync.Once
	file_api_walletadmin_v1_swap_proto_rawDescData = file_api_walletadmin_v1_swap_proto_rawDesc
)

func file_api_walletadmin_v1_swap_proto_rawDescGZIP() []byte {
	file_api_walletadmin_v1_swap_proto_rawDescOnce.Do(func() {
		file_api_walletadmin_v1_swap_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_walletadmin_v1_swap_proto_rawDescData)
	})
	return file_api_walletadmin_v1_swap_proto_rawDescData
}

var file_api_walletadmin_v1_swap_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_api_walletadmin_v1_swap_proto_goTypes = []any{
	(*SwapChannel)(nil),              // 0: api.walletadmin.v1.SwapChannel
	(*SwappableToken)(nil),           // 1: api.walletadmin.v1.SwappableToken
	(*SwapRecord)(nil),               // 2: api.walletadmin.v1.SwapRecord
	(*SwapToken)(nil),                // 3: api.walletadmin.v1.SwapToken
	(*SwapDetail)(nil),               // 4: api.walletadmin.v1.SwapDetail
	(*ListSwapChannelRequest)(nil),   // 5: api.walletadmin.v1.ListSwapChannelRequest
	(*ListSwapChannelReply)(nil),     // 6: api.walletadmin.v1.ListSwapChannelReply
	(*UpdateSwapChannelRequest)(nil), // 7: api.walletadmin.v1.UpdateSwapChannelRequest
	(*UpdateSwapChannelReply)(nil),   // 8: api.walletadmin.v1.UpdateSwapChannelReply
	(*ListTokenRequest)(nil),         // 9: api.walletadmin.v1.ListTokenRequest
	(*ListTokenReply)(nil),           // 10: api.walletadmin.v1.ListTokenReply
	(*UpdateTokenRequest)(nil),       // 11: api.walletadmin.v1.UpdateTokenRequest
	(*UpdateTokenReply)(nil),         // 12: api.walletadmin.v1.UpdateTokenReply
	(*ListHotTokenRequest)(nil),      // 13: api.walletadmin.v1.ListHotTokenRequest
	(*ListHotTokenReply)(nil),        // 14: api.walletadmin.v1.ListHotTokenReply
	(*CreateHotTokenRequest)(nil),    // 15: api.walletadmin.v1.CreateHotTokenRequest
	(*CreateHotTokenReply)(nil),      // 16: api.walletadmin.v1.CreateHotTokenReply
	(*DeleteHotTokenRequest)(nil),    // 17: api.walletadmin.v1.DeleteHotTokenRequest
	(*DeleteHotTokenReply)(nil),      // 18: api.walletadmin.v1.DeleteHotTokenReply
	(*GetSwapRecordRequest)(nil),     // 19: api.walletadmin.v1.GetSwapRecordRequest
	(*ListSwapRecordRequest)(nil),    // 20: api.walletadmin.v1.ListSwapRecordRequest
	(*ListSwapRecordReply)(nil),      // 21: api.walletadmin.v1.ListSwapRecordReply
	(*CommonSortReq)(nil),            // 22: api.walletadmin.v1.CommonSortReq
	(*CommonSortReply)(nil),          // 23: api.walletadmin.v1.CommonSortReply
}
var file_api_walletadmin_v1_swap_proto_depIdxs = []int32{
	3,  // 0: api.walletadmin.v1.SwapRecord.from:type_name -> api.walletadmin.v1.SwapToken
	3,  // 1: api.walletadmin.v1.SwapRecord.to:type_name -> api.walletadmin.v1.SwapToken
	4,  // 2: api.walletadmin.v1.SwapRecord.details:type_name -> api.walletadmin.v1.SwapDetail
	0,  // 3: api.walletadmin.v1.ListSwapChannelReply.list:type_name -> api.walletadmin.v1.SwapChannel
	1,  // 4: api.walletadmin.v1.ListTokenReply.list:type_name -> api.walletadmin.v1.SwappableToken
	1,  // 5: api.walletadmin.v1.ListHotTokenReply.list:type_name -> api.walletadmin.v1.SwappableToken
	2,  // 6: api.walletadmin.v1.ListSwapRecordReply.list:type_name -> api.walletadmin.v1.SwapRecord
	5,  // 7: api.walletadmin.v1.SwapService.ListSwapChannel:input_type -> api.walletadmin.v1.ListSwapChannelRequest
	7,  // 8: api.walletadmin.v1.SwapService.UpdateSwapChannel:input_type -> api.walletadmin.v1.UpdateSwapChannelRequest
	9,  // 9: api.walletadmin.v1.SwapService.ListToken:input_type -> api.walletadmin.v1.ListTokenRequest
	11, // 10: api.walletadmin.v1.SwapService.UpdateToken:input_type -> api.walletadmin.v1.UpdateTokenRequest
	13, // 11: api.walletadmin.v1.SwapService.ListHotToken:input_type -> api.walletadmin.v1.ListHotTokenRequest
	15, // 12: api.walletadmin.v1.SwapService.CreateHotToken:input_type -> api.walletadmin.v1.CreateHotTokenRequest
	17, // 13: api.walletadmin.v1.SwapService.DeleteHotToken:input_type -> api.walletadmin.v1.DeleteHotTokenRequest
	22, // 14: api.walletadmin.v1.SwapService.SortHotToken:input_type -> api.walletadmin.v1.CommonSortReq
	19, // 15: api.walletadmin.v1.SwapService.GetSwapRecord:input_type -> api.walletadmin.v1.GetSwapRecordRequest
	20, // 16: api.walletadmin.v1.SwapService.ListSwapRecord:input_type -> api.walletadmin.v1.ListSwapRecordRequest
	6,  // 17: api.walletadmin.v1.SwapService.ListSwapChannel:output_type -> api.walletadmin.v1.ListSwapChannelReply
	8,  // 18: api.walletadmin.v1.SwapService.UpdateSwapChannel:output_type -> api.walletadmin.v1.UpdateSwapChannelReply
	10, // 19: api.walletadmin.v1.SwapService.ListToken:output_type -> api.walletadmin.v1.ListTokenReply
	12, // 20: api.walletadmin.v1.SwapService.UpdateToken:output_type -> api.walletadmin.v1.UpdateTokenReply
	14, // 21: api.walletadmin.v1.SwapService.ListHotToken:output_type -> api.walletadmin.v1.ListHotTokenReply
	16, // 22: api.walletadmin.v1.SwapService.CreateHotToken:output_type -> api.walletadmin.v1.CreateHotTokenReply
	18, // 23: api.walletadmin.v1.SwapService.DeleteHotToken:output_type -> api.walletadmin.v1.DeleteHotTokenReply
	23, // 24: api.walletadmin.v1.SwapService.SortHotToken:output_type -> api.walletadmin.v1.CommonSortReply
	2,  // 25: api.walletadmin.v1.SwapService.GetSwapRecord:output_type -> api.walletadmin.v1.SwapRecord
	21, // 26: api.walletadmin.v1.SwapService.ListSwapRecord:output_type -> api.walletadmin.v1.ListSwapRecordReply
	17, // [17:27] is the sub-list for method output_type
	7,  // [7:17] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_api_walletadmin_v1_swap_proto_init() }
func file_api_walletadmin_v1_swap_proto_init() {
	if File_api_walletadmin_v1_swap_proto != nil {
		return
	}
	file_api_walletadmin_v1_sort_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_walletadmin_v1_swap_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*SwapChannel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_swap_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*SwappableToken); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_swap_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*SwapRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_swap_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*SwapToken); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_swap_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*SwapDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_swap_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*ListSwapChannelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_swap_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*ListSwapChannelReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_swap_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateSwapChannelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_swap_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateSwapChannelReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_swap_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*ListTokenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_swap_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*ListTokenReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_swap_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateTokenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_swap_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateTokenReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_swap_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*ListHotTokenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_swap_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*ListHotTokenReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_swap_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*CreateHotTokenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_swap_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*CreateHotTokenReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_swap_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteHotTokenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_swap_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteHotTokenReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_swap_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*GetSwapRecordRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_swap_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*ListSwapRecordRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_walletadmin_v1_swap_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*ListSwapRecordReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_walletadmin_v1_swap_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_walletadmin_v1_swap_proto_goTypes,
		DependencyIndexes: file_api_walletadmin_v1_swap_proto_depIdxs,
		MessageInfos:      file_api_walletadmin_v1_swap_proto_msgTypes,
	}.Build()
	File_api_walletadmin_v1_swap_proto = out.File
	file_api_walletadmin_v1_swap_proto_rawDesc = nil
	file_api_walletadmin_v1_swap_proto_goTypes = nil
	file_api_walletadmin_v1_swap_proto_depIdxs = nil
}
