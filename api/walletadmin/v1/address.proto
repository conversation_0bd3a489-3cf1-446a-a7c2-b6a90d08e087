syntax = "proto3";

package api.walletadmin.v1;

option go_package = "byd_wallet/api/walletadmin/v1;v1";
option java_multiple_files = true;
option java_package = "api.walletadmin.v1";

import "google/api/annotations.proto";
import "buf/validate/validate.proto";

service AddressSrv {
	// 地址列表
	rpc ListAddress(ListAddressReq) returns (ListAddressReply) {
		option (google.api.http) = {
			get: "/admin/v1/address/list_address"
		};
	}
}

message ListAddressReq {
	// 页码
	int64 page = 1 [(buf.validate.field).int64.gt = 0];
	// 页容量
	int64 page_size = 2 [(buf.validate.field).int64 = {gt: 0,lte:100}];
	// 链索引(传值-1查询全部链)
	int64 chain_index = 3;
	// 地址
	optional string address = 4;
}

message Address {
	// 地址ID
	uint64 id = 1;
	// BOSS ID
	string boss_id = 2;
	// 链索引
	int64 chain_index = 3;
	// 地址
	string address = 4;
	// 创建时间(时间戳,单位秒)
	int64 created_at = 5;
	// 链名称
	string chain_name = 6;
}

message ListAddressReply {
	repeated Address list = 1;
	// 总记录数
	int64 total_count = 2;
}