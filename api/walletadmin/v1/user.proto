syntax = "proto3";

package api.walletadmin.v1;

option go_package = "byd_wallet/api/walletadmin/v1;v1";
option java_multiple_files = true;
option java_package = "api.walletadmin.v1";

import "google/api/annotations.proto";
import "buf/validate/validate.proto";

service UserSrv {
	// 用户列表
	rpc ListUser (ListUserReq) returns (ListUserReply) {
		option (google.api.http) = {
			get: "/admin/v1/user/list_user"
		};
	};
}

message ListUserReq {
	// 页码
	int64 page = 1 [(buf.validate.field).int64.gt = 0];
	// 页容量
	int64 page_size = 2 [(buf.validate.field).int64 = {gt: 0,lte:100}];
	// 用户ID
	optional uint64 id = 3;
	// BOSS ID
	optional string boss_id = 4;
}

message User {
	// 用户ID
	uint64 id = 1;
	// BOSS ID
	string boss_id = 2;
	// 创建时间(时间戳,单位秒)
	int64 created_at = 3;
	// 是否可用
	bool enable = 4;
}

message ListUserReply {
	repeated User list = 1;
	// 总记录数
	int64 total_count = 2;
}