// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: api/wallet/v1/wallet.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TokenWithWallet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WalletAddress string `protobuf:"bytes,1,opt,name=wallet_address,json=walletAddress,proto3" json:"wallet_address,omitempty"`
	ChainIndex    int64  `protobuf:"varint,2,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	Name          string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Symbol        string `protobuf:"bytes,4,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Decimals      int64  `protobuf:"varint,5,opt,name=decimals,proto3" json:"decimals,omitempty"`
	LogoUrl       string `protobuf:"bytes,6,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	Address       string `protobuf:"bytes,7,opt,name=address,proto3" json:"address,omitempty"`
	ChainId       string `protobuf:"bytes,8,opt,name=chain_id,json=chainId,proto3" json:"chain_id,omitempty"`
}

func (x *TokenWithWallet) Reset() {
	*x = TokenWithWallet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_wallet_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TokenWithWallet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenWithWallet) ProtoMessage() {}

func (x *TokenWithWallet) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenWithWallet.ProtoReflect.Descriptor instead.
func (*TokenWithWallet) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{0}
}

func (x *TokenWithWallet) GetWalletAddress() string {
	if x != nil {
		return x.WalletAddress
	}
	return ""
}

func (x *TokenWithWallet) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *TokenWithWallet) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TokenWithWallet) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *TokenWithWallet) GetDecimals() int64 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *TokenWithWallet) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *TokenWithWallet) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *TokenWithWallet) GetChainId() string {
	if x != nil {
		return x.ChainId
	}
	return ""
}

type QueryTxLatestTokenReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*TokenWithWallet `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *QueryTxLatestTokenReply) Reset() {
	*x = QueryTxLatestTokenReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_wallet_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryTxLatestTokenReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryTxLatestTokenReply) ProtoMessage() {}

func (x *QueryTxLatestTokenReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryTxLatestTokenReply.ProtoReflect.Descriptor instead.
func (*QueryTxLatestTokenReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{1}
}

func (x *QueryTxLatestTokenReply) GetList() []*TokenWithWallet {
	if x != nil {
		return x.List
	}
	return nil
}

type WalletAddress4Chain struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 钱包地址
	Addresses  []string `protobuf:"bytes,1,rep,name=addresses,proto3" json:"addresses,omitempty"`
	ChainIndex int64    `protobuf:"varint,2,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
}

func (x *WalletAddress4Chain) Reset() {
	*x = WalletAddress4Chain{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_wallet_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WalletAddress4Chain) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WalletAddress4Chain) ProtoMessage() {}

func (x *WalletAddress4Chain) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WalletAddress4Chain.ProtoReflect.Descriptor instead.
func (*WalletAddress4Chain) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{2}
}

func (x *WalletAddress4Chain) GetAddresses() []string {
	if x != nil {
		return x.Addresses
	}
	return nil
}

func (x *WalletAddress4Chain) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

type QueryTxLatestTokenReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Chains []*WalletAddress4Chain `protobuf:"bytes,1,rep,name=chains,proto3" json:"chains,omitempty"`
}

func (x *QueryTxLatestTokenReq) Reset() {
	*x = QueryTxLatestTokenReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_wallet_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryTxLatestTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryTxLatestTokenReq) ProtoMessage() {}

func (x *QueryTxLatestTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryTxLatestTokenReq.ProtoReflect.Descriptor instead.
func (*QueryTxLatestTokenReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{3}
}

func (x *QueryTxLatestTokenReq) GetChains() []*WalletAddress4Chain {
	if x != nil {
		return x.Chains
	}
	return nil
}

type WalletAddress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 钱包地址
	Address string `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	// 链索引
	ChainIndexes []int64 `protobuf:"varint,2,rep,packed,name=chain_indexes,json=chainIndexes,proto3" json:"chain_indexes,omitempty"`
}

func (x *WalletAddress) Reset() {
	*x = WalletAddress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_wallet_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WalletAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WalletAddress) ProtoMessage() {}

func (x *WalletAddress) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WalletAddress.ProtoReflect.Descriptor instead.
func (*WalletAddress) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{4}
}

func (x *WalletAddress) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *WalletAddress) GetChainIndexes() []int64 {
	if x != nil {
		return x.ChainIndexes
	}
	return nil
}

type ListTokenBalanceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Addresses []*WalletAddress `protobuf:"bytes,1,rep,name=addresses,proto3" json:"addresses,omitempty"`
}

func (x *ListTokenBalanceReq) Reset() {
	*x = ListTokenBalanceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_wallet_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTokenBalanceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTokenBalanceReq) ProtoMessage() {}

func (x *ListTokenBalanceReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTokenBalanceReq.ProtoReflect.Descriptor instead.
func (*ListTokenBalanceReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{5}
}

func (x *ListTokenBalanceReq) GetAddresses() []*WalletAddress {
	if x != nil {
		return x.Addresses
	}
	return nil
}

type TokenBalance struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 链索引
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// token地址
	Address string `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	// token名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// token符号
	Symbol string `protobuf:"bytes,4,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// token精度
	Decimals int64 `protobuf:"varint,5,opt,name=decimals,proto3" json:"decimals,omitempty"`
	// token原始数量
	RawBalance string `protobuf:"bytes,6,opt,name=raw_balance,json=rawBalance,proto3" json:"raw_balance,omitempty"`
	// token价格(USD)
	Price string `protobuf:"bytes,7,opt,name=price,proto3" json:"price,omitempty"`
	// token图标
	LogoUrl string `protobuf:"bytes,8,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
}

func (x *TokenBalance) Reset() {
	*x = TokenBalance{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_wallet_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TokenBalance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenBalance) ProtoMessage() {}

func (x *TokenBalance) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenBalance.ProtoReflect.Descriptor instead.
func (*TokenBalance) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{6}
}

func (x *TokenBalance) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *TokenBalance) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *TokenBalance) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TokenBalance) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *TokenBalance) GetDecimals() int64 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *TokenBalance) GetRawBalance() string {
	if x != nil {
		return x.RawBalance
	}
	return ""
}

func (x *TokenBalance) GetPrice() string {
	if x != nil {
		return x.Price
	}
	return ""
}

func (x *TokenBalance) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

type WalletBalance struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 钱包地址
	Address  string          `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	Balances []*TokenBalance `protobuf:"bytes,2,rep,name=balances,proto3" json:"balances,omitempty"`
}

func (x *WalletBalance) Reset() {
	*x = WalletBalance{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_wallet_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WalletBalance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WalletBalance) ProtoMessage() {}

func (x *WalletBalance) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WalletBalance.ProtoReflect.Descriptor instead.
func (*WalletBalance) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{7}
}

func (x *WalletBalance) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *WalletBalance) GetBalances() []*TokenBalance {
	if x != nil {
		return x.Balances
	}
	return nil
}

type ListTokenBalanceReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*WalletBalance `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListTokenBalanceReply) Reset() {
	*x = ListTokenBalanceReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_wallet_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTokenBalanceReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTokenBalanceReply) ProtoMessage() {}

func (x *ListTokenBalanceReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTokenBalanceReply.ProtoReflect.Descriptor instead.
func (*ListTokenBalanceReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{8}
}

func (x *ListTokenBalanceReply) GetList() []*WalletBalance {
	if x != nil {
		return x.List
	}
	return nil
}

type GetTokenReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 链索引
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 合约地址
	Address string `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
}

func (x *GetTokenReq) Reset() {
	*x = GetTokenReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_wallet_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTokenReq) ProtoMessage() {}

func (x *GetTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTokenReq.ProtoReflect.Descriptor instead.
func (*GetTokenReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{9}
}

func (x *GetTokenReq) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *GetTokenReq) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type NetworkListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *NetworkListReq) Reset() {
	*x = NetworkListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_wallet_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NetworkListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworkListReq) ProtoMessage() {}

func (x *NetworkListReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworkListReq.ProtoReflect.Descriptor instead.
func (*NetworkListReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{10}
}

type NetworkListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 链列表
	List []*Network `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *NetworkListReply) Reset() {
	*x = NetworkListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_wallet_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NetworkListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworkListReply) ProtoMessage() {}

func (x *NetworkListReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworkListReply.ProtoReflect.Descriptor instead.
func (*NetworkListReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{11}
}

func (x *NetworkListReply) GetList() []*Network {
	if x != nil {
		return x.List
	}
	return nil
}

type TransactionsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 链索引
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 查询地址，多个用逗号(,)隔开
	Address string `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	// 查询合约地址，多个用逗号(,)隔开,不区分合约查询all
	ProgramId string `protobuf:"bytes,3,opt,name=program_id,json=programId,proto3" json:"program_id,omitempty"`
	Page      int64  `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	// 每页展示条数
	Limit int64 `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (x *TransactionsReq) Reset() {
	*x = TransactionsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_wallet_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransactionsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionsReq) ProtoMessage() {}

func (x *TransactionsReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionsReq.ProtoReflect.Descriptor instead.
func (*TransactionsReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{12}
}

func (x *TransactionsReq) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *TransactionsReq) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *TransactionsReq) GetProgramId() string {
	if x != nil {
		return x.ProgramId
	}
	return ""
}

func (x *TransactionsReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *TransactionsReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type TransactionsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 总条数
	Count int64 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	// transaction
	List []*Transaction `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *TransactionsReply) Reset() {
	*x = TransactionsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_wallet_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransactionsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionsReply) ProtoMessage() {}

func (x *TransactionsReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionsReply.ProtoReflect.Descriptor instead.
func (*TransactionsReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{13}
}

func (x *TransactionsReply) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *TransactionsReply) GetList() []*Transaction {
	if x != nil {
		return x.List
	}
	return nil
}

type TransactionsByAddressReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 链索引
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 查询from地址，多个用逗号(,)隔开
	FromAddress string `protobuf:"bytes,2,opt,name=from_address,json=fromAddress,proto3" json:"from_address,omitempty"`
	// 查询to地址，多个用逗号(,)隔开
	ToAddress string `protobuf:"bytes,3,opt,name=to_address,json=toAddress,proto3" json:"to_address,omitempty"`
	// 查询合约地址，多个用逗号(,)隔开
	ProgramId *string `protobuf:"bytes,4,opt,name=program_id,json=programId,proto3,oneof" json:"program_id,omitempty"`
	Page      int64   `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	// 每页展示条数
	Limit int64 `protobuf:"varint,6,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (x *TransactionsByAddressReq) Reset() {
	*x = TransactionsByAddressReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_wallet_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransactionsByAddressReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionsByAddressReq) ProtoMessage() {}

func (x *TransactionsByAddressReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionsByAddressReq.ProtoReflect.Descriptor instead.
func (*TransactionsByAddressReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{14}
}

func (x *TransactionsByAddressReq) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *TransactionsByAddressReq) GetFromAddress() string {
	if x != nil {
		return x.FromAddress
	}
	return ""
}

func (x *TransactionsByAddressReq) GetToAddress() string {
	if x != nil {
		return x.ToAddress
	}
	return ""
}

func (x *TransactionsByAddressReq) GetProgramId() string {
	if x != nil && x.ProgramId != nil {
		return *x.ProgramId
	}
	return ""
}

func (x *TransactionsByAddressReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *TransactionsByAddressReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type TransactionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 链索引
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 交易hash
	Txn string `protobuf:"bytes,2,opt,name=txn,proto3" json:"txn,omitempty"`
}

func (x *TransactionReq) Reset() {
	*x = TransactionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_wallet_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransactionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionReq) ProtoMessage() {}

func (x *TransactionReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionReq.ProtoReflect.Descriptor instead.
func (*TransactionReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{15}
}

func (x *TransactionReq) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *TransactionReq) GetTxn() string {
	if x != nil {
		return x.Txn
	}
	return ""
}

type Transaction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 交易hash
	Txn string `protobuf:"bytes,1,opt,name=txn,proto3" json:"txn,omitempty"`
	// 来源地址
	FromAddress string `protobuf:"bytes,2,opt,name=from_address,json=fromAddress,proto3" json:"from_address,omitempty"`
	// 接收地址
	ToAddress string `protobuf:"bytes,3,opt,name=to_address,json=toAddress,proto3" json:"to_address,omitempty"`
	// 转账余额
	Value string `protobuf:"bytes,4,opt,name=value,proto3" json:"value,omitempty"`
	// 交易手续费
	Fee string `protobuf:"bytes,5,opt,name=fee,proto3" json:"fee,omitempty"`
	// 交易方法
	Method string `protobuf:"bytes,6,opt,name=method,proto3" json:"method,omitempty"`
	// 合约地址
	ProgramId string `protobuf:"bytes,7,opt,name=program_id,json=programId,proto3" json:"program_id,omitempty"`
	// 链上完成时间
	Timestamp int64 `protobuf:"varint,8,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	// 上链状态 success、fail
	Status string `protobuf:"bytes,9,opt,name=status,proto3" json:"status,omitempty"`
	// 所在高度
	BlockNumber int64 `protobuf:"varint,10,opt,name=block_number,json=blockNumber,proto3" json:"block_number,omitempty"`
	// chain_index
	ChainIndex int64 `protobuf:"varint,11,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
}

func (x *Transaction) Reset() {
	*x = Transaction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_wallet_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Transaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Transaction) ProtoMessage() {}

func (x *Transaction) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Transaction.ProtoReflect.Descriptor instead.
func (*Transaction) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{16}
}

func (x *Transaction) GetTxn() string {
	if x != nil {
		return x.Txn
	}
	return ""
}

func (x *Transaction) GetFromAddress() string {
	if x != nil {
		return x.FromAddress
	}
	return ""
}

func (x *Transaction) GetToAddress() string {
	if x != nil {
		return x.ToAddress
	}
	return ""
}

func (x *Transaction) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *Transaction) GetFee() string {
	if x != nil {
		return x.Fee
	}
	return ""
}

func (x *Transaction) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *Transaction) GetProgramId() string {
	if x != nil {
		return x.ProgramId
	}
	return ""
}

func (x *Transaction) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *Transaction) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Transaction) GetBlockNumber() int64 {
	if x != nil {
		return x.BlockNumber
	}
	return 0
}

func (x *Transaction) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

type Network struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// chain名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// symbol 标识
	Symbol string `protobuf:"bytes,2,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// chainIndex
	ChainIndex int64 `protobuf:"varint,4,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 每条链的标记
	ChainId string `protobuf:"bytes,5,opt,name=chain_id,json=chainId,proto3" json:"chain_id,omitempty"`
	// 代币精度
	Decimals int64 `protobuf:"varint,6,opt,name=decimals,proto3" json:"decimals,omitempty"`
	// 链图标
	BlockchainUrl string `protobuf:"bytes,7,opt,name=blockchain_url,json=blockchainUrl,proto3" json:"blockchain_url,omitempty"`
	// token图标
	TokenUrl string `protobuf:"bytes,8,opt,name=token_url,json=tokenUrl,proto3" json:"token_url,omitempty"`
	// 区块浏览器 地址
	ExplorerUrl string `protobuf:"bytes,9,opt,name=explorer_url,json=explorerUrl,proto3" json:"explorer_url,omitempty"`
	// 支付gas的 symbol
	GasTokenSymbol string `protobuf:"bytes,10,opt,name=gas_token_symbol,json=gasTokenSymbol,proto3" json:"gas_token_symbol,omitempty"`
	// chain 类型
	ChainType string `protobuf:"bytes,11,opt,name=chain_type,json=chainType,proto3" json:"chain_type,omitempty"`
	// handle
	Handle string `protobuf:"bytes,12,opt,name=handle,proto3" json:"handle,omitempty"`
	// 排序
	SortOrder int64 `protobuf:"varint,13,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order,omitempty"`
	// 链名称
	ChainName string `protobuf:"bytes,14,opt,name=chain_name,json=chainName,proto3" json:"chain_name,omitempty"`
}

func (x *Network) Reset() {
	*x = Network{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_wallet_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Network) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Network) ProtoMessage() {}

func (x *Network) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Network.ProtoReflect.Descriptor instead.
func (*Network) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{17}
}

func (x *Network) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Network) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *Network) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *Network) GetChainId() string {
	if x != nil {
		return x.ChainId
	}
	return ""
}

func (x *Network) GetDecimals() int64 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *Network) GetBlockchainUrl() string {
	if x != nil {
		return x.BlockchainUrl
	}
	return ""
}

func (x *Network) GetTokenUrl() string {
	if x != nil {
		return x.TokenUrl
	}
	return ""
}

func (x *Network) GetExplorerUrl() string {
	if x != nil {
		return x.ExplorerUrl
	}
	return ""
}

func (x *Network) GetGasTokenSymbol() string {
	if x != nil {
		return x.GasTokenSymbol
	}
	return ""
}

func (x *Network) GetChainType() string {
	if x != nil {
		return x.ChainType
	}
	return ""
}

func (x *Network) GetHandle() string {
	if x != nil {
		return x.Handle
	}
	return ""
}

func (x *Network) GetSortOrder() int64 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

func (x *Network) GetChainName() string {
	if x != nil {
		return x.ChainName
	}
	return ""
}

type TokenListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 链索引(0,60,...支持查询多个链,查询全部就传空或不传)
	ChainIndexes string `protobuf:"bytes,1,opt,name=chain_indexes,json=chainIndexes,proto3" json:"chain_indexes,omitempty"`
	// 支持名字或者合约地址
	Query string `protobuf:"bytes,2,opt,name=query,proto3" json:"query,omitempty"`
	// 查询页
	Page int64 `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	// 每页条数，限制200条
	Limit int64 `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (x *TokenListReq) Reset() {
	*x = TokenListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_wallet_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TokenListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenListReq) ProtoMessage() {}

func (x *TokenListReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenListReq.ProtoReflect.Descriptor instead.
func (*TokenListReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{18}
}

func (x *TokenListReq) GetChainIndexes() string {
	if x != nil {
		return x.ChainIndexes
	}
	return ""
}

func (x *TokenListReq) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *TokenListReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *TokenListReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type Token struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChainIndex int64  `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	Name       string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Symbol     string `protobuf:"bytes,3,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Decimals   int64  `protobuf:"varint,4,opt,name=decimals,proto3" json:"decimals,omitempty"`
	LogoUrl    string `protobuf:"bytes,5,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	Address    string `protobuf:"bytes,6,opt,name=address,proto3" json:"address,omitempty"`
	ChainId    string `protobuf:"bytes,7,opt,name=chain_id,json=chainId,proto3" json:"chain_id,omitempty"`
}

func (x *Token) Reset() {
	*x = Token{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_wallet_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Token) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Token) ProtoMessage() {}

func (x *Token) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Token.ProtoReflect.Descriptor instead.
func (*Token) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{19}
}

func (x *Token) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *Token) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Token) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *Token) GetDecimals() int64 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *Token) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *Token) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *Token) GetChainId() string {
	if x != nil {
		return x.ChainId
	}
	return ""
}

type TokenListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*Token `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Count int64    `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *TokenListReply) Reset() {
	*x = TokenListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_wallet_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TokenListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenListReply) ProtoMessage() {}

func (x *TokenListReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenListReply.ProtoReflect.Descriptor instead.
func (*TokenListReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{20}
}

func (x *TokenListReply) GetList() []*Token {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *TokenListReply) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type ReportInternalTxnReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// chainIndex
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// hash
	Txn string `protobuf:"bytes,3,opt,name=txn,proto3" json:"txn,omitempty"`
}

func (x *ReportInternalTxnReq) Reset() {
	*x = ReportInternalTxnReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_wallet_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportInternalTxnReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportInternalTxnReq) ProtoMessage() {}

func (x *ReportInternalTxnReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportInternalTxnReq.ProtoReflect.Descriptor instead.
func (*ReportInternalTxnReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{21}
}

func (x *ReportInternalTxnReq) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *ReportInternalTxnReq) GetTxn() string {
	if x != nil {
		return x.Txn
	}
	return ""
}

var File_api_wallet_v1_wallet_proto protoreflect.FileDescriptor

var file_api_wallet_v1_wallet_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2f, 0x76, 0x31, 0x2f,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x61, 0x70,
	0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x62, 0x75, 0x66,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xf1, 0x01, 0x0a, 0x0f, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x57, 0x69, 0x74,
	0x68, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x64,
	0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64,
	0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x6f, 0x67, 0x6f, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x6f, 0x67, 0x6f, 0x55,
	0x72, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x19, 0x0a, 0x08,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x64, 0x22, 0x4d, 0x0a, 0x17, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x54, 0x78, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x32, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x5e, 0x0a, 0x13, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x34, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x26, 0x0a,
	0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x42, 0x08, 0xba, 0x48, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x09, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x5d, 0x0a, 0x15, 0x51, 0x75, 0x65, 0x72, 0x79, 0x54,
	0x78, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x12,
	0x44, 0x0a, 0x06, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x34, 0x43, 0x68,
	0x61, 0x69, 0x6e, 0x42, 0x08, 0xba, 0x48, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x06, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x73, 0x22, 0x61, 0x0a, 0x0d, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x21, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x2d, 0x0a, 0x0d, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03,
	0x42, 0x08, 0xba, 0x48, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0c, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x65, 0x73, 0x22, 0x5b, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12,
	0x44, 0x0a, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x42, 0x08, 0xba, 0x48, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x09, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x65, 0x73, 0x22, 0xe3, 0x01, 0x0a, 0x0c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f,
	0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x1a, 0x0a,
	0x08, 0x64, 0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x64, 0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x61, 0x77,
	0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x72, 0x61, 0x77, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6c, 0x6f, 0x67, 0x6f, 0x55, 0x72, 0x6c, 0x22, 0x62, 0x0a, 0x0d, 0x57,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x37, 0x0a, 0x08, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x08, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x22,
	0x49, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x30, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x48, 0x0a, 0x0b, 0x47, 0x65,
	0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x22, 0x10, 0x0a, 0x0e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x22, 0x3e, 0x0a, 0x10, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2a, 0x0a, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xa9, 0x01, 0x0a, 0x0f, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x68,
	0x61, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x21, 0x0a, 0x07, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x12, 0x1f, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x09, 0xba, 0x48, 0x06, 0x22, 0x04, 0x18, 0x64, 0x20, 0x00, 0x52, 0x05, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x22, 0x59, 0x0a, 0x11, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2e, 0x0a,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xab, 0x03,
	0x0a, 0x18, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x79,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x68,
	0x61, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x21, 0x0a, 0x0c, 0x66,
	0x72, 0x6f, 0x6d, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x66, 0x72, 0x6f, 0x6d, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1d,
	0x0a, 0x0a, 0x74, 0x6f, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x74, 0x6f, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x22, 0x0a,
	0x0a, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x88, 0x01,
	0x01, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1f, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x09, 0xba, 0x48, 0x06, 0x22, 0x04, 0x18, 0x64, 0x20, 0x00, 0x52,
	0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x3a, 0xc3, 0x01, 0xba, 0x48, 0xbf, 0x01, 0x1a, 0xbc, 0x01,
	0x0a, 0x1b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x62,
	0x79, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x72, 0x65, 0x71, 0x12, 0x30, 0x66,
	0x72, 0x6f, 0x6d, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x20, 0x61, 0x6e, 0x64, 0x20,
	0x74, 0x6f, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x20, 0x63, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x20, 0x62, 0x6f, 0x74, 0x68, 0x20, 0x62, 0x65, 0x20, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x1a,
	0x6b, 0x21, 0x28, 0x28, 0x21, 0x68, 0x61, 0x73, 0x28, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x66, 0x72,
	0x6f, 0x6d, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x29, 0x20, 0x7c, 0x7c, 0x20, 0x74,
	0x68, 0x69, 0x73, 0x2e, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x20, 0x3d, 0x3d, 0x20, 0x27, 0x27, 0x29, 0x20, 0x26, 0x26, 0x20, 0x28, 0x21, 0x68, 0x61, 0x73,
	0x28, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x74, 0x6f, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x29, 0x20, 0x7c, 0x7c, 0x20, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x74, 0x6f, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x20, 0x3d, 0x3d, 0x20, 0x27, 0x27, 0x29, 0x29, 0x42, 0x0d, 0x0a, 0x0b,
	0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x22, 0x4c, 0x0a, 0x0e, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x19,
	0x0a, 0x03, 0x74, 0x78, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x03, 0x74, 0x78, 0x6e, 0x22, 0xba, 0x02, 0x0a, 0x0b, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x78, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x78, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x66,
	0x72, 0x6f, 0x6d, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x66, 0x72, 0x6f, 0x6d, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1d,
	0x0a, 0x0a, 0x74, 0x6f, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x74, 0x6f, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x66, 0x65, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x66, 0x65, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x93, 0x03, 0x0a, 0x07, 0x4e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12,
	0x19, 0x0a, 0x08, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65,
	0x63, 0x69, 0x6d, 0x61, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x65,
	0x63, 0x69, 0x6d, 0x61, 0x6c, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a,
	0x09, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x78,
	0x70, 0x6c, 0x6f, 0x72, 0x65, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x28, 0x0a,
	0x10, 0x67, 0x61, 0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x79, 0x6d, 0x62, 0x6f,
	0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x67, 0x61, 0x73, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x73, 0x6f, 0x72, 0x74, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x73, 0x6f, 0x72, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x88, 0x01, 0x0a,
	0x0c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x23, 0x0a,
	0x0d, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x1b, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xba, 0x48, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x20, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x22, 0x05, 0x18, 0xc8, 0x01, 0x20, 0x00,
	0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0xc0, 0x01, 0x0a, 0x05, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x64,
	0x65, 0x78, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x1a,
	0x0a, 0x08, 0x64, 0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x64, 0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x6f,
	0x67, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x6f,
	0x67, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x19, 0x0a, 0x08, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x64, 0x22, 0x50, 0x0a, 0x0e, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x28, 0x0a, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x52, 0x0a, 0x14,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x78,
	0x6e, 0x52, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x6e,
	0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x19, 0x0a, 0x03, 0x74, 0x78, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x03, 0x74, 0x78, 0x6e,
	0x32, 0x84, 0x08, 0x0a, 0x09, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x53, 0x72, 0x76, 0x12, 0x66,
	0x0a, 0x0b, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1d, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x17, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x11, 0x12, 0x0f, 0x2f, 0x76, 0x31, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x6d, 0x0a, 0x0c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15,
	0x12, 0x13, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x85, 0x01, 0x0a, 0x15, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x79, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x1b, 0x12, 0x19, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x42, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x69, 0x0a,
	0x0f, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a,
	0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x1b, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x15, 0x12, 0x13, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x5e, 0x0a, 0x09, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c,
	0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x15, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0f, 0x12, 0x0d, 0x2f, 0x76, 0x31, 0x2f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x52, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71,
	0x1a, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x14, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0e, 0x12, 0x0c,
	0x2f, 0x76, 0x31, 0x2f, 0x67, 0x65, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x7d, 0x0a, 0x10,
	0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x12, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1f, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x19, 0x3a, 0x01, 0x2a, 0x22, 0x14, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x85, 0x01, 0x0a, 0x12,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x54, 0x78, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x54, 0x78, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x54, 0x78,
	0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x3a, 0x01, 0x2a, 0x22, 0x16, 0x2f, 0x76, 0x31,
	0x2f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x54, 0x78, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x72, 0x0a, 0x11, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x78, 0x6e, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x78, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x3a, 0x01, 0x2a,
	0x22, 0x15, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x54, 0x78, 0x6e, 0x42, 0x93, 0x01, 0x0a, 0x11, 0x63, 0x6f, 0x6d, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x42, 0x0b, 0x57,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x1b, 0x62, 0x79,
	0x64, 0x5f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0xa2, 0x02, 0x03, 0x41, 0x57, 0x58, 0xaa,
	0x02, 0x0d, 0x41, 0x70, 0x69, 0x2e, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x56, 0x31, 0xca,
	0x02, 0x0d, 0x41, 0x70, 0x69, 0x5c, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x5c, 0x56, 0x31, 0xe2,
	0x02, 0x19, 0x41, 0x70, 0x69, 0x5c, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x5c, 0x56, 0x31, 0x5c,
	0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0xea, 0x02, 0x0f, 0x41, 0x70,
	0x69, 0x3a, 0x3a, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x3a, 0x3a, 0x56, 0x31, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_wallet_v1_wallet_proto_rawDescOnce sync.Once
	file_api_wallet_v1_wallet_proto_rawDescData = file_api_wallet_v1_wallet_proto_rawDesc
)

func file_api_wallet_v1_wallet_proto_rawDescGZIP() []byte {
	file_api_wallet_v1_wallet_proto_rawDescOnce.Do(func() {
		file_api_wallet_v1_wallet_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_wallet_v1_wallet_proto_rawDescData)
	})
	return file_api_wallet_v1_wallet_proto_rawDescData
}

var file_api_wallet_v1_wallet_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_api_wallet_v1_wallet_proto_goTypes = []any{
	(*TokenWithWallet)(nil),          // 0: api.wallet.v1.TokenWithWallet
	(*QueryTxLatestTokenReply)(nil),  // 1: api.wallet.v1.QueryTxLatestTokenReply
	(*WalletAddress4Chain)(nil),      // 2: api.wallet.v1.WalletAddress4Chain
	(*QueryTxLatestTokenReq)(nil),    // 3: api.wallet.v1.QueryTxLatestTokenReq
	(*WalletAddress)(nil),            // 4: api.wallet.v1.WalletAddress
	(*ListTokenBalanceReq)(nil),      // 5: api.wallet.v1.ListTokenBalanceReq
	(*TokenBalance)(nil),             // 6: api.wallet.v1.TokenBalance
	(*WalletBalance)(nil),            // 7: api.wallet.v1.WalletBalance
	(*ListTokenBalanceReply)(nil),    // 8: api.wallet.v1.ListTokenBalanceReply
	(*GetTokenReq)(nil),              // 9: api.wallet.v1.GetTokenReq
	(*NetworkListReq)(nil),           // 10: api.wallet.v1.NetworkListReq
	(*NetworkListReply)(nil),         // 11: api.wallet.v1.NetworkListReply
	(*TransactionsReq)(nil),          // 12: api.wallet.v1.TransactionsReq
	(*TransactionsReply)(nil),        // 13: api.wallet.v1.TransactionsReply
	(*TransactionsByAddressReq)(nil), // 14: api.wallet.v1.TransactionsByAddressReq
	(*TransactionReq)(nil),           // 15: api.wallet.v1.TransactionReq
	(*Transaction)(nil),              // 16: api.wallet.v1.Transaction
	(*Network)(nil),                  // 17: api.wallet.v1.Network
	(*TokenListReq)(nil),             // 18: api.wallet.v1.TokenListReq
	(*Token)(nil),                    // 19: api.wallet.v1.Token
	(*TokenListReply)(nil),           // 20: api.wallet.v1.TokenListReply
	(*ReportInternalTxnReq)(nil),     // 21: api.wallet.v1.ReportInternalTxnReq
	(*emptypb.Empty)(nil),            // 22: google.protobuf.Empty
}
var file_api_wallet_v1_wallet_proto_depIdxs = []int32{
	0,  // 0: api.wallet.v1.QueryTxLatestTokenReply.list:type_name -> api.wallet.v1.TokenWithWallet
	2,  // 1: api.wallet.v1.QueryTxLatestTokenReq.chains:type_name -> api.wallet.v1.WalletAddress4Chain
	4,  // 2: api.wallet.v1.ListTokenBalanceReq.addresses:type_name -> api.wallet.v1.WalletAddress
	6,  // 3: api.wallet.v1.WalletBalance.balances:type_name -> api.wallet.v1.TokenBalance
	7,  // 4: api.wallet.v1.ListTokenBalanceReply.list:type_name -> api.wallet.v1.WalletBalance
	17, // 5: api.wallet.v1.NetworkListReply.list:type_name -> api.wallet.v1.Network
	16, // 6: api.wallet.v1.TransactionsReply.list:type_name -> api.wallet.v1.Transaction
	19, // 7: api.wallet.v1.TokenListReply.list:type_name -> api.wallet.v1.Token
	10, // 8: api.wallet.v1.WalletSrv.NetworkList:input_type -> api.wallet.v1.NetworkListReq
	12, // 9: api.wallet.v1.WalletSrv.Transactions:input_type -> api.wallet.v1.TransactionsReq
	14, // 10: api.wallet.v1.WalletSrv.TransactionsByAddress:input_type -> api.wallet.v1.TransactionsByAddressReq
	15, // 11: api.wallet.v1.WalletSrv.TransactionInfo:input_type -> api.wallet.v1.TransactionReq
	18, // 12: api.wallet.v1.WalletSrv.TokenList:input_type -> api.wallet.v1.TokenListReq
	9,  // 13: api.wallet.v1.WalletSrv.GetToken:input_type -> api.wallet.v1.GetTokenReq
	5,  // 14: api.wallet.v1.WalletSrv.ListTokenBalance:input_type -> api.wallet.v1.ListTokenBalanceReq
	3,  // 15: api.wallet.v1.WalletSrv.QueryTxLatestToken:input_type -> api.wallet.v1.QueryTxLatestTokenReq
	21, // 16: api.wallet.v1.WalletSrv.ReportInternalTxn:input_type -> api.wallet.v1.ReportInternalTxnReq
	11, // 17: api.wallet.v1.WalletSrv.NetworkList:output_type -> api.wallet.v1.NetworkListReply
	13, // 18: api.wallet.v1.WalletSrv.Transactions:output_type -> api.wallet.v1.TransactionsReply
	13, // 19: api.wallet.v1.WalletSrv.TransactionsByAddress:output_type -> api.wallet.v1.TransactionsReply
	16, // 20: api.wallet.v1.WalletSrv.TransactionInfo:output_type -> api.wallet.v1.Transaction
	20, // 21: api.wallet.v1.WalletSrv.TokenList:output_type -> api.wallet.v1.TokenListReply
	19, // 22: api.wallet.v1.WalletSrv.GetToken:output_type -> api.wallet.v1.Token
	8,  // 23: api.wallet.v1.WalletSrv.ListTokenBalance:output_type -> api.wallet.v1.ListTokenBalanceReply
	1,  // 24: api.wallet.v1.WalletSrv.QueryTxLatestToken:output_type -> api.wallet.v1.QueryTxLatestTokenReply
	22, // 25: api.wallet.v1.WalletSrv.ReportInternalTxn:output_type -> google.protobuf.Empty
	17, // [17:26] is the sub-list for method output_type
	8,  // [8:17] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_api_wallet_v1_wallet_proto_init() }
func file_api_wallet_v1_wallet_proto_init() {
	if File_api_wallet_v1_wallet_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_wallet_v1_wallet_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*TokenWithWallet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_wallet_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*QueryTxLatestTokenReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_wallet_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*WalletAddress4Chain); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_wallet_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*QueryTxLatestTokenReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_wallet_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*WalletAddress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_wallet_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*ListTokenBalanceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_wallet_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*TokenBalance); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_wallet_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*WalletBalance); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_wallet_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*ListTokenBalanceReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_wallet_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*GetTokenReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_wallet_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*NetworkListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_wallet_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*NetworkListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_wallet_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*TransactionsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_wallet_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*TransactionsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_wallet_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*TransactionsByAddressReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_wallet_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*TransactionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_wallet_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*Transaction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_wallet_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*Network); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_wallet_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*TokenListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_wallet_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*Token); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_wallet_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*TokenListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_wallet_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*ReportInternalTxnReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_wallet_v1_wallet_proto_msgTypes[14].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_wallet_v1_wallet_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_wallet_v1_wallet_proto_goTypes,
		DependencyIndexes: file_api_wallet_v1_wallet_proto_depIdxs,
		MessageInfos:      file_api_wallet_v1_wallet_proto_msgTypes,
	}.Build()
	File_api_wallet_v1_wallet_proto = out.File
	file_api_wallet_v1_wallet_proto_rawDesc = nil
	file_api_wallet_v1_wallet_proto_goTypes = nil
	file_api_wallet_v1_wallet_proto_depIdxs = nil
}
