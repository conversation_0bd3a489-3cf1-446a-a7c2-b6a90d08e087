syntax = "proto3";

package api.wallet.v1;

option go_package = "byd_wallet/api/wallet/v1;v1";

import "google/api/annotations.proto";
import "buf/validate/validate.proto";

service GasPoolSrv {
  // 可充值币种列表
  rpc ListGasPoolDepositToken(ListGasPoolDepositTokenReq) returns (ListGasPoolDepositTokenReply) {
    option (google.api.http) = {
      get: "/v1/gaspool/deposit_tokens"
    };
  }

  // 查询GasPool余额
  rpc GetGasPoolBalance(GetGasPoolBalanceReq) returns (GetGasPoolBalanceReply) {
    option (google.api.http) = {
      get: "/v1/gaspool/balance"
    };
  }

  // GasPool消费记录列表
  rpc ListGasPoolConsumeRecord(ListGasPoolConsumeRecordReq) returns (ListGasPoolConsumeRecordReply) {
    option (google.api.http) = {
      get: "/v1/gaspool/consume_records"
    };
  }

  // GasPool财务记录列表
  rpc ListGasPoolCashFlowRecord(ListGasPoolCashFlowRecordReq) returns (ListGasPoolCashFlowRecordReply) {
    option (google.api.http) = {
      get: "/v1/gaspool/cash_flow_records"
    };
  }

  // 获取paymaster address
  rpc GetPaymaster(GetPaymasterReq) returns (GetPaymasterReply) {
    option (google.api.http) = {
      get: "/v1/gaspool/paymaster"
    };
  }

  // 发送交易
  rpc SendTx(SendTxReq) returns (SendTxReply) {
    option (google.api.http) = {
      post: "/v1/gaspool/send_tx"
      body: "*"
    };
  }

}

message SendTxReq {
  // 链索引
  int64 chain_index = 1;
  // 原始交易
  string raw_tx = 2;
  // 交易类型 (
  // deposit:充值,deposit_pre_reduce_gas:预扣GasPool充值,
  // transfer:转账,swap:兑换
  // )
  string tx_type = 3;
}

message SendTxReply {
  string tx_hash = 1;
}

message GasPoolDepositToken {
  // 币名称
  string name = 1;
  // 币符号
  string symbol = 2;
  // 币精度
  int64 decimals = 3;
  // 币图标
  string logo_url = 4;
  // 币地址
  string address = 5;
  // 链索引
  int64 chain_index = 6;
  // 链ID
  string chain_id = 7;
  // 最小充值金额
  string min_deposit_amount = 8;
  // 充值地址
  string deposit_address = 9;
}

message ListGasPoolDepositTokenReq {}

message ListGasPoolDepositTokenReply {
  repeated GasPoolDepositToken list = 1;
}

message GetGasPoolBalanceReq {
  string wallet_id = 1 [(buf.validate.field).string.min_len = 1];
}

message GasPoolUserStats {
  // 充值额度
  string total_deposit_amount = 1;
  // 累计消耗
  string total_reduce_amount = 2;
  // 积分额度 // 暂无
  string total_credit_amount = 3;
  // 累计使用次数
  int64 total_reduce_count = 4;
  // 支持公链数
  int64 chain_count = 5;
}

message GetGasPoolBalanceReply {
  // 余额
  string balance = 1;
  // 统计数据
  GasPoolUserStats stats = 2;
}

message ListGasPoolConsumeRecordReq {
  string wallet_id = 1 [(buf.validate.field).string.min_len = 1];
  int64 page = 2 [(buf.validate.field).int64.gt = 0];
  int64 limit = 3 [(buf.validate.field).int64 = {
    gt: 0
    lte: 100
  }];
}

message GasPoolConsumeRecord {
  // 创建时间(时间戳,秒)
  int64 created_at = 1;
  // 链索引
  int64 chain_index = 2;
  // 记录状态(success:执行成功, fail:执行失败, pending:执行中)
  string status = 3;
  // 用户交易hash
  string tx_hash = 4;
  // GasPool扣款金额(USDT)
  string amount = 5;
  // 记录类型(transfer:转账, swap:兑换, deposit_without_gas:充值预扣)
  string record_type = 6;
}

message ListGasPoolConsumeRecordReply {
  int64 total_count = 1;
  repeated GasPoolConsumeRecord list = 2;
}


message ListGasPoolCashFlowRecordReq {
  string wallet_id = 1 [(buf.validate.field).string.min_len = 1];
  int64 page = 2 [(buf.validate.field).int64.gt = 0];
  int64 limit = 3 [(buf.validate.field).int64 = {
    gt: 0
    lte: 100
  }];
}

message GasPoolCashFlowRecord {
  // 创建时间(时间戳,秒)
  int64 created_at = 1;
  // 链索引
  int64 chain_index = 2;
  // 币合约地址(矿币为空)
  string token_address = 3;
  // 用户交易hash
  string tx_hash = 4;
  // GasPool扣款金额(USDT)
  string amount = 5;
  // 记录类型(deposit:充值, refund:退回)
  string record_type = 6;
}

message ListGasPoolCashFlowRecordReply {
  int64 total_count = 1;
  repeated GasPoolCashFlowRecord list = 2;
  repeated GasPoolDepositToken token_list = 3;
}

message GetPaymasterReq {
  // 链索引
  int64 chain_index = 1;
}
message GetPaymasterReply {
  string address = 1;
}