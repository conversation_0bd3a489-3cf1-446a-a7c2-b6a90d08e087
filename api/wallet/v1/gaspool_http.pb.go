// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.0
// - protoc             (unknown)
// source: api/wallet/v1/gaspool.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationGasPoolSrvGetGasPoolBalance = "/api.wallet.v1.GasPoolSrv/GetGasPoolBalance"
const OperationGasPoolSrvGetGasPoolTokenPrice = "/api.wallet.v1.GasPoolSrv/GetGasPoolTokenPrice"
const OperationGasPoolSrvGetPaymaster = "/api.wallet.v1.GasPoolSrv/GetPaymaster"
const OperationGasPoolSrvListGasPoolCashFlowRecord = "/api.wallet.v1.GasPoolSrv/ListGasPoolCashFlowRecord"
const OperationGasPoolSrvListGasPoolConsumeRecord = "/api.wallet.v1.GasPoolSrv/ListGasPoolConsumeRecord"
const OperationGasPoolSrvListGasPoolDepositToken = "/api.wallet.v1.GasPoolSrv/ListGasPoolDepositToken"
const OperationGasPoolSrvSendTx = "/api.wallet.v1.GasPoolSrv/SendTx"

type GasPoolSrvHTTPServer interface {
	// GetGasPoolBalance 查询GasPool余额
	GetGasPoolBalance(context.Context, *GetGasPoolBalanceReq) (*GetGasPoolBalanceReply, error)
	// GetGasPoolTokenPrice 获取GasPool币种价格
	GetGasPoolTokenPrice(context.Context, *GetGasPoolTokenPriceReq) (*GetGasPoolTokenPriceReply, error)
	// GetPaymaster 获取paymaster address
	GetPaymaster(context.Context, *GetPaymasterReq) (*GetPaymasterReply, error)
	// ListGasPoolCashFlowRecord GasPool财务记录列表
	ListGasPoolCashFlowRecord(context.Context, *ListGasPoolCashFlowRecordReq) (*ListGasPoolCashFlowRecordReply, error)
	// ListGasPoolConsumeRecord GasPool消费记录列表
	ListGasPoolConsumeRecord(context.Context, *ListGasPoolConsumeRecordReq) (*ListGasPoolConsumeRecordReply, error)
	// ListGasPoolDepositToken 可充值币种列表
	ListGasPoolDepositToken(context.Context, *ListGasPoolDepositTokenReq) (*ListGasPoolDepositTokenReply, error)
	// SendTx 发送交易
	SendTx(context.Context, *SendTxReq) (*SendTxReply, error)
}

func RegisterGasPoolSrvHTTPServer(s *http.Server, srv GasPoolSrvHTTPServer) {
	r := s.Route("/")
	r.GET("/v1/gaspool/deposit_tokens", _GasPoolSrv_ListGasPoolDepositToken0_HTTP_Handler(srv))
	r.GET("/v1/gaspool/balance", _GasPoolSrv_GetGasPoolBalance0_HTTP_Handler(srv))
	r.GET("/v1/gaspool/consume_records", _GasPoolSrv_ListGasPoolConsumeRecord0_HTTP_Handler(srv))
	r.GET("/v1/gaspool/cash_flow_records", _GasPoolSrv_ListGasPoolCashFlowRecord0_HTTP_Handler(srv))
	r.GET("/v1/gaspool/paymaster", _GasPoolSrv_GetPaymaster0_HTTP_Handler(srv))
	r.POST("/v1/gaspool/send_tx", _GasPoolSrv_SendTx0_HTTP_Handler(srv))
	r.GET("/v1/gaspool/token_price", _GasPoolSrv_GetGasPoolTokenPrice0_HTTP_Handler(srv))
}

func _GasPoolSrv_ListGasPoolDepositToken0_HTTP_Handler(srv GasPoolSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListGasPoolDepositTokenReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGasPoolSrvListGasPoolDepositToken)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListGasPoolDepositToken(ctx, req.(*ListGasPoolDepositTokenReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListGasPoolDepositTokenReply)
		return ctx.Result(200, reply)
	}
}

func _GasPoolSrv_GetGasPoolBalance0_HTTP_Handler(srv GasPoolSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetGasPoolBalanceReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGasPoolSrvGetGasPoolBalance)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetGasPoolBalance(ctx, req.(*GetGasPoolBalanceReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetGasPoolBalanceReply)
		return ctx.Result(200, reply)
	}
}

func _GasPoolSrv_ListGasPoolConsumeRecord0_HTTP_Handler(srv GasPoolSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListGasPoolConsumeRecordReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGasPoolSrvListGasPoolConsumeRecord)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListGasPoolConsumeRecord(ctx, req.(*ListGasPoolConsumeRecordReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListGasPoolConsumeRecordReply)
		return ctx.Result(200, reply)
	}
}

func _GasPoolSrv_ListGasPoolCashFlowRecord0_HTTP_Handler(srv GasPoolSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListGasPoolCashFlowRecordReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGasPoolSrvListGasPoolCashFlowRecord)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListGasPoolCashFlowRecord(ctx, req.(*ListGasPoolCashFlowRecordReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListGasPoolCashFlowRecordReply)
		return ctx.Result(200, reply)
	}
}

func _GasPoolSrv_GetPaymaster0_HTTP_Handler(srv GasPoolSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetPaymasterReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGasPoolSrvGetPaymaster)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetPaymaster(ctx, req.(*GetPaymasterReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetPaymasterReply)
		return ctx.Result(200, reply)
	}
}

func _GasPoolSrv_SendTx0_HTTP_Handler(srv GasPoolSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SendTxReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGasPoolSrvSendTx)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SendTx(ctx, req.(*SendTxReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SendTxReply)
		return ctx.Result(200, reply)
	}
}

func _GasPoolSrv_GetGasPoolTokenPrice0_HTTP_Handler(srv GasPoolSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetGasPoolTokenPriceReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGasPoolSrvGetGasPoolTokenPrice)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetGasPoolTokenPrice(ctx, req.(*GetGasPoolTokenPriceReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetGasPoolTokenPriceReply)
		return ctx.Result(200, reply)
	}
}

type GasPoolSrvHTTPClient interface {
	GetGasPoolBalance(ctx context.Context, req *GetGasPoolBalanceReq, opts ...http.CallOption) (rsp *GetGasPoolBalanceReply, err error)
	GetGasPoolTokenPrice(ctx context.Context, req *GetGasPoolTokenPriceReq, opts ...http.CallOption) (rsp *GetGasPoolTokenPriceReply, err error)
	GetPaymaster(ctx context.Context, req *GetPaymasterReq, opts ...http.CallOption) (rsp *GetPaymasterReply, err error)
	ListGasPoolCashFlowRecord(ctx context.Context, req *ListGasPoolCashFlowRecordReq, opts ...http.CallOption) (rsp *ListGasPoolCashFlowRecordReply, err error)
	ListGasPoolConsumeRecord(ctx context.Context, req *ListGasPoolConsumeRecordReq, opts ...http.CallOption) (rsp *ListGasPoolConsumeRecordReply, err error)
	ListGasPoolDepositToken(ctx context.Context, req *ListGasPoolDepositTokenReq, opts ...http.CallOption) (rsp *ListGasPoolDepositTokenReply, err error)
	SendTx(ctx context.Context, req *SendTxReq, opts ...http.CallOption) (rsp *SendTxReply, err error)
}

type GasPoolSrvHTTPClientImpl struct {
	cc *http.Client
}

func NewGasPoolSrvHTTPClient(client *http.Client) GasPoolSrvHTTPClient {
	return &GasPoolSrvHTTPClientImpl{client}
}

func (c *GasPoolSrvHTTPClientImpl) GetGasPoolBalance(ctx context.Context, in *GetGasPoolBalanceReq, opts ...http.CallOption) (*GetGasPoolBalanceReply, error) {
	var out GetGasPoolBalanceReply
	pattern := "/v1/gaspool/balance"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGasPoolSrvGetGasPoolBalance))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GasPoolSrvHTTPClientImpl) GetGasPoolTokenPrice(ctx context.Context, in *GetGasPoolTokenPriceReq, opts ...http.CallOption) (*GetGasPoolTokenPriceReply, error) {
	var out GetGasPoolTokenPriceReply
	pattern := "/v1/gaspool/token_price"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGasPoolSrvGetGasPoolTokenPrice))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GasPoolSrvHTTPClientImpl) GetPaymaster(ctx context.Context, in *GetPaymasterReq, opts ...http.CallOption) (*GetPaymasterReply, error) {
	var out GetPaymasterReply
	pattern := "/v1/gaspool/paymaster"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGasPoolSrvGetPaymaster))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GasPoolSrvHTTPClientImpl) ListGasPoolCashFlowRecord(ctx context.Context, in *ListGasPoolCashFlowRecordReq, opts ...http.CallOption) (*ListGasPoolCashFlowRecordReply, error) {
	var out ListGasPoolCashFlowRecordReply
	pattern := "/v1/gaspool/cash_flow_records"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGasPoolSrvListGasPoolCashFlowRecord))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GasPoolSrvHTTPClientImpl) ListGasPoolConsumeRecord(ctx context.Context, in *ListGasPoolConsumeRecordReq, opts ...http.CallOption) (*ListGasPoolConsumeRecordReply, error) {
	var out ListGasPoolConsumeRecordReply
	pattern := "/v1/gaspool/consume_records"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGasPoolSrvListGasPoolConsumeRecord))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GasPoolSrvHTTPClientImpl) ListGasPoolDepositToken(ctx context.Context, in *ListGasPoolDepositTokenReq, opts ...http.CallOption) (*ListGasPoolDepositTokenReply, error) {
	var out ListGasPoolDepositTokenReply
	pattern := "/v1/gaspool/deposit_tokens"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGasPoolSrvListGasPoolDepositToken))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GasPoolSrvHTTPClientImpl) SendTx(ctx context.Context, in *SendTxReq, opts ...http.CallOption) (*SendTxReply, error) {
	var out SendTxReply
	pattern := "/v1/gaspool/send_tx"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGasPoolSrvSendTx))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
