// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: api/wallet/v1/dapp.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ListApprovedAddressesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Addresses []*UserAddress `protobuf:"bytes,1,rep,name=addresses,proto3" json:"addresses,omitempty"`
}

func (x *ListApprovedAddressesReq) Reset() {
	*x = ListApprovedAddressesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_dapp_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListApprovedAddressesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListApprovedAddressesReq) ProtoMessage() {}

func (x *ListApprovedAddressesReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListApprovedAddressesReq.ProtoReflect.Descriptor instead.
func (*ListApprovedAddressesReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{0}
}

func (x *ListApprovedAddressesReq) GetAddresses() []*UserAddress {
	if x != nil {
		return x.Addresses
	}
	return nil
}

type ListApprovedAddressesReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*UserAddress `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListApprovedAddressesReply) Reset() {
	*x = ListApprovedAddressesReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_dapp_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListApprovedAddressesReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListApprovedAddressesReply) ProtoMessage() {}

func (x *ListApprovedAddressesReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListApprovedAddressesReply.ProtoReflect.Descriptor instead.
func (*ListApprovedAddressesReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{1}
}

func (x *ListApprovedAddressesReply) GetList() []*UserAddress {
	if x != nil {
		return x.List
	}
	return nil
}

type ListTokenApprovalsByDappReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SpenderAddress string `protobuf:"bytes,1,opt,name=spender_address,json=spenderAddress,proto3" json:"spender_address,omitempty"`
	ChainIndex     int64  `protobuf:"varint,2,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 用户钱包地址
	Addresses []string `protobuf:"bytes,3,rep,name=addresses,proto3" json:"addresses,omitempty"`
}

func (x *ListTokenApprovalsByDappReq) Reset() {
	*x = ListTokenApprovalsByDappReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_dapp_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTokenApprovalsByDappReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTokenApprovalsByDappReq) ProtoMessage() {}

func (x *ListTokenApprovalsByDappReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTokenApprovalsByDappReq.ProtoReflect.Descriptor instead.
func (*ListTokenApprovalsByDappReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{2}
}

func (x *ListTokenApprovalsByDappReq) GetSpenderAddress() string {
	if x != nil {
		return x.SpenderAddress
	}
	return ""
}

func (x *ListTokenApprovalsByDappReq) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *ListTokenApprovalsByDappReq) GetAddresses() []string {
	if x != nil {
		return x.Addresses
	}
	return nil
}

type ListTokenApprovalsByDappReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*TokenApproval `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListTokenApprovalsByDappReply) Reset() {
	*x = ListTokenApprovalsByDappReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_dapp_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTokenApprovalsByDappReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTokenApprovalsByDappReply) ProtoMessage() {}

func (x *ListTokenApprovalsByDappReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTokenApprovalsByDappReply.ProtoReflect.Descriptor instead.
func (*ListTokenApprovalsByDappReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{3}
}

func (x *ListTokenApprovalsByDappReply) GetList() []*TokenApproval {
	if x != nil {
		return x.List
	}
	return nil
}

type ListApprovedDappsByUserAddressesReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*UserApprovedDapp `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListApprovedDappsByUserAddressesReply) Reset() {
	*x = ListApprovedDappsByUserAddressesReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_dapp_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListApprovedDappsByUserAddressesReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListApprovedDappsByUserAddressesReply) ProtoMessage() {}

func (x *ListApprovedDappsByUserAddressesReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListApprovedDappsByUserAddressesReply.ProtoReflect.Descriptor instead.
func (*ListApprovedDappsByUserAddressesReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{4}
}

func (x *ListApprovedDappsByUserAddressesReply) GetList() []*UserApprovedDapp {
	if x != nil {
		return x.List
	}
	return nil
}

type ListApprovalByUserAddressesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Addresses []*UserAddress `protobuf:"bytes,1,rep,name=addresses,proto3" json:"addresses,omitempty"`
}

func (x *ListApprovalByUserAddressesReq) Reset() {
	*x = ListApprovalByUserAddressesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_dapp_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListApprovalByUserAddressesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListApprovalByUserAddressesReq) ProtoMessage() {}

func (x *ListApprovalByUserAddressesReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListApprovalByUserAddressesReq.ProtoReflect.Descriptor instead.
func (*ListApprovalByUserAddressesReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{5}
}

func (x *ListApprovalByUserAddressesReq) GetAddresses() []*UserAddress {
	if x != nil {
		return x.Addresses
	}
	return nil
}

type UserAddress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Address    string `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	ChainIndex int64  `protobuf:"varint,2,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
}

func (x *UserAddress) Reset() {
	*x = UserAddress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_dapp_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAddress) ProtoMessage() {}

func (x *UserAddress) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAddress.ProtoReflect.Descriptor instead.
func (*UserAddress) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{6}
}

func (x *UserAddress) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *UserAddress) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

type ListApprovalByUserAddressReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户钱包地址
	Address    string `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	ChainIndex int64  `protobuf:"varint,2,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
}

func (x *ListApprovalByUserAddressReq) Reset() {
	*x = ListApprovalByUserAddressReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_dapp_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListApprovalByUserAddressReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListApprovalByUserAddressReq) ProtoMessage() {}

func (x *ListApprovalByUserAddressReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListApprovalByUserAddressReq.ProtoReflect.Descriptor instead.
func (*ListApprovalByUserAddressReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{7}
}

func (x *ListApprovalByUserAddressReq) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *ListApprovalByUserAddressReq) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

type ListApprovalByUserAddressReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Address *UserAddress         `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	Network *BlockchainNetwork   `protobuf:"bytes,2,opt,name=network,proto3" json:"network,omitempty"`
	List    []*TokenDappApproval `protobuf:"bytes,3,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListApprovalByUserAddressReply) Reset() {
	*x = ListApprovalByUserAddressReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_dapp_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListApprovalByUserAddressReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListApprovalByUserAddressReply) ProtoMessage() {}

func (x *ListApprovalByUserAddressReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListApprovalByUserAddressReply.ProtoReflect.Descriptor instead.
func (*ListApprovalByUserAddressReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{8}
}

func (x *ListApprovalByUserAddressReply) GetAddress() *UserAddress {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *ListApprovalByUserAddressReply) GetNetwork() *BlockchainNetwork {
	if x != nil {
		return x.Network
	}
	return nil
}

func (x *ListApprovalByUserAddressReply) GetList() []*TokenDappApproval {
	if x != nil {
		return x.List
	}
	return nil
}

type SearchDappReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 搜索关键词
	Key string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
}

func (x *SearchDappReq) Reset() {
	*x = SearchDappReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_dapp_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchDappReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchDappReq) ProtoMessage() {}

func (x *SearchDappReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchDappReq.ProtoReflect.Descriptor instead.
func (*SearchDappReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{9}
}

func (x *SearchDappReq) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type SearchDappReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*Dapp `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *SearchDappReply) Reset() {
	*x = SearchDappReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_dapp_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchDappReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchDappReply) ProtoMessage() {}

func (x *SearchDappReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchDappReply.ProtoReflect.Descriptor instead.
func (*SearchDappReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{10}
}

func (x *SearchDappReply) GetList() []*Dapp {
	if x != nil {
		return x.List
	}
	return nil
}

type GetDappCategoryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ChainIndex int64  `protobuf:"varint,2,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	ChainId    string `protobuf:"bytes,3,opt,name=chain_id,json=chainId,proto3" json:"chain_id,omitempty"`
}

func (x *GetDappCategoryReq) Reset() {
	*x = GetDappCategoryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_dapp_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDappCategoryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDappCategoryReq) ProtoMessage() {}

func (x *GetDappCategoryReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDappCategoryReq.ProtoReflect.Descriptor instead.
func (*GetDappCategoryReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{11}
}

func (x *GetDappCategoryReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetDappCategoryReq) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *GetDappCategoryReq) GetChainId() string {
	if x != nil {
		return x.ChainId
	}
	return ""
}

type GetDappCategoryReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*Dapp `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *GetDappCategoryReply) Reset() {
	*x = GetDappCategoryReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_dapp_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDappCategoryReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDappCategoryReply) ProtoMessage() {}

func (x *GetDappCategoryReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDappCategoryReply.ProtoReflect.Descriptor instead.
func (*GetDappCategoryReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{12}
}

func (x *GetDappCategoryReply) GetList() []*Dapp {
	if x != nil {
		return x.List
	}
	return nil
}

type GetDappTopicReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetDappTopicReq) Reset() {
	*x = GetDappTopicReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_dapp_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDappTopicReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDappTopicReq) ProtoMessage() {}

func (x *GetDappTopicReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDappTopicReq.ProtoReflect.Descriptor instead.
func (*GetDappTopicReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{13}
}

func (x *GetDappTopicReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetDappTopicReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Topic *DappTopic `protobuf:"bytes,1,opt,name=topic,proto3" json:"topic,omitempty"`
}

func (x *GetDappTopicReply) Reset() {
	*x = GetDappTopicReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_dapp_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDappTopicReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDappTopicReply) ProtoMessage() {}

func (x *GetDappTopicReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDappTopicReply.ProtoReflect.Descriptor instead.
func (*GetDappTopicReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{14}
}

func (x *GetDappTopicReply) GetTopic() *DappTopic {
	if x != nil {
		return x.Topic
	}
	return nil
}

type ListDappIndexReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListDappIndexReq) Reset() {
	*x = ListDappIndexReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_dapp_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDappIndexReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDappIndexReq) ProtoMessage() {}

func (x *ListDappIndexReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDappIndexReq.ProtoReflect.Descriptor instead.
func (*ListDappIndexReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{15}
}

type Dapp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       uint64               `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Logo     string               `protobuf:"bytes,2,opt,name=logo,proto3" json:"logo,omitempty"`
	Link     string               `protobuf:"bytes,3,opt,name=link,proto3" json:"link,omitempty"`
	Tags     string               `protobuf:"bytes,4,opt,name=tags,proto3" json:"tags,omitempty"`
	Hot      bool                 `protobuf:"varint,5,opt,name=hot,proto3" json:"hot,omitempty"`
	I18Ns    []*DappI18N          `protobuf:"bytes,6,rep,name=i18ns,proto3" json:"i18ns,omitempty"`
	Networks []*BlockchainNetwork `protobuf:"bytes,7,rep,name=networks,proto3" json:"networks,omitempty"`
}

func (x *Dapp) Reset() {
	*x = Dapp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_dapp_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Dapp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Dapp) ProtoMessage() {}

func (x *Dapp) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Dapp.ProtoReflect.Descriptor instead.
func (*Dapp) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{16}
}

func (x *Dapp) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Dapp) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *Dapp) GetLink() string {
	if x != nil {
		return x.Link
	}
	return ""
}

func (x *Dapp) GetTags() string {
	if x != nil {
		return x.Tags
	}
	return ""
}

func (x *Dapp) GetHot() bool {
	if x != nil {
		return x.Hot
	}
	return false
}

func (x *Dapp) GetI18Ns() []*DappI18N {
	if x != nil {
		return x.I18Ns
	}
	return nil
}

func (x *Dapp) GetNetworks() []*BlockchainNetwork {
	if x != nil {
		return x.Networks
	}
	return nil
}

type DappI18N struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Summary string `protobuf:"bytes,2,opt,name=summary,proto3" json:"summary,omitempty"`
}

func (x *DappI18N) Reset() {
	*x = DappI18N{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_dapp_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DappI18N) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DappI18N) ProtoMessage() {}

func (x *DappI18N) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DappI18N.ProtoReflect.Descriptor instead.
func (*DappI18N) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{17}
}

func (x *DappI18N) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DappI18N) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

type BlockchainNetwork struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChainId string `protobuf:"bytes,1,opt,name=chain_id,json=chainId,proto3" json:"chain_id,omitempty"`
}

func (x *BlockchainNetwork) Reset() {
	*x = BlockchainNetwork{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_dapp_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockchainNetwork) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockchainNetwork) ProtoMessage() {}

func (x *BlockchainNetwork) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockchainNetwork.ProtoReflect.Descriptor instead.
func (*BlockchainNetwork) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{18}
}

func (x *BlockchainNetwork) GetChainId() string {
	if x != nil {
		return x.ChainId
	}
	return ""
}

type DappCategory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       uint64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name     string  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Summary  string  `protobuf:"bytes,3,opt,name=summary,proto3" json:"summary,omitempty"`
	Language string  `protobuf:"bytes,4,opt,name=language,proto3" json:"language,omitempty"`
	Dapps    []*Dapp `protobuf:"bytes,5,rep,name=dapps,proto3" json:"dapps,omitempty"`
}

func (x *DappCategory) Reset() {
	*x = DappCategory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_dapp_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DappCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DappCategory) ProtoMessage() {}

func (x *DappCategory) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DappCategory.ProtoReflect.Descriptor instead.
func (*DappCategory) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{19}
}

func (x *DappCategory) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DappCategory) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DappCategory) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *DappCategory) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *DappCategory) GetDapps() []*Dapp {
	if x != nil {
		return x.Dapps
	}
	return nil
}

type DappTopic struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            uint64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	BackgroundUrl string  `protobuf:"bytes,2,opt,name=background_url,json=backgroundUrl,proto3" json:"background_url,omitempty"`
	Name          string  `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Summary       string  `protobuf:"bytes,4,opt,name=summary,proto3" json:"summary,omitempty"`
	Language      string  `protobuf:"bytes,5,opt,name=language,proto3" json:"language,omitempty"`
	Title         string  `protobuf:"bytes,6,opt,name=title,proto3" json:"title,omitempty"`
	TopTitle      string  `protobuf:"bytes,7,opt,name=top_title,json=topTitle,proto3" json:"top_title,omitempty"`
	BottomTitle   string  `protobuf:"bytes,8,opt,name=bottom_title,json=bottomTitle,proto3" json:"bottom_title,omitempty"`
	Dapps         []*Dapp `protobuf:"bytes,9,rep,name=dapps,proto3" json:"dapps,omitempty"`
}

func (x *DappTopic) Reset() {
	*x = DappTopic{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_dapp_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DappTopic) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DappTopic) ProtoMessage() {}

func (x *DappTopic) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DappTopic.ProtoReflect.Descriptor instead.
func (*DappTopic) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{20}
}

func (x *DappTopic) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DappTopic) GetBackgroundUrl() string {
	if x != nil {
		return x.BackgroundUrl
	}
	return ""
}

func (x *DappTopic) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DappTopic) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *DappTopic) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *DappTopic) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *DappTopic) GetTopTitle() string {
	if x != nil {
		return x.TopTitle
	}
	return ""
}

func (x *DappTopic) GetBottomTitle() string {
	if x != nil {
		return x.BottomTitle
	}
	return ""
}

func (x *DappTopic) GetDapps() []*Dapp {
	if x != nil {
		return x.Dapps
	}
	return nil
}

type DappIndex struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Topic    *DappTopic    `protobuf:"bytes,1,opt,name=topic,proto3" json:"topic,omitempty"`
	Category *DappCategory `protobuf:"bytes,2,opt,name=category,proto3" json:"category,omitempty"`
}

func (x *DappIndex) Reset() {
	*x = DappIndex{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_dapp_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DappIndex) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DappIndex) ProtoMessage() {}

func (x *DappIndex) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DappIndex.ProtoReflect.Descriptor instead.
func (*DappIndex) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{21}
}

func (x *DappIndex) GetTopic() *DappTopic {
	if x != nil {
		return x.Topic
	}
	return nil
}

func (x *DappIndex) GetCategory() *DappCategory {
	if x != nil {
		return x.Category
	}
	return nil
}

type ListDappIndexReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*DappIndex `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListDappIndexReply) Reset() {
	*x = ListDappIndexReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_dapp_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDappIndexReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDappIndexReply) ProtoMessage() {}

func (x *ListDappIndexReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDappIndexReply.ProtoReflect.Descriptor instead.
func (*ListDappIndexReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{22}
}

func (x *ListDappIndexReply) GetList() []*DappIndex {
	if x != nil {
		return x.List
	}
	return nil
}

type ListNavigationReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListNavigationReq) Reset() {
	*x = ListNavigationReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_dapp_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListNavigationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNavigationReq) ProtoMessage() {}

func (x *ListNavigationReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNavigationReq.ProtoReflect.Descriptor instead.
func (*ListNavigationReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{23}
}

type ListNavigationReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*DappNavigation `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListNavigationReply) Reset() {
	*x = ListNavigationReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_dapp_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListNavigationReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNavigationReply) ProtoMessage() {}

func (x *ListNavigationReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNavigationReply.ProtoReflect.Descriptor instead.
func (*ListNavigationReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{24}
}

func (x *ListNavigationReply) GetList() []*DappNavigation {
	if x != nil {
		return x.List
	}
	return nil
}

type DappNavigation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DappCategoryId uint64 `protobuf:"varint,1,opt,name=dapp_category_id,json=dappCategoryId,proto3" json:"dapp_category_id,omitempty"`
	Name           string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DappNavigation) Reset() {
	*x = DappNavigation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_dapp_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DappNavigation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DappNavigation) ProtoMessage() {}

func (x *DappNavigation) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DappNavigation.ProtoReflect.Descriptor instead.
func (*DappNavigation) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{25}
}

func (x *DappNavigation) GetDappCategoryId() uint64 {
	if x != nil {
		return x.DappCategoryId
	}
	return 0
}

func (x *DappNavigation) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type Approval struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChainIndex     int64  `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	OwnerAddress   string `protobuf:"bytes,2,opt,name=owner_address,json=ownerAddress,proto3" json:"owner_address,omitempty"`
	SpenderAddress string `protobuf:"bytes,3,opt,name=spender_address,json=spenderAddress,proto3" json:"spender_address,omitempty"`
	TokenAddress   string `protobuf:"bytes,4,opt,name=token_address,json=tokenAddress,proto3" json:"token_address,omitempty"`
	Value          string `protobuf:"bytes,5,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Approval) Reset() {
	*x = Approval{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_dapp_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Approval) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Approval) ProtoMessage() {}

func (x *Approval) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Approval.ProtoReflect.Descriptor instead.
func (*Approval) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{26}
}

func (x *Approval) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *Approval) GetOwnerAddress() string {
	if x != nil {
		return x.OwnerAddress
	}
	return ""
}

func (x *Approval) GetSpenderAddress() string {
	if x != nil {
		return x.SpenderAddress
	}
	return ""
}

func (x *Approval) GetTokenAddress() string {
	if x != nil {
		return x.TokenAddress
	}
	return ""
}

func (x *Approval) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type TokenDappApproval struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token    *Token    `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	Dapp     *Dapp     `protobuf:"bytes,2,opt,name=dapp,proto3" json:"dapp,omitempty"`
	Approval *Approval `protobuf:"bytes,3,opt,name=approval,proto3" json:"approval,omitempty"`
}

func (x *TokenDappApproval) Reset() {
	*x = TokenDappApproval{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_dapp_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TokenDappApproval) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenDappApproval) ProtoMessage() {}

func (x *TokenDappApproval) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenDappApproval.ProtoReflect.Descriptor instead.
func (*TokenDappApproval) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{27}
}

func (x *TokenDappApproval) GetToken() *Token {
	if x != nil {
		return x.Token
	}
	return nil
}

func (x *TokenDappApproval) GetDapp() *Dapp {
	if x != nil {
		return x.Dapp
	}
	return nil
}

func (x *TokenDappApproval) GetApproval() *Approval {
	if x != nil {
		return x.Approval
	}
	return nil
}

type UserApprovedDapp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Addresses []string           `protobuf:"bytes,1,rep,name=addresses,proto3" json:"addresses,omitempty"`
	Dapp      *Dapp              `protobuf:"bytes,2,opt,name=dapp,proto3" json:"dapp,omitempty"`
	Network   *BlockchainNetwork `protobuf:"bytes,3,opt,name=network,proto3" json:"network,omitempty"`
	Spender   string             `protobuf:"bytes,4,opt,name=spender,proto3" json:"spender,omitempty"`
}

func (x *UserApprovedDapp) Reset() {
	*x = UserApprovedDapp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_dapp_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserApprovedDapp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserApprovedDapp) ProtoMessage() {}

func (x *UserApprovedDapp) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserApprovedDapp.ProtoReflect.Descriptor instead.
func (*UserApprovedDapp) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{28}
}

func (x *UserApprovedDapp) GetAddresses() []string {
	if x != nil {
		return x.Addresses
	}
	return nil
}

func (x *UserApprovedDapp) GetDapp() *Dapp {
	if x != nil {
		return x.Dapp
	}
	return nil
}

func (x *UserApprovedDapp) GetNetwork() *BlockchainNetwork {
	if x != nil {
		return x.Network
	}
	return nil
}

func (x *UserApprovedDapp) GetSpender() string {
	if x != nil {
		return x.Spender
	}
	return ""
}

type TokenApproval struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token    *Token    `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	Approval *Approval `protobuf:"bytes,2,opt,name=approval,proto3" json:"approval,omitempty"`
}

func (x *TokenApproval) Reset() {
	*x = TokenApproval{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_dapp_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TokenApproval) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenApproval) ProtoMessage() {}

func (x *TokenApproval) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenApproval.ProtoReflect.Descriptor instead.
func (*TokenApproval) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{29}
}

func (x *TokenApproval) GetToken() *Token {
	if x != nil {
		return x.Token
	}
	return nil
}

func (x *TokenApproval) GetApproval() *Approval {
	if x != nil {
		return x.Approval
	}
	return nil
}

var File_api_wallet_v1_dapp_proto protoreflect.FileDescriptor

var file_api_wallet_v1_dapp_proto_rawDesc = []byte{
	0x0a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2f, 0x76, 0x31, 0x2f,
	0x64, 0x61, 0x70, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x62, 0x75, 0x66, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x2f, 0x76, 0x31, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x54, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x38, 0x0a, 0x09,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x09, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x22, 0x4c, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x2e, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x22, 0x8e, 0x01, 0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x73, 0x42, 0x79, 0x44, 0x61, 0x70,
	0x70, 0x52, 0x65, 0x71, 0x12, 0x30, 0x0a, 0x0f, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba,
	0x48, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0e, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f,
	0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x65, 0x73, 0x22, 0x51, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x73, 0x42, 0x79, 0x44, 0x61, 0x70,
	0x70, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x30, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x61, 0x6c, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x5c, 0x0a, 0x25, 0x4c, 0x69, 0x73, 0x74,
	0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x44, 0x61, 0x70, 0x70, 0x73, 0x42, 0x79, 0x55,
	0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x33, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x44, 0x61, 0x70, 0x70,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x5a, 0x0a, 0x1e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x38, 0x0a, 0x09, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x65, 0x73, 0x22, 0x48, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x62, 0x0a, 0x1c,
	0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x42, 0x79, 0x55, 0x73,
	0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x12, 0x21, 0x0a, 0x07,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba,
	0x48, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x1f, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x22, 0xc8, 0x01, 0x0a, 0x1e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61,
	0x6c, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x34, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x3a, 0x0a, 0x07, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x07, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x34, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x44, 0x61, 0x70, 0x70, 0x41, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x61, 0x6c, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x21, 0x0a, 0x0d, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x44, 0x61, 0x70, 0x70, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x22, 0x3a,
	0x0a, 0x0f, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x44, 0x61, 0x70, 0x70, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x27, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x61, 0x70, 0x70, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x69, 0x0a, 0x12, 0x47, 0x65,
	0x74, 0x44, 0x61, 0x70, 0x70, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71,
	0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xba, 0x48,
	0x04, 0x32, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x68,
	0x61, 0x69, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68,
	0x61, 0x69, 0x6e, 0x49, 0x64, 0x22, 0x3f, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x44, 0x61, 0x70, 0x70,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x27, 0x0a,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x70, 0x70,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x2a, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x44, 0x61, 0x70,
	0x70, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xba, 0x48, 0x04, 0x32, 0x02, 0x20, 0x00, 0x52, 0x02,
	0x69, 0x64, 0x22, 0x43, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x44, 0x61, 0x70, 0x70, 0x54, 0x6f, 0x70,
	0x69, 0x63, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x70, 0x70, 0x54, 0x6f, 0x70, 0x69, 0x63,
	0x52, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x22, 0x12, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x44,
	0x61, 0x70, 0x70, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x71, 0x22, 0xd1, 0x01, 0x0a, 0x04,
	0x44, 0x61, 0x70, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x69, 0x6e, 0x6b,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x61, 0x67, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73,
	0x12, 0x10, 0x0a, 0x03, 0x68, 0x6f, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03, 0x68,
	0x6f, 0x74, 0x12, 0x2d, 0x0a, 0x05, 0x69, 0x31, 0x38, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x61, 0x70, 0x70, 0x49, 0x31, 0x38, 0x4e, 0x52, 0x05, 0x69, 0x31, 0x38, 0x6e,
	0x73, 0x12, 0x3c, 0x0a, 0x08, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x4e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x08, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x22,
	0x38, 0x0a, 0x08, 0x44, 0x61, 0x70, 0x70, 0x49, 0x31, 0x38, 0x4e, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x22, 0x2e, 0x0a, 0x11, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x19,
	0x0a, 0x08, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x64, 0x22, 0x93, 0x01, 0x0a, 0x0c, 0x44, 0x61,
	0x70, 0x70, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x12, 0x29, 0x0a, 0x05, 0x64, 0x61, 0x70, 0x70, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x70, 0x70, 0x52, 0x05, 0x64, 0x61, 0x70, 0x70, 0x73, 0x22,
	0x8d, 0x02, 0x0a, 0x09, 0x44, 0x61, 0x70, 0x70, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x25, 0x0a,
	0x0e, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x55, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x6f, 0x70, 0x5f, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x6f, 0x70, 0x54, 0x69, 0x74, 0x6c,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x5f, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x54,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x29, 0x0a, 0x05, 0x64, 0x61, 0x70, 0x70, 0x73, 0x18, 0x09, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x70, 0x70, 0x52, 0x05, 0x64, 0x61, 0x70, 0x70, 0x73, 0x22,
	0x74, 0x0a, 0x09, 0x44, 0x61, 0x70, 0x70, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x2e, 0x0a, 0x05,
	0x74, 0x6f, 0x70, 0x69, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x70, 0x70,
	0x54, 0x6f, 0x70, 0x69, 0x63, 0x52, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x12, 0x37, 0x0a, 0x08,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x61, 0x70, 0x70, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x22, 0x42, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x70,
	0x70, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2c, 0x0a, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x70, 0x70, 0x49, 0x6e,
	0x64, 0x65, 0x78, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x13, 0x0a, 0x11, 0x4c, 0x69, 0x73,
	0x74, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x22, 0x48,
	0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x31, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x4e, 0x0a, 0x0e, 0x44, 0x61, 0x70, 0x70,
	0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x10, 0x64, 0x61,
	0x70, 0x70, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x64, 0x61, 0x70, 0x70, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xb4, 0x01, 0x0a, 0x08, 0x41, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x61, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f,
	0x77, 0x6e, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x73,
	0x70, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0x9d, 0x01, 0x0a, 0x11, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x44, 0x61, 0x70, 0x70, 0x41, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x61, 0x6c, 0x12, 0x2a, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x27, 0x0a, 0x04, 0x64, 0x61, 0x70, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x61, 0x70, 0x70, 0x52, 0x04, 0x64, 0x61, 0x70, 0x70, 0x12, 0x33, 0x0a, 0x08, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x61, 0x6c, 0x52, 0x08, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x22,
	0xaf, 0x01, 0x0a, 0x10, 0x55, 0x73, 0x65, 0x72, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x44, 0x61, 0x70, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x65, 0x73, 0x12, 0x27, 0x0a, 0x04, 0x64, 0x61, 0x70, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x61, 0x70, 0x70, 0x52, 0x04, 0x64, 0x61, 0x70, 0x70, 0x12, 0x3a, 0x0a, 0x07, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x07,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x70, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x22, 0x70, 0x0a, 0x0d, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x61, 0x6c, 0x12, 0x2a, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x33,
	0x0a, 0x08, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x52, 0x08, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x61, 0x6c, 0x32, 0xbb, 0x09, 0x0a, 0x07, 0x44, 0x61, 0x70, 0x70, 0x53, 0x72, 0x76, 0x12,
	0x73, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x12,
	0x13, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x70, 0x70, 0x2f, 0x6e, 0x61, 0x76, 0x69, 0x67, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x6b, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x70, 0x70,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c,
	0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x70, 0x70, 0x49, 0x6e,
	0x64, 0x65, 0x78, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x70, 0x70, 0x49,
	0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x16, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x10, 0x12, 0x0e, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x70, 0x70, 0x2f, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x12, 0x6d, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x44, 0x61, 0x70, 0x70, 0x54, 0x6f, 0x70, 0x69,
	0x63, 0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x70, 0x70, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x52, 0x65,
	0x71, 0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x70, 0x70, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x12, 0x13, 0x2f, 0x76, 0x31,
	0x2f, 0x64, 0x61, 0x70, 0x70, 0x2f, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x2f, 0x7b, 0x69, 0x64, 0x7d,
	0x12, 0x79, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x44, 0x61, 0x70, 0x70, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x12, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x70, 0x70, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x70, 0x70, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1e, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x18, 0x12, 0x16, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x70, 0x70, 0x2f, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x5c, 0x0a, 0x0a, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x44, 0x61, 0x70, 0x70, 0x12, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x44, 0x61, 0x70, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x44, 0x61,
	0x70, 0x70, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x10, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0a, 0x12,
	0x08, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x70, 0x70, 0x12, 0x93, 0x01, 0x0a, 0x15, 0x4c, 0x69,
	0x73, 0x74, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x65, 0x73, 0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x29, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x65, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x3a,
	0x01, 0x2a, 0x22, 0x1b, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x70, 0x70, 0x2f, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x12,
	0x9d, 0x01, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c,
	0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x2b, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x2d, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x1e, 0x3a, 0x01, 0x2a, 0x22, 0x19, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x70, 0x70, 0x2f, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x12,
	0xaf, 0x01, 0x0a, 0x20, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x44, 0x61, 0x70, 0x70, 0x73, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x65, 0x73, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61,
	0x6c, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x1a, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x44, 0x61, 0x70, 0x70, 0x73, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x65, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x20, 0x3a, 0x01, 0x2a, 0x22, 0x1b, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x70, 0x70, 0x2f, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61,
	0x6c, 0x12, 0x9d, 0x01, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x41,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x73, 0x42, 0x79, 0x44, 0x61, 0x70, 0x70, 0x12, 0x2a,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c,
	0x73, 0x42, 0x79, 0x44, 0x61, 0x70, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x2c, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x73, 0x42, 0x79, 0x44,
	0x61, 0x70, 0x70, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21,
	0x3a, 0x01, 0x2a, 0x22, 0x1c, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x70, 0x70, 0x2f, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2f, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61,
	0x6c, 0x42, 0x91, 0x01, 0x0a, 0x11, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x42, 0x09, 0x44, 0x61, 0x70, 0x70, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x50, 0x01, 0x5a, 0x1b, 0x62, 0x79, 0x64, 0x5f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x76,
	0x31, 0xa2, 0x02, 0x03, 0x41, 0x57, 0x58, 0xaa, 0x02, 0x0d, 0x41, 0x70, 0x69, 0x2e, 0x57, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x56, 0x31, 0xca, 0x02, 0x0d, 0x41, 0x70, 0x69, 0x5c, 0x57, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x5c, 0x56, 0x31, 0xe2, 0x02, 0x19, 0x41, 0x70, 0x69, 0x5c, 0x57, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x5c, 0x56, 0x31, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0xea, 0x02, 0x0f, 0x41, 0x70, 0x69, 0x3a, 0x3a, 0x57, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x3a, 0x3a, 0x56, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_wallet_v1_dapp_proto_rawDescOnce sync.Once
	file_api_wallet_v1_dapp_proto_rawDescData = file_api_wallet_v1_dapp_proto_rawDesc
)

func file_api_wallet_v1_dapp_proto_rawDescGZIP() []byte {
	file_api_wallet_v1_dapp_proto_rawDescOnce.Do(func() {
		file_api_wallet_v1_dapp_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_wallet_v1_dapp_proto_rawDescData)
	})
	return file_api_wallet_v1_dapp_proto_rawDescData
}

var file_api_wallet_v1_dapp_proto_msgTypes = make([]protoimpl.MessageInfo, 30)
var file_api_wallet_v1_dapp_proto_goTypes = []any{
	(*ListApprovedAddressesReq)(nil),              // 0: api.wallet.v1.ListApprovedAddressesReq
	(*ListApprovedAddressesReply)(nil),            // 1: api.wallet.v1.ListApprovedAddressesReply
	(*ListTokenApprovalsByDappReq)(nil),           // 2: api.wallet.v1.ListTokenApprovalsByDappReq
	(*ListTokenApprovalsByDappReply)(nil),         // 3: api.wallet.v1.ListTokenApprovalsByDappReply
	(*ListApprovedDappsByUserAddressesReply)(nil), // 4: api.wallet.v1.ListApprovedDappsByUserAddressesReply
	(*ListApprovalByUserAddressesReq)(nil),        // 5: api.wallet.v1.ListApprovalByUserAddressesReq
	(*UserAddress)(nil),                           // 6: api.wallet.v1.UserAddress
	(*ListApprovalByUserAddressReq)(nil),          // 7: api.wallet.v1.ListApprovalByUserAddressReq
	(*ListApprovalByUserAddressReply)(nil),        // 8: api.wallet.v1.ListApprovalByUserAddressReply
	(*SearchDappReq)(nil),                         // 9: api.wallet.v1.SearchDappReq
	(*SearchDappReply)(nil),                       // 10: api.wallet.v1.SearchDappReply
	(*GetDappCategoryReq)(nil),                    // 11: api.wallet.v1.GetDappCategoryReq
	(*GetDappCategoryReply)(nil),                  // 12: api.wallet.v1.GetDappCategoryReply
	(*GetDappTopicReq)(nil),                       // 13: api.wallet.v1.GetDappTopicReq
	(*GetDappTopicReply)(nil),                     // 14: api.wallet.v1.GetDappTopicReply
	(*ListDappIndexReq)(nil),                      // 15: api.wallet.v1.ListDappIndexReq
	(*Dapp)(nil),                                  // 16: api.wallet.v1.Dapp
	(*DappI18N)(nil),                              // 17: api.wallet.v1.DappI18N
	(*BlockchainNetwork)(nil),                     // 18: api.wallet.v1.BlockchainNetwork
	(*DappCategory)(nil),                          // 19: api.wallet.v1.DappCategory
	(*DappTopic)(nil),                             // 20: api.wallet.v1.DappTopic
	(*DappIndex)(nil),                             // 21: api.wallet.v1.DappIndex
	(*ListDappIndexReply)(nil),                    // 22: api.wallet.v1.ListDappIndexReply
	(*ListNavigationReq)(nil),                     // 23: api.wallet.v1.ListNavigationReq
	(*ListNavigationReply)(nil),                   // 24: api.wallet.v1.ListNavigationReply
	(*DappNavigation)(nil),                        // 25: api.wallet.v1.DappNavigation
	(*Approval)(nil),                              // 26: api.wallet.v1.Approval
	(*TokenDappApproval)(nil),                     // 27: api.wallet.v1.TokenDappApproval
	(*UserApprovedDapp)(nil),                      // 28: api.wallet.v1.UserApprovedDapp
	(*TokenApproval)(nil),                         // 29: api.wallet.v1.TokenApproval
	(*Token)(nil),                                 // 30: api.wallet.v1.Token
}
var file_api_wallet_v1_dapp_proto_depIdxs = []int32{
	6,  // 0: api.wallet.v1.ListApprovedAddressesReq.addresses:type_name -> api.wallet.v1.UserAddress
	6,  // 1: api.wallet.v1.ListApprovedAddressesReply.list:type_name -> api.wallet.v1.UserAddress
	29, // 2: api.wallet.v1.ListTokenApprovalsByDappReply.list:type_name -> api.wallet.v1.TokenApproval
	28, // 3: api.wallet.v1.ListApprovedDappsByUserAddressesReply.list:type_name -> api.wallet.v1.UserApprovedDapp
	6,  // 4: api.wallet.v1.ListApprovalByUserAddressesReq.addresses:type_name -> api.wallet.v1.UserAddress
	6,  // 5: api.wallet.v1.ListApprovalByUserAddressReply.address:type_name -> api.wallet.v1.UserAddress
	18, // 6: api.wallet.v1.ListApprovalByUserAddressReply.network:type_name -> api.wallet.v1.BlockchainNetwork
	27, // 7: api.wallet.v1.ListApprovalByUserAddressReply.list:type_name -> api.wallet.v1.TokenDappApproval
	16, // 8: api.wallet.v1.SearchDappReply.list:type_name -> api.wallet.v1.Dapp
	16, // 9: api.wallet.v1.GetDappCategoryReply.list:type_name -> api.wallet.v1.Dapp
	20, // 10: api.wallet.v1.GetDappTopicReply.topic:type_name -> api.wallet.v1.DappTopic
	17, // 11: api.wallet.v1.Dapp.i18ns:type_name -> api.wallet.v1.DappI18N
	18, // 12: api.wallet.v1.Dapp.networks:type_name -> api.wallet.v1.BlockchainNetwork
	16, // 13: api.wallet.v1.DappCategory.dapps:type_name -> api.wallet.v1.Dapp
	16, // 14: api.wallet.v1.DappTopic.dapps:type_name -> api.wallet.v1.Dapp
	20, // 15: api.wallet.v1.DappIndex.topic:type_name -> api.wallet.v1.DappTopic
	19, // 16: api.wallet.v1.DappIndex.category:type_name -> api.wallet.v1.DappCategory
	21, // 17: api.wallet.v1.ListDappIndexReply.list:type_name -> api.wallet.v1.DappIndex
	25, // 18: api.wallet.v1.ListNavigationReply.list:type_name -> api.wallet.v1.DappNavigation
	30, // 19: api.wallet.v1.TokenDappApproval.token:type_name -> api.wallet.v1.Token
	16, // 20: api.wallet.v1.TokenDappApproval.dapp:type_name -> api.wallet.v1.Dapp
	26, // 21: api.wallet.v1.TokenDappApproval.approval:type_name -> api.wallet.v1.Approval
	16, // 22: api.wallet.v1.UserApprovedDapp.dapp:type_name -> api.wallet.v1.Dapp
	18, // 23: api.wallet.v1.UserApprovedDapp.network:type_name -> api.wallet.v1.BlockchainNetwork
	30, // 24: api.wallet.v1.TokenApproval.token:type_name -> api.wallet.v1.Token
	26, // 25: api.wallet.v1.TokenApproval.approval:type_name -> api.wallet.v1.Approval
	23, // 26: api.wallet.v1.DappSrv.ListNavigation:input_type -> api.wallet.v1.ListNavigationReq
	15, // 27: api.wallet.v1.DappSrv.ListDappIndex:input_type -> api.wallet.v1.ListDappIndexReq
	13, // 28: api.wallet.v1.DappSrv.GetDappTopic:input_type -> api.wallet.v1.GetDappTopicReq
	11, // 29: api.wallet.v1.DappSrv.GetDappCategory:input_type -> api.wallet.v1.GetDappCategoryReq
	9,  // 30: api.wallet.v1.DappSrv.SearchDapp:input_type -> api.wallet.v1.SearchDappReq
	0,  // 31: api.wallet.v1.DappSrv.ListApprovedAddresses:input_type -> api.wallet.v1.ListApprovedAddressesReq
	7,  // 32: api.wallet.v1.DappSrv.ListApprovalByUserAddress:input_type -> api.wallet.v1.ListApprovalByUserAddressReq
	5,  // 33: api.wallet.v1.DappSrv.ListApprovedDappsByUserAddresses:input_type -> api.wallet.v1.ListApprovalByUserAddressesReq
	2,  // 34: api.wallet.v1.DappSrv.ListTokenApprovalsByDapp:input_type -> api.wallet.v1.ListTokenApprovalsByDappReq
	24, // 35: api.wallet.v1.DappSrv.ListNavigation:output_type -> api.wallet.v1.ListNavigationReply
	22, // 36: api.wallet.v1.DappSrv.ListDappIndex:output_type -> api.wallet.v1.ListDappIndexReply
	14, // 37: api.wallet.v1.DappSrv.GetDappTopic:output_type -> api.wallet.v1.GetDappTopicReply
	12, // 38: api.wallet.v1.DappSrv.GetDappCategory:output_type -> api.wallet.v1.GetDappCategoryReply
	10, // 39: api.wallet.v1.DappSrv.SearchDapp:output_type -> api.wallet.v1.SearchDappReply
	1,  // 40: api.wallet.v1.DappSrv.ListApprovedAddresses:output_type -> api.wallet.v1.ListApprovedAddressesReply
	8,  // 41: api.wallet.v1.DappSrv.ListApprovalByUserAddress:output_type -> api.wallet.v1.ListApprovalByUserAddressReply
	4,  // 42: api.wallet.v1.DappSrv.ListApprovedDappsByUserAddresses:output_type -> api.wallet.v1.ListApprovedDappsByUserAddressesReply
	3,  // 43: api.wallet.v1.DappSrv.ListTokenApprovalsByDapp:output_type -> api.wallet.v1.ListTokenApprovalsByDappReply
	35, // [35:44] is the sub-list for method output_type
	26, // [26:35] is the sub-list for method input_type
	26, // [26:26] is the sub-list for extension type_name
	26, // [26:26] is the sub-list for extension extendee
	0,  // [0:26] is the sub-list for field type_name
}

func init() { file_api_wallet_v1_dapp_proto_init() }
func file_api_wallet_v1_dapp_proto_init() {
	if File_api_wallet_v1_dapp_proto != nil {
		return
	}
	file_api_wallet_v1_wallet_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_wallet_v1_dapp_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*ListApprovedAddressesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_dapp_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*ListApprovedAddressesReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_dapp_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*ListTokenApprovalsByDappReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_dapp_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*ListTokenApprovalsByDappReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_dapp_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*ListApprovedDappsByUserAddressesReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_dapp_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*ListApprovalByUserAddressesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_dapp_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*UserAddress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_dapp_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*ListApprovalByUserAddressReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_dapp_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*ListApprovalByUserAddressReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_dapp_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*SearchDappReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_dapp_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*SearchDappReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_dapp_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*GetDappCategoryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_dapp_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*GetDappCategoryReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_dapp_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*GetDappTopicReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_dapp_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*GetDappTopicReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_dapp_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*ListDappIndexReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_dapp_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*Dapp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_dapp_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*DappI18N); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_dapp_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*BlockchainNetwork); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_dapp_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*DappCategory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_dapp_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*DappTopic); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_dapp_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*DappIndex); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_dapp_proto_msgTypes[22].Exporter = func(v any, i int) any {
			switch v := v.(*ListDappIndexReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_dapp_proto_msgTypes[23].Exporter = func(v any, i int) any {
			switch v := v.(*ListNavigationReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_dapp_proto_msgTypes[24].Exporter = func(v any, i int) any {
			switch v := v.(*ListNavigationReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_dapp_proto_msgTypes[25].Exporter = func(v any, i int) any {
			switch v := v.(*DappNavigation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_dapp_proto_msgTypes[26].Exporter = func(v any, i int) any {
			switch v := v.(*Approval); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_dapp_proto_msgTypes[27].Exporter = func(v any, i int) any {
			switch v := v.(*TokenDappApproval); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_dapp_proto_msgTypes[28].Exporter = func(v any, i int) any {
			switch v := v.(*UserApprovedDapp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_dapp_proto_msgTypes[29].Exporter = func(v any, i int) any {
			switch v := v.(*TokenApproval); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_wallet_v1_dapp_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   30,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_wallet_v1_dapp_proto_goTypes,
		DependencyIndexes: file_api_wallet_v1_dapp_proto_depIdxs,
		MessageInfos:      file_api_wallet_v1_dapp_proto_msgTypes,
	}.Build()
	File_api_wallet_v1_dapp_proto = out.File
	file_api_wallet_v1_dapp_proto_rawDesc = nil
	file_api_wallet_v1_dapp_proto_goTypes = nil
	file_api_wallet_v1_dapp_proto_depIdxs = nil
}
