syntax = "proto3";

package api.wallet.v1;

option go_package = "byd_wallet/api/wallet/v1;v1";

import "google/api/annotations.proto";
import "buf/validate/validate.proto";

service MarketSrv {
  // 查询币种价格
  rpc QueryCoinPrice(QueryCoinPriceReq) returns (QueryCoinPriceReply) {
    option (google.api.http) = {
      post: "/v1/market/query_coin_price"
      body: "*"
    };
  }

  // 查询USD汇率
  rpc QueryCurrencyUSDRate(QueryCurrencyUSDRateReq) returns (CurrencyUSDRate) {
    option (google.api.http) = {
      get: "/v1/market/query_currency_usdrate"
    };
  }

  // 查询24小时简易k线(仅收盘价)
  rpc QuerySimpleKline(QuerySimpleKlineReq) returns (QuerySimpleKlineReply) {
    option (google.api.http) = {
      post: "/v1/market/query_simple_kline"
      body: "*"
    };
  }

  // 获取热门代币列表
  rpc ListPopularToken(ListPopularTokenReq) returns (ListPopularTokenReply) {
    option (google.api.http) = {
      get: "/v1/market/list_popular_token"
    };
  }
}

message TokenWithMarket {
  // 链索引
  int64 chain_index = 1;
  // 合约地址
  string address = 2;
  // 币名称
  string name = 3;
  // 币符号
  string symbol = 4;
  // 币精度位数
  int64 decimals = 5;
  // 流动性
  string circulating_supply = 6;
  // 24小时成交额
  string trading_volume_24h = 7;
  // 24小时涨跌幅（百分比）
  string price_change_percentage_24h = 8;
  // 币图标
  string logo_url = 9;
  // 币价格(usd)
  string price = 10;
}

message SearchTokenReply {
  repeated TokenWithMarket list = 1;
}

message ListPopularTokenReq {
  // 页码
	int64 page = 1 [(buf.validate.field).int64.gt = 0];
	// 页容量
	int64 limit = 2 [(buf.validate.field).int64 = {gt: 0,lte:20}];
  // 关键字(搜索范围 代币符号 和 合约地址)
  optional string keyword = 3;
  // 链索引(0,60,...支持查询多个链,查询全部就传空或不传)
  string chain_indexes = 4;
}

message ListPopularTokenReply {
  repeated TokenWithMarket list = 1;
  // 总记录数
	int64 total_count = 2;
}

message QuerySimpleKlineReq {
  // 查询指定的token
  repeated TokenID token_ids = 1 [(buf.validate.field).repeated.min_items = 1];
}

message SimpleKline {
  // 链索引
  int64 chain_index = 1;
  // 合约地址
  string address = 2;
  // k线时间（时间戳，单位秒） // 升序
  repeated int64 times = 3;
  // 收盘价(USD) // 按时间升序
  repeated string close_prices = 4;
}

message QuerySimpleKlineReply {
  repeated SimpleKline list = 1;
}

message QueryCurrencyUSDRateReq {
  // 法币 // cny,jpy,...
  string currencies = 1;
}

message CurrencyUSDRate {
  // key:法币(cny,jpy等小写) value:汇率
  map<string, string> value = 1;
}

message TokenID {
  // 链索引
  int64 chain_index = 1;
  // 合约地址
  string address = 2;
}

message QueryCoinPriceReq {
  // 查询指定的token
  repeated TokenID token_ids = 1 [(buf.validate.field).repeated.min_items = 1];
}

message CoinPrice {
  // 链索引
  int64 chain_index = 1;
  // 合约地址
  string address = 2;
  // 价格(USD)
  string price = 3;
  // 更新时间（时间戳，单位秒）
  int64 last_updated_at = 4;
}

message QueryCoinPriceReply {
  repeated CoinPrice list = 1;
}