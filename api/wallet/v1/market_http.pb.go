// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.0
// - protoc             (unknown)
// source: api/wallet/v1/market.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationMarketSrvListPopularToken = "/api.wallet.v1.MarketSrv/ListPopularToken"
const OperationMarketSrvQueryCoinPrice = "/api.wallet.v1.MarketSrv/QueryCoinPrice"
const OperationMarketSrvQueryCurrencyUSDRate = "/api.wallet.v1.MarketSrv/QueryCurrencyUSDRate"
const OperationMarketSrvQuerySimpleKline = "/api.wallet.v1.MarketSrv/QuerySimpleKline"

type MarketSrvHTTPServer interface {
	// ListPopularToken 获取热门代币列表
	ListPopularToken(context.Context, *ListPopularTokenReq) (*ListPopularTokenReply, error)
	// QueryCoinPrice 查询币种价格
	QueryCoinPrice(context.Context, *QueryCoinPriceReq) (*QueryCoinPriceReply, error)
	// QueryCurrencyUSDRate 查询USD汇率
	QueryCurrencyUSDRate(context.Context, *QueryCurrencyUSDRateReq) (*CurrencyUSDRate, error)
	// QuerySimpleKline 查询24小时简易k线(仅收盘价)
	QuerySimpleKline(context.Context, *QuerySimpleKlineReq) (*QuerySimpleKlineReply, error)
}

func RegisterMarketSrvHTTPServer(s *http.Server, srv MarketSrvHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/market/query_coin_price", _MarketSrv_QueryCoinPrice0_HTTP_Handler(srv))
	r.GET("/v1/market/query_currency_usdrate", _MarketSrv_QueryCurrencyUSDRate0_HTTP_Handler(srv))
	r.POST("/v1/market/query_simple_kline", _MarketSrv_QuerySimpleKline0_HTTP_Handler(srv))
	r.GET("/v1/market/list_popular_token", _MarketSrv_ListPopularToken0_HTTP_Handler(srv))
}

func _MarketSrv_QueryCoinPrice0_HTTP_Handler(srv MarketSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QueryCoinPriceReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMarketSrvQueryCoinPrice)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QueryCoinPrice(ctx, req.(*QueryCoinPriceReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QueryCoinPriceReply)
		return ctx.Result(200, reply)
	}
}

func _MarketSrv_QueryCurrencyUSDRate0_HTTP_Handler(srv MarketSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QueryCurrencyUSDRateReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMarketSrvQueryCurrencyUSDRate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QueryCurrencyUSDRate(ctx, req.(*QueryCurrencyUSDRateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CurrencyUSDRate)
		return ctx.Result(200, reply)
	}
}

func _MarketSrv_QuerySimpleKline0_HTTP_Handler(srv MarketSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QuerySimpleKlineReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMarketSrvQuerySimpleKline)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QuerySimpleKline(ctx, req.(*QuerySimpleKlineReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QuerySimpleKlineReply)
		return ctx.Result(200, reply)
	}
}

func _MarketSrv_ListPopularToken0_HTTP_Handler(srv MarketSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListPopularTokenReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMarketSrvListPopularToken)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListPopularToken(ctx, req.(*ListPopularTokenReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListPopularTokenReply)
		return ctx.Result(200, reply)
	}
}

type MarketSrvHTTPClient interface {
	ListPopularToken(ctx context.Context, req *ListPopularTokenReq, opts ...http.CallOption) (rsp *ListPopularTokenReply, err error)
	QueryCoinPrice(ctx context.Context, req *QueryCoinPriceReq, opts ...http.CallOption) (rsp *QueryCoinPriceReply, err error)
	QueryCurrencyUSDRate(ctx context.Context, req *QueryCurrencyUSDRateReq, opts ...http.CallOption) (rsp *CurrencyUSDRate, err error)
	QuerySimpleKline(ctx context.Context, req *QuerySimpleKlineReq, opts ...http.CallOption) (rsp *QuerySimpleKlineReply, err error)
}

type MarketSrvHTTPClientImpl struct {
	cc *http.Client
}

func NewMarketSrvHTTPClient(client *http.Client) MarketSrvHTTPClient {
	return &MarketSrvHTTPClientImpl{client}
}

func (c *MarketSrvHTTPClientImpl) ListPopularToken(ctx context.Context, in *ListPopularTokenReq, opts ...http.CallOption) (*ListPopularTokenReply, error) {
	var out ListPopularTokenReply
	pattern := "/v1/market/list_popular_token"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationMarketSrvListPopularToken))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *MarketSrvHTTPClientImpl) QueryCoinPrice(ctx context.Context, in *QueryCoinPriceReq, opts ...http.CallOption) (*QueryCoinPriceReply, error) {
	var out QueryCoinPriceReply
	pattern := "/v1/market/query_coin_price"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationMarketSrvQueryCoinPrice))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *MarketSrvHTTPClientImpl) QueryCurrencyUSDRate(ctx context.Context, in *QueryCurrencyUSDRateReq, opts ...http.CallOption) (*CurrencyUSDRate, error) {
	var out CurrencyUSDRate
	pattern := "/v1/market/query_currency_usdrate"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationMarketSrvQueryCurrencyUSDRate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *MarketSrvHTTPClientImpl) QuerySimpleKline(ctx context.Context, in *QuerySimpleKlineReq, opts ...http.CallOption) (*QuerySimpleKlineReply, error) {
	var out QuerySimpleKlineReply
	pattern := "/v1/market/query_simple_kline"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationMarketSrvQuerySimpleKline))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
