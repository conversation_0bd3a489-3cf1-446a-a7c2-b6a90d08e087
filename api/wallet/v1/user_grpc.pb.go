// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: api/wallet/v1/user.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	UserSrv_UserRegister_FullMethodName   = "/api.wallet.v1.UserSrv/UserRegister"
	UserSrv_UpdateWalletID_FullMethodName = "/api.wallet.v1.UserSrv/UpdateWalletID"
	UserSrv_GetWalletIds_FullMethodName   = "/api.wallet.v1.UserSrv/GetWalletIds"
)

// UserSrvClient is the client API for UserSrv service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserSrvClient interface {
	// 生成wallet Id
	UserRegister(ctx context.Context, in *UserRegisterReq, opts ...grpc.CallOption) (*UserRegisterReply, error)
	// 更新wallet Id
	UpdateWalletID(ctx context.Context, in *UpdateWalletIDReq, opts ...grpc.CallOption) (*UpdateWalletIDReply, error)
	// 获取wallet Id
	GetWalletIds(ctx context.Context, in *GetWalletIdReq, opts ...grpc.CallOption) (*GeWalletIdsReply, error)
}

type userSrvClient struct {
	cc grpc.ClientConnInterface
}

func NewUserSrvClient(cc grpc.ClientConnInterface) UserSrvClient {
	return &userSrvClient{cc}
}

func (c *userSrvClient) UserRegister(ctx context.Context, in *UserRegisterReq, opts ...grpc.CallOption) (*UserRegisterReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UserRegisterReply)
	err := c.cc.Invoke(ctx, UserSrv_UserRegister_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userSrvClient) UpdateWalletID(ctx context.Context, in *UpdateWalletIDReq, opts ...grpc.CallOption) (*UpdateWalletIDReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateWalletIDReply)
	err := c.cc.Invoke(ctx, UserSrv_UpdateWalletID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userSrvClient) GetWalletIds(ctx context.Context, in *GetWalletIdReq, opts ...grpc.CallOption) (*GeWalletIdsReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GeWalletIdsReply)
	err := c.cc.Invoke(ctx, UserSrv_GetWalletIds_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserSrvServer is the server API for UserSrv service.
// All implementations must embed UnimplementedUserSrvServer
// for forward compatibility.
type UserSrvServer interface {
	// 生成wallet Id
	UserRegister(context.Context, *UserRegisterReq) (*UserRegisterReply, error)
	// 更新wallet Id
	UpdateWalletID(context.Context, *UpdateWalletIDReq) (*UpdateWalletIDReply, error)
	// 获取wallet Id
	GetWalletIds(context.Context, *GetWalletIdReq) (*GeWalletIdsReply, error)
	mustEmbedUnimplementedUserSrvServer()
}

// UnimplementedUserSrvServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedUserSrvServer struct{}

func (UnimplementedUserSrvServer) UserRegister(context.Context, *UserRegisterReq) (*UserRegisterReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserRegister not implemented")
}
func (UnimplementedUserSrvServer) UpdateWalletID(context.Context, *UpdateWalletIDReq) (*UpdateWalletIDReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateWalletID not implemented")
}
func (UnimplementedUserSrvServer) GetWalletIds(context.Context, *GetWalletIdReq) (*GeWalletIdsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWalletIds not implemented")
}
func (UnimplementedUserSrvServer) mustEmbedUnimplementedUserSrvServer() {}
func (UnimplementedUserSrvServer) testEmbeddedByValue()                 {}

// UnsafeUserSrvServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserSrvServer will
// result in compilation errors.
type UnsafeUserSrvServer interface {
	mustEmbedUnimplementedUserSrvServer()
}

func RegisterUserSrvServer(s grpc.ServiceRegistrar, srv UserSrvServer) {
	// If the following call pancis, it indicates UnimplementedUserSrvServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&UserSrv_ServiceDesc, srv)
}

func _UserSrv_UserRegister_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserRegisterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserSrvServer).UserRegister(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserSrv_UserRegister_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserSrvServer).UserRegister(ctx, req.(*UserRegisterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserSrv_UpdateWalletID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateWalletIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserSrvServer).UpdateWalletID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserSrv_UpdateWalletID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserSrvServer).UpdateWalletID(ctx, req.(*UpdateWalletIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserSrv_GetWalletIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWalletIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserSrvServer).GetWalletIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserSrv_GetWalletIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserSrvServer).GetWalletIds(ctx, req.(*GetWalletIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

// UserSrv_ServiceDesc is the grpc.ServiceDesc for UserSrv service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserSrv_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.wallet.v1.UserSrv",
	HandlerType: (*UserSrvServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UserRegister",
			Handler:    _UserSrv_UserRegister_Handler,
		},
		{
			MethodName: "UpdateWalletID",
			Handler:    _UserSrv_UpdateWalletID_Handler,
		},
		{
			MethodName: "GetWalletIds",
			Handler:    _UserSrv_GetWalletIds_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/wallet/v1/user.proto",
}
