syntax = "proto3";

package api.wallet.v1;

option go_package = "byd_wallet/api/wallet/v1;v1";

import "buf/validate/validate.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";

service WalletSrv {
  // 链列表
  rpc NetworkList(NetworkListReq) returns (NetworkListReply) {
    option (google.api.http) = {get: "/v1/networkList"};
  }

  // 获取交易信息
  rpc Transactions(TransactionsReq) returns (TransactionsReply) {
    option (google.api.http) = {get: "/v1/transactionList"};
  }

  // 获取交易信息
  rpc TransactionsByAddress(TransactionsByAddressReq) returns (TransactionsReply) {
    option (google.api.http) = {get: "/v1/transactionsByAddress"};
  }

  // 获取交易详情信息
  rpc TransactionInfo(TransactionReq) returns (Transaction) {
    option (google.api.http) = {get: "/v1/transactionInfo"};
  }

  // 获取查询token
  rpc TokenList(TokenListReq) returns (TokenListReply) {
    option (google.api.http) = {get: "/v1/tokenList"};
  }
  
  // 获取指定token
  rpc GetToken(GetTokenReq) returns (Token) {
    option (google.api.http) = {get: "/v1/getToken"};
  }

  // 获取地址的资产明细
  rpc ListTokenBalance(ListTokenBalanceReq) returns (ListTokenBalanceReply) {
    option (google.api.http) = {post: "/v1/listTokenBalance", body: "*"};
  }

  // 获取地址接受转账的最新币种信息
  rpc QueryTxLatestToken(QueryTxLatestTokenReq) returns (QueryTxLatestTokenReply) {
    option (google.api.http) = {post: "/v1/queryTxLatestToken", body: "*"};
  }

  // 上报并更新一笔内部交易
  rpc ReportInternalTxn(ReportInternalTxnReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {post: "/v1/reportInternalTxn", body: "*"};
  }
}

message TokenWithWallet {
  string wallet_address = 1;
  int64 chain_index = 2;
  string name = 3;
  string symbol = 4;
  int64 decimals = 5;
  string logo_url = 6;
  string address = 7;
  string chain_id = 8;
}

message QueryTxLatestTokenReply {
  repeated TokenWithWallet list = 1;
}

message WalletAddress4Chain {
  // 钱包地址
  repeated string addresses = 1 [(buf.validate.field).repeated.min_items = 1];
  int64 chain_index = 2;
}

message QueryTxLatestTokenReq {
  repeated WalletAddress4Chain chains = 1 [(buf.validate.field).repeated.min_items = 1];
}

message WalletAddress {
  // 钱包地址
  string address = 1 [(buf.validate.field).string.min_len = 1];
  // 链索引
  repeated int64 chain_indexes = 2 [(buf.validate.field).repeated.min_items = 1];
}
message ListTokenBalanceReq {
  repeated WalletAddress addresses = 1 [(buf.validate.field).repeated.min_items = 1];
}

message TokenBalance {
  // 链索引
  int64 chain_index = 1;
  // token地址
  string address = 2;
  // token名称
  string name = 3;
  // token符号
  string symbol = 4;
  // token精度
  int64 decimals = 5;
  // token原始数量
  string raw_balance = 6;
  // token价格(USD)
  string price = 7;
  // token图标
  string logo_url = 8;
}

message WalletBalance {
  // 钱包地址
  string address = 1;
  repeated TokenBalance balances = 2;
}

message ListTokenBalanceReply {
  repeated WalletBalance list = 1;
}

message GetTokenReq {
  // 链索引
  int64 chain_index = 1;
  // 合约地址
  string address = 2;
}

message NetworkListReq {}

message NetworkListReply {
  // 链列表
  repeated Network list = 2;
}

message TransactionsReq {
  // 链索引
  int64 chain_index = 1;
  // 查询地址，多个用逗号(,)隔开
  string address = 2 [(buf.validate.field).string.min_len = 1];
  // 查询合约地址，多个用逗号(,)隔开,不区分合约查询all
  string program_id = 3 ;
  int64 page = 4;
  // 每页展示条数
  int64 limit = 5 [(buf.validate.field).int64 = {
    gt: 0
    lte: 100
  }];
}

message TransactionsReply {
  // 总条数
  int64 count = 1;
  // transaction
  repeated Transaction list = 2;
}

message TransactionsByAddressReq {
  // 链索引
  int64 chain_index = 1;
  // 查询from地址，多个用逗号(,)隔开
  string from_address = 2 ;

  // 查询to地址，多个用逗号(,)隔开
  string to_address = 3 ;
  // 查询合约地址，多个用逗号(,)隔开
  optional string program_id = 4 ;
  int64 page = 5;
  // 每页展示条数
  int64 limit =6 [(buf.validate.field).int64 = {
    gt: 0
    lte: 100
  }];
  // 添加自定义验证规则
  option (buf.validate.message).cel = {
    id: "transactions_by_address_req",
    message: "from_address and to_address cannot both be empty",
    expression: "!((!has(this.from_address) || this.from_address == '') && (!has(this.to_address) || this.to_address == ''))"
  };
}



message TransactionReq {
  // 链索引
  int64 chain_index = 1;
  // 交易hash
  string txn = 2 [(buf.validate.field).string.min_len = 1];
}

message Transaction {
  // 交易hash
  string txn = 1;
  // 来源地址
  string from_address = 2;
  // 接收地址
  string to_address = 3;
  // 转账余额
  string value = 4;
  // 交易手续费
  string fee = 5;
  // 交易方法
  string method = 6;
  // 合约地址
  string program_id = 7;
  // 链上完成时间
  int64 timestamp = 8;
  // 上链状态 success、fail
  string status = 9;
  // 所在高度
  int64 block_number = 10;
  // chain_index
  int64 chain_index = 11;
}

message Network {
  // chain名称
  string name = 1;
  // symbol 标识
  string symbol = 2;
  // chainIndex
  int64 chain_index = 4;
  // 每条链的标记
  string chain_id = 5;
  //代币精度
  int64 decimals = 6;
  // 链图标
  string blockchain_url = 7;
  // token图标
  string token_url = 8;
  // 区块浏览器 地址
  string explorer_url = 9;
  // 支付gas的 symbol
  string gas_token_symbol = 10;
  // chain 类型
  string chain_type = 11;
  // handle
  string handle = 12;
  // 排序
  int64 sort_order = 13;
  // 链名称
  string chain_name = 14;
}

message TokenListReq {
  // 链索引(0,60,...支持查询多个链,查询全部就传空或不传)
  string chain_indexes = 1;
  // 支持名字或者合约地址
  string query = 2 ;
  // 查询页
  int64 page = 3 [(buf.validate.field).int64 = {
    gt: 0
  }];
  // 每页条数，限制200条
  int64 limit = 4 [(buf.validate.field).int64 = {
    gt: 0
    lte: 200
  }];
}

message Token {
  int64 chain_index = 1;
  string name = 2;
  string symbol = 3;
  int64 decimals = 4;
  string logo_url = 5;
  string address = 6;
  string chain_id = 7;
}

message TokenListReply {
  repeated Token list = 1;
  int64  count = 2;
}

message ReportInternalTxnReq {
  // chainIndex
  int64 chain_index = 1;
  // hash
  string txn = 3 [(buf.validate.field).string.min_len = 1];
}
