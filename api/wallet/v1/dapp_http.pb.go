// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.0
// - protoc             (unknown)
// source: api/wallet/v1/dapp.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationDappSrvGetDappCategory = "/api.wallet.v1.DappSrv/GetDappCategory"
const OperationDappSrvGetDappTopic = "/api.wallet.v1.DappSrv/GetDappTopic"
const OperationDappSrvListApprovalByUserAddress = "/api.wallet.v1.DappSrv/ListApprovalByUserAddress"
const OperationDappSrvListApprovedAddresses = "/api.wallet.v1.DappSrv/ListApprovedAddresses"
const OperationDappSrvListApprovedDappsByUserAddresses = "/api.wallet.v1.DappSrv/ListApprovedDappsByUserAddresses"
const OperationDappSrvListDappIndex = "/api.wallet.v1.DappSrv/ListDappIndex"
const OperationDappSrvListNavigation = "/api.wallet.v1.DappSrv/ListNavigation"
const OperationDappSrvListTokenApprovalsByDapp = "/api.wallet.v1.DappSrv/ListTokenApprovalsByDapp"
const OperationDappSrvSearchDapp = "/api.wallet.v1.DappSrv/SearchDapp"

type DappSrvHTTPServer interface {
	// GetDappCategory GetDappCategory 获取分类详情
	GetDappCategory(context.Context, *GetDappCategoryReq) (*GetDappCategoryReply, error)
	// GetDappTopic GetDappTopic 获取专题详情
	GetDappTopic(context.Context, *GetDappTopicReq) (*GetDappTopicReply, error)
	// ListApprovalByUserAddress ListApprovalByUserAddress 查询单个用户钱包地址授权记录
	ListApprovalByUserAddress(context.Context, *ListApprovalByUserAddressReq) (*ListApprovalByUserAddressReply, error)
	// ListApprovedAddresses ListApprovedAddresses 根据用户地址筛选出已授权的地址
	ListApprovedAddresses(context.Context, *ListApprovedAddressesReq) (*ListApprovedAddressesReply, error)
	// ListApprovedDappsByUserAddresses ListApprovedDappsByUserAddresses 根据用户地址查询已授权的dapp
	ListApprovedDappsByUserAddresses(context.Context, *ListApprovalByUserAddressesReq) (*ListApprovedDappsByUserAddressesReply, error)
	// ListDappIndex ListDappIndex 获取dapp首页列表
	ListDappIndex(context.Context, *ListDappIndexReq) (*ListDappIndexReply, error)
	// ListNavigation ListNavigation 获取dapp导航栏配置
	ListNavigation(context.Context, *ListNavigationReq) (*ListNavigationReply, error)
	// ListTokenApprovalsByDapp ListTokenApprovalsByDapp 根据dapp应用查询授权信息
	ListTokenApprovalsByDapp(context.Context, *ListTokenApprovalsByDappReq) (*ListTokenApprovalsByDappReply, error)
	// SearchDapp SearchDapp 搜索dapp
	SearchDapp(context.Context, *SearchDappReq) (*SearchDappReply, error)
}

func RegisterDappSrvHTTPServer(s *http.Server, srv DappSrvHTTPServer) {
	r := s.Route("/")
	r.GET("/v1/dapp/navigation", _DappSrv_ListNavigation0_HTTP_Handler(srv))
	r.GET("/v1/dapp/index", _DappSrv_ListDappIndex0_HTTP_Handler(srv))
	r.GET("/v1/dapp/topic/{id}", _DappSrv_GetDappTopic0_HTTP_Handler(srv))
	r.GET("/v1/dapp/category/{id}", _DappSrv_GetDappCategory0_HTTP_Handler(srv))
	r.GET("/v1/dapp", _DappSrv_SearchDapp0_HTTP_Handler(srv))
	r.POST("/v1/dapp/approved_addresses", _DappSrv_ListApprovedAddresses0_HTTP_Handler(srv))
	r.POST("/v1/dapp/address/approval", _DappSrv_ListApprovalByUserAddress0_HTTP_Handler(srv))
	r.POST("/v1/dapp/addresses/approval", _DappSrv_ListApprovedDappsByUserAddresses0_HTTP_Handler(srv))
	r.POST("/v1/dapp/blockchain/approval", _DappSrv_ListTokenApprovalsByDapp0_HTTP_Handler(srv))
}

func _DappSrv_ListNavigation0_HTTP_Handler(srv DappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListNavigationReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDappSrvListNavigation)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListNavigation(ctx, req.(*ListNavigationReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListNavigationReply)
		return ctx.Result(200, reply)
	}
}

func _DappSrv_ListDappIndex0_HTTP_Handler(srv DappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListDappIndexReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDappSrvListDappIndex)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListDappIndex(ctx, req.(*ListDappIndexReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListDappIndexReply)
		return ctx.Result(200, reply)
	}
}

func _DappSrv_GetDappTopic0_HTTP_Handler(srv DappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetDappTopicReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDappSrvGetDappTopic)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetDappTopic(ctx, req.(*GetDappTopicReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetDappTopicReply)
		return ctx.Result(200, reply)
	}
}

func _DappSrv_GetDappCategory0_HTTP_Handler(srv DappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetDappCategoryReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDappSrvGetDappCategory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetDappCategory(ctx, req.(*GetDappCategoryReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetDappCategoryReply)
		return ctx.Result(200, reply)
	}
}

func _DappSrv_SearchDapp0_HTTP_Handler(srv DappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SearchDappReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDappSrvSearchDapp)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SearchDapp(ctx, req.(*SearchDappReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SearchDappReply)
		return ctx.Result(200, reply)
	}
}

func _DappSrv_ListApprovedAddresses0_HTTP_Handler(srv DappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListApprovedAddressesReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDappSrvListApprovedAddresses)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListApprovedAddresses(ctx, req.(*ListApprovedAddressesReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListApprovedAddressesReply)
		return ctx.Result(200, reply)
	}
}

func _DappSrv_ListApprovalByUserAddress0_HTTP_Handler(srv DappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListApprovalByUserAddressReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDappSrvListApprovalByUserAddress)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListApprovalByUserAddress(ctx, req.(*ListApprovalByUserAddressReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListApprovalByUserAddressReply)
		return ctx.Result(200, reply)
	}
}

func _DappSrv_ListApprovedDappsByUserAddresses0_HTTP_Handler(srv DappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListApprovalByUserAddressesReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDappSrvListApprovedDappsByUserAddresses)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListApprovedDappsByUserAddresses(ctx, req.(*ListApprovalByUserAddressesReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListApprovedDappsByUserAddressesReply)
		return ctx.Result(200, reply)
	}
}

func _DappSrv_ListTokenApprovalsByDapp0_HTTP_Handler(srv DappSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListTokenApprovalsByDappReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDappSrvListTokenApprovalsByDapp)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListTokenApprovalsByDapp(ctx, req.(*ListTokenApprovalsByDappReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListTokenApprovalsByDappReply)
		return ctx.Result(200, reply)
	}
}

type DappSrvHTTPClient interface {
	GetDappCategory(ctx context.Context, req *GetDappCategoryReq, opts ...http.CallOption) (rsp *GetDappCategoryReply, err error)
	GetDappTopic(ctx context.Context, req *GetDappTopicReq, opts ...http.CallOption) (rsp *GetDappTopicReply, err error)
	ListApprovalByUserAddress(ctx context.Context, req *ListApprovalByUserAddressReq, opts ...http.CallOption) (rsp *ListApprovalByUserAddressReply, err error)
	ListApprovedAddresses(ctx context.Context, req *ListApprovedAddressesReq, opts ...http.CallOption) (rsp *ListApprovedAddressesReply, err error)
	ListApprovedDappsByUserAddresses(ctx context.Context, req *ListApprovalByUserAddressesReq, opts ...http.CallOption) (rsp *ListApprovedDappsByUserAddressesReply, err error)
	ListDappIndex(ctx context.Context, req *ListDappIndexReq, opts ...http.CallOption) (rsp *ListDappIndexReply, err error)
	ListNavigation(ctx context.Context, req *ListNavigationReq, opts ...http.CallOption) (rsp *ListNavigationReply, err error)
	ListTokenApprovalsByDapp(ctx context.Context, req *ListTokenApprovalsByDappReq, opts ...http.CallOption) (rsp *ListTokenApprovalsByDappReply, err error)
	SearchDapp(ctx context.Context, req *SearchDappReq, opts ...http.CallOption) (rsp *SearchDappReply, err error)
}

type DappSrvHTTPClientImpl struct {
	cc *http.Client
}

func NewDappSrvHTTPClient(client *http.Client) DappSrvHTTPClient {
	return &DappSrvHTTPClientImpl{client}
}

func (c *DappSrvHTTPClientImpl) GetDappCategory(ctx context.Context, in *GetDappCategoryReq, opts ...http.CallOption) (*GetDappCategoryReply, error) {
	var out GetDappCategoryReply
	pattern := "/v1/dapp/category/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDappSrvGetDappCategory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DappSrvHTTPClientImpl) GetDappTopic(ctx context.Context, in *GetDappTopicReq, opts ...http.CallOption) (*GetDappTopicReply, error) {
	var out GetDappTopicReply
	pattern := "/v1/dapp/topic/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDappSrvGetDappTopic))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DappSrvHTTPClientImpl) ListApprovalByUserAddress(ctx context.Context, in *ListApprovalByUserAddressReq, opts ...http.CallOption) (*ListApprovalByUserAddressReply, error) {
	var out ListApprovalByUserAddressReply
	pattern := "/v1/dapp/address/approval"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDappSrvListApprovalByUserAddress))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DappSrvHTTPClientImpl) ListApprovedAddresses(ctx context.Context, in *ListApprovedAddressesReq, opts ...http.CallOption) (*ListApprovedAddressesReply, error) {
	var out ListApprovedAddressesReply
	pattern := "/v1/dapp/approved_addresses"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDappSrvListApprovedAddresses))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DappSrvHTTPClientImpl) ListApprovedDappsByUserAddresses(ctx context.Context, in *ListApprovalByUserAddressesReq, opts ...http.CallOption) (*ListApprovedDappsByUserAddressesReply, error) {
	var out ListApprovedDappsByUserAddressesReply
	pattern := "/v1/dapp/addresses/approval"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDappSrvListApprovedDappsByUserAddresses))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DappSrvHTTPClientImpl) ListDappIndex(ctx context.Context, in *ListDappIndexReq, opts ...http.CallOption) (*ListDappIndexReply, error) {
	var out ListDappIndexReply
	pattern := "/v1/dapp/index"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDappSrvListDappIndex))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DappSrvHTTPClientImpl) ListNavigation(ctx context.Context, in *ListNavigationReq, opts ...http.CallOption) (*ListNavigationReply, error) {
	var out ListNavigationReply
	pattern := "/v1/dapp/navigation"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDappSrvListNavigation))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DappSrvHTTPClientImpl) ListTokenApprovalsByDapp(ctx context.Context, in *ListTokenApprovalsByDappReq, opts ...http.CallOption) (*ListTokenApprovalsByDappReply, error) {
	var out ListTokenApprovalsByDappReply
	pattern := "/v1/dapp/blockchain/approval"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDappSrvListTokenApprovalsByDapp))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DappSrvHTTPClientImpl) SearchDapp(ctx context.Context, in *SearchDappReq, opts ...http.CallOption) (*SearchDappReply, error) {
	var out SearchDappReply
	pattern := "/v1/dapp"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDappSrvSearchDapp))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
