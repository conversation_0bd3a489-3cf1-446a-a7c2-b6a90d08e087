syntax = "proto3";

package api.wallet.v1;

import "buf/validate/validate.proto";
import "google/api/annotations.proto";
import "api/wallet/v1/wallet.proto";

option go_package = "byd_wallet/api/wallet/v1;v1";

service SwapService {
  // 查询支持的链
  rpc ListBlockchainNetwork (ListBlockchainNetworkRequest) returns (ListBlockchainNetworkReply) {
    option (google.api.http) = {
      get: "/v1/swap/blockchain_network"
    };
  }
  // 查询兑换的币种列表
  rpc ListToken (ListTokenRequest) returns (ListTokenReply) {
    option (google.api.http) = {
      get: "/v1/swap/token"
    };
  }
  // 查询热门代币
  rpc ListHotToken (ListHotTokenRequest) returns (ListHotTokenReply) {
    option (google.api.http) = {
      get: "/v1/swap/token/hot"
    };
  }
  // 兑换询价
  rpc MultiQuote (MultiQuoteRequest) returns (MultiQuoteReply) {
    option (google.api.http) = {
      post: "/v1/swap/multi_quote"
      body: "*"
    };
  }
  // 兑换
  rpc Swap (SwapRequest) returns (SwapReply) {
    option (google.api.http) = {
      post: "/v1/swap"
      body: "*"
    };
  }
  // 添加兑换记录
  rpc AddSwapRecord (AddSwapRecordRequest) returns (SwapRecord) {
    option (google.api.http) = {
      post: "/v1/swap/record"
      body: "*"
    };
  }
  // 查询兑换记录
  rpc GetSwapRecord (GetSwapRecordRequest) returns (SwapRecord) {
    option (google.api.http) = {
      get: "/v1/swap/record/{hash}"
    };
  }
  // 查询兑换记录列表
  rpc ListSwapRecord (ListSwapRecordRequest) returns (ListSwapRecordReply) {
    option (google.api.http) = {
      post: "/v1/swap/record/list"
      body: "*"
    };
  }
}

message ListSwapRecordRequest {
  // 页码 从1开始
  int64 page = 1 [(buf.validate.field).int64.gt = 0];
  // 每页展示条数
  int64 limit = 2 [(buf.validate.field).int64 = {
    gt: 0
    lte: 100
  }];
  // 用户钱包地址
  repeated string addresses = 3;
}

message ListSwapRecordReply {
  repeated SwapRecord list = 1;
  // 总条数
  int64 count = 2;
}

message GetSwapRecordRequest {
  // 兑换记录hash
  string hash = 1 [(buf.validate.field).string.min_len = 1];
}

message AddSwapRecordRequest {
  SwapToken from = 1 [(buf.validate.field).required = true];
  SwapToken to = 2 [(buf.validate.field).required = true];
  string path = 3;
  string dex = 4 [(buf.validate.field).string.min_len = 1];
  // 滑点百分比，比如传1代表滑点1%
  string slippage = 5;
  // 客户端上传的交易hash
  string hash = 6 [(buf.validate.field).string.min_len = 1];
  // 授权hash
  string approval_hash = 7;
  // 汇率
  string swap_price = 8;
  // 费率
  string fee_rate = 9 [(buf.validate.field).string.min_len = 1];
  // 预估时间：1(1-3分钟)，2(1-10分钟)，3(5-30分钟)
  string estimated_time = 10;
  // 矿工费
  string gas_fee = 11;
  string order_id = 12;
}

message SwapRecord {
  // 兑换记录id
  uint64 id = 1;
  // 兑换时间(unix时间戳)
  int64 swapped_at = 2;
  // 兑换状态
  // pending(处理中), success(兑换成功), fail(兑换失败)
  string status = 3;
  SwapToken from = 4;
  SwapToken to = 5;
  // 用户兑换消耗的GasFee
  string gas_fee = 6;
  // 兑换手续费率
  string fee_rate = 7;
  // 用户发起的交易hash
  string hash = 8;
  // 授权hash
  string approval_hash = 9;
  // 区块高度
  int64 height = 10;
  // 兑换平台名称
  string dex = 11;
  // 兑换平台logo url
  string dex_logo = 12;
  // 兑换价格
  string swap_price = 13;
  // 兑换详情列表
  repeated SwapDetail details = 14;
  // 结束时间(unix时间戳)
  int64 finished_at = 15;
  // 预估时间：1(1-3分钟)，2(1-10分钟)，3(5-30分钟)
  string estimated_time = 16;
}

message SwapDetail {
  // 链索引
  int64 chain_index = 1;
  // 区块浏览器url
  string explorer_url = 2;
  // 交易hash
  string hash = 3;
  // 交易状态
  string status = 4;
}

message ListBlockchainNetworkRequest {}

message ListBlockchainNetworkReply {
  repeated Network list = 1;
}

message SwapRequest {
  SwapToken from = 1;
  SwapToken to = 2;
  string path = 3;
  string dex = 4;
  // 滑点百分比，比如传1代表滑点1%
  string slippage = 5;
}

message SwapReply {
  // 调用合约请求地址
  string from = 1;
  // 被调用合约地址
  string to = 2;
  // 调用传入金额
  string value = 3;
  string gas_limit = 4;
  string gas_price = 5;
  // 合约调用数据
  string data = 6;
  // 授权地址
  string approve_address = 7;
  // Deposit coin type,0 (common address, no call data);1 (common address,have call data);2(contract address,have call data )
  string to_type = 8;
  // Only the SWFT channel,deposit address
  string platform_addr = 9;
  // 兑换汇率
  string swap_price = 10;
  // 第三方原始数据
  string raw_data = 11;
  string order_id = 12;
}

message SwapToken {
  // 代币合约地址
  string token_address = 1;
  // 用户地址
  string address = 2 [(buf.validate.field).string.min_len = 1];
  // 链
  int64 chain_index = 3;
  // 数量
  string amount = 4;
  // token logo url
  string token_logo = 5;
  // 标的
  string symbol = 6;
  // 精度
  string decimals = 7;
}

message MultiQuoteRequest {
  SwapToken from = 1 [(buf.validate.field).required = true];
  SwapToken to = 2 [(buf.validate.field).required = true];
  // 滑点百分比，比如传1代表滑点1%
  string slippage = 3;
}

message MultiQuoteReply {
  repeated QuoteInfo list = 1;
}

message QuoteInfo {
  // 预估目标代币接收数量
  string receive_token_amount = 1;
  // 最小目标代币接收数量
  string min_receive_token_amount = 2;
  // 滑点百分比，比如1代表滑点1%
  string slippage = 3;
  // 兑换平台
  string dex = 4;
  // 兑换平台logo
  string dex_logo = 5;
  // 交易费率
  string fee_rate = 6;
  // 预估时间：1(1-3分钟)，2(1-10分钟)，3(5-30分钟)
  string estimated_time = 7;
  // 用于传给Swap接口
  string path = 8;
  // 最大兑换数量
  string max_from_token_amount = 9;
  // 最小兑换数量
  string min_from_token_amount = 10;
}

message ListTokenRequest {
  // 传-1 获取所有
  int64 chain_index = 1;
  string search_key = 2;
}

message ListTokenReply {
  repeated Token list = 1;
}

message ListHotTokenRequest {}

message ListHotTokenReply {
  repeated SwappableHotToken list = 1;
}

message SwappableHotToken {
  int64 chain_index = 1;
  string name = 2;
  string symbol = 3;
  int64 decimals = 4;
  string logo_url = 5;
  string address = 6;
  string chain_id = 7;
  int64 sort_order = 8;
  // 是否是全部的分组
  bool is_all = 9;
}