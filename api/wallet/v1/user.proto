syntax = "proto3";

package api.wallet.v1;

option go_package = "byd_wallet/api/wallet/v1;v1";

import "google/api/annotations.proto";
import "buf/validate/validate.proto";

service UserSrv {
	// 生成wallet Id
	rpc UserRegister (UserRegisterReq) returns (UserRegisterReply) {
		option (google.api.http) = {
            post: "/v1/wallet/user_register"
            body: "*"
        };
	}

	// 更新wallet Id
	rpc UpdateWalletID (UpdateWalletIDReq) returns (UpdateWalletIDReply) {
		option (google.api.http) = {
            post: "/v1/wallet/update_wallet_id"
            body: "*"
        };
	}

	// 获取wallet Id
	rpc GetWalletIds (GetWalletIdReq) returns (GeWalletIdsReply) {
		option (google.api.http) = {
			get: "/v1/wallet/ids"
		};
	}
}

message BaseSignPayload {
	// 签名数据
	string sign = 1 [(buf.validate.field).string.min_len=1];
	// 签名消息 如注册walletId:"[{\"chainIndex\":60,\"address\":\"******************************************\"}]" UpDataWalletId:"{\"oldWalletId\":\"888888\",\"newWalletId\":\"99999999\"}"
	string message = 2 [(buf.validate.field).string.min_len=1];
	// 签名地址
	string address = 3 [(buf.validate.field).string.min_len=1];
	// chain_index
	int64 chain_index = 4;
}

message UserRegisterReq {
	BaseSignPayload base = 1 [(buf.validate.field).required = true];
}

message UserRegisterReply {
	string wallet_id = 1 [(buf.validate.field).required = true];
}

message UpdateWalletIDReq {
	BaseSignPayload base = 1;
}

message UpdateWalletIDReply{
	string wallet_id = 1;
}

message GetWalletIdReq{
	// 用户地址，多个地址以逗号隔开，如：0x123...23,0x23...434
	string addresses = 1;
}

message GeWalletIdsReply{
	// walletId 列表
	repeated GetWalletId list = 1;
}

message GetWalletId{
		string wallet_id = 1 [(buf.validate.field).string.min_len=1];
		string address = 2;
}