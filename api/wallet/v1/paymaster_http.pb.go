// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: api/wallet/v1/paymaster.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationPaymasterGetDepositAddress = "/api.wallet.v1.Paymaster/GetDepositAddress"
const OperationPaymasterGetDepositTokenList = "/api.wallet.v1.Paymaster/GetDepositTokenList"
const OperationPaymasterGetRelays = "/api.wallet.v1.Paymaster/GetRelays"
const OperationPaymasterPrepareTransaction = "/api.wallet.v1.Paymaster/PrepareTransaction"
const OperationPaymasterRelay = "/api.wallet.v1.Paymaster/Relay"

type PaymasterHTTPServer interface {
	// GetDepositAddress 获取paymaster充值地址
	GetDepositAddress(context.Context, *DepositAddressRequest) (*DepositAddressReply, error)
	// GetDepositTokenList 获取支持代币充值
	GetDepositTokenList(context.Context, *emptypb.Empty) (*DepositTokensReply, error)
	// GetRelays 获取gas pool交易记录
	GetRelays(context.Context, *GetRelayReq) (*GetRelayReply, error)
	// PrepareTransaction 获取gas pool交易记录
	PrepareTransaction(context.Context, *RelayRequest) (*PrepareTransactionReply, error)
	// Relay 发起交易
	Relay(context.Context, *RelayRequest) (*RelayResponse, error)
}

func RegisterPaymasterHTTPServer(s *http.Server, srv PaymasterHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/paymaster/deposit/address", _Paymaster_GetDepositAddress0_HTTP_Handler(srv))
	r.GET("/v1/paymaster/deposit/tokens", _Paymaster_GetDepositTokenList0_HTTP_Handler(srv))
	r.POST("/v1/paymaster/transaction/relay", _Paymaster_Relay0_HTTP_Handler(srv))
	r.GET("/v1/paymaster/relayList", _Paymaster_GetRelays0_HTTP_Handler(srv))
	r.POST("/v1/paymaster/transaction/prepare", _Paymaster_PrepareTransaction0_HTTP_Handler(srv))
}

func _Paymaster_GetDepositAddress0_HTTP_Handler(srv PaymasterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DepositAddressRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPaymasterGetDepositAddress)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetDepositAddress(ctx, req.(*DepositAddressRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DepositAddressReply)
		return ctx.Result(200, reply)
	}
}

func _Paymaster_GetDepositTokenList0_HTTP_Handler(srv PaymasterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in emptypb.Empty
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPaymasterGetDepositTokenList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetDepositTokenList(ctx, req.(*emptypb.Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DepositTokensReply)
		return ctx.Result(200, reply)
	}
}

func _Paymaster_Relay0_HTTP_Handler(srv PaymasterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RelayRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPaymasterRelay)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Relay(ctx, req.(*RelayRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RelayResponse)
		return ctx.Result(200, reply)
	}
}

func _Paymaster_GetRelays0_HTTP_Handler(srv PaymasterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetRelayReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPaymasterGetRelays)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetRelays(ctx, req.(*GetRelayReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetRelayReply)
		return ctx.Result(200, reply)
	}
}

func _Paymaster_PrepareTransaction0_HTTP_Handler(srv PaymasterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RelayRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPaymasterPrepareTransaction)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PrepareTransaction(ctx, req.(*RelayRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PrepareTransactionReply)
		return ctx.Result(200, reply)
	}
}

type PaymasterHTTPClient interface {
	GetDepositAddress(ctx context.Context, req *DepositAddressRequest, opts ...http.CallOption) (rsp *DepositAddressReply, err error)
	GetDepositTokenList(ctx context.Context, req *emptypb.Empty, opts ...http.CallOption) (rsp *DepositTokensReply, err error)
	GetRelays(ctx context.Context, req *GetRelayReq, opts ...http.CallOption) (rsp *GetRelayReply, err error)
	PrepareTransaction(ctx context.Context, req *RelayRequest, opts ...http.CallOption) (rsp *PrepareTransactionReply, err error)
	Relay(ctx context.Context, req *RelayRequest, opts ...http.CallOption) (rsp *RelayResponse, err error)
}

type PaymasterHTTPClientImpl struct {
	cc *http.Client
}

func NewPaymasterHTTPClient(client *http.Client) PaymasterHTTPClient {
	return &PaymasterHTTPClientImpl{client}
}

func (c *PaymasterHTTPClientImpl) GetDepositAddress(ctx context.Context, in *DepositAddressRequest, opts ...http.CallOption) (*DepositAddressReply, error) {
	var out DepositAddressReply
	pattern := "/v1/paymaster/deposit/address"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPaymasterGetDepositAddress))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PaymasterHTTPClientImpl) GetDepositTokenList(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*DepositTokensReply, error) {
	var out DepositTokensReply
	pattern := "/v1/paymaster/deposit/tokens"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPaymasterGetDepositTokenList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PaymasterHTTPClientImpl) GetRelays(ctx context.Context, in *GetRelayReq, opts ...http.CallOption) (*GetRelayReply, error) {
	var out GetRelayReply
	pattern := "/v1/paymaster/relayList"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPaymasterGetRelays))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PaymasterHTTPClientImpl) PrepareTransaction(ctx context.Context, in *RelayRequest, opts ...http.CallOption) (*PrepareTransactionReply, error) {
	var out PrepareTransactionReply
	pattern := "/v1/paymaster/transaction/prepare"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPaymasterPrepareTransaction))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PaymasterHTTPClientImpl) Relay(ctx context.Context, in *RelayRequest, opts ...http.CallOption) (*RelayResponse, error) {
	var out RelayResponse
	pattern := "/v1/paymaster/transaction/relay"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPaymasterRelay))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
