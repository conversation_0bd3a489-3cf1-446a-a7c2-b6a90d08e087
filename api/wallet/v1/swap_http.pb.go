// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.0
// - protoc             (unknown)
// source: api/wallet/v1/swap.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationSwapServiceAddSwapRecord = "/api.wallet.v1.SwapService/AddSwapRecord"
const OperationSwapServiceGetSwapRecord = "/api.wallet.v1.SwapService/GetSwapRecord"
const OperationSwapServiceListBlockchainNetwork = "/api.wallet.v1.SwapService/ListBlockchainNetwork"
const OperationSwapServiceListHotToken = "/api.wallet.v1.SwapService/ListHotToken"
const OperationSwapServiceListSwapRecord = "/api.wallet.v1.SwapService/ListSwapRecord"
const OperationSwapServiceListToken = "/api.wallet.v1.SwapService/ListToken"
const OperationSwapServiceMultiQuote = "/api.wallet.v1.SwapService/MultiQuote"
const OperationSwapServiceSwap = "/api.wallet.v1.SwapService/Swap"

type SwapServiceHTTPServer interface {
	// AddSwapRecord 添加兑换记录
	AddSwapRecord(context.Context, *AddSwapRecordRequest) (*SwapRecord, error)
	// GetSwapRecord 查询兑换记录
	GetSwapRecord(context.Context, *GetSwapRecordRequest) (*SwapRecord, error)
	// ListBlockchainNetwork 查询支持的链
	ListBlockchainNetwork(context.Context, *ListBlockchainNetworkRequest) (*ListBlockchainNetworkReply, error)
	// ListHotToken 查询热门代币
	ListHotToken(context.Context, *ListHotTokenRequest) (*ListHotTokenReply, error)
	// ListSwapRecord 查询兑换记录列表
	ListSwapRecord(context.Context, *ListSwapRecordRequest) (*ListSwapRecordReply, error)
	// ListToken 查询兑换的币种列表
	ListToken(context.Context, *ListTokenRequest) (*ListTokenReply, error)
	// MultiQuote 兑换询价
	MultiQuote(context.Context, *MultiQuoteRequest) (*MultiQuoteReply, error)
	// Swap 兑换
	Swap(context.Context, *SwapRequest) (*SwapReply, error)
}

func RegisterSwapServiceHTTPServer(s *http.Server, srv SwapServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/v1/swap/blockchain_network", _SwapService_ListBlockchainNetwork0_HTTP_Handler(srv))
	r.GET("/v1/swap/token", _SwapService_ListToken0_HTTP_Handler(srv))
	r.GET("/v1/swap/token/hot", _SwapService_ListHotToken0_HTTP_Handler(srv))
	r.POST("/v1/swap/multi_quote", _SwapService_MultiQuote0_HTTP_Handler(srv))
	r.POST("/v1/swap", _SwapService_Swap0_HTTP_Handler(srv))
	r.POST("/v1/swap/record", _SwapService_AddSwapRecord0_HTTP_Handler(srv))
	r.GET("/v1/swap/record/{hash}", _SwapService_GetSwapRecord0_HTTP_Handler(srv))
	r.POST("/v1/swap/record/list", _SwapService_ListSwapRecord0_HTTP_Handler(srv))
}

func _SwapService_ListBlockchainNetwork0_HTTP_Handler(srv SwapServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListBlockchainNetworkRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSwapServiceListBlockchainNetwork)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListBlockchainNetwork(ctx, req.(*ListBlockchainNetworkRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListBlockchainNetworkReply)
		return ctx.Result(200, reply)
	}
}

func _SwapService_ListToken0_HTTP_Handler(srv SwapServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListTokenRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSwapServiceListToken)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListToken(ctx, req.(*ListTokenRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListTokenReply)
		return ctx.Result(200, reply)
	}
}

func _SwapService_ListHotToken0_HTTP_Handler(srv SwapServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListHotTokenRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSwapServiceListHotToken)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListHotToken(ctx, req.(*ListHotTokenRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListHotTokenReply)
		return ctx.Result(200, reply)
	}
}

func _SwapService_MultiQuote0_HTTP_Handler(srv SwapServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in MultiQuoteRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSwapServiceMultiQuote)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.MultiQuote(ctx, req.(*MultiQuoteRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*MultiQuoteReply)
		return ctx.Result(200, reply)
	}
}

func _SwapService_Swap0_HTTP_Handler(srv SwapServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SwapRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSwapServiceSwap)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Swap(ctx, req.(*SwapRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SwapReply)
		return ctx.Result(200, reply)
	}
}

func _SwapService_AddSwapRecord0_HTTP_Handler(srv SwapServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AddSwapRecordRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSwapServiceAddSwapRecord)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddSwapRecord(ctx, req.(*AddSwapRecordRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SwapRecord)
		return ctx.Result(200, reply)
	}
}

func _SwapService_GetSwapRecord0_HTTP_Handler(srv SwapServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetSwapRecordRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSwapServiceGetSwapRecord)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetSwapRecord(ctx, req.(*GetSwapRecordRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SwapRecord)
		return ctx.Result(200, reply)
	}
}

func _SwapService_ListSwapRecord0_HTTP_Handler(srv SwapServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListSwapRecordRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSwapServiceListSwapRecord)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListSwapRecord(ctx, req.(*ListSwapRecordRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListSwapRecordReply)
		return ctx.Result(200, reply)
	}
}

type SwapServiceHTTPClient interface {
	AddSwapRecord(ctx context.Context, req *AddSwapRecordRequest, opts ...http.CallOption) (rsp *SwapRecord, err error)
	GetSwapRecord(ctx context.Context, req *GetSwapRecordRequest, opts ...http.CallOption) (rsp *SwapRecord, err error)
	ListBlockchainNetwork(ctx context.Context, req *ListBlockchainNetworkRequest, opts ...http.CallOption) (rsp *ListBlockchainNetworkReply, err error)
	ListHotToken(ctx context.Context, req *ListHotTokenRequest, opts ...http.CallOption) (rsp *ListHotTokenReply, err error)
	ListSwapRecord(ctx context.Context, req *ListSwapRecordRequest, opts ...http.CallOption) (rsp *ListSwapRecordReply, err error)
	ListToken(ctx context.Context, req *ListTokenRequest, opts ...http.CallOption) (rsp *ListTokenReply, err error)
	MultiQuote(ctx context.Context, req *MultiQuoteRequest, opts ...http.CallOption) (rsp *MultiQuoteReply, err error)
	Swap(ctx context.Context, req *SwapRequest, opts ...http.CallOption) (rsp *SwapReply, err error)
}

type SwapServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewSwapServiceHTTPClient(client *http.Client) SwapServiceHTTPClient {
	return &SwapServiceHTTPClientImpl{client}
}

func (c *SwapServiceHTTPClientImpl) AddSwapRecord(ctx context.Context, in *AddSwapRecordRequest, opts ...http.CallOption) (*SwapRecord, error) {
	var out SwapRecord
	pattern := "/v1/swap/record"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSwapServiceAddSwapRecord))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SwapServiceHTTPClientImpl) GetSwapRecord(ctx context.Context, in *GetSwapRecordRequest, opts ...http.CallOption) (*SwapRecord, error) {
	var out SwapRecord
	pattern := "/v1/swap/record/{hash}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSwapServiceGetSwapRecord))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SwapServiceHTTPClientImpl) ListBlockchainNetwork(ctx context.Context, in *ListBlockchainNetworkRequest, opts ...http.CallOption) (*ListBlockchainNetworkReply, error) {
	var out ListBlockchainNetworkReply
	pattern := "/v1/swap/blockchain_network"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSwapServiceListBlockchainNetwork))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SwapServiceHTTPClientImpl) ListHotToken(ctx context.Context, in *ListHotTokenRequest, opts ...http.CallOption) (*ListHotTokenReply, error) {
	var out ListHotTokenReply
	pattern := "/v1/swap/token/hot"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSwapServiceListHotToken))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SwapServiceHTTPClientImpl) ListSwapRecord(ctx context.Context, in *ListSwapRecordRequest, opts ...http.CallOption) (*ListSwapRecordReply, error) {
	var out ListSwapRecordReply
	pattern := "/v1/swap/record/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSwapServiceListSwapRecord))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SwapServiceHTTPClientImpl) ListToken(ctx context.Context, in *ListTokenRequest, opts ...http.CallOption) (*ListTokenReply, error) {
	var out ListTokenReply
	pattern := "/v1/swap/token"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSwapServiceListToken))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SwapServiceHTTPClientImpl) MultiQuote(ctx context.Context, in *MultiQuoteRequest, opts ...http.CallOption) (*MultiQuoteReply, error) {
	var out MultiQuoteReply
	pattern := "/v1/swap/multi_quote"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSwapServiceMultiQuote))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SwapServiceHTTPClientImpl) Swap(ctx context.Context, in *SwapRequest, opts ...http.CallOption) (*SwapReply, error) {
	var out SwapReply
	pattern := "/v1/swap"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSwapServiceSwap))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
