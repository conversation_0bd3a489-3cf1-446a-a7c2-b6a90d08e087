// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: api/wallet/v1/market.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	MarketSrv_QueryCoinPrice_FullMethodName       = "/api.wallet.v1.MarketSrv/QueryCoinPrice"
	MarketSrv_QueryCurrencyUSDRate_FullMethodName = "/api.wallet.v1.MarketSrv/QueryCurrencyUSDRate"
	MarketSrv_QuerySimpleKline_FullMethodName     = "/api.wallet.v1.MarketSrv/QuerySimpleKline"
	MarketSrv_ListPopularToken_FullMethodName     = "/api.wallet.v1.MarketSrv/ListPopularToken"
)

// MarketSrvClient is the client API for MarketSrv service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MarketSrvClient interface {
	// 查询币种价格
	QueryCoinPrice(ctx context.Context, in *QueryCoinPriceReq, opts ...grpc.CallOption) (*QueryCoinPriceReply, error)
	// 查询USD汇率
	QueryCurrencyUSDRate(ctx context.Context, in *QueryCurrencyUSDRateReq, opts ...grpc.CallOption) (*CurrencyUSDRate, error)
	// 查询24小时简易k线(仅收盘价)
	QuerySimpleKline(ctx context.Context, in *QuerySimpleKlineReq, opts ...grpc.CallOption) (*QuerySimpleKlineReply, error)
	// 获取热门代币列表
	ListPopularToken(ctx context.Context, in *ListPopularTokenReq, opts ...grpc.CallOption) (*ListPopularTokenReply, error)
}

type marketSrvClient struct {
	cc grpc.ClientConnInterface
}

func NewMarketSrvClient(cc grpc.ClientConnInterface) MarketSrvClient {
	return &marketSrvClient{cc}
}

func (c *marketSrvClient) QueryCoinPrice(ctx context.Context, in *QueryCoinPriceReq, opts ...grpc.CallOption) (*QueryCoinPriceReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryCoinPriceReply)
	err := c.cc.Invoke(ctx, MarketSrv_QueryCoinPrice_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketSrvClient) QueryCurrencyUSDRate(ctx context.Context, in *QueryCurrencyUSDRateReq, opts ...grpc.CallOption) (*CurrencyUSDRate, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CurrencyUSDRate)
	err := c.cc.Invoke(ctx, MarketSrv_QueryCurrencyUSDRate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketSrvClient) QuerySimpleKline(ctx context.Context, in *QuerySimpleKlineReq, opts ...grpc.CallOption) (*QuerySimpleKlineReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QuerySimpleKlineReply)
	err := c.cc.Invoke(ctx, MarketSrv_QuerySimpleKline_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketSrvClient) ListPopularToken(ctx context.Context, in *ListPopularTokenReq, opts ...grpc.CallOption) (*ListPopularTokenReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListPopularTokenReply)
	err := c.cc.Invoke(ctx, MarketSrv_ListPopularToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MarketSrvServer is the server API for MarketSrv service.
// All implementations must embed UnimplementedMarketSrvServer
// for forward compatibility.
type MarketSrvServer interface {
	// 查询币种价格
	QueryCoinPrice(context.Context, *QueryCoinPriceReq) (*QueryCoinPriceReply, error)
	// 查询USD汇率
	QueryCurrencyUSDRate(context.Context, *QueryCurrencyUSDRateReq) (*CurrencyUSDRate, error)
	// 查询24小时简易k线(仅收盘价)
	QuerySimpleKline(context.Context, *QuerySimpleKlineReq) (*QuerySimpleKlineReply, error)
	// 获取热门代币列表
	ListPopularToken(context.Context, *ListPopularTokenReq) (*ListPopularTokenReply, error)
	mustEmbedUnimplementedMarketSrvServer()
}

// UnimplementedMarketSrvServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedMarketSrvServer struct{}

func (UnimplementedMarketSrvServer) QueryCoinPrice(context.Context, *QueryCoinPriceReq) (*QueryCoinPriceReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryCoinPrice not implemented")
}
func (UnimplementedMarketSrvServer) QueryCurrencyUSDRate(context.Context, *QueryCurrencyUSDRateReq) (*CurrencyUSDRate, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryCurrencyUSDRate not implemented")
}
func (UnimplementedMarketSrvServer) QuerySimpleKline(context.Context, *QuerySimpleKlineReq) (*QuerySimpleKlineReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QuerySimpleKline not implemented")
}
func (UnimplementedMarketSrvServer) ListPopularToken(context.Context, *ListPopularTokenReq) (*ListPopularTokenReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPopularToken not implemented")
}
func (UnimplementedMarketSrvServer) mustEmbedUnimplementedMarketSrvServer() {}
func (UnimplementedMarketSrvServer) testEmbeddedByValue()                   {}

// UnsafeMarketSrvServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MarketSrvServer will
// result in compilation errors.
type UnsafeMarketSrvServer interface {
	mustEmbedUnimplementedMarketSrvServer()
}

func RegisterMarketSrvServer(s grpc.ServiceRegistrar, srv MarketSrvServer) {
	// If the following call pancis, it indicates UnimplementedMarketSrvServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&MarketSrv_ServiceDesc, srv)
}

func _MarketSrv_QueryCoinPrice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryCoinPriceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketSrvServer).QueryCoinPrice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketSrv_QueryCoinPrice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketSrvServer).QueryCoinPrice(ctx, req.(*QueryCoinPriceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketSrv_QueryCurrencyUSDRate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryCurrencyUSDRateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketSrvServer).QueryCurrencyUSDRate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketSrv_QueryCurrencyUSDRate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketSrvServer).QueryCurrencyUSDRate(ctx, req.(*QueryCurrencyUSDRateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketSrv_QuerySimpleKline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QuerySimpleKlineReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketSrvServer).QuerySimpleKline(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketSrv_QuerySimpleKline_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketSrvServer).QuerySimpleKline(ctx, req.(*QuerySimpleKlineReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketSrv_ListPopularToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPopularTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketSrvServer).ListPopularToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketSrv_ListPopularToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketSrvServer).ListPopularToken(ctx, req.(*ListPopularTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

// MarketSrv_ServiceDesc is the grpc.ServiceDesc for MarketSrv service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MarketSrv_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.wallet.v1.MarketSrv",
	HandlerType: (*MarketSrvServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryCoinPrice",
			Handler:    _MarketSrv_QueryCoinPrice_Handler,
		},
		{
			MethodName: "QueryCurrencyUSDRate",
			Handler:    _MarketSrv_QueryCurrencyUSDRate_Handler,
		},
		{
			MethodName: "QuerySimpleKline",
			Handler:    _MarketSrv_QuerySimpleKline_Handler,
		},
		{
			MethodName: "ListPopularToken",
			Handler:    _MarketSrv_ListPopularToken_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/wallet/v1/market.proto",
}
