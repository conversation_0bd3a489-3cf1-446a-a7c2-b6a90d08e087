// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: api/wallet/v1/dapp.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	DappSrv_ListNavigation_FullMethodName                   = "/api.wallet.v1.DappSrv/ListNavigation"
	DappSrv_ListDappIndex_FullMethodName                    = "/api.wallet.v1.DappSrv/ListDappIndex"
	DappSrv_GetDappTopic_FullMethodName                     = "/api.wallet.v1.DappSrv/GetDappTopic"
	DappSrv_GetDappCategory_FullMethodName                  = "/api.wallet.v1.DappSrv/GetDappCategory"
	DappSrv_SearchDapp_FullMethodName                       = "/api.wallet.v1.DappSrv/SearchDapp"
	DappSrv_ListApprovedAddresses_FullMethodName            = "/api.wallet.v1.DappSrv/ListApprovedAddresses"
	DappSrv_ListApprovalByUserAddress_FullMethodName        = "/api.wallet.v1.DappSrv/ListApprovalByUserAddress"
	DappSrv_ListApprovedDappsByUserAddresses_FullMethodName = "/api.wallet.v1.DappSrv/ListApprovedDappsByUserAddresses"
	DappSrv_ListTokenApprovalsByDapp_FullMethodName         = "/api.wallet.v1.DappSrv/ListTokenApprovalsByDapp"
)

// DappSrvClient is the client API for DappSrv service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DappSrvClient interface {
	// ListNavigation 获取dapp导航栏配置
	ListNavigation(ctx context.Context, in *ListNavigationReq, opts ...grpc.CallOption) (*ListNavigationReply, error)
	// ListDappIndex 获取dapp首页列表
	ListDappIndex(ctx context.Context, in *ListDappIndexReq, opts ...grpc.CallOption) (*ListDappIndexReply, error)
	// GetDappTopic 获取专题详情
	GetDappTopic(ctx context.Context, in *GetDappTopicReq, opts ...grpc.CallOption) (*GetDappTopicReply, error)
	// GetDappCategory 获取分类详情
	GetDappCategory(ctx context.Context, in *GetDappCategoryReq, opts ...grpc.CallOption) (*GetDappCategoryReply, error)
	// SearchDapp 搜索dapp
	SearchDapp(ctx context.Context, in *SearchDappReq, opts ...grpc.CallOption) (*SearchDappReply, error)
	// ListApprovedAddresses 根据用户地址筛选出已授权的地址
	ListApprovedAddresses(ctx context.Context, in *ListApprovedAddressesReq, opts ...grpc.CallOption) (*ListApprovedAddressesReply, error)
	// ListApprovalByUserAddress 查询单个用户钱包地址授权记录
	ListApprovalByUserAddress(ctx context.Context, in *ListApprovalByUserAddressReq, opts ...grpc.CallOption) (*ListApprovalByUserAddressReply, error)
	// ListApprovedDappsByUserAddresses 根据用户地址查询已授权的dapp
	ListApprovedDappsByUserAddresses(ctx context.Context, in *ListApprovalByUserAddressesReq, opts ...grpc.CallOption) (*ListApprovedDappsByUserAddressesReply, error)
	// ListTokenApprovalsByDapp 根据dapp应用查询授权信息
	ListTokenApprovalsByDapp(ctx context.Context, in *ListTokenApprovalsByDappReq, opts ...grpc.CallOption) (*ListTokenApprovalsByDappReply, error)
}

type dappSrvClient struct {
	cc grpc.ClientConnInterface
}

func NewDappSrvClient(cc grpc.ClientConnInterface) DappSrvClient {
	return &dappSrvClient{cc}
}

func (c *dappSrvClient) ListNavigation(ctx context.Context, in *ListNavigationReq, opts ...grpc.CallOption) (*ListNavigationReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListNavigationReply)
	err := c.cc.Invoke(ctx, DappSrv_ListNavigation_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dappSrvClient) ListDappIndex(ctx context.Context, in *ListDappIndexReq, opts ...grpc.CallOption) (*ListDappIndexReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListDappIndexReply)
	err := c.cc.Invoke(ctx, DappSrv_ListDappIndex_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dappSrvClient) GetDappTopic(ctx context.Context, in *GetDappTopicReq, opts ...grpc.CallOption) (*GetDappTopicReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDappTopicReply)
	err := c.cc.Invoke(ctx, DappSrv_GetDappTopic_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dappSrvClient) GetDappCategory(ctx context.Context, in *GetDappCategoryReq, opts ...grpc.CallOption) (*GetDappCategoryReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDappCategoryReply)
	err := c.cc.Invoke(ctx, DappSrv_GetDappCategory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dappSrvClient) SearchDapp(ctx context.Context, in *SearchDappReq, opts ...grpc.CallOption) (*SearchDappReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchDappReply)
	err := c.cc.Invoke(ctx, DappSrv_SearchDapp_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dappSrvClient) ListApprovedAddresses(ctx context.Context, in *ListApprovedAddressesReq, opts ...grpc.CallOption) (*ListApprovedAddressesReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListApprovedAddressesReply)
	err := c.cc.Invoke(ctx, DappSrv_ListApprovedAddresses_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dappSrvClient) ListApprovalByUserAddress(ctx context.Context, in *ListApprovalByUserAddressReq, opts ...grpc.CallOption) (*ListApprovalByUserAddressReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListApprovalByUserAddressReply)
	err := c.cc.Invoke(ctx, DappSrv_ListApprovalByUserAddress_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dappSrvClient) ListApprovedDappsByUserAddresses(ctx context.Context, in *ListApprovalByUserAddressesReq, opts ...grpc.CallOption) (*ListApprovedDappsByUserAddressesReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListApprovedDappsByUserAddressesReply)
	err := c.cc.Invoke(ctx, DappSrv_ListApprovedDappsByUserAddresses_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dappSrvClient) ListTokenApprovalsByDapp(ctx context.Context, in *ListTokenApprovalsByDappReq, opts ...grpc.CallOption) (*ListTokenApprovalsByDappReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListTokenApprovalsByDappReply)
	err := c.cc.Invoke(ctx, DappSrv_ListTokenApprovalsByDapp_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DappSrvServer is the server API for DappSrv service.
// All implementations must embed UnimplementedDappSrvServer
// for forward compatibility.
type DappSrvServer interface {
	// ListNavigation 获取dapp导航栏配置
	ListNavigation(context.Context, *ListNavigationReq) (*ListNavigationReply, error)
	// ListDappIndex 获取dapp首页列表
	ListDappIndex(context.Context, *ListDappIndexReq) (*ListDappIndexReply, error)
	// GetDappTopic 获取专题详情
	GetDappTopic(context.Context, *GetDappTopicReq) (*GetDappTopicReply, error)
	// GetDappCategory 获取分类详情
	GetDappCategory(context.Context, *GetDappCategoryReq) (*GetDappCategoryReply, error)
	// SearchDapp 搜索dapp
	SearchDapp(context.Context, *SearchDappReq) (*SearchDappReply, error)
	// ListApprovedAddresses 根据用户地址筛选出已授权的地址
	ListApprovedAddresses(context.Context, *ListApprovedAddressesReq) (*ListApprovedAddressesReply, error)
	// ListApprovalByUserAddress 查询单个用户钱包地址授权记录
	ListApprovalByUserAddress(context.Context, *ListApprovalByUserAddressReq) (*ListApprovalByUserAddressReply, error)
	// ListApprovedDappsByUserAddresses 根据用户地址查询已授权的dapp
	ListApprovedDappsByUserAddresses(context.Context, *ListApprovalByUserAddressesReq) (*ListApprovedDappsByUserAddressesReply, error)
	// ListTokenApprovalsByDapp 根据dapp应用查询授权信息
	ListTokenApprovalsByDapp(context.Context, *ListTokenApprovalsByDappReq) (*ListTokenApprovalsByDappReply, error)
	mustEmbedUnimplementedDappSrvServer()
}

// UnimplementedDappSrvServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedDappSrvServer struct{}

func (UnimplementedDappSrvServer) ListNavigation(context.Context, *ListNavigationReq) (*ListNavigationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListNavigation not implemented")
}
func (UnimplementedDappSrvServer) ListDappIndex(context.Context, *ListDappIndexReq) (*ListDappIndexReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDappIndex not implemented")
}
func (UnimplementedDappSrvServer) GetDappTopic(context.Context, *GetDappTopicReq) (*GetDappTopicReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDappTopic not implemented")
}
func (UnimplementedDappSrvServer) GetDappCategory(context.Context, *GetDappCategoryReq) (*GetDappCategoryReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDappCategory not implemented")
}
func (UnimplementedDappSrvServer) SearchDapp(context.Context, *SearchDappReq) (*SearchDappReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchDapp not implemented")
}
func (UnimplementedDappSrvServer) ListApprovedAddresses(context.Context, *ListApprovedAddressesReq) (*ListApprovedAddressesReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListApprovedAddresses not implemented")
}
func (UnimplementedDappSrvServer) ListApprovalByUserAddress(context.Context, *ListApprovalByUserAddressReq) (*ListApprovalByUserAddressReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListApprovalByUserAddress not implemented")
}
func (UnimplementedDappSrvServer) ListApprovedDappsByUserAddresses(context.Context, *ListApprovalByUserAddressesReq) (*ListApprovedDappsByUserAddressesReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListApprovedDappsByUserAddresses not implemented")
}
func (UnimplementedDappSrvServer) ListTokenApprovalsByDapp(context.Context, *ListTokenApprovalsByDappReq) (*ListTokenApprovalsByDappReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTokenApprovalsByDapp not implemented")
}
func (UnimplementedDappSrvServer) mustEmbedUnimplementedDappSrvServer() {}
func (UnimplementedDappSrvServer) testEmbeddedByValue()                 {}

// UnsafeDappSrvServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DappSrvServer will
// result in compilation errors.
type UnsafeDappSrvServer interface {
	mustEmbedUnimplementedDappSrvServer()
}

func RegisterDappSrvServer(s grpc.ServiceRegistrar, srv DappSrvServer) {
	// If the following call pancis, it indicates UnimplementedDappSrvServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&DappSrv_ServiceDesc, srv)
}

func _DappSrv_ListNavigation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNavigationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DappSrvServer).ListNavigation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DappSrv_ListNavigation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DappSrvServer).ListNavigation(ctx, req.(*ListNavigationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DappSrv_ListDappIndex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDappIndexReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DappSrvServer).ListDappIndex(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DappSrv_ListDappIndex_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DappSrvServer).ListDappIndex(ctx, req.(*ListDappIndexReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DappSrv_GetDappTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDappTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DappSrvServer).GetDappTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DappSrv_GetDappTopic_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DappSrvServer).GetDappTopic(ctx, req.(*GetDappTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DappSrv_GetDappCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDappCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DappSrvServer).GetDappCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DappSrv_GetDappCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DappSrvServer).GetDappCategory(ctx, req.(*GetDappCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DappSrv_SearchDapp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchDappReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DappSrvServer).SearchDapp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DappSrv_SearchDapp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DappSrvServer).SearchDapp(ctx, req.(*SearchDappReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DappSrv_ListApprovedAddresses_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListApprovedAddressesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DappSrvServer).ListApprovedAddresses(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DappSrv_ListApprovedAddresses_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DappSrvServer).ListApprovedAddresses(ctx, req.(*ListApprovedAddressesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DappSrv_ListApprovalByUserAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListApprovalByUserAddressReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DappSrvServer).ListApprovalByUserAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DappSrv_ListApprovalByUserAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DappSrvServer).ListApprovalByUserAddress(ctx, req.(*ListApprovalByUserAddressReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DappSrv_ListApprovedDappsByUserAddresses_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListApprovalByUserAddressesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DappSrvServer).ListApprovedDappsByUserAddresses(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DappSrv_ListApprovedDappsByUserAddresses_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DappSrvServer).ListApprovedDappsByUserAddresses(ctx, req.(*ListApprovalByUserAddressesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DappSrv_ListTokenApprovalsByDapp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTokenApprovalsByDappReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DappSrvServer).ListTokenApprovalsByDapp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DappSrv_ListTokenApprovalsByDapp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DappSrvServer).ListTokenApprovalsByDapp(ctx, req.(*ListTokenApprovalsByDappReq))
	}
	return interceptor(ctx, in, info, handler)
}

// DappSrv_ServiceDesc is the grpc.ServiceDesc for DappSrv service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DappSrv_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.wallet.v1.DappSrv",
	HandlerType: (*DappSrvServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListNavigation",
			Handler:    _DappSrv_ListNavigation_Handler,
		},
		{
			MethodName: "ListDappIndex",
			Handler:    _DappSrv_ListDappIndex_Handler,
		},
		{
			MethodName: "GetDappTopic",
			Handler:    _DappSrv_GetDappTopic_Handler,
		},
		{
			MethodName: "GetDappCategory",
			Handler:    _DappSrv_GetDappCategory_Handler,
		},
		{
			MethodName: "SearchDapp",
			Handler:    _DappSrv_SearchDapp_Handler,
		},
		{
			MethodName: "ListApprovedAddresses",
			Handler:    _DappSrv_ListApprovedAddresses_Handler,
		},
		{
			MethodName: "ListApprovalByUserAddress",
			Handler:    _DappSrv_ListApprovalByUserAddress_Handler,
		},
		{
			MethodName: "ListApprovedDappsByUserAddresses",
			Handler:    _DappSrv_ListApprovedDappsByUserAddresses_Handler,
		},
		{
			MethodName: "ListTokenApprovalsByDapp",
			Handler:    _DappSrv_ListTokenApprovalsByDapp_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/wallet/v1/dapp.proto",
}
