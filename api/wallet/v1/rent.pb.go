// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: api/wallet/v1/rent.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UploadHashReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UploadHashReply) Reset() {
	*x = UploadHashReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_rent_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadHashReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadHashReply) ProtoMessage() {}

func (x *UploadHashReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_rent_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadHashReply.ProtoReflect.Descriptor instead.
func (*UploadHashReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_rent_proto_rawDescGZIP(), []int{0}
}

type UploadHashReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderId    string `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	FromHash   string `protobuf:"bytes,2,opt,name=from_hash,json=fromHash,proto3" json:"from_hash,omitempty"`
	SignedData string `protobuf:"bytes,3,opt,name=signed_data,json=signedData,proto3" json:"signed_data,omitempty"`
}

func (x *UploadHashReq) Reset() {
	*x = UploadHashReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_rent_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadHashReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadHashReq) ProtoMessage() {}

func (x *UploadHashReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_rent_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadHashReq.ProtoReflect.Descriptor instead.
func (*UploadHashReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_rent_proto_rawDescGZIP(), []int{1}
}

func (x *UploadHashReq) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *UploadHashReq) GetFromHash() string {
	if x != nil {
		return x.FromHash
	}
	return ""
}

func (x *UploadHashReq) GetSignedData() string {
	if x != nil {
		return x.SignedData
	}
	return ""
}

type AddTronRentRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户地址（创建买单的钱包地址）
	FromAddress string `protobuf:"bytes,1,opt,name=from_address,json=fromAddress,proto3" json:"from_address,omitempty"`
	// 能量/带宽接收地址
	PledgeAddress string `protobuf:"bytes,2,opt,name=pledge_address,json=pledgeAddress,proto3" json:"pledge_address,omitempty"`
	// 能量数量（需大于32000）
	PledgeNum int64 `protobuf:"varint,3,opt,name=pledge_num,json=pledgeNum,proto3" json:"pledge_num,omitempty"`
}

func (x *AddTronRentRecordReq) Reset() {
	*x = AddTronRentRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_rent_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddTronRentRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddTronRentRecordReq) ProtoMessage() {}

func (x *AddTronRentRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_rent_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddTronRentRecordReq.ProtoReflect.Descriptor instead.
func (*AddTronRentRecordReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_rent_proto_rawDescGZIP(), []int{2}
}

func (x *AddTronRentRecordReq) GetFromAddress() string {
	if x != nil {
		return x.FromAddress
	}
	return ""
}

func (x *AddTronRentRecordReq) GetPledgeAddress() string {
	if x != nil {
		return x.PledgeAddress
	}
	return ""
}

func (x *AddTronRentRecordReq) GetPledgeNum() int64 {
	if x != nil {
		return x.PledgeNum
	}
	return 0
}

type AddTronRentRecordReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderId      string `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Transaction  string `protobuf:"bytes,2,opt,name=transaction,proto3" json:"transaction,omitempty"`
	PledgeTrxNum string `protobuf:"bytes,3,opt,name=pledge_trx_num,json=pledgeTrxNum,proto3" json:"pledge_trx_num,omitempty"`
}

func (x *AddTronRentRecordReply) Reset() {
	*x = AddTronRentRecordReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_rent_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddTronRentRecordReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddTronRentRecordReply) ProtoMessage() {}

func (x *AddTronRentRecordReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_rent_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddTronRentRecordReply.ProtoReflect.Descriptor instead.
func (*AddTronRentRecordReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_rent_proto_rawDescGZIP(), []int{3}
}

func (x *AddTronRentRecordReply) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *AddTronRentRecordReply) GetTransaction() string {
	if x != nil {
		return x.Transaction
	}
	return ""
}

func (x *AddTronRentRecordReply) GetPledgeTrxNum() string {
	if x != nil {
		return x.PledgeTrxNum
	}
	return ""
}

type QueryPreorderInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户地址（创建买单的钱包地址）
	FromAddress string `protobuf:"bytes,1,opt,name=from_address,json=fromAddress,proto3" json:"from_address,omitempty"`
	// 能量/带宽接收地址
	PledgeAddress string `protobuf:"bytes,2,opt,name=pledge_address,json=pledgeAddress,proto3" json:"pledge_address,omitempty"`
	// 能量数量（需大于32000）
	PledgeNum int64 `protobuf:"varint,3,opt,name=pledge_num,json=pledgeNum,proto3" json:"pledge_num,omitempty"`
}

func (x *QueryPreorderInfoReq) Reset() {
	*x = QueryPreorderInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_rent_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryPreorderInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryPreorderInfoReq) ProtoMessage() {}

func (x *QueryPreorderInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_rent_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryPreorderInfoReq.ProtoReflect.Descriptor instead.
func (*QueryPreorderInfoReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_rent_proto_rawDescGZIP(), []int{4}
}

func (x *QueryPreorderInfoReq) GetFromAddress() string {
	if x != nil {
		return x.FromAddress
	}
	return ""
}

func (x *QueryPreorderInfoReq) GetPledgeAddress() string {
	if x != nil {
		return x.PledgeAddress
	}
	return ""
}

func (x *QueryPreorderInfoReq) GetPledgeNum() int64 {
	if x != nil {
		return x.PledgeNum
	}
	return 0
}

type QueryPreorderInfoReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderPrice   string `protobuf:"bytes,1,opt,name=order_price,json=orderPrice,proto3" json:"order_price,omitempty"`
	PledgeNum    string `protobuf:"bytes,2,opt,name=pledge_num,json=pledgeNum,proto3" json:"pledge_num,omitempty"`
	PledgeTrxNum string `protobuf:"bytes,3,opt,name=pledge_trx_num,json=pledgeTrxNum,proto3" json:"pledge_trx_num,omitempty"`
}

func (x *QueryPreorderInfoReply) Reset() {
	*x = QueryPreorderInfoReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_rent_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryPreorderInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryPreorderInfoReply) ProtoMessage() {}

func (x *QueryPreorderInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_rent_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryPreorderInfoReply.ProtoReflect.Descriptor instead.
func (*QueryPreorderInfoReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_rent_proto_rawDescGZIP(), []int{5}
}

func (x *QueryPreorderInfoReply) GetOrderPrice() string {
	if x != nil {
		return x.OrderPrice
	}
	return ""
}

func (x *QueryPreorderInfoReply) GetPledgeNum() string {
	if x != nil {
		return x.PledgeNum
	}
	return ""
}

func (x *QueryPreorderInfoReply) GetPledgeTrxNum() string {
	if x != nil {
		return x.PledgeTrxNum
	}
	return ""
}

var File_api_wallet_v1_rent_proto protoreflect.FileDescriptor

var file_api_wallet_v1_rent_proto_rawDesc = []byte{
	0x0a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2f, 0x76, 0x31, 0x2f,
	0x72, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x11, 0x0a, 0x0f, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x48, 0x61,
	0x73, 0x68, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x83, 0x01, 0x0a, 0x0d, 0x55, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x48, 0x61, 0x73, 0x68, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x08, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x24, 0x0a,
	0x09, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x66, 0x72, 0x6f, 0x6d, 0x48,
	0x61, 0x73, 0x68, 0x12, 0x28, 0x0a, 0x0b, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x0a, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x22, 0x9a, 0x01,
	0x0a, 0x14, 0x41, 0x64, 0x64, 0x54, 0x72, 0x6f, 0x6e, 0x52, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x2a, 0x0a, 0x0c, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x66, 0x72, 0x6f, 0x6d, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x2e, 0x0a, 0x0e, 0x70, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x0d, 0x70, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x26, 0x0a, 0x0a, 0x70, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xba, 0x48, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x09, 0x70, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x22, 0x7b, 0x0a, 0x16, 0x41, 0x64,
	0x64, 0x54, 0x72, 0x6f, 0x6e, 0x52, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x20, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x24, 0x0a, 0x0e, 0x70, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x74, 0x72, 0x78, 0x5f,
	0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x54, 0x72, 0x78, 0x4e, 0x75, 0x6d, 0x22, 0x9a, 0x01, 0x0a, 0x14, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x50, 0x72, 0x65, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71,
	0x12, 0x2a, 0x0a, 0x0c, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x0b, 0x66, 0x72, 0x6f, 0x6d, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x2e, 0x0a, 0x0e,
	0x70, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0d, 0x70,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x26, 0x0a, 0x0a,
	0x70, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xba, 0x48, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x70, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x4e, 0x75, 0x6d, 0x22, 0x7e, 0x0a, 0x16, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x72, 0x65,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1f,
	0x0a, 0x0b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x70, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x24,
	0x0a, 0x0e, 0x70, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x74, 0x72, 0x78, 0x5f, 0x6e, 0x75, 0x6d,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x54, 0x72,
	0x78, 0x4e, 0x75, 0x6d, 0x32, 0xff, 0x02, 0x0a, 0x07, 0x54, 0x72, 0x6f, 0x6e, 0x53, 0x72, 0x76,
	0x12, 0x7d, 0x0a, 0x11, 0x41, 0x64, 0x64, 0x54, 0x72, 0x6f, 0x6e, 0x52, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c,
	0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x54, 0x72, 0x6f, 0x6e, 0x52, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x54, 0x72,
	0x6f, 0x6e, 0x52, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x1c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x3a, 0x01, 0x2a, 0x22, 0x11, 0x2f, 0x76,
	0x31, 0x2f, 0x74, 0x72, 0x6f, 0x6e, 0x2f, 0x72, 0x65, 0x6e, 0x74, 0x2f, 0x61, 0x64, 0x64, 0x12,
	0x82, 0x01, 0x0a, 0x11, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x72, 0x65, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c,
	0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x72, 0x65, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x50, 0x72, 0x65, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x3a, 0x01, 0x2a, 0x22, 0x16, 0x2f, 0x76,
	0x31, 0x2f, 0x74, 0x72, 0x6f, 0x6e, 0x2f, 0x72, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x65, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x12, 0x70, 0x0a, 0x0a, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x48, 0x61,
	0x73, 0x68, 0x12, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x48, 0x61, 0x73, 0x68, 0x52, 0x65, 0x71,
	0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x48, 0x61, 0x73, 0x68, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x3a, 0x01, 0x2a, 0x22, 0x19, 0x2f, 0x76, 0x31,
	0x2f, 0x74, 0x72, 0x6f, 0x6e, 0x2f, 0x72, 0x65, 0x6e, 0x74, 0x2f, 0x75, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x42, 0x91, 0x01, 0x0a, 0x11, 0x63, 0x6f, 0x6d, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x42, 0x09, 0x52, 0x65,
	0x6e, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x1b, 0x62, 0x79, 0x64, 0x5f, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0xa2, 0x02, 0x03, 0x41, 0x57, 0x58, 0xaa, 0x02, 0x0d, 0x41,
	0x70, 0x69, 0x2e, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x56, 0x31, 0xca, 0x02, 0x0d, 0x41,
	0x70, 0x69, 0x5c, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x5c, 0x56, 0x31, 0xe2, 0x02, 0x19, 0x41,
	0x70, 0x69, 0x5c, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x5c, 0x56, 0x31, 0x5c, 0x47, 0x50, 0x42,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0xea, 0x02, 0x0f, 0x41, 0x70, 0x69, 0x3a, 0x3a,
	0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x3a, 0x3a, 0x56, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_api_wallet_v1_rent_proto_rawDescOnce sync.Once
	file_api_wallet_v1_rent_proto_rawDescData = file_api_wallet_v1_rent_proto_rawDesc
)

func file_api_wallet_v1_rent_proto_rawDescGZIP() []byte {
	file_api_wallet_v1_rent_proto_rawDescOnce.Do(func() {
		file_api_wallet_v1_rent_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_wallet_v1_rent_proto_rawDescData)
	})
	return file_api_wallet_v1_rent_proto_rawDescData
}

var file_api_wallet_v1_rent_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_wallet_v1_rent_proto_goTypes = []any{
	(*UploadHashReply)(nil),        // 0: api.wallet.v1.UploadHashReply
	(*UploadHashReq)(nil),          // 1: api.wallet.v1.UploadHashReq
	(*AddTronRentRecordReq)(nil),   // 2: api.wallet.v1.AddTronRentRecordReq
	(*AddTronRentRecordReply)(nil), // 3: api.wallet.v1.AddTronRentRecordReply
	(*QueryPreorderInfoReq)(nil),   // 4: api.wallet.v1.QueryPreorderInfoReq
	(*QueryPreorderInfoReply)(nil), // 5: api.wallet.v1.QueryPreorderInfoReply
}
var file_api_wallet_v1_rent_proto_depIdxs = []int32{
	2, // 0: api.wallet.v1.TronSrv.AddTronRentRecord:input_type -> api.wallet.v1.AddTronRentRecordReq
	4, // 1: api.wallet.v1.TronSrv.QueryPreorderInfo:input_type -> api.wallet.v1.QueryPreorderInfoReq
	1, // 2: api.wallet.v1.TronSrv.UploadHash:input_type -> api.wallet.v1.UploadHashReq
	3, // 3: api.wallet.v1.TronSrv.AddTronRentRecord:output_type -> api.wallet.v1.AddTronRentRecordReply
	5, // 4: api.wallet.v1.TronSrv.QueryPreorderInfo:output_type -> api.wallet.v1.QueryPreorderInfoReply
	0, // 5: api.wallet.v1.TronSrv.UploadHash:output_type -> api.wallet.v1.UploadHashReply
	3, // [3:6] is the sub-list for method output_type
	0, // [0:3] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_wallet_v1_rent_proto_init() }
func file_api_wallet_v1_rent_proto_init() {
	if File_api_wallet_v1_rent_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_wallet_v1_rent_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*UploadHashReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_rent_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*UploadHashReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_rent_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*AddTronRentRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_rent_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*AddTronRentRecordReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_rent_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*QueryPreorderInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_rent_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*QueryPreorderInfoReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_wallet_v1_rent_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_wallet_v1_rent_proto_goTypes,
		DependencyIndexes: file_api_wallet_v1_rent_proto_depIdxs,
		MessageInfos:      file_api_wallet_v1_rent_proto_msgTypes,
	}.Build()
	File_api_wallet_v1_rent_proto = out.File
	file_api_wallet_v1_rent_proto_rawDesc = nil
	file_api_wallet_v1_rent_proto_goTypes = nil
	file_api_wallet_v1_rent_proto_depIdxs = nil
}
