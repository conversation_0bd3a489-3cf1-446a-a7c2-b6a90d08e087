syntax = "proto3";

package api.wallet.v1;

option go_package = "byd_wallet/api/wallet/v1;v1";

import "errors/errors.proto";

//
enum ErrorReason {
    // 注意：这里的 code 依然是 Kratos 内部建议的 HTTP 状态码，
    // 但我们会在 ErrorEncoder 中强制覆写为 200。
    option (errors.default_code) = 500;

    // 内部服务错误
    INTERNAL_SERVER = 0 [(errors.code) = 100];
    // 参数校验错误
    VALIDATOR = 1 [(errors.code) = 101];
    // 波场租赁错误
    RENT = 2 [(errors.code) = 102];
    // boss wallet  user 相关
    BOSSID_LOCK = 3 [(errors.code) = 103];
    BOSSID_EXISTS  = 4 [(errors.code) = 104];
    // 数据已存在
    DATA_EXISTS = 5 [(errors.code) = 105];

}
