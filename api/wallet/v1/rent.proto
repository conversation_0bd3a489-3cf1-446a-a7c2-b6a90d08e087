syntax = "proto3";

package api.wallet.v1;

option go_package = "byd_wallet/api/wallet/v1;v1";

import "google/api/annotations.proto";
import "buf/validate/validate.proto";

service TronSrv {
  // AddTronRentRecord 创建能量买单
  rpc AddTronRentRecord (AddTronRentRecordReq) returns (AddTronRentRecordReply) {
    option (google.api.http) = {
      post: "/v1/tron/rent/add"
      body: "*"
    };
  }
  // QueryPreorderInfo 查询预订单信息，用于预估租赁费用
  rpc QueryPreorderInfo (QueryPreorderInfoReq) returns (QueryPreorderInfoReply) {
    option (google.api.http) = {
      post: "/v1/tron/rent/preorder"
      body: "*"
    };
  }
  // UploadHash 上传买单哈希
  rpc UploadHash (UploadHashReq) returns (UploadHashReply) {
    option (google.api.http) = {
      post: "/v1/tron/rent/upload_hash"
      body: "*"
    };
  }

}

message UploadHashReply {}

message UploadHashReq {
  string order_id = 1 [(buf.validate.field).string.min_len = 1];
  string from_hash = 2 [(buf.validate.field).string.min_len = 1];
  string signed_data = 3 [(buf.validate.field).string.min_len = 1];
}

message AddTronRentRecordReq {
  // 用户地址（创建买单的钱包地址）
  string from_address = 1 [(buf.validate.field).string.min_len = 1];
  // 能量/带宽接收地址
  string pledge_address = 2 [(buf.validate.field).string.min_len = 1];
  // 能量数量（需大于32000）
  int64 pledge_num = 3 [(buf.validate.field).int64.gt = 0];
}

message AddTronRentRecordReply {
    string order_id = 1;
    string transaction = 2;
    string pledge_trx_num = 3;
}

message QueryPreorderInfoReq {
  // 用户地址（创建买单的钱包地址）
  string from_address = 1 [(buf.validate.field).string.min_len = 1];
  // 能量/带宽接收地址
  string pledge_address = 2 [(buf.validate.field).string.min_len = 1];
  // 能量数量（需大于32000）
  int64 pledge_num = 3 [(buf.validate.field).int64.gt = 0];
}

message QueryPreorderInfoReply {
  string order_price = 1;
  string pledge_num = 2;
  string pledge_trx_num = 3;
}