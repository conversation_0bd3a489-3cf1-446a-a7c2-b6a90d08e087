// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: api/wallet/v1/user.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BaseSignPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 签名数据
	Sign string `protobuf:"bytes,1,opt,name=sign,proto3" json:"sign,omitempty"`
	// 签名消息 如注册walletId:"[{\"chainIndex\":60,\"address\":\"******************************************\"}]" UpDataWalletId:"{\"oldWalletId\":\"888888\",\"newWalletId\":\"99999999\"}"
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// 签名地址
	Address string `protobuf:"bytes,3,opt,name=address,proto3" json:"address,omitempty"`
	// chain_index
	ChainIndex int64 `protobuf:"varint,4,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
}

func (x *BaseSignPayload) Reset() {
	*x = BaseSignPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_user_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BaseSignPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseSignPayload) ProtoMessage() {}

func (x *BaseSignPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_user_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseSignPayload.ProtoReflect.Descriptor instead.
func (*BaseSignPayload) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_user_proto_rawDescGZIP(), []int{0}
}

func (x *BaseSignPayload) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

func (x *BaseSignPayload) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *BaseSignPayload) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *BaseSignPayload) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

type UserRegisterReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Base *BaseSignPayload `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
}

func (x *UserRegisterReq) Reset() {
	*x = UserRegisterReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_user_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserRegisterReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserRegisterReq) ProtoMessage() {}

func (x *UserRegisterReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_user_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserRegisterReq.ProtoReflect.Descriptor instead.
func (*UserRegisterReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_user_proto_rawDescGZIP(), []int{1}
}

func (x *UserRegisterReq) GetBase() *BaseSignPayload {
	if x != nil {
		return x.Base
	}
	return nil
}

type UserRegisterReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WalletId string `protobuf:"bytes,1,opt,name=wallet_id,json=walletId,proto3" json:"wallet_id,omitempty"`
}

func (x *UserRegisterReply) Reset() {
	*x = UserRegisterReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_user_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserRegisterReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserRegisterReply) ProtoMessage() {}

func (x *UserRegisterReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_user_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserRegisterReply.ProtoReflect.Descriptor instead.
func (*UserRegisterReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_user_proto_rawDescGZIP(), []int{2}
}

func (x *UserRegisterReply) GetWalletId() string {
	if x != nil {
		return x.WalletId
	}
	return ""
}

type UpdateWalletIDReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Base *BaseSignPayload `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
}

func (x *UpdateWalletIDReq) Reset() {
	*x = UpdateWalletIDReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_user_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWalletIDReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWalletIDReq) ProtoMessage() {}

func (x *UpdateWalletIDReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_user_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWalletIDReq.ProtoReflect.Descriptor instead.
func (*UpdateWalletIDReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_user_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateWalletIDReq) GetBase() *BaseSignPayload {
	if x != nil {
		return x.Base
	}
	return nil
}

type UpdateWalletIDReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WalletId string `protobuf:"bytes,1,opt,name=wallet_id,json=walletId,proto3" json:"wallet_id,omitempty"`
}

func (x *UpdateWalletIDReply) Reset() {
	*x = UpdateWalletIDReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_user_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWalletIDReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWalletIDReply) ProtoMessage() {}

func (x *UpdateWalletIDReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_user_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWalletIDReply.ProtoReflect.Descriptor instead.
func (*UpdateWalletIDReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_user_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateWalletIDReply) GetWalletId() string {
	if x != nil {
		return x.WalletId
	}
	return ""
}

type GetWalletIdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户地址，多个地址以逗号隔开，如：0x123...23,0x23...434
	Addresses string `protobuf:"bytes,1,opt,name=addresses,proto3" json:"addresses,omitempty"`
}

func (x *GetWalletIdReq) Reset() {
	*x = GetWalletIdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_user_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWalletIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWalletIdReq) ProtoMessage() {}

func (x *GetWalletIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_user_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWalletIdReq.ProtoReflect.Descriptor instead.
func (*GetWalletIdReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_user_proto_rawDescGZIP(), []int{5}
}

func (x *GetWalletIdReq) GetAddresses() string {
	if x != nil {
		return x.Addresses
	}
	return ""
}

type GeWalletIdsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// walletId 列表
	List []*GetWalletId `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *GeWalletIdsReply) Reset() {
	*x = GeWalletIdsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_user_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GeWalletIdsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GeWalletIdsReply) ProtoMessage() {}

func (x *GeWalletIdsReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_user_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GeWalletIdsReply.ProtoReflect.Descriptor instead.
func (*GeWalletIdsReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_user_proto_rawDescGZIP(), []int{6}
}

func (x *GeWalletIdsReply) GetList() []*GetWalletId {
	if x != nil {
		return x.List
	}
	return nil
}

type GetWalletId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WalletId string `protobuf:"bytes,1,opt,name=wallet_id,json=walletId,proto3" json:"wallet_id,omitempty"`
	Address  string `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
}

func (x *GetWalletId) Reset() {
	*x = GetWalletId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_user_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWalletId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWalletId) ProtoMessage() {}

func (x *GetWalletId) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_user_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWalletId.ProtoReflect.Descriptor instead.
func (*GetWalletId) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_user_proto_rawDescGZIP(), []int{7}
}

func (x *GetWalletId) GetWalletId() string {
	if x != nil {
		return x.WalletId
	}
	return ""
}

func (x *GetWalletId) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

var File_api_wallet_v1_user_proto protoreflect.FileDescriptor

var file_api_wallet_v1_user_proto_rawDesc = []byte{
	0x0a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2f, 0x76, 0x31, 0x2f,
	0x75, 0x73, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x95, 0x01, 0x0a, 0x0f, 0x42, 0x61, 0x73, 0x65, 0x53, 0x69, 0x67,
	0x6e, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x1b, 0x0a, 0x04, 0x73, 0x69, 0x67, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x04, 0x73, 0x69, 0x67, 0x6e, 0x12, 0x21, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x21, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x4d, 0x0a, 0x0f,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12,
	0x3a, 0x0a, 0x04, 0x62, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61,
	0x73, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x42, 0x06, 0xba,
	0x48, 0x03, 0xc8, 0x01, 0x01, 0x52, 0x04, 0x62, 0x61, 0x73, 0x65, 0x22, 0x38, 0x0a, 0x11, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x23, 0x0a, 0x09, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x06, 0xba, 0x48, 0x03, 0xc8, 0x01, 0x01, 0x52, 0x08, 0x77, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x49, 0x64, 0x22, 0x47, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x49, 0x44, 0x52, 0x65, 0x71, 0x12, 0x32, 0x0a, 0x04, 0x62, 0x61,
	0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x53, 0x69, 0x67,
	0x6e, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x04, 0x62, 0x61, 0x73, 0x65, 0x22, 0x32,
	0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x49, 0x44,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x49, 0x64, 0x22, 0x2e, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x49,
	0x64, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x65, 0x73, 0x22, 0x42, 0x0a, 0x10, 0x47, 0x65, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x49, 0x64,
	0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2e, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x49, 0x64,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x4d, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x57, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x09, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x08, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x32, 0xe8, 0x02, 0x0a, 0x07, 0x55, 0x73, 0x65, 0x72, 0x53, 0x72,
	0x76, 0x12, 0x75, 0x0a, 0x0c, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65,
	0x72, 0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x3a, 0x01, 0x2a, 0x22, 0x18,
	0x2f, 0x76, 0x31, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x12, 0x7e, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x49, 0x44, 0x12, 0x20, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x49, 0x44, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x49, 0x44, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x3a, 0x01, 0x2a, 0x22, 0x1b, 0x2f, 0x76, 0x31,
	0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x66, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x57,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x61, 0x6c, 0x6c,
	0x65, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x49, 0x64, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x16, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x10,
	0x12, 0x0e, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2f, 0x69, 0x64, 0x73,
	0x42, 0x91, 0x01, 0x0a, 0x11, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x42, 0x09, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x50, 0x01, 0x5a, 0x1b, 0x62, 0x79, 0x64, 0x5f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31,
	0xa2, 0x02, 0x03, 0x41, 0x57, 0x58, 0xaa, 0x02, 0x0d, 0x41, 0x70, 0x69, 0x2e, 0x57, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x2e, 0x56, 0x31, 0xca, 0x02, 0x0d, 0x41, 0x70, 0x69, 0x5c, 0x57, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x5c, 0x56, 0x31, 0xe2, 0x02, 0x19, 0x41, 0x70, 0x69, 0x5c, 0x57, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x5c, 0x56, 0x31, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0xea, 0x02, 0x0f, 0x41, 0x70, 0x69, 0x3a, 0x3a, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x3a, 0x3a, 0x56, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_wallet_v1_user_proto_rawDescOnce sync.Once
	file_api_wallet_v1_user_proto_rawDescData = file_api_wallet_v1_user_proto_rawDesc
)

func file_api_wallet_v1_user_proto_rawDescGZIP() []byte {
	file_api_wallet_v1_user_proto_rawDescOnce.Do(func() {
		file_api_wallet_v1_user_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_wallet_v1_user_proto_rawDescData)
	})
	return file_api_wallet_v1_user_proto_rawDescData
}

var file_api_wallet_v1_user_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_api_wallet_v1_user_proto_goTypes = []any{
	(*BaseSignPayload)(nil),     // 0: api.wallet.v1.BaseSignPayload
	(*UserRegisterReq)(nil),     // 1: api.wallet.v1.UserRegisterReq
	(*UserRegisterReply)(nil),   // 2: api.wallet.v1.UserRegisterReply
	(*UpdateWalletIDReq)(nil),   // 3: api.wallet.v1.UpdateWalletIDReq
	(*UpdateWalletIDReply)(nil), // 4: api.wallet.v1.UpdateWalletIDReply
	(*GetWalletIdReq)(nil),      // 5: api.wallet.v1.GetWalletIdReq
	(*GeWalletIdsReply)(nil),    // 6: api.wallet.v1.GeWalletIdsReply
	(*GetWalletId)(nil),         // 7: api.wallet.v1.GetWalletId
}
var file_api_wallet_v1_user_proto_depIdxs = []int32{
	0, // 0: api.wallet.v1.UserRegisterReq.base:type_name -> api.wallet.v1.BaseSignPayload
	0, // 1: api.wallet.v1.UpdateWalletIDReq.base:type_name -> api.wallet.v1.BaseSignPayload
	7, // 2: api.wallet.v1.GeWalletIdsReply.list:type_name -> api.wallet.v1.GetWalletId
	1, // 3: api.wallet.v1.UserSrv.UserRegister:input_type -> api.wallet.v1.UserRegisterReq
	3, // 4: api.wallet.v1.UserSrv.UpdateWalletID:input_type -> api.wallet.v1.UpdateWalletIDReq
	5, // 5: api.wallet.v1.UserSrv.GetWalletIds:input_type -> api.wallet.v1.GetWalletIdReq
	2, // 6: api.wallet.v1.UserSrv.UserRegister:output_type -> api.wallet.v1.UserRegisterReply
	4, // 7: api.wallet.v1.UserSrv.UpdateWalletID:output_type -> api.wallet.v1.UpdateWalletIDReply
	6, // 8: api.wallet.v1.UserSrv.GetWalletIds:output_type -> api.wallet.v1.GeWalletIdsReply
	6, // [6:9] is the sub-list for method output_type
	3, // [3:6] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_wallet_v1_user_proto_init() }
func file_api_wallet_v1_user_proto_init() {
	if File_api_wallet_v1_user_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_wallet_v1_user_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*BaseSignPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_user_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*UserRegisterReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_user_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*UserRegisterReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_user_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateWalletIDReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_user_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateWalletIDReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_user_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*GetWalletIdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_user_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*GeWalletIdsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_user_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*GetWalletId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_wallet_v1_user_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_wallet_v1_user_proto_goTypes,
		DependencyIndexes: file_api_wallet_v1_user_proto_depIdxs,
		MessageInfos:      file_api_wallet_v1_user_proto_msgTypes,
	}.Build()
	File_api_wallet_v1_user_proto = out.File
	file_api_wallet_v1_user_proto_rawDesc = nil
	file_api_wallet_v1_user_proto_goTypes = nil
	file_api_wallet_v1_user_proto_depIdxs = nil
}
