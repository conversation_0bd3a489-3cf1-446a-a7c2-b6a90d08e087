// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: api/wallet/v1/swap.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	SwapService_ListBlockchainNetwork_FullMethodName = "/api.wallet.v1.SwapService/ListBlockchainNetwork"
	SwapService_ListToken_FullMethodName             = "/api.wallet.v1.SwapService/ListToken"
	SwapService_ListHotToken_FullMethodName          = "/api.wallet.v1.SwapService/ListHotToken"
	SwapService_MultiQuote_FullMethodName            = "/api.wallet.v1.SwapService/MultiQuote"
	SwapService_Swap_FullMethodName                  = "/api.wallet.v1.SwapService/Swap"
	SwapService_AddSwapRecord_FullMethodName         = "/api.wallet.v1.SwapService/AddSwapRecord"
	SwapService_GetSwapRecord_FullMethodName         = "/api.wallet.v1.SwapService/GetSwapRecord"
	SwapService_ListSwapRecord_FullMethodName        = "/api.wallet.v1.SwapService/ListSwapRecord"
)

// SwapServiceClient is the client API for SwapService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SwapServiceClient interface {
	// 查询支持的链
	ListBlockchainNetwork(ctx context.Context, in *ListBlockchainNetworkRequest, opts ...grpc.CallOption) (*ListBlockchainNetworkReply, error)
	// 查询兑换的币种列表
	ListToken(ctx context.Context, in *ListTokenRequest, opts ...grpc.CallOption) (*ListTokenReply, error)
	// 查询热门代币
	ListHotToken(ctx context.Context, in *ListHotTokenRequest, opts ...grpc.CallOption) (*ListHotTokenReply, error)
	// 兑换询价
	MultiQuote(ctx context.Context, in *MultiQuoteRequest, opts ...grpc.CallOption) (*MultiQuoteReply, error)
	// 兑换
	Swap(ctx context.Context, in *SwapRequest, opts ...grpc.CallOption) (*SwapReply, error)
	// 添加兑换记录
	AddSwapRecord(ctx context.Context, in *AddSwapRecordRequest, opts ...grpc.CallOption) (*SwapRecord, error)
	// 查询兑换记录
	GetSwapRecord(ctx context.Context, in *GetSwapRecordRequest, opts ...grpc.CallOption) (*SwapRecord, error)
	// 查询兑换记录列表
	ListSwapRecord(ctx context.Context, in *ListSwapRecordRequest, opts ...grpc.CallOption) (*ListSwapRecordReply, error)
}

type swapServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSwapServiceClient(cc grpc.ClientConnInterface) SwapServiceClient {
	return &swapServiceClient{cc}
}

func (c *swapServiceClient) ListBlockchainNetwork(ctx context.Context, in *ListBlockchainNetworkRequest, opts ...grpc.CallOption) (*ListBlockchainNetworkReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListBlockchainNetworkReply)
	err := c.cc.Invoke(ctx, SwapService_ListBlockchainNetwork_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *swapServiceClient) ListToken(ctx context.Context, in *ListTokenRequest, opts ...grpc.CallOption) (*ListTokenReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListTokenReply)
	err := c.cc.Invoke(ctx, SwapService_ListToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *swapServiceClient) ListHotToken(ctx context.Context, in *ListHotTokenRequest, opts ...grpc.CallOption) (*ListHotTokenReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListHotTokenReply)
	err := c.cc.Invoke(ctx, SwapService_ListHotToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *swapServiceClient) MultiQuote(ctx context.Context, in *MultiQuoteRequest, opts ...grpc.CallOption) (*MultiQuoteReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MultiQuoteReply)
	err := c.cc.Invoke(ctx, SwapService_MultiQuote_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *swapServiceClient) Swap(ctx context.Context, in *SwapRequest, opts ...grpc.CallOption) (*SwapReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SwapReply)
	err := c.cc.Invoke(ctx, SwapService_Swap_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *swapServiceClient) AddSwapRecord(ctx context.Context, in *AddSwapRecordRequest, opts ...grpc.CallOption) (*SwapRecord, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SwapRecord)
	err := c.cc.Invoke(ctx, SwapService_AddSwapRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *swapServiceClient) GetSwapRecord(ctx context.Context, in *GetSwapRecordRequest, opts ...grpc.CallOption) (*SwapRecord, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SwapRecord)
	err := c.cc.Invoke(ctx, SwapService_GetSwapRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *swapServiceClient) ListSwapRecord(ctx context.Context, in *ListSwapRecordRequest, opts ...grpc.CallOption) (*ListSwapRecordReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListSwapRecordReply)
	err := c.cc.Invoke(ctx, SwapService_ListSwapRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SwapServiceServer is the server API for SwapService service.
// All implementations must embed UnimplementedSwapServiceServer
// for forward compatibility.
type SwapServiceServer interface {
	// 查询支持的链
	ListBlockchainNetwork(context.Context, *ListBlockchainNetworkRequest) (*ListBlockchainNetworkReply, error)
	// 查询兑换的币种列表
	ListToken(context.Context, *ListTokenRequest) (*ListTokenReply, error)
	// 查询热门代币
	ListHotToken(context.Context, *ListHotTokenRequest) (*ListHotTokenReply, error)
	// 兑换询价
	MultiQuote(context.Context, *MultiQuoteRequest) (*MultiQuoteReply, error)
	// 兑换
	Swap(context.Context, *SwapRequest) (*SwapReply, error)
	// 添加兑换记录
	AddSwapRecord(context.Context, *AddSwapRecordRequest) (*SwapRecord, error)
	// 查询兑换记录
	GetSwapRecord(context.Context, *GetSwapRecordRequest) (*SwapRecord, error)
	// 查询兑换记录列表
	ListSwapRecord(context.Context, *ListSwapRecordRequest) (*ListSwapRecordReply, error)
	mustEmbedUnimplementedSwapServiceServer()
}

// UnimplementedSwapServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSwapServiceServer struct{}

func (UnimplementedSwapServiceServer) ListBlockchainNetwork(context.Context, *ListBlockchainNetworkRequest) (*ListBlockchainNetworkReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBlockchainNetwork not implemented")
}
func (UnimplementedSwapServiceServer) ListToken(context.Context, *ListTokenRequest) (*ListTokenReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListToken not implemented")
}
func (UnimplementedSwapServiceServer) ListHotToken(context.Context, *ListHotTokenRequest) (*ListHotTokenReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListHotToken not implemented")
}
func (UnimplementedSwapServiceServer) MultiQuote(context.Context, *MultiQuoteRequest) (*MultiQuoteReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MultiQuote not implemented")
}
func (UnimplementedSwapServiceServer) Swap(context.Context, *SwapRequest) (*SwapReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Swap not implemented")
}
func (UnimplementedSwapServiceServer) AddSwapRecord(context.Context, *AddSwapRecordRequest) (*SwapRecord, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddSwapRecord not implemented")
}
func (UnimplementedSwapServiceServer) GetSwapRecord(context.Context, *GetSwapRecordRequest) (*SwapRecord, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSwapRecord not implemented")
}
func (UnimplementedSwapServiceServer) ListSwapRecord(context.Context, *ListSwapRecordRequest) (*ListSwapRecordReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSwapRecord not implemented")
}
func (UnimplementedSwapServiceServer) mustEmbedUnimplementedSwapServiceServer() {}
func (UnimplementedSwapServiceServer) testEmbeddedByValue()                     {}

// UnsafeSwapServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SwapServiceServer will
// result in compilation errors.
type UnsafeSwapServiceServer interface {
	mustEmbedUnimplementedSwapServiceServer()
}

func RegisterSwapServiceServer(s grpc.ServiceRegistrar, srv SwapServiceServer) {
	// If the following call pancis, it indicates UnimplementedSwapServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&SwapService_ServiceDesc, srv)
}

func _SwapService_ListBlockchainNetwork_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBlockchainNetworkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SwapServiceServer).ListBlockchainNetwork(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SwapService_ListBlockchainNetwork_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SwapServiceServer).ListBlockchainNetwork(ctx, req.(*ListBlockchainNetworkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SwapService_ListToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SwapServiceServer).ListToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SwapService_ListToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SwapServiceServer).ListToken(ctx, req.(*ListTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SwapService_ListHotToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListHotTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SwapServiceServer).ListHotToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SwapService_ListHotToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SwapServiceServer).ListHotToken(ctx, req.(*ListHotTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SwapService_MultiQuote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MultiQuoteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SwapServiceServer).MultiQuote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SwapService_MultiQuote_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SwapServiceServer).MultiQuote(ctx, req.(*MultiQuoteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SwapService_Swap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SwapRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SwapServiceServer).Swap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SwapService_Swap_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SwapServiceServer).Swap(ctx, req.(*SwapRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SwapService_AddSwapRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddSwapRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SwapServiceServer).AddSwapRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SwapService_AddSwapRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SwapServiceServer).AddSwapRecord(ctx, req.(*AddSwapRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SwapService_GetSwapRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSwapRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SwapServiceServer).GetSwapRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SwapService_GetSwapRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SwapServiceServer).GetSwapRecord(ctx, req.(*GetSwapRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SwapService_ListSwapRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSwapRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SwapServiceServer).ListSwapRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SwapService_ListSwapRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SwapServiceServer).ListSwapRecord(ctx, req.(*ListSwapRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SwapService_ServiceDesc is the grpc.ServiceDesc for SwapService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SwapService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.wallet.v1.SwapService",
	HandlerType: (*SwapServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListBlockchainNetwork",
			Handler:    _SwapService_ListBlockchainNetwork_Handler,
		},
		{
			MethodName: "ListToken",
			Handler:    _SwapService_ListToken_Handler,
		},
		{
			MethodName: "ListHotToken",
			Handler:    _SwapService_ListHotToken_Handler,
		},
		{
			MethodName: "MultiQuote",
			Handler:    _SwapService_MultiQuote_Handler,
		},
		{
			MethodName: "Swap",
			Handler:    _SwapService_Swap_Handler,
		},
		{
			MethodName: "AddSwapRecord",
			Handler:    _SwapService_AddSwapRecord_Handler,
		},
		{
			MethodName: "GetSwapRecord",
			Handler:    _SwapService_GetSwapRecord_Handler,
		},
		{
			MethodName: "ListSwapRecord",
			Handler:    _SwapService_ListSwapRecord_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/wallet/v1/swap.proto",
}
