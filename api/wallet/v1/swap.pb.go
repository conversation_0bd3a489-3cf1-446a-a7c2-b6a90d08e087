// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: api/wallet/v1/swap.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ListSwapRecordRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 页码 从1开始
	Page int64 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	// 每页展示条数
	Limit int64 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	// 用户钱包地址
	Addresses []string `protobuf:"bytes,3,rep,name=addresses,proto3" json:"addresses,omitempty"`
}

func (x *ListSwapRecordRequest) Reset() {
	*x = ListSwapRecordRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_swap_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSwapRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSwapRecordRequest) ProtoMessage() {}

func (x *ListSwapRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSwapRecordRequest.ProtoReflect.Descriptor instead.
func (*ListSwapRecordRequest) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{0}
}

func (x *ListSwapRecordRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListSwapRecordRequest) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ListSwapRecordRequest) GetAddresses() []string {
	if x != nil {
		return x.Addresses
	}
	return nil
}

type ListSwapRecordReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*SwapRecord `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 总条数
	Count int64 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *ListSwapRecordReply) Reset() {
	*x = ListSwapRecordReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_swap_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSwapRecordReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSwapRecordReply) ProtoMessage() {}

func (x *ListSwapRecordReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSwapRecordReply.ProtoReflect.Descriptor instead.
func (*ListSwapRecordReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{1}
}

func (x *ListSwapRecordReply) GetList() []*SwapRecord {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListSwapRecordReply) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type GetSwapRecordRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 兑换记录hash
	Hash string `protobuf:"bytes,1,opt,name=hash,proto3" json:"hash,omitempty"`
}

func (x *GetSwapRecordRequest) Reset() {
	*x = GetSwapRecordRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_swap_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSwapRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSwapRecordRequest) ProtoMessage() {}

func (x *GetSwapRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSwapRecordRequest.ProtoReflect.Descriptor instead.
func (*GetSwapRecordRequest) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{2}
}

func (x *GetSwapRecordRequest) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

type AddSwapRecordRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	From *SwapToken `protobuf:"bytes,1,opt,name=from,proto3" json:"from,omitempty"`
	To   *SwapToken `protobuf:"bytes,2,opt,name=to,proto3" json:"to,omitempty"`
	Path string     `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"`
	Dex  string     `protobuf:"bytes,4,opt,name=dex,proto3" json:"dex,omitempty"`
	// 滑点百分比，比如传1代表滑点1%
	Slippage string `protobuf:"bytes,5,opt,name=slippage,proto3" json:"slippage,omitempty"`
	// 客户端上传的交易hash
	Hash string `protobuf:"bytes,6,opt,name=hash,proto3" json:"hash,omitempty"`
	// 授权hash
	ApprovalHash string `protobuf:"bytes,7,opt,name=approval_hash,json=approvalHash,proto3" json:"approval_hash,omitempty"`
	// 汇率
	SwapPrice string `protobuf:"bytes,8,opt,name=swap_price,json=swapPrice,proto3" json:"swap_price,omitempty"`
	// 费率
	FeeRate string `protobuf:"bytes,9,opt,name=fee_rate,json=feeRate,proto3" json:"fee_rate,omitempty"`
	// 预估时间：1(1-3分钟)，2(1-10分钟)，3(5-30分钟)
	EstimatedTime string `protobuf:"bytes,10,opt,name=estimated_time,json=estimatedTime,proto3" json:"estimated_time,omitempty"`
	// 矿工费
	GasFee  string `protobuf:"bytes,11,opt,name=gas_fee,json=gasFee,proto3" json:"gas_fee,omitempty"`
	OrderId string `protobuf:"bytes,12,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
}

func (x *AddSwapRecordRequest) Reset() {
	*x = AddSwapRecordRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_swap_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddSwapRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddSwapRecordRequest) ProtoMessage() {}

func (x *AddSwapRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddSwapRecordRequest.ProtoReflect.Descriptor instead.
func (*AddSwapRecordRequest) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{3}
}

func (x *AddSwapRecordRequest) GetFrom() *SwapToken {
	if x != nil {
		return x.From
	}
	return nil
}

func (x *AddSwapRecordRequest) GetTo() *SwapToken {
	if x != nil {
		return x.To
	}
	return nil
}

func (x *AddSwapRecordRequest) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *AddSwapRecordRequest) GetDex() string {
	if x != nil {
		return x.Dex
	}
	return ""
}

func (x *AddSwapRecordRequest) GetSlippage() string {
	if x != nil {
		return x.Slippage
	}
	return ""
}

func (x *AddSwapRecordRequest) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *AddSwapRecordRequest) GetApprovalHash() string {
	if x != nil {
		return x.ApprovalHash
	}
	return ""
}

func (x *AddSwapRecordRequest) GetSwapPrice() string {
	if x != nil {
		return x.SwapPrice
	}
	return ""
}

func (x *AddSwapRecordRequest) GetFeeRate() string {
	if x != nil {
		return x.FeeRate
	}
	return ""
}

func (x *AddSwapRecordRequest) GetEstimatedTime() string {
	if x != nil {
		return x.EstimatedTime
	}
	return ""
}

func (x *AddSwapRecordRequest) GetGasFee() string {
	if x != nil {
		return x.GasFee
	}
	return ""
}

func (x *AddSwapRecordRequest) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

type SwapRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 兑换记录id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 兑换时间(unix时间戳)
	SwappedAt int64 `protobuf:"varint,2,opt,name=swapped_at,json=swappedAt,proto3" json:"swapped_at,omitempty"`
	// 兑换状态
	// pending(处理中), success(兑换成功), fail(兑换失败)
	Status string     `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	From   *SwapToken `protobuf:"bytes,4,opt,name=from,proto3" json:"from,omitempty"`
	To     *SwapToken `protobuf:"bytes,5,opt,name=to,proto3" json:"to,omitempty"`
	// 用户兑换消耗的GasFee
	GasFee string `protobuf:"bytes,6,opt,name=gas_fee,json=gasFee,proto3" json:"gas_fee,omitempty"`
	// 兑换手续费率
	FeeRate string `protobuf:"bytes,7,opt,name=fee_rate,json=feeRate,proto3" json:"fee_rate,omitempty"`
	// 用户发起的交易hash
	Hash string `protobuf:"bytes,8,opt,name=hash,proto3" json:"hash,omitempty"`
	// 授权hash
	ApprovalHash string `protobuf:"bytes,9,opt,name=approval_hash,json=approvalHash,proto3" json:"approval_hash,omitempty"`
	// 区块高度
	Height int64 `protobuf:"varint,10,opt,name=height,proto3" json:"height,omitempty"`
	// 兑换平台名称
	Dex string `protobuf:"bytes,11,opt,name=dex,proto3" json:"dex,omitempty"`
	// 兑换平台logo url
	DexLogo string `protobuf:"bytes,12,opt,name=dex_logo,json=dexLogo,proto3" json:"dex_logo,omitempty"`
	// 兑换价格
	SwapPrice string `protobuf:"bytes,13,opt,name=swap_price,json=swapPrice,proto3" json:"swap_price,omitempty"`
	// 兑换详情列表
	Details []*SwapDetail `protobuf:"bytes,14,rep,name=details,proto3" json:"details,omitempty"`
	// 结束时间(unix时间戳)
	FinishedAt int64 `protobuf:"varint,15,opt,name=finished_at,json=finishedAt,proto3" json:"finished_at,omitempty"`
	// 预估时间：1(1-3分钟)，2(1-10分钟)，3(5-30分钟)
	EstimatedTime string `protobuf:"bytes,16,opt,name=estimated_time,json=estimatedTime,proto3" json:"estimated_time,omitempty"`
}

func (x *SwapRecord) Reset() {
	*x = SwapRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_swap_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwapRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwapRecord) ProtoMessage() {}

func (x *SwapRecord) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwapRecord.ProtoReflect.Descriptor instead.
func (*SwapRecord) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{4}
}

func (x *SwapRecord) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SwapRecord) GetSwappedAt() int64 {
	if x != nil {
		return x.SwappedAt
	}
	return 0
}

func (x *SwapRecord) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *SwapRecord) GetFrom() *SwapToken {
	if x != nil {
		return x.From
	}
	return nil
}

func (x *SwapRecord) GetTo() *SwapToken {
	if x != nil {
		return x.To
	}
	return nil
}

func (x *SwapRecord) GetGasFee() string {
	if x != nil {
		return x.GasFee
	}
	return ""
}

func (x *SwapRecord) GetFeeRate() string {
	if x != nil {
		return x.FeeRate
	}
	return ""
}

func (x *SwapRecord) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *SwapRecord) GetApprovalHash() string {
	if x != nil {
		return x.ApprovalHash
	}
	return ""
}

func (x *SwapRecord) GetHeight() int64 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *SwapRecord) GetDex() string {
	if x != nil {
		return x.Dex
	}
	return ""
}

func (x *SwapRecord) GetDexLogo() string {
	if x != nil {
		return x.DexLogo
	}
	return ""
}

func (x *SwapRecord) GetSwapPrice() string {
	if x != nil {
		return x.SwapPrice
	}
	return ""
}

func (x *SwapRecord) GetDetails() []*SwapDetail {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *SwapRecord) GetFinishedAt() int64 {
	if x != nil {
		return x.FinishedAt
	}
	return 0
}

func (x *SwapRecord) GetEstimatedTime() string {
	if x != nil {
		return x.EstimatedTime
	}
	return ""
}

type SwapDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 链索引
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 区块浏览器url
	ExplorerUrl string `protobuf:"bytes,2,opt,name=explorer_url,json=explorerUrl,proto3" json:"explorer_url,omitempty"`
	// 交易hash
	Hash string `protobuf:"bytes,3,opt,name=hash,proto3" json:"hash,omitempty"`
	// 交易状态
	Status string `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *SwapDetail) Reset() {
	*x = SwapDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_swap_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwapDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwapDetail) ProtoMessage() {}

func (x *SwapDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwapDetail.ProtoReflect.Descriptor instead.
func (*SwapDetail) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{5}
}

func (x *SwapDetail) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *SwapDetail) GetExplorerUrl() string {
	if x != nil {
		return x.ExplorerUrl
	}
	return ""
}

func (x *SwapDetail) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *SwapDetail) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type ListBlockchainNetworkRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListBlockchainNetworkRequest) Reset() {
	*x = ListBlockchainNetworkRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_swap_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBlockchainNetworkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBlockchainNetworkRequest) ProtoMessage() {}

func (x *ListBlockchainNetworkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBlockchainNetworkRequest.ProtoReflect.Descriptor instead.
func (*ListBlockchainNetworkRequest) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{6}
}

type ListBlockchainNetworkReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*Network `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListBlockchainNetworkReply) Reset() {
	*x = ListBlockchainNetworkReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_swap_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBlockchainNetworkReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBlockchainNetworkReply) ProtoMessage() {}

func (x *ListBlockchainNetworkReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBlockchainNetworkReply.ProtoReflect.Descriptor instead.
func (*ListBlockchainNetworkReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{7}
}

func (x *ListBlockchainNetworkReply) GetList() []*Network {
	if x != nil {
		return x.List
	}
	return nil
}

type SwapRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	From *SwapToken `protobuf:"bytes,1,opt,name=from,proto3" json:"from,omitempty"`
	To   *SwapToken `protobuf:"bytes,2,opt,name=to,proto3" json:"to,omitempty"`
	Path string     `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"`
	Dex  string     `protobuf:"bytes,4,opt,name=dex,proto3" json:"dex,omitempty"`
	// 滑点百分比，比如传1代表滑点1%
	Slippage string `protobuf:"bytes,5,opt,name=slippage,proto3" json:"slippage,omitempty"`
}

func (x *SwapRequest) Reset() {
	*x = SwapRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_swap_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwapRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwapRequest) ProtoMessage() {}

func (x *SwapRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwapRequest.ProtoReflect.Descriptor instead.
func (*SwapRequest) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{8}
}

func (x *SwapRequest) GetFrom() *SwapToken {
	if x != nil {
		return x.From
	}
	return nil
}

func (x *SwapRequest) GetTo() *SwapToken {
	if x != nil {
		return x.To
	}
	return nil
}

func (x *SwapRequest) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *SwapRequest) GetDex() string {
	if x != nil {
		return x.Dex
	}
	return ""
}

func (x *SwapRequest) GetSlippage() string {
	if x != nil {
		return x.Slippage
	}
	return ""
}

type SwapReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 调用合约请求地址
	From string `protobuf:"bytes,1,opt,name=from,proto3" json:"from,omitempty"`
	// 被调用合约地址
	To string `protobuf:"bytes,2,opt,name=to,proto3" json:"to,omitempty"`
	// 调用传入金额
	Value    string `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
	GasLimit string `protobuf:"bytes,4,opt,name=gas_limit,json=gasLimit,proto3" json:"gas_limit,omitempty"`
	GasPrice string `protobuf:"bytes,5,opt,name=gas_price,json=gasPrice,proto3" json:"gas_price,omitempty"`
	// 合约调用数据
	Data string `protobuf:"bytes,6,opt,name=data,proto3" json:"data,omitempty"`
	// 授权地址
	ApproveAddress string `protobuf:"bytes,7,opt,name=approve_address,json=approveAddress,proto3" json:"approve_address,omitempty"`
	// Deposit coin type,0 (common address, no call data);1 (common address,have call data);2(contract address,have call data )
	ToType string `protobuf:"bytes,8,opt,name=to_type,json=toType,proto3" json:"to_type,omitempty"`
	// Only the SWFT channel,deposit address
	PlatformAddr string `protobuf:"bytes,9,opt,name=platform_addr,json=platformAddr,proto3" json:"platform_addr,omitempty"`
	// 兑换汇率
	SwapPrice string `protobuf:"bytes,10,opt,name=swap_price,json=swapPrice,proto3" json:"swap_price,omitempty"`
	// 第三方原始数据
	RawData string `protobuf:"bytes,11,opt,name=raw_data,json=rawData,proto3" json:"raw_data,omitempty"`
	OrderId string `protobuf:"bytes,12,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
}

func (x *SwapReply) Reset() {
	*x = SwapReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_swap_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwapReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwapReply) ProtoMessage() {}

func (x *SwapReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwapReply.ProtoReflect.Descriptor instead.
func (*SwapReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{9}
}

func (x *SwapReply) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *SwapReply) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

func (x *SwapReply) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *SwapReply) GetGasLimit() string {
	if x != nil {
		return x.GasLimit
	}
	return ""
}

func (x *SwapReply) GetGasPrice() string {
	if x != nil {
		return x.GasPrice
	}
	return ""
}

func (x *SwapReply) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

func (x *SwapReply) GetApproveAddress() string {
	if x != nil {
		return x.ApproveAddress
	}
	return ""
}

func (x *SwapReply) GetToType() string {
	if x != nil {
		return x.ToType
	}
	return ""
}

func (x *SwapReply) GetPlatformAddr() string {
	if x != nil {
		return x.PlatformAddr
	}
	return ""
}

func (x *SwapReply) GetSwapPrice() string {
	if x != nil {
		return x.SwapPrice
	}
	return ""
}

func (x *SwapReply) GetRawData() string {
	if x != nil {
		return x.RawData
	}
	return ""
}

func (x *SwapReply) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

type SwapToken struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 代币合约地址
	TokenAddress string `protobuf:"bytes,1,opt,name=token_address,json=tokenAddress,proto3" json:"token_address,omitempty"`
	// 用户地址
	Address string `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	// 链
	ChainIndex int64 `protobuf:"varint,3,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 数量
	Amount string `protobuf:"bytes,4,opt,name=amount,proto3" json:"amount,omitempty"`
	// token logo url
	TokenLogo string `protobuf:"bytes,5,opt,name=token_logo,json=tokenLogo,proto3" json:"token_logo,omitempty"`
	// 标的
	Symbol string `protobuf:"bytes,6,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// 精度
	Decimals string `protobuf:"bytes,7,opt,name=decimals,proto3" json:"decimals,omitempty"`
}

func (x *SwapToken) Reset() {
	*x = SwapToken{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_swap_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwapToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwapToken) ProtoMessage() {}

func (x *SwapToken) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwapToken.ProtoReflect.Descriptor instead.
func (*SwapToken) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{10}
}

func (x *SwapToken) GetTokenAddress() string {
	if x != nil {
		return x.TokenAddress
	}
	return ""
}

func (x *SwapToken) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *SwapToken) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *SwapToken) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *SwapToken) GetTokenLogo() string {
	if x != nil {
		return x.TokenLogo
	}
	return ""
}

func (x *SwapToken) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *SwapToken) GetDecimals() string {
	if x != nil {
		return x.Decimals
	}
	return ""
}

type MultiQuoteRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	From *SwapToken `protobuf:"bytes,1,opt,name=from,proto3" json:"from,omitempty"`
	To   *SwapToken `protobuf:"bytes,2,opt,name=to,proto3" json:"to,omitempty"`
	// 滑点百分比，比如传1代表滑点1%
	Slippage string `protobuf:"bytes,3,opt,name=slippage,proto3" json:"slippage,omitempty"`
}

func (x *MultiQuoteRequest) Reset() {
	*x = MultiQuoteRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_swap_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MultiQuoteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MultiQuoteRequest) ProtoMessage() {}

func (x *MultiQuoteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MultiQuoteRequest.ProtoReflect.Descriptor instead.
func (*MultiQuoteRequest) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{11}
}

func (x *MultiQuoteRequest) GetFrom() *SwapToken {
	if x != nil {
		return x.From
	}
	return nil
}

func (x *MultiQuoteRequest) GetTo() *SwapToken {
	if x != nil {
		return x.To
	}
	return nil
}

func (x *MultiQuoteRequest) GetSlippage() string {
	if x != nil {
		return x.Slippage
	}
	return ""
}

type MultiQuoteReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*QuoteInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *MultiQuoteReply) Reset() {
	*x = MultiQuoteReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_swap_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MultiQuoteReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MultiQuoteReply) ProtoMessage() {}

func (x *MultiQuoteReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MultiQuoteReply.ProtoReflect.Descriptor instead.
func (*MultiQuoteReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{12}
}

func (x *MultiQuoteReply) GetList() []*QuoteInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type QuoteInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 预估目标代币接收数量
	ReceiveTokenAmount string `protobuf:"bytes,1,opt,name=receive_token_amount,json=receiveTokenAmount,proto3" json:"receive_token_amount,omitempty"`
	// 最小目标代币接收数量
	MinReceiveTokenAmount string `protobuf:"bytes,2,opt,name=min_receive_token_amount,json=minReceiveTokenAmount,proto3" json:"min_receive_token_amount,omitempty"`
	// 滑点百分比，比如1代表滑点1%
	Slippage string `protobuf:"bytes,3,opt,name=slippage,proto3" json:"slippage,omitempty"`
	// 兑换平台
	Dex string `protobuf:"bytes,4,opt,name=dex,proto3" json:"dex,omitempty"`
	// 兑换平台logo
	DexLogo string `protobuf:"bytes,5,opt,name=dex_logo,json=dexLogo,proto3" json:"dex_logo,omitempty"`
	// 交易费率
	FeeRate string `protobuf:"bytes,6,opt,name=fee_rate,json=feeRate,proto3" json:"fee_rate,omitempty"`
	// 预估时间：1(1-3分钟)，2(1-10分钟)，3(5-30分钟)
	EstimatedTime string `protobuf:"bytes,7,opt,name=estimated_time,json=estimatedTime,proto3" json:"estimated_time,omitempty"`
	// 用于传给Swap接口
	Path string `protobuf:"bytes,8,opt,name=path,proto3" json:"path,omitempty"`
	// 最大兑换数量
	MaxFromTokenAmount string `protobuf:"bytes,9,opt,name=max_from_token_amount,json=maxFromTokenAmount,proto3" json:"max_from_token_amount,omitempty"`
	// 最小兑换数量
	MinFromTokenAmount string `protobuf:"bytes,10,opt,name=min_from_token_amount,json=minFromTokenAmount,proto3" json:"min_from_token_amount,omitempty"`
}

func (x *QuoteInfo) Reset() {
	*x = QuoteInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_swap_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuoteInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuoteInfo) ProtoMessage() {}

func (x *QuoteInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuoteInfo.ProtoReflect.Descriptor instead.
func (*QuoteInfo) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{13}
}

func (x *QuoteInfo) GetReceiveTokenAmount() string {
	if x != nil {
		return x.ReceiveTokenAmount
	}
	return ""
}

func (x *QuoteInfo) GetMinReceiveTokenAmount() string {
	if x != nil {
		return x.MinReceiveTokenAmount
	}
	return ""
}

func (x *QuoteInfo) GetSlippage() string {
	if x != nil {
		return x.Slippage
	}
	return ""
}

func (x *QuoteInfo) GetDex() string {
	if x != nil {
		return x.Dex
	}
	return ""
}

func (x *QuoteInfo) GetDexLogo() string {
	if x != nil {
		return x.DexLogo
	}
	return ""
}

func (x *QuoteInfo) GetFeeRate() string {
	if x != nil {
		return x.FeeRate
	}
	return ""
}

func (x *QuoteInfo) GetEstimatedTime() string {
	if x != nil {
		return x.EstimatedTime
	}
	return ""
}

func (x *QuoteInfo) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *QuoteInfo) GetMaxFromTokenAmount() string {
	if x != nil {
		return x.MaxFromTokenAmount
	}
	return ""
}

func (x *QuoteInfo) GetMinFromTokenAmount() string {
	if x != nil {
		return x.MinFromTokenAmount
	}
	return ""
}

type ListTokenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 传-1 获取所有
	ChainIndex int64  `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	SearchKey  string `protobuf:"bytes,2,opt,name=search_key,json=searchKey,proto3" json:"search_key,omitempty"`
}

func (x *ListTokenRequest) Reset() {
	*x = ListTokenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_swap_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTokenRequest) ProtoMessage() {}

func (x *ListTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTokenRequest.ProtoReflect.Descriptor instead.
func (*ListTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{14}
}

func (x *ListTokenRequest) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *ListTokenRequest) GetSearchKey() string {
	if x != nil {
		return x.SearchKey
	}
	return ""
}

type ListTokenReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*Token `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListTokenReply) Reset() {
	*x = ListTokenReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_swap_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTokenReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTokenReply) ProtoMessage() {}

func (x *ListTokenReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTokenReply.ProtoReflect.Descriptor instead.
func (*ListTokenReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{15}
}

func (x *ListTokenReply) GetList() []*Token {
	if x != nil {
		return x.List
	}
	return nil
}

type ListHotTokenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListHotTokenRequest) Reset() {
	*x = ListHotTokenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_swap_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListHotTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHotTokenRequest) ProtoMessage() {}

func (x *ListHotTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHotTokenRequest.ProtoReflect.Descriptor instead.
func (*ListHotTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{16}
}

type ListHotTokenReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*SwappableHotToken `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListHotTokenReply) Reset() {
	*x = ListHotTokenReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_swap_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListHotTokenReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHotTokenReply) ProtoMessage() {}

func (x *ListHotTokenReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHotTokenReply.ProtoReflect.Descriptor instead.
func (*ListHotTokenReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{17}
}

func (x *ListHotTokenReply) GetList() []*SwappableHotToken {
	if x != nil {
		return x.List
	}
	return nil
}

type SwappableHotToken struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChainIndex int64  `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	Name       string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Symbol     string `protobuf:"bytes,3,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Decimals   int64  `protobuf:"varint,4,opt,name=decimals,proto3" json:"decimals,omitempty"`
	LogoUrl    string `protobuf:"bytes,5,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	Address    string `protobuf:"bytes,6,opt,name=address,proto3" json:"address,omitempty"`
	ChainId    string `protobuf:"bytes,7,opt,name=chain_id,json=chainId,proto3" json:"chain_id,omitempty"`
	SortOrder  int64  `protobuf:"varint,8,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order,omitempty"`
	// 是否是全部的分组
	IsAll bool `protobuf:"varint,9,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
}

func (x *SwappableHotToken) Reset() {
	*x = SwappableHotToken{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_swap_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwappableHotToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwappableHotToken) ProtoMessage() {}

func (x *SwappableHotToken) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwappableHotToken.ProtoReflect.Descriptor instead.
func (*SwappableHotToken) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{18}
}

func (x *SwappableHotToken) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *SwappableHotToken) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SwappableHotToken) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *SwappableHotToken) GetDecimals() int64 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *SwappableHotToken) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *SwappableHotToken) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *SwappableHotToken) GetChainId() string {
	if x != nil {
		return x.ChainId
	}
	return ""
}

func (x *SwappableHotToken) GetSortOrder() int64 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

func (x *SwappableHotToken) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

var File_api_wallet_v1_swap_proto protoreflect.FileDescriptor

var file_api_wallet_v1_swap_proto_rawDesc = []byte{
	0x0a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x77, 0x61, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x62, 0x75, 0x66, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x2f, 0x76, 0x31, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x73, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x77, 0x61, 0x70, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xba, 0x48, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1f, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x09, 0xba, 0x48, 0x06, 0x22, 0x04, 0x18, 0x64, 0x20, 0x00,
	0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x65, 0x73, 0x22, 0x5a, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x77, 0x61,
	0x70, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2d, 0x0a, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x77, 0x61, 0x70, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0x33, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x53, 0x77, 0x61, 0x70, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x04, 0x68, 0x61, 0x73,
	0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x22, 0xa9, 0x03, 0x0a, 0x14, 0x41, 0x64, 0x64, 0x53, 0x77,
	0x61, 0x70, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x34, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x77,
	0x61, 0x70, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x06, 0xba, 0x48, 0x03, 0xc8, 0x01, 0x01, 0x52,
	0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x30, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x77, 0x61, 0x70, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x06, 0xba, 0x48, 0x03,
	0xc8, 0x01, 0x01, 0x52, 0x02, 0x74, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x19, 0x0a, 0x03, 0x64,
	0x65, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x03, 0x64, 0x65, 0x78, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x6c, 0x69, 0x70, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6c, 0x69, 0x70, 0x70, 0x61,
	0x67, 0x65, 0x12, 0x1b, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x12,
	0x23, 0x0a, 0x0d, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x68, 0x61, 0x73, 0x68,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c,
	0x48, 0x61, 0x73, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x77, 0x61, 0x70, 0x5f, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x77, 0x61, 0x70, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x12, 0x22, 0x0a, 0x08, 0x66, 0x65, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07,
	0x66, 0x65, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x73, 0x74, 0x69, 0x6d,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x17,
	0x0a, 0x07, 0x67, 0x61, 0x73, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x67, 0x61, 0x73, 0x46, 0x65, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x49, 0x64, 0x22, 0xf9, 0x03, 0x0a, 0x0a, 0x53, 0x77, 0x61, 0x70, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x77, 0x61, 0x70, 0x70, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x77, 0x61, 0x70, 0x70, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2c, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x77, 0x61, 0x70, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x28, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x77, 0x61, 0x70, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x02, 0x74, 0x6f,
	0x12, 0x17, 0x0a, 0x07, 0x67, 0x61, 0x73, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x67, 0x61, 0x73, 0x46, 0x65, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x65, 0x65,
	0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x65, 0x65,
	0x52, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x48, 0x61, 0x73, 0x68, 0x12, 0x16, 0x0a,
	0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x68,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x65, 0x78, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x64, 0x65, 0x78, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x65, 0x78, 0x5f, 0x6c,
	0x6f, 0x67, 0x6f, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x65, 0x78, 0x4c, 0x6f,
	0x67, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x77, 0x61, 0x70, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x77, 0x61, 0x70, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x12, 0x33, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0e, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x77, 0x61, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x07, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x66, 0x69, 0x6e,
	0x69, 0x73, 0x68, 0x65, 0x64, 0x41, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x73, 0x74, 0x69, 0x6d,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x7c,
	0x0a, 0x0a, 0x53, 0x77, 0x61, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x21, 0x0a,
	0x0c, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x72, 0x55, 0x72, 0x6c,
	0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x68, 0x61, 0x73, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x1e, 0x0a, 0x1c,
	0x4c, 0x69, 0x73, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x4e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x48, 0x0a, 0x1a,
	0x4c, 0x69, 0x73, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x4e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2a, 0x0a, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xa7, 0x01, 0x0a, 0x0b, 0x53, 0x77, 0x61, 0x70, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x77, 0x61, 0x70, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x04,
	0x66, 0x72, 0x6f, 0x6d, 0x12, 0x28, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x77, 0x61, 0x70, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x02, 0x74, 0x6f, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61,
	0x74, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x65, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x64, 0x65, 0x78, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x6c, 0x69, 0x70, 0x70, 0x61, 0x67, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6c, 0x69, 0x70, 0x70, 0x61, 0x67, 0x65,
	0x22, 0xcf, 0x02, 0x0a, 0x09, 0x53, 0x77, 0x61, 0x70, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12,
	0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x72,
	0x6f, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x74, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x73, 0x5f,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x73,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x73, 0x5f, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x73, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x17, 0x0a, 0x07, 0x74, 0x6f, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x74, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x41, 0x64, 0x64, 0x72, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x77, 0x61, 0x70, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x73, 0x77, 0x61, 0x70, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x72, 0x61, 0x77, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x72, 0x61, 0x77, 0x44, 0x61, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x49, 0x64, 0x22, 0xdf, 0x01, 0x0a, 0x09, 0x53, 0x77, 0x61, 0x70, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x23, 0x0a, 0x0d, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x21, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x6c, 0x6f, 0x67, 0x6f, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x4c, 0x6f, 0x67, 0x6f,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x63, 0x69,
	0x6d, 0x61, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x63, 0x69,
	0x6d, 0x61, 0x6c, 0x73, 0x22, 0x97, 0x01, 0x0a, 0x11, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x51, 0x75,
	0x6f, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x04, 0x66, 0x72,
	0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x77, 0x61, 0x70, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x42, 0x06, 0xba, 0x48, 0x03, 0xc8, 0x01, 0x01, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d,
	0x12, 0x30, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x77, 0x61,
	0x70, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x06, 0xba, 0x48, 0x03, 0xc8, 0x01, 0x01, 0x52, 0x02,
	0x74, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x6c, 0x69, 0x70, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6c, 0x69, 0x70, 0x70, 0x61, 0x67, 0x65, 0x22, 0x3f,
	0x0a, 0x0f, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x51, 0x75, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x2c, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x51, 0x75, 0x6f, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22,
	0xfb, 0x02, 0x0a, 0x09, 0x51, 0x75, 0x6f, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x30, 0x0a,
	0x14, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x72, 0x65, 0x63,
	0x65, 0x69, 0x76, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x37, 0x0a, 0x18, 0x6d, 0x69, 0x6e, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x15, 0x6d, 0x69, 0x6e, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x6c, 0x69, 0x70,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6c, 0x69, 0x70,
	0x70, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x65, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x64, 0x65, 0x78, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x65, 0x78, 0x5f, 0x6c, 0x6f,
	0x67, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x65, 0x78, 0x4c, 0x6f, 0x67,
	0x6f, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x65, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x65, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12, 0x25, 0x0a, 0x0e,
	0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x31, 0x0a, 0x15, 0x6d, 0x61, 0x78, 0x5f, 0x66,
	0x72, 0x6f, 0x6d, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x6d, 0x61, 0x78, 0x46, 0x72, 0x6f, 0x6d, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x31, 0x0a, 0x15, 0x6d, 0x69,
	0x6e, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x6d, 0x69, 0x6e, 0x46, 0x72,
	0x6f, 0x6d, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x52, 0x0a,
	0x10, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x64,
	0x65, 0x78, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x6b, 0x65, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4b, 0x65,
	0x79, 0x22, 0x3a, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x28, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x15, 0x0a,
	0x13, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x6f, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x22, 0x49, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x6f, 0x74, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x34, 0x0a, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x77, 0x61, 0x70, 0x70, 0x61, 0x62, 0x6c,
	0x65, 0x48, 0x6f, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22,
	0x82, 0x02, 0x0a, 0x11, 0x53, 0x77, 0x61, 0x70, 0x70, 0x61, 0x62, 0x6c, 0x65, 0x48, 0x6f, 0x74,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79,
	0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62,
	0x6f, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x73, 0x12, 0x19,
	0x0a, 0x08, 0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6c, 0x6f, 0x67, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x73, 0x6f, 0x72, 0x74, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x73, 0x6f, 0x72, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x15, 0x0a,
	0x06, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69,
	0x73, 0x41, 0x6c, 0x6c, 0x32, 0x9a, 0x07, 0x0a, 0x0b, 0x53, 0x77, 0x61, 0x70, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x94, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x2b,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x4e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x12, 0x1b,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x77, 0x61, 0x70, 0x2f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68,
	0x61, 0x69, 0x6e, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x63, 0x0a, 0x09, 0x4c,
	0x69, 0x73, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x16, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x10,
	0x12, 0x0e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x77, 0x61, 0x70, 0x2f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x70, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x6f, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x6f, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x6f, 0x74, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14, 0x12, 0x12,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x77, 0x61, 0x70, 0x2f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x2f, 0x68,
	0x6f, 0x74, 0x12, 0x6f, 0x0a, 0x0a, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x51, 0x75, 0x6f, 0x74, 0x65,
	0x12, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x51, 0x75, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x51, 0x75, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x1f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x3a, 0x01, 0x2a, 0x22, 0x14, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x77, 0x61, 0x70, 0x2f, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x5f, 0x71, 0x75,
	0x6f, 0x74, 0x65, 0x12, 0x51, 0x0a, 0x04, 0x53, 0x77, 0x61, 0x70, 0x12, 0x1a, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x77, 0x61, 0x70,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x77, 0x61, 0x70, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x13, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0d, 0x3a, 0x01, 0x2a, 0x22, 0x08, 0x2f, 0x76,
	0x31, 0x2f, 0x73, 0x77, 0x61, 0x70, 0x12, 0x6b, 0x0a, 0x0d, 0x41, 0x64, 0x64, 0x53, 0x77, 0x61,
	0x70, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x53, 0x77, 0x61, 0x70, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x77, 0x61,
	0x70, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x22, 0x1a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14, 0x3a,
	0x01, 0x2a, 0x22, 0x0f, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x77, 0x61, 0x70, 0x2f, 0x72, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x12, 0x6f, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x53, 0x77, 0x61, 0x70, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x77, 0x61, 0x70, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x77, 0x61, 0x70, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x22, 0x1e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x12, 0x16, 0x2f, 0x76,
	0x31, 0x2f, 0x73, 0x77, 0x61, 0x70, 0x2f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2f, 0x7b, 0x68,
	0x61, 0x73, 0x68, 0x7d, 0x12, 0x7b, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x77, 0x61, 0x70,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x77, 0x61, 0x70, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x53, 0x77, 0x61, 0x70, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x1f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x3a, 0x01, 0x2a, 0x22, 0x14, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x77, 0x61, 0x70, 0x2f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2f, 0x6c, 0x69, 0x73,
	0x74, 0x42, 0x91, 0x01, 0x0a, 0x11, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x42, 0x09, 0x53, 0x77, 0x61, 0x70, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x50, 0x01, 0x5a, 0x1b, 0x62, 0x79, 0x64, 0x5f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x76,
	0x31, 0xa2, 0x02, 0x03, 0x41, 0x57, 0x58, 0xaa, 0x02, 0x0d, 0x41, 0x70, 0x69, 0x2e, 0x57, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x56, 0x31, 0xca, 0x02, 0x0d, 0x41, 0x70, 0x69, 0x5c, 0x57, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x5c, 0x56, 0x31, 0xe2, 0x02, 0x19, 0x41, 0x70, 0x69, 0x5c, 0x57, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x5c, 0x56, 0x31, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0xea, 0x02, 0x0f, 0x41, 0x70, 0x69, 0x3a, 0x3a, 0x57, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x3a, 0x3a, 0x56, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_wallet_v1_swap_proto_rawDescOnce sync.Once
	file_api_wallet_v1_swap_proto_rawDescData = file_api_wallet_v1_swap_proto_rawDesc
)

func file_api_wallet_v1_swap_proto_rawDescGZIP() []byte {
	file_api_wallet_v1_swap_proto_rawDescOnce.Do(func() {
		file_api_wallet_v1_swap_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_wallet_v1_swap_proto_rawDescData)
	})
	return file_api_wallet_v1_swap_proto_rawDescData
}

var file_api_wallet_v1_swap_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_api_wallet_v1_swap_proto_goTypes = []any{
	(*ListSwapRecordRequest)(nil),        // 0: api.wallet.v1.ListSwapRecordRequest
	(*ListSwapRecordReply)(nil),          // 1: api.wallet.v1.ListSwapRecordReply
	(*GetSwapRecordRequest)(nil),         // 2: api.wallet.v1.GetSwapRecordRequest
	(*AddSwapRecordRequest)(nil),         // 3: api.wallet.v1.AddSwapRecordRequest
	(*SwapRecord)(nil),                   // 4: api.wallet.v1.SwapRecord
	(*SwapDetail)(nil),                   // 5: api.wallet.v1.SwapDetail
	(*ListBlockchainNetworkRequest)(nil), // 6: api.wallet.v1.ListBlockchainNetworkRequest
	(*ListBlockchainNetworkReply)(nil),   // 7: api.wallet.v1.ListBlockchainNetworkReply
	(*SwapRequest)(nil),                  // 8: api.wallet.v1.SwapRequest
	(*SwapReply)(nil),                    // 9: api.wallet.v1.SwapReply
	(*SwapToken)(nil),                    // 10: api.wallet.v1.SwapToken
	(*MultiQuoteRequest)(nil),            // 11: api.wallet.v1.MultiQuoteRequest
	(*MultiQuoteReply)(nil),              // 12: api.wallet.v1.MultiQuoteReply
	(*QuoteInfo)(nil),                    // 13: api.wallet.v1.QuoteInfo
	(*ListTokenRequest)(nil),             // 14: api.wallet.v1.ListTokenRequest
	(*ListTokenReply)(nil),               // 15: api.wallet.v1.ListTokenReply
	(*ListHotTokenRequest)(nil),          // 16: api.wallet.v1.ListHotTokenRequest
	(*ListHotTokenReply)(nil),            // 17: api.wallet.v1.ListHotTokenReply
	(*SwappableHotToken)(nil),            // 18: api.wallet.v1.SwappableHotToken
	(*Network)(nil),                      // 19: api.wallet.v1.Network
	(*Token)(nil),                        // 20: api.wallet.v1.Token
}
var file_api_wallet_v1_swap_proto_depIdxs = []int32{
	4,  // 0: api.wallet.v1.ListSwapRecordReply.list:type_name -> api.wallet.v1.SwapRecord
	10, // 1: api.wallet.v1.AddSwapRecordRequest.from:type_name -> api.wallet.v1.SwapToken
	10, // 2: api.wallet.v1.AddSwapRecordRequest.to:type_name -> api.wallet.v1.SwapToken
	10, // 3: api.wallet.v1.SwapRecord.from:type_name -> api.wallet.v1.SwapToken
	10, // 4: api.wallet.v1.SwapRecord.to:type_name -> api.wallet.v1.SwapToken
	5,  // 5: api.wallet.v1.SwapRecord.details:type_name -> api.wallet.v1.SwapDetail
	19, // 6: api.wallet.v1.ListBlockchainNetworkReply.list:type_name -> api.wallet.v1.Network
	10, // 7: api.wallet.v1.SwapRequest.from:type_name -> api.wallet.v1.SwapToken
	10, // 8: api.wallet.v1.SwapRequest.to:type_name -> api.wallet.v1.SwapToken
	10, // 9: api.wallet.v1.MultiQuoteRequest.from:type_name -> api.wallet.v1.SwapToken
	10, // 10: api.wallet.v1.MultiQuoteRequest.to:type_name -> api.wallet.v1.SwapToken
	13, // 11: api.wallet.v1.MultiQuoteReply.list:type_name -> api.wallet.v1.QuoteInfo
	20, // 12: api.wallet.v1.ListTokenReply.list:type_name -> api.wallet.v1.Token
	18, // 13: api.wallet.v1.ListHotTokenReply.list:type_name -> api.wallet.v1.SwappableHotToken
	6,  // 14: api.wallet.v1.SwapService.ListBlockchainNetwork:input_type -> api.wallet.v1.ListBlockchainNetworkRequest
	14, // 15: api.wallet.v1.SwapService.ListToken:input_type -> api.wallet.v1.ListTokenRequest
	16, // 16: api.wallet.v1.SwapService.ListHotToken:input_type -> api.wallet.v1.ListHotTokenRequest
	11, // 17: api.wallet.v1.SwapService.MultiQuote:input_type -> api.wallet.v1.MultiQuoteRequest
	8,  // 18: api.wallet.v1.SwapService.Swap:input_type -> api.wallet.v1.SwapRequest
	3,  // 19: api.wallet.v1.SwapService.AddSwapRecord:input_type -> api.wallet.v1.AddSwapRecordRequest
	2,  // 20: api.wallet.v1.SwapService.GetSwapRecord:input_type -> api.wallet.v1.GetSwapRecordRequest
	0,  // 21: api.wallet.v1.SwapService.ListSwapRecord:input_type -> api.wallet.v1.ListSwapRecordRequest
	7,  // 22: api.wallet.v1.SwapService.ListBlockchainNetwork:output_type -> api.wallet.v1.ListBlockchainNetworkReply
	15, // 23: api.wallet.v1.SwapService.ListToken:output_type -> api.wallet.v1.ListTokenReply
	17, // 24: api.wallet.v1.SwapService.ListHotToken:output_type -> api.wallet.v1.ListHotTokenReply
	12, // 25: api.wallet.v1.SwapService.MultiQuote:output_type -> api.wallet.v1.MultiQuoteReply
	9,  // 26: api.wallet.v1.SwapService.Swap:output_type -> api.wallet.v1.SwapReply
	4,  // 27: api.wallet.v1.SwapService.AddSwapRecord:output_type -> api.wallet.v1.SwapRecord
	4,  // 28: api.wallet.v1.SwapService.GetSwapRecord:output_type -> api.wallet.v1.SwapRecord
	1,  // 29: api.wallet.v1.SwapService.ListSwapRecord:output_type -> api.wallet.v1.ListSwapRecordReply
	22, // [22:30] is the sub-list for method output_type
	14, // [14:22] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_api_wallet_v1_swap_proto_init() }
func file_api_wallet_v1_swap_proto_init() {
	if File_api_wallet_v1_swap_proto != nil {
		return
	}
	file_api_wallet_v1_wallet_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_wallet_v1_swap_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*ListSwapRecordRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_swap_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*ListSwapRecordReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_swap_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*GetSwapRecordRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_swap_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*AddSwapRecordRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_swap_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*SwapRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_swap_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*SwapDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_swap_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*ListBlockchainNetworkRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_swap_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*ListBlockchainNetworkReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_swap_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*SwapRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_swap_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*SwapReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_swap_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*SwapToken); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_swap_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*MultiQuoteRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_swap_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*MultiQuoteReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_swap_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*QuoteInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_swap_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*ListTokenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_swap_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*ListTokenReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_swap_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*ListHotTokenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_swap_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*ListHotTokenReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_swap_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*SwappableHotToken); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_wallet_v1_swap_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_wallet_v1_swap_proto_goTypes,
		DependencyIndexes: file_api_wallet_v1_swap_proto_depIdxs,
		MessageInfos:      file_api_wallet_v1_swap_proto_msgTypes,
	}.Build()
	File_api_wallet_v1_swap_proto = out.File
	file_api_wallet_v1_swap_proto_rawDesc = nil
	file_api_wallet_v1_swap_proto_goTypes = nil
	file_api_wallet_v1_swap_proto_depIdxs = nil
}
