// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: api/wallet/v1/gaspool.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SendTxReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 链索引
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 原始交易
	RawTx string `protobuf:"bytes,2,opt,name=raw_tx,json=rawTx,proto3" json:"raw_tx,omitempty"`
	// 交易类型 (
	// deposit:充值,deposit_pre_reduce_gas:预扣GasPool充值,
	// transfer:转账,swap:兑换
	// )
	TxType string `protobuf:"bytes,3,opt,name=tx_type,json=txType,proto3" json:"tx_type,omitempty"`
}

func (x *SendTxReq) Reset() {
	*x = SendTxReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_gaspool_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendTxReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendTxReq) ProtoMessage() {}

func (x *SendTxReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendTxReq.ProtoReflect.Descriptor instead.
func (*SendTxReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{0}
}

func (x *SendTxReq) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *SendTxReq) GetRawTx() string {
	if x != nil {
		return x.RawTx
	}
	return ""
}

func (x *SendTxReq) GetTxType() string {
	if x != nil {
		return x.TxType
	}
	return ""
}

type SendTxReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TxHash string `protobuf:"bytes,1,opt,name=tx_hash,json=txHash,proto3" json:"tx_hash,omitempty"`
}

func (x *SendTxReply) Reset() {
	*x = SendTxReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_gaspool_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendTxReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendTxReply) ProtoMessage() {}

func (x *SendTxReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendTxReply.ProtoReflect.Descriptor instead.
func (*SendTxReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{1}
}

func (x *SendTxReply) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

type GasPoolDepositToken struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 币名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 币符号
	Symbol string `protobuf:"bytes,2,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// 币精度
	Decimals int64 `protobuf:"varint,3,opt,name=decimals,proto3" json:"decimals,omitempty"`
	// 币图标
	LogoUrl string `protobuf:"bytes,4,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	// 币地址
	Address string `protobuf:"bytes,5,opt,name=address,proto3" json:"address,omitempty"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,6,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 链ID
	ChainId string `protobuf:"bytes,7,opt,name=chain_id,json=chainId,proto3" json:"chain_id,omitempty"`
	// 最小充值金额
	MinDepositAmount string `protobuf:"bytes,8,opt,name=min_deposit_amount,json=minDepositAmount,proto3" json:"min_deposit_amount,omitempty"`
	// 充值地址
	DepositAddress string `protobuf:"bytes,9,opt,name=deposit_address,json=depositAddress,proto3" json:"deposit_address,omitempty"`
}

func (x *GasPoolDepositToken) Reset() {
	*x = GasPoolDepositToken{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_gaspool_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GasPoolDepositToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GasPoolDepositToken) ProtoMessage() {}

func (x *GasPoolDepositToken) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GasPoolDepositToken.ProtoReflect.Descriptor instead.
func (*GasPoolDepositToken) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{2}
}

func (x *GasPoolDepositToken) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GasPoolDepositToken) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *GasPoolDepositToken) GetDecimals() int64 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *GasPoolDepositToken) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *GasPoolDepositToken) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *GasPoolDepositToken) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *GasPoolDepositToken) GetChainId() string {
	if x != nil {
		return x.ChainId
	}
	return ""
}

func (x *GasPoolDepositToken) GetMinDepositAmount() string {
	if x != nil {
		return x.MinDepositAmount
	}
	return ""
}

func (x *GasPoolDepositToken) GetDepositAddress() string {
	if x != nil {
		return x.DepositAddress
	}
	return ""
}

type ListGasPoolDepositTokenReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListGasPoolDepositTokenReq) Reset() {
	*x = ListGasPoolDepositTokenReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_gaspool_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListGasPoolDepositTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGasPoolDepositTokenReq) ProtoMessage() {}

func (x *ListGasPoolDepositTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGasPoolDepositTokenReq.ProtoReflect.Descriptor instead.
func (*ListGasPoolDepositTokenReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{3}
}

type ListGasPoolDepositTokenReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*GasPoolDepositToken `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListGasPoolDepositTokenReply) Reset() {
	*x = ListGasPoolDepositTokenReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_gaspool_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListGasPoolDepositTokenReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGasPoolDepositTokenReply) ProtoMessage() {}

func (x *ListGasPoolDepositTokenReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGasPoolDepositTokenReply.ProtoReflect.Descriptor instead.
func (*ListGasPoolDepositTokenReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{4}
}

func (x *ListGasPoolDepositTokenReply) GetList() []*GasPoolDepositToken {
	if x != nil {
		return x.List
	}
	return nil
}

type GetGasPoolBalanceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WalletId string `protobuf:"bytes,1,opt,name=wallet_id,json=walletId,proto3" json:"wallet_id,omitempty"`
}

func (x *GetGasPoolBalanceReq) Reset() {
	*x = GetGasPoolBalanceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_gaspool_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGasPoolBalanceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGasPoolBalanceReq) ProtoMessage() {}

func (x *GetGasPoolBalanceReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGasPoolBalanceReq.ProtoReflect.Descriptor instead.
func (*GetGasPoolBalanceReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{5}
}

func (x *GetGasPoolBalanceReq) GetWalletId() string {
	if x != nil {
		return x.WalletId
	}
	return ""
}

type GasPoolUserStats struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 充值额度
	TotalDepositAmount string `protobuf:"bytes,1,opt,name=total_deposit_amount,json=totalDepositAmount,proto3" json:"total_deposit_amount,omitempty"`
	// 累计消耗
	TotalReduceAmount string `protobuf:"bytes,2,opt,name=total_reduce_amount,json=totalReduceAmount,proto3" json:"total_reduce_amount,omitempty"`
	// 积分额度 // 暂无
	TotalCreditAmount string `protobuf:"bytes,3,opt,name=total_credit_amount,json=totalCreditAmount,proto3" json:"total_credit_amount,omitempty"`
	// 累计使用次数
	TotalReduceCount int64 `protobuf:"varint,4,opt,name=total_reduce_count,json=totalReduceCount,proto3" json:"total_reduce_count,omitempty"`
	// 支持公链数
	ChainCount int64 `protobuf:"varint,5,opt,name=chain_count,json=chainCount,proto3" json:"chain_count,omitempty"`
}

func (x *GasPoolUserStats) Reset() {
	*x = GasPoolUserStats{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_gaspool_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GasPoolUserStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GasPoolUserStats) ProtoMessage() {}

func (x *GasPoolUserStats) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GasPoolUserStats.ProtoReflect.Descriptor instead.
func (*GasPoolUserStats) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{6}
}

func (x *GasPoolUserStats) GetTotalDepositAmount() string {
	if x != nil {
		return x.TotalDepositAmount
	}
	return ""
}

func (x *GasPoolUserStats) GetTotalReduceAmount() string {
	if x != nil {
		return x.TotalReduceAmount
	}
	return ""
}

func (x *GasPoolUserStats) GetTotalCreditAmount() string {
	if x != nil {
		return x.TotalCreditAmount
	}
	return ""
}

func (x *GasPoolUserStats) GetTotalReduceCount() int64 {
	if x != nil {
		return x.TotalReduceCount
	}
	return 0
}

func (x *GasPoolUserStats) GetChainCount() int64 {
	if x != nil {
		return x.ChainCount
	}
	return 0
}

type GetGasPoolBalanceReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 余额
	Balance string `protobuf:"bytes,1,opt,name=balance,proto3" json:"balance,omitempty"`
	// 统计数据
	Stats *GasPoolUserStats `protobuf:"bytes,2,opt,name=stats,proto3" json:"stats,omitempty"`
}

func (x *GetGasPoolBalanceReply) Reset() {
	*x = GetGasPoolBalanceReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_gaspool_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGasPoolBalanceReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGasPoolBalanceReply) ProtoMessage() {}

func (x *GetGasPoolBalanceReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGasPoolBalanceReply.ProtoReflect.Descriptor instead.
func (*GetGasPoolBalanceReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{7}
}

func (x *GetGasPoolBalanceReply) GetBalance() string {
	if x != nil {
		return x.Balance
	}
	return ""
}

func (x *GetGasPoolBalanceReply) GetStats() *GasPoolUserStats {
	if x != nil {
		return x.Stats
	}
	return nil
}

type ListGasPoolConsumeRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WalletId string `protobuf:"bytes,1,opt,name=wallet_id,json=walletId,proto3" json:"wallet_id,omitempty"`
	Page     int64  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Limit    int64  `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (x *ListGasPoolConsumeRecordReq) Reset() {
	*x = ListGasPoolConsumeRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_gaspool_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListGasPoolConsumeRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGasPoolConsumeRecordReq) ProtoMessage() {}

func (x *ListGasPoolConsumeRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGasPoolConsumeRecordReq.ProtoReflect.Descriptor instead.
func (*ListGasPoolConsumeRecordReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{8}
}

func (x *ListGasPoolConsumeRecordReq) GetWalletId() string {
	if x != nil {
		return x.WalletId
	}
	return ""
}

func (x *ListGasPoolConsumeRecordReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListGasPoolConsumeRecordReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type GasPoolConsumeRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 创建时间(时间戳,秒)
	CreatedAt int64 `protobuf:"varint,1,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,2,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 记录状态(success:执行成功, fail:执行失败, pending:执行中)
	Status string `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	// 用户交易hash
	TxHash string `protobuf:"bytes,4,opt,name=tx_hash,json=txHash,proto3" json:"tx_hash,omitempty"`
	// GasPool扣款金额(USDT)
	Amount string `protobuf:"bytes,5,opt,name=amount,proto3" json:"amount,omitempty"`
	// 记录类型(transfer:转账, swap:兑换, deposit_without_gas:充值预扣)
	RecordType string `protobuf:"bytes,6,opt,name=record_type,json=recordType,proto3" json:"record_type,omitempty"`
}

func (x *GasPoolConsumeRecord) Reset() {
	*x = GasPoolConsumeRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_gaspool_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GasPoolConsumeRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GasPoolConsumeRecord) ProtoMessage() {}

func (x *GasPoolConsumeRecord) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GasPoolConsumeRecord.ProtoReflect.Descriptor instead.
func (*GasPoolConsumeRecord) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{9}
}

func (x *GasPoolConsumeRecord) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *GasPoolConsumeRecord) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *GasPoolConsumeRecord) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GasPoolConsumeRecord) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

func (x *GasPoolConsumeRecord) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *GasPoolConsumeRecord) GetRecordType() string {
	if x != nil {
		return x.RecordType
	}
	return ""
}

type ListGasPoolConsumeRecordReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalCount int64                   `protobuf:"varint,1,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	List       []*GasPoolConsumeRecord `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListGasPoolConsumeRecordReply) Reset() {
	*x = ListGasPoolConsumeRecordReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_gaspool_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListGasPoolConsumeRecordReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGasPoolConsumeRecordReply) ProtoMessage() {}

func (x *ListGasPoolConsumeRecordReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGasPoolConsumeRecordReply.ProtoReflect.Descriptor instead.
func (*ListGasPoolConsumeRecordReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{10}
}

func (x *ListGasPoolConsumeRecordReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *ListGasPoolConsumeRecordReply) GetList() []*GasPoolConsumeRecord {
	if x != nil {
		return x.List
	}
	return nil
}

type ListGasPoolCashFlowRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WalletId string `protobuf:"bytes,1,opt,name=wallet_id,json=walletId,proto3" json:"wallet_id,omitempty"`
	Page     int64  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Limit    int64  `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (x *ListGasPoolCashFlowRecordReq) Reset() {
	*x = ListGasPoolCashFlowRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_gaspool_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListGasPoolCashFlowRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGasPoolCashFlowRecordReq) ProtoMessage() {}

func (x *ListGasPoolCashFlowRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGasPoolCashFlowRecordReq.ProtoReflect.Descriptor instead.
func (*ListGasPoolCashFlowRecordReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{11}
}

func (x *ListGasPoolCashFlowRecordReq) GetWalletId() string {
	if x != nil {
		return x.WalletId
	}
	return ""
}

func (x *ListGasPoolCashFlowRecordReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListGasPoolCashFlowRecordReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type GasPoolCashFlowRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 创建时间(时间戳,秒)
	CreatedAt int64 `protobuf:"varint,1,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,2,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 币合约地址(矿币为空)
	TokenAddress string `protobuf:"bytes,3,opt,name=token_address,json=tokenAddress,proto3" json:"token_address,omitempty"`
	// 用户交易hash
	TxHash string `protobuf:"bytes,4,opt,name=tx_hash,json=txHash,proto3" json:"tx_hash,omitempty"`
	// GasPool扣款金额(USDT)
	Amount string `protobuf:"bytes,5,opt,name=amount,proto3" json:"amount,omitempty"`
	// 记录类型(deposit:充值, refund:退回)
	RecordType string `protobuf:"bytes,6,opt,name=record_type,json=recordType,proto3" json:"record_type,omitempty"`
}

func (x *GasPoolCashFlowRecord) Reset() {
	*x = GasPoolCashFlowRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_gaspool_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GasPoolCashFlowRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GasPoolCashFlowRecord) ProtoMessage() {}

func (x *GasPoolCashFlowRecord) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GasPoolCashFlowRecord.ProtoReflect.Descriptor instead.
func (*GasPoolCashFlowRecord) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{12}
}

func (x *GasPoolCashFlowRecord) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *GasPoolCashFlowRecord) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *GasPoolCashFlowRecord) GetTokenAddress() string {
	if x != nil {
		return x.TokenAddress
	}
	return ""
}

func (x *GasPoolCashFlowRecord) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

func (x *GasPoolCashFlowRecord) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *GasPoolCashFlowRecord) GetRecordType() string {
	if x != nil {
		return x.RecordType
	}
	return ""
}

type ListGasPoolCashFlowRecordReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalCount int64                    `protobuf:"varint,1,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	List       []*GasPoolCashFlowRecord `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	TokenList  []*GasPoolDepositToken   `protobuf:"bytes,3,rep,name=token_list,json=tokenList,proto3" json:"token_list,omitempty"`
}

func (x *ListGasPoolCashFlowRecordReply) Reset() {
	*x = ListGasPoolCashFlowRecordReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_gaspool_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListGasPoolCashFlowRecordReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGasPoolCashFlowRecordReply) ProtoMessage() {}

func (x *ListGasPoolCashFlowRecordReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGasPoolCashFlowRecordReply.ProtoReflect.Descriptor instead.
func (*ListGasPoolCashFlowRecordReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{13}
}

func (x *ListGasPoolCashFlowRecordReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *ListGasPoolCashFlowRecordReply) GetList() []*GasPoolCashFlowRecord {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListGasPoolCashFlowRecordReply) GetTokenList() []*GasPoolDepositToken {
	if x != nil {
		return x.TokenList
	}
	return nil
}

type GetPaymasterReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 链索引
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
}

func (x *GetPaymasterReq) Reset() {
	*x = GetPaymasterReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_gaspool_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPaymasterReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymasterReq) ProtoMessage() {}

func (x *GetPaymasterReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymasterReq.ProtoReflect.Descriptor instead.
func (*GetPaymasterReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{14}
}

func (x *GetPaymasterReq) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

type GetPaymasterReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Address string `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
}

func (x *GetPaymasterReply) Reset() {
	*x = GetPaymasterReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_gaspool_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPaymasterReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymasterReply) ProtoMessage() {}

func (x *GetPaymasterReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymasterReply.ProtoReflect.Descriptor instead.
func (*GetPaymasterReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{15}
}

func (x *GetPaymasterReply) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

var File_api_wallet_v1_gaspool_proto protoreflect.FileDescriptor

var file_api_wallet_v1_gaspool_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2f, 0x76, 0x31, 0x2f,
	0x67, 0x61, 0x73, 0x70, 0x6f, 0x6f, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x62, 0x75, 0x66, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5c, 0x0a, 0x09, 0x53, 0x65, 0x6e, 0x64, 0x54,
	0x78, 0x52, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x6e,
	0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x61, 0x77, 0x5f, 0x74, 0x78, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x61, 0x77, 0x54, 0x78, 0x12, 0x17, 0x0a, 0x07,
	0x74, 0x78, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74,
	0x78, 0x54, 0x79, 0x70, 0x65, 0x22, 0x26, 0x0a, 0x0b, 0x53, 0x65, 0x6e, 0x64, 0x54, 0x78, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x78, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x78, 0x48, 0x61, 0x73, 0x68, 0x22, 0xa5, 0x02,
	0x0a, 0x13, 0x47, 0x61, 0x73, 0x50, 0x6f, 0x6f, 0x6c, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d,
	0x62, 0x6f, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f,
	0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x73, 0x12, 0x19, 0x0a,
	0x08, 0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6c, 0x6f, 0x67, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e,
	0x64, 0x65, 0x78, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x2c,
	0x0a, 0x12, 0x6d, 0x69, 0x6e, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6d, 0x69, 0x6e, 0x44,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x27, 0x0a, 0x0f,
	0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x1c, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x61, 0x73,
	0x50, 0x6f, 0x6f, 0x6c, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x52, 0x65, 0x71, 0x22, 0x56, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x61, 0x73, 0x50, 0x6f,
	0x6f, 0x6c, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x36, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x61, 0x73, 0x50, 0x6f, 0x6f, 0x6c, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x3c, 0x0a, 0x14, 0x47,
	0x65, 0x74, 0x47, 0x61, 0x73, 0x50, 0x6f, 0x6f, 0x6c, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x09, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x08, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x49, 0x64, 0x22, 0xf3, 0x01, 0x0a, 0x10, 0x47, 0x61,
	0x73, 0x50, 0x6f, 0x6f, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x30,
	0x0a, 0x14, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x2e, 0x0a, 0x13, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x64, 0x75, 0x63, 0x65,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x52, 0x65, 0x64, 0x75, 0x63, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x2e, 0x0a, 0x13, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x2c, 0x0a, 0x12, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x64, 0x75, 0x63, 0x65,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x52, 0x65, 0x64, 0x75, 0x63, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22,
	0x69, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x47, 0x61, 0x73, 0x50, 0x6f, 0x6f, 0x6c, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x12, 0x35, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x61, 0x73, 0x50, 0x6f, 0x6f, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74,
	0x61, 0x74, 0x73, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x73, 0x22, 0x81, 0x01, 0x0a, 0x1b, 0x4c,
	0x69, 0x73, 0x74, 0x47, 0x61, 0x73, 0x50, 0x6f, 0x6f, 0x6c, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x09, 0x77, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba,
	0x48, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x49, 0x64,
	0x12, 0x1b, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xba, 0x48, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1f, 0x0a,
	0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x09, 0xba, 0x48,
	0x06, 0x22, 0x04, 0x18, 0x64, 0x20, 0x00, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0xc0,
	0x01, 0x0a, 0x14, 0x47, 0x61, 0x73, 0x50, 0x6f, 0x6f, 0x6c, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f,
	0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x17, 0x0a, 0x07, 0x74, 0x78, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x74, 0x78, 0x48, 0x61, 0x73, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x22, 0x79, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x61, 0x73, 0x50, 0x6f, 0x6f, 0x6c,
	0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x37, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x61, 0x73, 0x50, 0x6f, 0x6f, 0x6c, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x82, 0x01, 0x0a,
	0x1c, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x61, 0x73, 0x50, 0x6f, 0x6f, 0x6c, 0x43, 0x61, 0x73, 0x68,
	0x46, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a,
	0x09, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xba, 0x48, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x12, 0x1f, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x09, 0xba, 0x48, 0x06, 0x22, 0x04, 0x18, 0x64, 0x20, 0x00, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x22, 0xce, 0x01, 0x0a, 0x15, 0x47, 0x61, 0x73, 0x50, 0x6f, 0x6f, 0x6c, 0x43, 0x61, 0x73,
	0x68, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x68,
	0x61, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x23, 0x0a, 0x0d, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x17, 0x0a, 0x07, 0x74, 0x78, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x74, 0x78, 0x48, 0x61, 0x73, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x54, 0x79,
	0x70, 0x65, 0x22, 0xbe, 0x01, 0x0a, 0x1e, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x61, 0x73, 0x50, 0x6f,
	0x6f, 0x6c, 0x43, 0x61, 0x73, 0x68, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x38, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x61, 0x73, 0x50, 0x6f, 0x6f, 0x6c, 0x43, 0x61, 0x73, 0x68,
	0x46, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x12, 0x41, 0x0a, 0x0a, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x61, 0x73, 0x50, 0x6f, 0x6f, 0x6c, 0x44, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x09, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x4c,
	0x69, 0x73, 0x74, 0x22, 0x32, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x61, 0x73,
	0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f,
	0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x2d, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x50, 0x61,
	0x79, 0x6d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x18, 0x0a, 0x07,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x32, 0xb0, 0x06, 0x0a, 0x0a, 0x47, 0x61, 0x73, 0x50, 0x6f,
	0x6f, 0x6c, 0x53, 0x72, 0x76, 0x12, 0x95, 0x01, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x61,
	0x73, 0x50, 0x6f, 0x6f, 0x6c, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x61, 0x73, 0x50, 0x6f, 0x6f, 0x6c, 0x44, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x2b, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x47, 0x61, 0x73, 0x50, 0x6f, 0x6f, 0x6c, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x22, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x1c, 0x12, 0x1a, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x61, 0x73, 0x70, 0x6f, 0x6f, 0x6c, 0x2f, 0x64,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x12, 0x7c, 0x0a,
	0x11, 0x47, 0x65, 0x74, 0x47, 0x61, 0x73, 0x50, 0x6f, 0x6f, 0x6c, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x61, 0x73, 0x50, 0x6f, 0x6f, 0x6c, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x61, 0x73, 0x50, 0x6f,
	0x6f, 0x6c, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1b,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x12, 0x13, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x61, 0x73, 0x70,
	0x6f, 0x6f, 0x6c, 0x2f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x99, 0x01, 0x0a, 0x18,
	0x4c, 0x69, 0x73, 0x74, 0x47, 0x61, 0x73, 0x50, 0x6f, 0x6f, 0x6c, 0x43, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x61, 0x73,
	0x50, 0x6f, 0x6f, 0x6c, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x52, 0x65, 0x71, 0x1a, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x61, 0x73, 0x50, 0x6f, 0x6f, 0x6c,
	0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x12, 0x1b, 0x2f, 0x76, 0x31, 0x2f,
	0x67, 0x61, 0x73, 0x70, 0x6f, 0x6f, 0x6c, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x5f,
	0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x9e, 0x01, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74,
	0x47, 0x61, 0x73, 0x50, 0x6f, 0x6f, 0x6c, 0x43, 0x61, 0x73, 0x68, 0x46, 0x6c, 0x6f, 0x77, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c,
	0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x61, 0x73, 0x50, 0x6f, 0x6f,
	0x6c, 0x43, 0x61, 0x73, 0x68, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52,
	0x65, 0x71, 0x1a, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x61, 0x73, 0x50, 0x6f, 0x6f, 0x6c, 0x43, 0x61,
	0x73, 0x68, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x12, 0x1d, 0x2f, 0x76, 0x31, 0x2f, 0x67,
	0x61, 0x73, 0x70, 0x6f, 0x6f, 0x6c, 0x2f, 0x63, 0x61, 0x73, 0x68, 0x5f, 0x66, 0x6c, 0x6f, 0x77,
	0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x6f, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x50,
	0x61, 0x79, 0x6d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d,
	0x61, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d,
	0x61, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1d, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x17, 0x12, 0x15, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x61, 0x73, 0x70, 0x6f, 0x6f, 0x6c, 0x2f,
	0x70, 0x61, 0x79, 0x6d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x12, 0x5e, 0x0a, 0x06, 0x53, 0x65, 0x6e,
	0x64, 0x54, 0x78, 0x12, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x54, 0x78, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x6e, 0x64, 0x54, 0x78, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1e, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x18, 0x3a, 0x01, 0x2a, 0x22, 0x13, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x61, 0x73, 0x70, 0x6f, 0x6f,
	0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x78, 0x42, 0x94, 0x01, 0x0a, 0x11, 0x63, 0x6f,
	0x6d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x42,
	0x0c, 0x47, 0x61, 0x73, 0x70, 0x6f, 0x6f, 0x6c, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a,
	0x1b, 0x62, 0x79, 0x64, 0x5f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0xa2, 0x02, 0x03, 0x41,
	0x57, 0x58, 0xaa, 0x02, 0x0d, 0x41, 0x70, 0x69, 0x2e, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e,
	0x56, 0x31, 0xca, 0x02, 0x0d, 0x41, 0x70, 0x69, 0x5c, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x5c,
	0x56, 0x31, 0xe2, 0x02, 0x19, 0x41, 0x70, 0x69, 0x5c, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x5c,
	0x56, 0x31, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0xea, 0x02,
	0x0f, 0x41, 0x70, 0x69, 0x3a, 0x3a, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x3a, 0x3a, 0x56, 0x31,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_wallet_v1_gaspool_proto_rawDescOnce sync.Once
	file_api_wallet_v1_gaspool_proto_rawDescData = file_api_wallet_v1_gaspool_proto_rawDesc
)

func file_api_wallet_v1_gaspool_proto_rawDescGZIP() []byte {
	file_api_wallet_v1_gaspool_proto_rawDescOnce.Do(func() {
		file_api_wallet_v1_gaspool_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_wallet_v1_gaspool_proto_rawDescData)
	})
	return file_api_wallet_v1_gaspool_proto_rawDescData
}

var file_api_wallet_v1_gaspool_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_api_wallet_v1_gaspool_proto_goTypes = []any{
	(*SendTxReq)(nil),                      // 0: api.wallet.v1.SendTxReq
	(*SendTxReply)(nil),                    // 1: api.wallet.v1.SendTxReply
	(*GasPoolDepositToken)(nil),            // 2: api.wallet.v1.GasPoolDepositToken
	(*ListGasPoolDepositTokenReq)(nil),     // 3: api.wallet.v1.ListGasPoolDepositTokenReq
	(*ListGasPoolDepositTokenReply)(nil),   // 4: api.wallet.v1.ListGasPoolDepositTokenReply
	(*GetGasPoolBalanceReq)(nil),           // 5: api.wallet.v1.GetGasPoolBalanceReq
	(*GasPoolUserStats)(nil),               // 6: api.wallet.v1.GasPoolUserStats
	(*GetGasPoolBalanceReply)(nil),         // 7: api.wallet.v1.GetGasPoolBalanceReply
	(*ListGasPoolConsumeRecordReq)(nil),    // 8: api.wallet.v1.ListGasPoolConsumeRecordReq
	(*GasPoolConsumeRecord)(nil),           // 9: api.wallet.v1.GasPoolConsumeRecord
	(*ListGasPoolConsumeRecordReply)(nil),  // 10: api.wallet.v1.ListGasPoolConsumeRecordReply
	(*ListGasPoolCashFlowRecordReq)(nil),   // 11: api.wallet.v1.ListGasPoolCashFlowRecordReq
	(*GasPoolCashFlowRecord)(nil),          // 12: api.wallet.v1.GasPoolCashFlowRecord
	(*ListGasPoolCashFlowRecordReply)(nil), // 13: api.wallet.v1.ListGasPoolCashFlowRecordReply
	(*GetPaymasterReq)(nil),                // 14: api.wallet.v1.GetPaymasterReq
	(*GetPaymasterReply)(nil),              // 15: api.wallet.v1.GetPaymasterReply
}
var file_api_wallet_v1_gaspool_proto_depIdxs = []int32{
	2,  // 0: api.wallet.v1.ListGasPoolDepositTokenReply.list:type_name -> api.wallet.v1.GasPoolDepositToken
	6,  // 1: api.wallet.v1.GetGasPoolBalanceReply.stats:type_name -> api.wallet.v1.GasPoolUserStats
	9,  // 2: api.wallet.v1.ListGasPoolConsumeRecordReply.list:type_name -> api.wallet.v1.GasPoolConsumeRecord
	12, // 3: api.wallet.v1.ListGasPoolCashFlowRecordReply.list:type_name -> api.wallet.v1.GasPoolCashFlowRecord
	2,  // 4: api.wallet.v1.ListGasPoolCashFlowRecordReply.token_list:type_name -> api.wallet.v1.GasPoolDepositToken
	3,  // 5: api.wallet.v1.GasPoolSrv.ListGasPoolDepositToken:input_type -> api.wallet.v1.ListGasPoolDepositTokenReq
	5,  // 6: api.wallet.v1.GasPoolSrv.GetGasPoolBalance:input_type -> api.wallet.v1.GetGasPoolBalanceReq
	8,  // 7: api.wallet.v1.GasPoolSrv.ListGasPoolConsumeRecord:input_type -> api.wallet.v1.ListGasPoolConsumeRecordReq
	11, // 8: api.wallet.v1.GasPoolSrv.ListGasPoolCashFlowRecord:input_type -> api.wallet.v1.ListGasPoolCashFlowRecordReq
	14, // 9: api.wallet.v1.GasPoolSrv.GetPaymaster:input_type -> api.wallet.v1.GetPaymasterReq
	0,  // 10: api.wallet.v1.GasPoolSrv.SendTx:input_type -> api.wallet.v1.SendTxReq
	4,  // 11: api.wallet.v1.GasPoolSrv.ListGasPoolDepositToken:output_type -> api.wallet.v1.ListGasPoolDepositTokenReply
	7,  // 12: api.wallet.v1.GasPoolSrv.GetGasPoolBalance:output_type -> api.wallet.v1.GetGasPoolBalanceReply
	10, // 13: api.wallet.v1.GasPoolSrv.ListGasPoolConsumeRecord:output_type -> api.wallet.v1.ListGasPoolConsumeRecordReply
	13, // 14: api.wallet.v1.GasPoolSrv.ListGasPoolCashFlowRecord:output_type -> api.wallet.v1.ListGasPoolCashFlowRecordReply
	15, // 15: api.wallet.v1.GasPoolSrv.GetPaymaster:output_type -> api.wallet.v1.GetPaymasterReply
	1,  // 16: api.wallet.v1.GasPoolSrv.SendTx:output_type -> api.wallet.v1.SendTxReply
	11, // [11:17] is the sub-list for method output_type
	5,  // [5:11] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_api_wallet_v1_gaspool_proto_init() }
func file_api_wallet_v1_gaspool_proto_init() {
	if File_api_wallet_v1_gaspool_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_wallet_v1_gaspool_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*SendTxReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_gaspool_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*SendTxReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_gaspool_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*GasPoolDepositToken); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_gaspool_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*ListGasPoolDepositTokenReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_gaspool_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*ListGasPoolDepositTokenReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_gaspool_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*GetGasPoolBalanceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_gaspool_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*GasPoolUserStats); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_gaspool_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*GetGasPoolBalanceReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_gaspool_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*ListGasPoolConsumeRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_gaspool_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*GasPoolConsumeRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_gaspool_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*ListGasPoolConsumeRecordReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_gaspool_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*ListGasPoolCashFlowRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_gaspool_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*GasPoolCashFlowRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_gaspool_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*ListGasPoolCashFlowRecordReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_gaspool_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*GetPaymasterReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_gaspool_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*GetPaymasterReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_wallet_v1_gaspool_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_wallet_v1_gaspool_proto_goTypes,
		DependencyIndexes: file_api_wallet_v1_gaspool_proto_depIdxs,
		MessageInfos:      file_api_wallet_v1_gaspool_proto_msgTypes,
	}.Build()
	File_api_wallet_v1_gaspool_proto = out.File
	file_api_wallet_v1_gaspool_proto_rawDesc = nil
	file_api_wallet_v1_gaspool_proto_goTypes = nil
	file_api_wallet_v1_gaspool_proto_depIdxs = nil
}
