// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/wallet/v1/paymaster.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TransactionMode int32

const (
	TransactionMode_UNKNOWN TransactionMode = 0
	// 充值gas pool
	TransactionMode_DEPOSIT TransactionMode = 1
	// 代付使用gas pool
	TransactionMode_RELAY TransactionMode = 2
)

// Enum value maps for TransactionMode.
var (
	TransactionMode_name = map[int32]string{
		0: "UNKNOWN",
		1: "DEPOSIT",
		2: "RELAY",
	}
	TransactionMode_value = map[string]int32{
		"UNKNOWN": 0,
		"DEPOSIT": 1,
		"RELAY":   2,
	}
)

func (x TransactionMode) Enum() *TransactionMode {
	p := new(TransactionMode)
	*p = x
	return p
}

func (x TransactionMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TransactionMode) Descriptor() protoreflect.EnumDescriptor {
	return file_api_wallet_v1_paymaster_proto_enumTypes[0].Descriptor()
}

func (TransactionMode) Type() protoreflect.EnumType {
	return &file_api_wallet_v1_paymaster_proto_enumTypes[0]
}

func (x TransactionMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TransactionMode.Descriptor instead.
func (TransactionMode) EnumDescriptor() ([]byte, []int) {
	return file_api_wallet_v1_paymaster_proto_rawDescGZIP(), []int{0}
}

// 定义交易相关消息
type EvmTransaction struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// chain index
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// from地址
	From string `protobuf:"bytes,2,opt,name=from,proto3" json:"from,omitempty"`
	// 接收地址，代币转账就是合约地址
	To string `protobuf:"bytes,3,opt,name=to,proto3" json:"to,omitempty"`
	// 原生代币的数量，如：eth，bnb等
	Value string `protobuf:"bytes,4,opt,name=value,proto3" json:"value,omitempty"` // string
	// gas price 待定
	GasPrice string `protobuf:"bytes,5,opt,name=gas_price,json=gasPrice,proto3" json:"gas_price,omitempty"`
	// gas limit
	GasLimit string `protobuf:"bytes,6,opt,name=gas_limit,json=gasLimit,proto3" json:"gas_limit,omitempty"`
	// nonce
	Nonce uint64 `protobuf:"varint,7,opt,name=nonce,proto3" json:"nonce,omitempty"`
	// data 如：0xa9059cbb000000000000000000000000796fa99985003bb3868e15bb5a9e5c9be19e4f210000000000000000000000000000000000000000000000000de0b6b3a7640000
	Data string `protobuf:"bytes,8,opt,name=data,proto3" json:"data,omitempty"`
	// 签名数据 如：0xf8a60780830493e094337610d27c682e347c9cd60bd4b3b107c9d34ddd80b844a9059cbb000000000000000000000000796fa99985003bb3868e15bb5a9e5c9be19e4f210000000000000000000000000000000000000000000000000de0b6b3a764000081e5a0dcb3a8cb59c05501d133cddc68c0c6a12b5f4a7f6b5a99679e91c32fbc120afea02f09afe4fd9af6af8db6d8ac6e6b1453bc839abcde16399e54cd4cc21fb39091
	UnsignedTxSerialized string          `protobuf:"bytes,9,opt,name=unsigned_tx_serialized,json=unsignedTxSerialized,proto3" json:"unsigned_tx_serialized,omitempty"`
	Mode                 TransactionMode `protobuf:"varint,10,opt,name=mode,proto3,enum=api.wallet.v1.TransactionMode" json:"mode,omitempty"`
	// Optional EIP-2612 permit signature data for atomic permit+transfer operations
	// When these fields are provided along with transfer data, the transaction will be processed atomically
	PermitParams   *PermitSignatureData `protobuf:"bytes,11,opt,name=permit_params,json=permitParams,proto3,oneof" json:"permit_params,omitempty"`
	TransferParams *TransferParameters  `protobuf:"bytes,12,opt,name=transfer_params,json=transferParams,proto3,oneof" json:"transfer_params,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *EvmTransaction) Reset() {
	*x = EvmTransaction{}
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EvmTransaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvmTransaction) ProtoMessage() {}

func (x *EvmTransaction) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvmTransaction.ProtoReflect.Descriptor instead.
func (*EvmTransaction) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_paymaster_proto_rawDescGZIP(), []int{0}
}

func (x *EvmTransaction) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *EvmTransaction) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *EvmTransaction) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

func (x *EvmTransaction) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *EvmTransaction) GetGasPrice() string {
	if x != nil {
		return x.GasPrice
	}
	return ""
}

func (x *EvmTransaction) GetGasLimit() string {
	if x != nil {
		return x.GasLimit
	}
	return ""
}

func (x *EvmTransaction) GetNonce() uint64 {
	if x != nil {
		return x.Nonce
	}
	return 0
}

func (x *EvmTransaction) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

func (x *EvmTransaction) GetUnsignedTxSerialized() string {
	if x != nil {
		return x.UnsignedTxSerialized
	}
	return ""
}

func (x *EvmTransaction) GetMode() TransactionMode {
	if x != nil {
		return x.Mode
	}
	return TransactionMode_UNKNOWN
}

func (x *EvmTransaction) GetPermitParams() *PermitSignatureData {
	if x != nil {
		return x.PermitParams
	}
	return nil
}

func (x *EvmTransaction) GetTransferParams() *TransferParameters {
	if x != nil {
		return x.TransferParams
	}
	return nil
}

type TokenPermissions struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// token 地址
	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	// 授权数量
	Amount        string `protobuf:"bytes,2,opt,name=amount,proto3" json:"amount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TokenPermissions) Reset() {
	*x = TokenPermissions{}
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TokenPermissions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenPermissions) ProtoMessage() {}

func (x *TokenPermissions) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenPermissions.ProtoReflect.Descriptor instead.
func (*TokenPermissions) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_paymaster_proto_rawDescGZIP(), []int{1}
}

func (x *TokenPermissions) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *TokenPermissions) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

// Permit 数据
type PermitTransferFrom struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// token 授权
	Permitted *TokenPermissions `protobuf:"bytes,1,opt,name=permitted,proto3" json:"permitted,omitempty"`
	// 防止重放攻击
	Nonce uint64 `protobuf:"varint,3,opt,name=nonce,proto3" json:"nonce,omitempty"`
	// 过期时间（Unix 时间戳）
	Deadline      uint64 `protobuf:"varint,4,opt,name=deadline,proto3" json:"deadline,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PermitTransferFrom) Reset() {
	*x = PermitTransferFrom{}
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PermitTransferFrom) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PermitTransferFrom) ProtoMessage() {}

func (x *PermitTransferFrom) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PermitTransferFrom.ProtoReflect.Descriptor instead.
func (*PermitTransferFrom) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_paymaster_proto_rawDescGZIP(), []int{2}
}

func (x *PermitTransferFrom) GetPermitted() *TokenPermissions {
	if x != nil {
		return x.Permitted
	}
	return nil
}

func (x *PermitTransferFrom) GetNonce() uint64 {
	if x != nil {
		return x.Nonce
	}
	return 0
}

func (x *PermitTransferFrom) GetDeadline() uint64 {
	if x != nil {
		return x.Deadline
	}
	return 0
}

// 转账详情
type TransferDetails struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 接收方地址
	To string `protobuf:"bytes,1,opt,name=to,proto3" json:"to,omitempty"`
	// 扣款金额（字符串格式，如 "1000000000000000000"）
	RequestedAmount string `protobuf:"bytes,2,opt,name=requested_amount,json=requestedAmount,proto3" json:"requested_amount,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *TransferDetails) Reset() {
	*x = TransferDetails{}
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransferDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransferDetails) ProtoMessage() {}

func (x *TransferDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransferDetails.ProtoReflect.Descriptor instead.
func (*TransferDetails) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_paymaster_proto_rawDescGZIP(), []int{3}
}

func (x *TransferDetails) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

func (x *TransferDetails) GetRequestedAmount() string {
	if x != nil {
		return x.RequestedAmount
	}
	return ""
}

// EIP-2612 permit signature data for atomic operations
type PermitSignatureData struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Permit          *PermitTransferFrom    `protobuf:"bytes,1,opt,name=permit,proto3" json:"permit,omitempty"`
	TransferDetails *TransferDetails       `protobuf:"bytes,2,opt,name=transfer_details,json=transferDetails,proto3" json:"transfer_details,omitempty"`
	// 用户地址
	Owner string `protobuf:"bytes,3,opt,name=owner,proto3" json:"owner,omitempty"`
	// 用户签名（v, r, s 编码）
	Signature string `protobuf:"bytes,4,opt,name=signature,proto3" json:"signature,omitempty"`
	// 用户原始交易数据（可选）
	UserTxData    string `protobuf:"bytes,5,opt,name=user_tx_data,json=userTxData,proto3" json:"user_tx_data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PermitSignatureData) Reset() {
	*x = PermitSignatureData{}
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PermitSignatureData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PermitSignatureData) ProtoMessage() {}

func (x *PermitSignatureData) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PermitSignatureData.ProtoReflect.Descriptor instead.
func (*PermitSignatureData) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_paymaster_proto_rawDescGZIP(), []int{4}
}

func (x *PermitSignatureData) GetPermit() *PermitTransferFrom {
	if x != nil {
		return x.Permit
	}
	return nil
}

func (x *PermitSignatureData) GetTransferDetails() *TransferDetails {
	if x != nil {
		return x.TransferDetails
	}
	return nil
}

func (x *PermitSignatureData) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

func (x *PermitSignatureData) GetSignature() string {
	if x != nil {
		return x.Signature
	}
	return ""
}

func (x *PermitSignatureData) GetUserTxData() string {
	if x != nil {
		return x.UserTxData
	}
	return ""
}

// Transfer parameters for atomic permit+transfer operations
type TransferParameters struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Recipient address for the transfer
	To string `protobuf:"bytes,1,opt,name=to,proto3" json:"to,omitempty"`
	// Amount to transfer (as string to handle large numbers)
	Amount string `protobuf:"bytes,2,opt,name=amount,proto3" json:"amount,omitempty"`
	// Optional additional data for the transfer
	TransferData  string `protobuf:"bytes,3,opt,name=transfer_data,json=transferData,proto3" json:"transfer_data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TransferParameters) Reset() {
	*x = TransferParameters{}
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransferParameters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransferParameters) ProtoMessage() {}

func (x *TransferParameters) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransferParameters.ProtoReflect.Descriptor instead.
func (*TransferParameters) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_paymaster_proto_rawDescGZIP(), []int{5}
}

func (x *TransferParameters) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

func (x *TransferParameters) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *TransferParameters) GetTransferData() string {
	if x != nil {
		return x.TransferData
	}
	return ""
}

// Solana特有的交易结构
type SolTransaction struct {
	state      protoimpl.MessageState `protogen:"open.v1"`
	ChainIndex int64                  `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	Mode       TransactionMode        `protobuf:"varint,2,opt,name=mode,proto3,enum=api.wallet.v1.TransactionMode" json:"mode,omitempty"`
	// 最近交易hash
	//
	//	string recent_block_hash = 2;
	//
	// 签名地址
	Sender string `protobuf:"bytes,3,opt,name=sender,proto3" json:"sender,omitempty"`
	// 序列化message base64
	SerializedMessage string `protobuf:"bytes,7,opt,name=serialized_message,json=serializedMessage,proto3" json:"serialized_message,omitempty"`
	// 签名数据
	Signature     string `protobuf:"bytes,8,opt,name=signature,proto3" json:"signature,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SolTransaction) Reset() {
	*x = SolTransaction{}
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SolTransaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SolTransaction) ProtoMessage() {}

func (x *SolTransaction) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SolTransaction.ProtoReflect.Descriptor instead.
func (*SolTransaction) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_paymaster_proto_rawDescGZIP(), []int{6}
}

func (x *SolTransaction) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *SolTransaction) GetMode() TransactionMode {
	if x != nil {
		return x.Mode
	}
	return TransactionMode_UNKNOWN
}

func (x *SolTransaction) GetSender() string {
	if x != nil {
		return x.Sender
	}
	return ""
}

func (x *SolTransaction) GetSerializedMessage() string {
	if x != nil {
		return x.SerializedMessage
	}
	return ""
}

func (x *SolTransaction) GetSignature() string {
	if x != nil {
		return x.Signature
	}
	return ""
}

// 请求响应定义
type RelayRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Transaction:
	//
	//	*RelayRequest_Evm
	//	*RelayRequest_Solana
	Transaction   isRelayRequest_Transaction `protobuf_oneof:"transaction"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RelayRequest) Reset() {
	*x = RelayRequest{}
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RelayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelayRequest) ProtoMessage() {}

func (x *RelayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelayRequest.ProtoReflect.Descriptor instead.
func (*RelayRequest) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_paymaster_proto_rawDescGZIP(), []int{7}
}

func (x *RelayRequest) GetTransaction() isRelayRequest_Transaction {
	if x != nil {
		return x.Transaction
	}
	return nil
}

func (x *RelayRequest) GetEvm() *EvmTransaction {
	if x != nil {
		if x, ok := x.Transaction.(*RelayRequest_Evm); ok {
			return x.Evm
		}
	}
	return nil
}

func (x *RelayRequest) GetSolana() *SolTransaction {
	if x != nil {
		if x, ok := x.Transaction.(*RelayRequest_Solana); ok {
			return x.Solana
		}
	}
	return nil
}

type isRelayRequest_Transaction interface {
	isRelayRequest_Transaction()
}

type RelayRequest_Evm struct {
	Evm *EvmTransaction `protobuf:"bytes,2,opt,name=evm,proto3,oneof"`
}

type RelayRequest_Solana struct {
	Solana *SolTransaction `protobuf:"bytes,3,opt,name=solana,proto3,oneof"`
}

func (*RelayRequest_Evm) isRelayRequest_Transaction() {}

func (*RelayRequest_Solana) isRelayRequest_Transaction() {}

type RelayResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 当前链上hash
	TxHash string `protobuf:"bytes,1,opt,name=tx_hash,json=txHash,proto3" json:"tx_hash,omitempty"`
	// 费用
	Fee string `protobuf:"bytes,2,opt,name=fee,proto3" json:"fee,omitempty"`
	// 新增字段，用于返回赞助交易哈希
	SponsorTxHash string `protobuf:"bytes,3,opt,name=sponsor_tx_hash,json=sponsorTxHash,proto3" json:"sponsor_tx_hash,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RelayResponse) Reset() {
	*x = RelayResponse{}
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RelayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelayResponse) ProtoMessage() {}

func (x *RelayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelayResponse.ProtoReflect.Descriptor instead.
func (*RelayResponse) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_paymaster_proto_rawDescGZIP(), []int{8}
}

func (x *RelayResponse) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

func (x *RelayResponse) GetFee() string {
	if x != nil {
		return x.Fee
	}
	return ""
}

func (x *RelayResponse) GetSponsorTxHash() string {
	if x != nil {
		return x.SponsorTxHash
	}
	return ""
}

type GetRelayReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ChainIndex    int64                  `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	Txn           string                 `protobuf:"bytes,2,opt,name=txn,proto3" json:"txn,omitempty"`
	Username      string                 `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRelayReq) Reset() {
	*x = GetRelayReq{}
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRelayReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRelayReq) ProtoMessage() {}

func (x *GetRelayReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRelayReq.ProtoReflect.Descriptor instead.
func (*GetRelayReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_paymaster_proto_rawDescGZIP(), []int{9}
}

func (x *GetRelayReq) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *GetRelayReq) GetTxn() string {
	if x != nil {
		return x.Txn
	}
	return ""
}

func (x *GetRelayReq) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

type GetRelayReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ChainIndex    int64                  `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	Txn           string                 `protobuf:"bytes,2,opt,name=txn,proto3" json:"txn,omitempty"`
	Status        int64                  `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRelayReply) Reset() {
	*x = GetRelayReply{}
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRelayReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRelayReply) ProtoMessage() {}

func (x *GetRelayReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRelayReply.ProtoReflect.Descriptor instead.
func (*GetRelayReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_paymaster_proto_rawDescGZIP(), []int{10}
}

func (x *GetRelayReply) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *GetRelayReply) GetTxn() string {
	if x != nil {
		return x.Txn
	}
	return ""
}

func (x *GetRelayReply) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

type PrepareTransactionReply struct {
	state      protoimpl.MessageState `protogen:"open.v1"`
	ChainIndex int64                  `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 序列化数据
	SerializedMessage string `protobuf:"bytes,2,opt,name=serialized_message,json=serializedMessage,proto3" json:"serialized_message,omitempty"`
	// Use oneof to support different chain types while maintaining backward compatibility
	//
	// Types that are valid to be assigned to UnsignedTx:
	//
	//	*PrepareTransactionReply_SolanaTx
	//	*PrepareTransactionReply_EvmTx
	UnsignedTx    isPrepareTransactionReply_UnsignedTx `protobuf_oneof:"unsigned_tx"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PrepareTransactionReply) Reset() {
	*x = PrepareTransactionReply{}
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PrepareTransactionReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrepareTransactionReply) ProtoMessage() {}

func (x *PrepareTransactionReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrepareTransactionReply.ProtoReflect.Descriptor instead.
func (*PrepareTransactionReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_paymaster_proto_rawDescGZIP(), []int{11}
}

func (x *PrepareTransactionReply) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *PrepareTransactionReply) GetSerializedMessage() string {
	if x != nil {
		return x.SerializedMessage
	}
	return ""
}

func (x *PrepareTransactionReply) GetUnsignedTx() isPrepareTransactionReply_UnsignedTx {
	if x != nil {
		return x.UnsignedTx
	}
	return nil
}

func (x *PrepareTransactionReply) GetSolanaTx() *SolUnsignedTx {
	if x != nil {
		if x, ok := x.UnsignedTx.(*PrepareTransactionReply_SolanaTx); ok {
			return x.SolanaTx
		}
	}
	return nil
}

func (x *PrepareTransactionReply) GetEvmTx() *EvmUnsignedTx {
	if x != nil {
		if x, ok := x.UnsignedTx.(*PrepareTransactionReply_EvmTx); ok {
			return x.EvmTx
		}
	}
	return nil
}

type isPrepareTransactionReply_UnsignedTx interface {
	isPrepareTransactionReply_UnsignedTx()
}

type PrepareTransactionReply_SolanaTx struct {
	SolanaTx *SolUnsignedTx `protobuf:"bytes,3,opt,name=solana_tx,json=solanaTx,proto3,oneof"`
}

type PrepareTransactionReply_EvmTx struct {
	EvmTx *EvmUnsignedTx `protobuf:"bytes,4,opt,name=evm_tx,json=evmTx,proto3,oneof"`
}

func (*PrepareTransactionReply_SolanaTx) isPrepareTransactionReply_UnsignedTx() {}

func (*PrepareTransactionReply_EvmTx) isPrepareTransactionReply_UnsignedTx() {}

type SolUnsignedTx struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	AccountKeys     []string               `protobuf:"bytes,3,rep,name=account_keys,json=accountKeys,proto3" json:"account_keys,omitempty"`
	Instructions    []*Instruction         `protobuf:"bytes,4,rep,name=instructions,proto3" json:"instructions,omitempty"`
	RecentBlockHash string                 `protobuf:"bytes,5,opt,name=recent_block_hash,json=recentBlockHash,proto3" json:"recent_block_hash,omitempty"`
	Header          *MessageHeader         `protobuf:"bytes,6,opt,name=header,proto3" json:"header,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *SolUnsignedTx) Reset() {
	*x = SolUnsignedTx{}
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SolUnsignedTx) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SolUnsignedTx) ProtoMessage() {}

func (x *SolUnsignedTx) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SolUnsignedTx.ProtoReflect.Descriptor instead.
func (*SolUnsignedTx) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_paymaster_proto_rawDescGZIP(), []int{12}
}

func (x *SolUnsignedTx) GetAccountKeys() []string {
	if x != nil {
		return x.AccountKeys
	}
	return nil
}

func (x *SolUnsignedTx) GetInstructions() []*Instruction {
	if x != nil {
		return x.Instructions
	}
	return nil
}

func (x *SolUnsignedTx) GetRecentBlockHash() string {
	if x != nil {
		return x.RecentBlockHash
	}
	return ""
}

func (x *SolUnsignedTx) GetHeader() *MessageHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

type Instruction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProgramId     string                 `protobuf:"bytes,1,opt,name=programId,proto3" json:"programId,omitempty"`
	Accounts      []int32                `protobuf:"varint,2,rep,packed,name=accounts,proto3" json:"accounts,omitempty"`
	Data          string                 `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"` // base64 编码
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Instruction) Reset() {
	*x = Instruction{}
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Instruction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Instruction) ProtoMessage() {}

func (x *Instruction) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Instruction.ProtoReflect.Descriptor instead.
func (*Instruction) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_paymaster_proto_rawDescGZIP(), []int{13}
}

func (x *Instruction) GetProgramId() string {
	if x != nil {
		return x.ProgramId
	}
	return ""
}

func (x *Instruction) GetAccounts() []int32 {
	if x != nil {
		return x.Accounts
	}
	return nil
}

func (x *Instruction) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type MessageHeader struct {
	state                       protoimpl.MessageState `protogen:"open.v1"`
	NumRequiredSignatures       int32                  `protobuf:"varint,1,opt,name=numRequiredSignatures,proto3" json:"numRequiredSignatures,omitempty"`
	NumReadonlySignedAccounts   int32                  `protobuf:"varint,2,opt,name=numReadonlySignedAccounts,proto3" json:"numReadonlySignedAccounts,omitempty"`
	NumReadonlyUnsignedAccounts int32                  `protobuf:"varint,3,opt,name=numReadonlyUnsignedAccounts,proto3" json:"numReadonlyUnsignedAccounts,omitempty"`
	unknownFields               protoimpl.UnknownFields
	sizeCache                   protoimpl.SizeCache
}

func (x *MessageHeader) Reset() {
	*x = MessageHeader{}
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageHeader) ProtoMessage() {}

func (x *MessageHeader) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageHeader.ProtoReflect.Descriptor instead.
func (*MessageHeader) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_paymaster_proto_rawDescGZIP(), []int{14}
}

func (x *MessageHeader) GetNumRequiredSignatures() int32 {
	if x != nil {
		return x.NumRequiredSignatures
	}
	return 0
}

func (x *MessageHeader) GetNumReadonlySignedAccounts() int32 {
	if x != nil {
		return x.NumReadonlySignedAccounts
	}
	return 0
}

func (x *MessageHeader) GetNumReadonlyUnsignedAccounts() int32 {
	if x != nil {
		return x.NumReadonlyUnsignedAccounts
	}
	return 0
}

// EVM unsigned transaction structure for Ethereum, BSC, and other EVM-compatible chains
type EvmUnsignedTx struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Transaction sender address
	From string `protobuf:"bytes,1,opt,name=from,proto3" json:"from,omitempty"`
	// Transaction recipient address
	To string `protobuf:"bytes,2,opt,name=to,proto3" json:"to,omitempty"`
	// Transaction value in wei (as string to handle large numbers)
	Value string `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
	// Gas price in wei (as string to handle large numbers)
	GasPrice string `protobuf:"bytes,4,opt,name=gas_price,json=gasPrice,proto3" json:"gas_price,omitempty"`
	// Gas limit
	GasLimit string `protobuf:"bytes,5,opt,name=gas_limit,json=gasLimit,proto3" json:"gas_limit,omitempty"`
	// Transaction nonce
	Nonce uint64 `protobuf:"varint,6,opt,name=nonce,proto3" json:"nonce,omitempty"`
	// Transaction data (contract call data or empty for simple transfers)
	Data string `protobuf:"bytes,7,opt,name=data,proto3" json:"data,omitempty"`
	// Chain ID for EIP-155 replay protection
	ChainId uint64 `protobuf:"varint,8,opt,name=chain_id,json=chainId,proto3" json:"chain_id,omitempty"`
	// Transaction type (0 for legacy, 2 for EIP-1559)
	TransactionType uint32 `protobuf:"varint,9,opt,name=transaction_type,json=transactionType,proto3" json:"transaction_type,omitempty"`
	// EIP-1559 fields (optional, used when transaction_type = 2)
	MaxFeePerGas         string `protobuf:"bytes,10,opt,name=max_fee_per_gas,json=maxFeePerGas,proto3" json:"max_fee_per_gas,omitempty"`
	MaxPriorityFeePerGas string `protobuf:"bytes,11,opt,name=max_priority_fee_per_gas,json=maxPriorityFeePerGas,proto3" json:"max_priority_fee_per_gas,omitempty"`
	V                    string `protobuf:"bytes,12,opt,name=v,proto3" json:"v,omitempty"`
	R                    string `protobuf:"bytes,13,opt,name=r,proto3" json:"r,omitempty"`
	S                    string `protobuf:"bytes,14,opt,name=s,proto3" json:"s,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *EvmUnsignedTx) Reset() {
	*x = EvmUnsignedTx{}
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EvmUnsignedTx) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvmUnsignedTx) ProtoMessage() {}

func (x *EvmUnsignedTx) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvmUnsignedTx.ProtoReflect.Descriptor instead.
func (*EvmUnsignedTx) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_paymaster_proto_rawDescGZIP(), []int{15}
}

func (x *EvmUnsignedTx) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *EvmUnsignedTx) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

func (x *EvmUnsignedTx) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *EvmUnsignedTx) GetGasPrice() string {
	if x != nil {
		return x.GasPrice
	}
	return ""
}

func (x *EvmUnsignedTx) GetGasLimit() string {
	if x != nil {
		return x.GasLimit
	}
	return ""
}

func (x *EvmUnsignedTx) GetNonce() uint64 {
	if x != nil {
		return x.Nonce
	}
	return 0
}

func (x *EvmUnsignedTx) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

func (x *EvmUnsignedTx) GetChainId() uint64 {
	if x != nil {
		return x.ChainId
	}
	return 0
}

func (x *EvmUnsignedTx) GetTransactionType() uint32 {
	if x != nil {
		return x.TransactionType
	}
	return 0
}

func (x *EvmUnsignedTx) GetMaxFeePerGas() string {
	if x != nil {
		return x.MaxFeePerGas
	}
	return ""
}

func (x *EvmUnsignedTx) GetMaxPriorityFeePerGas() string {
	if x != nil {
		return x.MaxPriorityFeePerGas
	}
	return ""
}

func (x *EvmUnsignedTx) GetV() string {
	if x != nil {
		return x.V
	}
	return ""
}

func (x *EvmUnsignedTx) GetR() string {
	if x != nil {
		return x.R
	}
	return ""
}

func (x *EvmUnsignedTx) GetS() string {
	if x != nil {
		return x.S
	}
	return ""
}

type DepositAddressRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ChainIndex    int64                  `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DepositAddressRequest) Reset() {
	*x = DepositAddressRequest{}
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DepositAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositAddressRequest) ProtoMessage() {}

func (x *DepositAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositAddressRequest.ProtoReflect.Descriptor instead.
func (*DepositAddressRequest) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_paymaster_proto_rawDescGZIP(), []int{16}
}

func (x *DepositAddressRequest) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

type DepositAddressReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ChainIndex    int64                  `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	Address       string                 `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DepositAddressReply) Reset() {
	*x = DepositAddressReply{}
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DepositAddressReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositAddressReply) ProtoMessage() {}

func (x *DepositAddressReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositAddressReply.ProtoReflect.Descriptor instead.
func (*DepositAddressReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_paymaster_proto_rawDescGZIP(), []int{17}
}

func (x *DepositAddressReply) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *DepositAddressReply) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type DepositToken struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 合约地址
	Contract string `protobuf:"bytes,1,opt,name=contract,proto3" json:"contract,omitempty"`
	// 最小充值
	Min string `protobuf:"bytes,2,opt,name=min,proto3" json:"min,omitempty"`
	// 最大充值
	Max string `protobuf:"bytes,3,opt,name=max,proto3" json:"max,omitempty"`
	// 链标识
	ChainIndex    int64 `protobuf:"varint,4,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DepositToken) Reset() {
	*x = DepositToken{}
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DepositToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositToken) ProtoMessage() {}

func (x *DepositToken) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositToken.ProtoReflect.Descriptor instead.
func (*DepositToken) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_paymaster_proto_rawDescGZIP(), []int{18}
}

func (x *DepositToken) GetContract() string {
	if x != nil {
		return x.Contract
	}
	return ""
}

func (x *DepositToken) GetMin() string {
	if x != nil {
		return x.Min
	}
	return ""
}

func (x *DepositToken) GetMax() string {
	if x != nil {
		return x.Max
	}
	return ""
}

func (x *DepositToken) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

type DepositTokensReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*DepositToken        `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DepositTokensReply) Reset() {
	*x = DepositTokensReply{}
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DepositTokensReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositTokensReply) ProtoMessage() {}

func (x *DepositTokensReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_paymaster_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositTokensReply.ProtoReflect.Descriptor instead.
func (*DepositTokensReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_paymaster_proto_rawDescGZIP(), []int{19}
}

func (x *DepositTokensReply) GetList() []*DepositToken {
	if x != nil {
		return x.List
	}
	return nil
}

var File_api_wallet_v1_paymaster_proto protoreflect.FileDescriptor

const file_api_wallet_v1_paymaster_proto_rawDesc = "" +
	"\n" +
	"\x1dapi/wallet/v1/paymaster.proto\x12\rapi.wallet.v1\x1a\x1bbuf/validate/validate.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\"\xbf\x04\n" +
	"\x0eEvmTransaction\x12\x1f\n" +
	"\vchain_index\x18\x01 \x01(\x03R\n" +
	"chainIndex\x12.\n" +
	"\x04from\x18\x02 \x01(\tB\x1a\xbaH\x17r\x152\x13^0x[0-9a-fA-F]{40}$R\x04from\x12*\n" +
	"\x02to\x18\x03 \x01(\tB\x1a\xbaH\x17r\x152\x13^0x[0-9a-fA-F]{40}$R\x02to\x12\x14\n" +
	"\x05value\x18\x04 \x01(\tR\x05value\x12\x1b\n" +
	"\tgas_price\x18\x05 \x01(\tR\bgasPrice\x12\x1b\n" +
	"\tgas_limit\x18\x06 \x01(\tR\bgasLimit\x12\x14\n" +
	"\x05nonce\x18\a \x01(\x04R\x05nonce\x12\x12\n" +
	"\x04data\x18\b \x01(\tR\x04data\x12=\n" +
	"\x16unsigned_tx_serialized\x18\t \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x14unsignedTxSerialized\x122\n" +
	"\x04mode\x18\n" +
	" \x01(\x0e2\x1e.api.wallet.v1.TransactionModeR\x04mode\x12L\n" +
	"\rpermit_params\x18\v \x01(\v2\".api.wallet.v1.PermitSignatureDataH\x00R\fpermitParams\x88\x01\x01\x12O\n" +
	"\x0ftransfer_params\x18\f \x01(\v2!.api.wallet.v1.TransferParametersH\x01R\x0etransferParams\x88\x01\x01B\x10\n" +
	"\x0e_permit_paramsB\x12\n" +
	"\x10_transfer_params\"@\n" +
	"\x10TokenPermissions\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\tR\x06amount\"\x85\x01\n" +
	"\x12PermitTransferFrom\x12=\n" +
	"\tpermitted\x18\x01 \x01(\v2\x1f.api.wallet.v1.TokenPermissionsR\tpermitted\x12\x14\n" +
	"\x05nonce\x18\x03 \x01(\x04R\x05nonce\x12\x1a\n" +
	"\bdeadline\x18\x04 \x01(\x04R\bdeadline\"L\n" +
	"\x0fTransferDetails\x12\x0e\n" +
	"\x02to\x18\x01 \x01(\tR\x02to\x12)\n" +
	"\x10requested_amount\x18\x02 \x01(\tR\x0frequestedAmount\"\xf1\x01\n" +
	"\x13PermitSignatureData\x129\n" +
	"\x06permit\x18\x01 \x01(\v2!.api.wallet.v1.PermitTransferFromR\x06permit\x12I\n" +
	"\x10transfer_details\x18\x02 \x01(\v2\x1e.api.wallet.v1.TransferDetailsR\x0ftransferDetails\x12\x14\n" +
	"\x05owner\x18\x03 \x01(\tR\x05owner\x12\x1c\n" +
	"\tsignature\x18\x04 \x01(\tR\tsignature\x12 \n" +
	"\fuser_tx_data\x18\x05 \x01(\tR\n" +
	"userTxData\"}\n" +
	"\x12TransferParameters\x12*\n" +
	"\x02to\x18\x01 \x01(\tB\x1a\xbaH\x17r\x152\x13^0x[0-9a-fA-F]{40}$R\x02to\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\tR\x06amount\x12#\n" +
	"\rtransfer_data\x18\x03 \x01(\tR\ftransferData\"\xca\x01\n" +
	"\x0eSolTransaction\x12\x1f\n" +
	"\vchain_index\x18\x01 \x01(\x03R\n" +
	"chainIndex\x122\n" +
	"\x04mode\x18\x02 \x01(\x0e2\x1e.api.wallet.v1.TransactionModeR\x04mode\x12\x16\n" +
	"\x06sender\x18\x03 \x01(\tR\x06sender\x12-\n" +
	"\x12serialized_message\x18\a \x01(\tR\x11serializedMessage\x12\x1c\n" +
	"\tsignature\x18\b \x01(\tR\tsignature\"\x89\x01\n" +
	"\fRelayRequest\x121\n" +
	"\x03evm\x18\x02 \x01(\v2\x1d.api.wallet.v1.EvmTransactionH\x00R\x03evm\x127\n" +
	"\x06solana\x18\x03 \x01(\v2\x1d.api.wallet.v1.SolTransactionH\x00R\x06solanaB\r\n" +
	"\vtransaction\"b\n" +
	"\rRelayResponse\x12\x17\n" +
	"\atx_hash\x18\x01 \x01(\tR\x06txHash\x12\x10\n" +
	"\x03fee\x18\x02 \x01(\tR\x03fee\x12&\n" +
	"\x0fsponsor_tx_hash\x18\x03 \x01(\tR\rsponsorTxHash\"\\\n" +
	"\vGetRelayReq\x12\x1f\n" +
	"\vchain_index\x18\x01 \x01(\x03R\n" +
	"chainIndex\x12\x10\n" +
	"\x03txn\x18\x02 \x01(\tR\x03txn\x12\x1a\n" +
	"\busername\x18\x03 \x01(\tR\busername\"Z\n" +
	"\rGetRelayReply\x12\x1f\n" +
	"\vchain_index\x18\x01 \x01(\x03R\n" +
	"chainIndex\x12\x10\n" +
	"\x03txn\x18\x02 \x01(\tR\x03txn\x12\x16\n" +
	"\x06status\x18\x03 \x01(\x03R\x06status\"\xec\x01\n" +
	"\x17PrepareTransactionReply\x12\x1f\n" +
	"\vchain_index\x18\x01 \x01(\x03R\n" +
	"chainIndex\x12-\n" +
	"\x12serialized_message\x18\x02 \x01(\tR\x11serializedMessage\x12;\n" +
	"\tsolana_tx\x18\x03 \x01(\v2\x1c.api.wallet.v1.SolUnsignedTxH\x00R\bsolanaTx\x125\n" +
	"\x06evm_tx\x18\x04 \x01(\v2\x1c.api.wallet.v1.EvmUnsignedTxH\x00R\x05evmTxB\r\n" +
	"\vunsigned_tx\"\xd4\x01\n" +
	"\rSolUnsignedTx\x12!\n" +
	"\faccount_keys\x18\x03 \x03(\tR\vaccountKeys\x12>\n" +
	"\finstructions\x18\x04 \x03(\v2\x1a.api.wallet.v1.InstructionR\finstructions\x12*\n" +
	"\x11recent_block_hash\x18\x05 \x01(\tR\x0frecentBlockHash\x124\n" +
	"\x06header\x18\x06 \x01(\v2\x1c.api.wallet.v1.MessageHeaderR\x06header\"[\n" +
	"\vInstruction\x12\x1c\n" +
	"\tprogramId\x18\x01 \x01(\tR\tprogramId\x12\x1a\n" +
	"\baccounts\x18\x02 \x03(\x05R\baccounts\x12\x12\n" +
	"\x04data\x18\x03 \x01(\tR\x04data\"\xc5\x01\n" +
	"\rMessageHeader\x124\n" +
	"\x15numRequiredSignatures\x18\x01 \x01(\x05R\x15numRequiredSignatures\x12<\n" +
	"\x19numReadonlySignedAccounts\x18\x02 \x01(\x05R\x19numReadonlySignedAccounts\x12@\n" +
	"\x1bnumReadonlyUnsignedAccounts\x18\x03 \x01(\x05R\x1bnumReadonlyUnsignedAccounts\"\xb4\x03\n" +
	"\rEvmUnsignedTx\x12.\n" +
	"\x04from\x18\x01 \x01(\tB\x1a\xbaH\x17r\x152\x13^0x[0-9a-fA-F]{40}$R\x04from\x12*\n" +
	"\x02to\x18\x02 \x01(\tB\x1a\xbaH\x17r\x152\x13^0x[0-9a-fA-F]{40}$R\x02to\x12\x14\n" +
	"\x05value\x18\x03 \x01(\tR\x05value\x12\x1b\n" +
	"\tgas_price\x18\x04 \x01(\tR\bgasPrice\x12\x1b\n" +
	"\tgas_limit\x18\x05 \x01(\tR\bgasLimit\x12\x14\n" +
	"\x05nonce\x18\x06 \x01(\x04R\x05nonce\x12\x12\n" +
	"\x04data\x18\a \x01(\tR\x04data\x12\x19\n" +
	"\bchain_id\x18\b \x01(\x04R\achainId\x12)\n" +
	"\x10transaction_type\x18\t \x01(\rR\x0ftransactionType\x12%\n" +
	"\x0fmax_fee_per_gas\x18\n" +
	" \x01(\tR\fmaxFeePerGas\x126\n" +
	"\x18max_priority_fee_per_gas\x18\v \x01(\tR\x14maxPriorityFeePerGas\x12\f\n" +
	"\x01v\x18\f \x01(\tR\x01v\x12\f\n" +
	"\x01r\x18\r \x01(\tR\x01r\x12\f\n" +
	"\x01s\x18\x0e \x01(\tR\x01s\"8\n" +
	"\x15DepositAddressRequest\x12\x1f\n" +
	"\vchain_index\x18\x01 \x01(\x03R\n" +
	"chainIndex\"P\n" +
	"\x13DepositAddressReply\x12\x1f\n" +
	"\vchain_index\x18\x01 \x01(\x03R\n" +
	"chainIndex\x12\x18\n" +
	"\aaddress\x18\x02 \x01(\tR\aaddress\"o\n" +
	"\fDepositToken\x12\x1a\n" +
	"\bcontract\x18\x01 \x01(\tR\bcontract\x12\x10\n" +
	"\x03min\x18\x02 \x01(\tR\x03min\x12\x10\n" +
	"\x03max\x18\x03 \x01(\tR\x03max\x12\x1f\n" +
	"\vchain_index\x18\x04 \x01(\x03R\n" +
	"chainIndex\"E\n" +
	"\x12DepositTokensReply\x12/\n" +
	"\x04list\x18\x01 \x03(\v2\x1b.api.wallet.v1.DepositTokenR\x04list*6\n" +
	"\x0fTransactionMode\x12\v\n" +
	"\aUNKNOWN\x10\x00\x12\v\n" +
	"\aDEPOSIT\x10\x01\x12\t\n" +
	"\x05RELAY\x10\x022\xef\x04\n" +
	"\tPaymaster\x12\x87\x01\n" +
	"\x11GetDepositAddress\x12$.api.wallet.v1.DepositAddressRequest\x1a\".api.wallet.v1.DepositAddressReply\"(\x82\xd3\xe4\x93\x02\":\x01*\"\x1d/v1/paymaster/deposit/address\x12v\n" +
	"\x13GetDepositTokenList\x12\x16.google.protobuf.Empty\x1a!.api.wallet.v1.DepositTokensReply\"$\x82\xd3\xe4\x93\x02\x1e\x12\x1c/v1/paymaster/deposit/tokens\x12n\n" +
	"\x05Relay\x12\x1b.api.wallet.v1.RelayRequest\x1a\x1c.api.wallet.v1.RelayResponse\"*\x82\xd3\xe4\x93\x02$:\x01*\"\x1f/v1/paymaster/transaction/relay\x12f\n" +
	"\tGetRelays\x12\x1a.api.wallet.v1.GetRelayReq\x1a\x1c.api.wallet.v1.GetRelayReply\"\x1f\x82\xd3\xe4\x93\x02\x19\x12\x17/v1/paymaster/relayList\x12\x87\x01\n" +
	"\x12PrepareTransaction\x12\x1b.api.wallet.v1.RelayRequest\x1a&.api.wallet.v1.PrepareTransactionReply\",\x82\xd3\xe4\x93\x02&:\x01*\"!/v1/paymaster/transaction/prepareB\x96\x01\n" +
	"\x11com.api.wallet.v1B\x0ePaymasterProtoP\x01Z\x1bbyd_wallet/api/wallet/v1;v1\xa2\x02\x03AWX\xaa\x02\rApi.Wallet.V1\xca\x02\rApi\\Wallet\\V1\xe2\x02\x19Api\\Wallet\\V1\\GPBMetadata\xea\x02\x0fApi::Wallet::V1b\x06proto3"

var (
	file_api_wallet_v1_paymaster_proto_rawDescOnce sync.Once
	file_api_wallet_v1_paymaster_proto_rawDescData []byte
)

func file_api_wallet_v1_paymaster_proto_rawDescGZIP() []byte {
	file_api_wallet_v1_paymaster_proto_rawDescOnce.Do(func() {
		file_api_wallet_v1_paymaster_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_wallet_v1_paymaster_proto_rawDesc), len(file_api_wallet_v1_paymaster_proto_rawDesc)))
	})
	return file_api_wallet_v1_paymaster_proto_rawDescData
}

var file_api_wallet_v1_paymaster_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_wallet_v1_paymaster_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_api_wallet_v1_paymaster_proto_goTypes = []any{
	(TransactionMode)(0),            // 0: api.wallet.v1.TransactionMode
	(*EvmTransaction)(nil),          // 1: api.wallet.v1.EvmTransaction
	(*TokenPermissions)(nil),        // 2: api.wallet.v1.TokenPermissions
	(*PermitTransferFrom)(nil),      // 3: api.wallet.v1.PermitTransferFrom
	(*TransferDetails)(nil),         // 4: api.wallet.v1.TransferDetails
	(*PermitSignatureData)(nil),     // 5: api.wallet.v1.PermitSignatureData
	(*TransferParameters)(nil),      // 6: api.wallet.v1.TransferParameters
	(*SolTransaction)(nil),          // 7: api.wallet.v1.SolTransaction
	(*RelayRequest)(nil),            // 8: api.wallet.v1.RelayRequest
	(*RelayResponse)(nil),           // 9: api.wallet.v1.RelayResponse
	(*GetRelayReq)(nil),             // 10: api.wallet.v1.GetRelayReq
	(*GetRelayReply)(nil),           // 11: api.wallet.v1.GetRelayReply
	(*PrepareTransactionReply)(nil), // 12: api.wallet.v1.PrepareTransactionReply
	(*SolUnsignedTx)(nil),           // 13: api.wallet.v1.SolUnsignedTx
	(*Instruction)(nil),             // 14: api.wallet.v1.Instruction
	(*MessageHeader)(nil),           // 15: api.wallet.v1.MessageHeader
	(*EvmUnsignedTx)(nil),           // 16: api.wallet.v1.EvmUnsignedTx
	(*DepositAddressRequest)(nil),   // 17: api.wallet.v1.DepositAddressRequest
	(*DepositAddressReply)(nil),     // 18: api.wallet.v1.DepositAddressReply
	(*DepositToken)(nil),            // 19: api.wallet.v1.DepositToken
	(*DepositTokensReply)(nil),      // 20: api.wallet.v1.DepositTokensReply
	(*emptypb.Empty)(nil),           // 21: google.protobuf.Empty
}
var file_api_wallet_v1_paymaster_proto_depIdxs = []int32{
	0,  // 0: api.wallet.v1.EvmTransaction.mode:type_name -> api.wallet.v1.TransactionMode
	5,  // 1: api.wallet.v1.EvmTransaction.permit_params:type_name -> api.wallet.v1.PermitSignatureData
	6,  // 2: api.wallet.v1.EvmTransaction.transfer_params:type_name -> api.wallet.v1.TransferParameters
	2,  // 3: api.wallet.v1.PermitTransferFrom.permitted:type_name -> api.wallet.v1.TokenPermissions
	3,  // 4: api.wallet.v1.PermitSignatureData.permit:type_name -> api.wallet.v1.PermitTransferFrom
	4,  // 5: api.wallet.v1.PermitSignatureData.transfer_details:type_name -> api.wallet.v1.TransferDetails
	0,  // 6: api.wallet.v1.SolTransaction.mode:type_name -> api.wallet.v1.TransactionMode
	1,  // 7: api.wallet.v1.RelayRequest.evm:type_name -> api.wallet.v1.EvmTransaction
	7,  // 8: api.wallet.v1.RelayRequest.solana:type_name -> api.wallet.v1.SolTransaction
	13, // 9: api.wallet.v1.PrepareTransactionReply.solana_tx:type_name -> api.wallet.v1.SolUnsignedTx
	16, // 10: api.wallet.v1.PrepareTransactionReply.evm_tx:type_name -> api.wallet.v1.EvmUnsignedTx
	14, // 11: api.wallet.v1.SolUnsignedTx.instructions:type_name -> api.wallet.v1.Instruction
	15, // 12: api.wallet.v1.SolUnsignedTx.header:type_name -> api.wallet.v1.MessageHeader
	19, // 13: api.wallet.v1.DepositTokensReply.list:type_name -> api.wallet.v1.DepositToken
	17, // 14: api.wallet.v1.Paymaster.GetDepositAddress:input_type -> api.wallet.v1.DepositAddressRequest
	21, // 15: api.wallet.v1.Paymaster.GetDepositTokenList:input_type -> google.protobuf.Empty
	8,  // 16: api.wallet.v1.Paymaster.Relay:input_type -> api.wallet.v1.RelayRequest
	10, // 17: api.wallet.v1.Paymaster.GetRelays:input_type -> api.wallet.v1.GetRelayReq
	8,  // 18: api.wallet.v1.Paymaster.PrepareTransaction:input_type -> api.wallet.v1.RelayRequest
	18, // 19: api.wallet.v1.Paymaster.GetDepositAddress:output_type -> api.wallet.v1.DepositAddressReply
	20, // 20: api.wallet.v1.Paymaster.GetDepositTokenList:output_type -> api.wallet.v1.DepositTokensReply
	9,  // 21: api.wallet.v1.Paymaster.Relay:output_type -> api.wallet.v1.RelayResponse
	11, // 22: api.wallet.v1.Paymaster.GetRelays:output_type -> api.wallet.v1.GetRelayReply
	12, // 23: api.wallet.v1.Paymaster.PrepareTransaction:output_type -> api.wallet.v1.PrepareTransactionReply
	19, // [19:24] is the sub-list for method output_type
	14, // [14:19] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_api_wallet_v1_paymaster_proto_init() }
func file_api_wallet_v1_paymaster_proto_init() {
	if File_api_wallet_v1_paymaster_proto != nil {
		return
	}
	file_api_wallet_v1_paymaster_proto_msgTypes[0].OneofWrappers = []any{}
	file_api_wallet_v1_paymaster_proto_msgTypes[7].OneofWrappers = []any{
		(*RelayRequest_Evm)(nil),
		(*RelayRequest_Solana)(nil),
	}
	file_api_wallet_v1_paymaster_proto_msgTypes[11].OneofWrappers = []any{
		(*PrepareTransactionReply_SolanaTx)(nil),
		(*PrepareTransactionReply_EvmTx)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_wallet_v1_paymaster_proto_rawDesc), len(file_api_wallet_v1_paymaster_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_wallet_v1_paymaster_proto_goTypes,
		DependencyIndexes: file_api_wallet_v1_paymaster_proto_depIdxs,
		EnumInfos:         file_api_wallet_v1_paymaster_proto_enumTypes,
		MessageInfos:      file_api_wallet_v1_paymaster_proto_msgTypes,
	}.Build()
	File_api_wallet_v1_paymaster_proto = out.File
	file_api_wallet_v1_paymaster_proto_goTypes = nil
	file_api_wallet_v1_paymaster_proto_depIdxs = nil
}
