// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.0
// - protoc             (unknown)
// source: api/wallet/v1/wallet.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationWalletSrvGetToken = "/api.wallet.v1.WalletSrv/GetToken"
const OperationWalletSrvListTokenBalance = "/api.wallet.v1.WalletSrv/ListTokenBalance"
const OperationWalletSrvNetworkList = "/api.wallet.v1.WalletSrv/NetworkList"
const OperationWalletSrvQueryTxLatestToken = "/api.wallet.v1.WalletSrv/QueryTxLatestToken"
const OperationWalletSrvReportInternalTxn = "/api.wallet.v1.WalletSrv/ReportInternalTxn"
const OperationWalletSrvTokenList = "/api.wallet.v1.WalletSrv/TokenList"
const OperationWalletSrvTransactionInfo = "/api.wallet.v1.WalletSrv/TransactionInfo"
const OperationWalletSrvTransactions = "/api.wallet.v1.WalletSrv/Transactions"
const OperationWalletSrvTransactionsByAddress = "/api.wallet.v1.WalletSrv/TransactionsByAddress"

type WalletSrvHTTPServer interface {
	// GetToken 获取指定token
	GetToken(context.Context, *GetTokenReq) (*Token, error)
	// ListTokenBalance 获取地址的资产明细
	ListTokenBalance(context.Context, *ListTokenBalanceReq) (*ListTokenBalanceReply, error)
	// NetworkList 链列表
	NetworkList(context.Context, *NetworkListReq) (*NetworkListReply, error)
	// QueryTxLatestToken 获取地址接受转账的最新币种信息
	QueryTxLatestToken(context.Context, *QueryTxLatestTokenReq) (*QueryTxLatestTokenReply, error)
	// ReportInternalTxn 上报并更新一笔内部交易
	ReportInternalTxn(context.Context, *ReportInternalTxnReq) (*emptypb.Empty, error)
	// TokenList 获取查询token
	TokenList(context.Context, *TokenListReq) (*TokenListReply, error)
	// TransactionInfo 获取交易详情信息
	TransactionInfo(context.Context, *TransactionReq) (*Transaction, error)
	// Transactions 获取交易信息
	Transactions(context.Context, *TransactionsReq) (*TransactionsReply, error)
	// TransactionsByAddress 获取交易信息
	TransactionsByAddress(context.Context, *TransactionsByAddressReq) (*TransactionsReply, error)
}

func RegisterWalletSrvHTTPServer(s *http.Server, srv WalletSrvHTTPServer) {
	r := s.Route("/")
	r.GET("/v1/networkList", _WalletSrv_NetworkList0_HTTP_Handler(srv))
	r.GET("/v1/transactionList", _WalletSrv_Transactions0_HTTP_Handler(srv))
	r.GET("/v1/transactionsByAddress", _WalletSrv_TransactionsByAddress0_HTTP_Handler(srv))
	r.GET("/v1/transactionInfo", _WalletSrv_TransactionInfo0_HTTP_Handler(srv))
	r.GET("/v1/tokenList", _WalletSrv_TokenList0_HTTP_Handler(srv))
	r.GET("/v1/getToken", _WalletSrv_GetToken0_HTTP_Handler(srv))
	r.POST("/v1/listTokenBalance", _WalletSrv_ListTokenBalance0_HTTP_Handler(srv))
	r.POST("/v1/queryTxLatestToken", _WalletSrv_QueryTxLatestToken0_HTTP_Handler(srv))
	r.POST("/v1/reportInternalTxn", _WalletSrv_ReportInternalTxn0_HTTP_Handler(srv))
}

func _WalletSrv_NetworkList0_HTTP_Handler(srv WalletSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in NetworkListReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWalletSrvNetworkList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.NetworkList(ctx, req.(*NetworkListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*NetworkListReply)
		return ctx.Result(200, reply)
	}
}

func _WalletSrv_Transactions0_HTTP_Handler(srv WalletSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in TransactionsReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWalletSrvTransactions)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Transactions(ctx, req.(*TransactionsReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*TransactionsReply)
		return ctx.Result(200, reply)
	}
}

func _WalletSrv_TransactionsByAddress0_HTTP_Handler(srv WalletSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in TransactionsByAddressReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWalletSrvTransactionsByAddress)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.TransactionsByAddress(ctx, req.(*TransactionsByAddressReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*TransactionsReply)
		return ctx.Result(200, reply)
	}
}

func _WalletSrv_TransactionInfo0_HTTP_Handler(srv WalletSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in TransactionReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWalletSrvTransactionInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.TransactionInfo(ctx, req.(*TransactionReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Transaction)
		return ctx.Result(200, reply)
	}
}

func _WalletSrv_TokenList0_HTTP_Handler(srv WalletSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in TokenListReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWalletSrvTokenList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.TokenList(ctx, req.(*TokenListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*TokenListReply)
		return ctx.Result(200, reply)
	}
}

func _WalletSrv_GetToken0_HTTP_Handler(srv WalletSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetTokenReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWalletSrvGetToken)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetToken(ctx, req.(*GetTokenReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Token)
		return ctx.Result(200, reply)
	}
}

func _WalletSrv_ListTokenBalance0_HTTP_Handler(srv WalletSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListTokenBalanceReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWalletSrvListTokenBalance)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListTokenBalance(ctx, req.(*ListTokenBalanceReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListTokenBalanceReply)
		return ctx.Result(200, reply)
	}
}

func _WalletSrv_QueryTxLatestToken0_HTTP_Handler(srv WalletSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QueryTxLatestTokenReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWalletSrvQueryTxLatestToken)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QueryTxLatestToken(ctx, req.(*QueryTxLatestTokenReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QueryTxLatestTokenReply)
		return ctx.Result(200, reply)
	}
}

func _WalletSrv_ReportInternalTxn0_HTTP_Handler(srv WalletSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ReportInternalTxnReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWalletSrvReportInternalTxn)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ReportInternalTxn(ctx, req.(*ReportInternalTxnReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type WalletSrvHTTPClient interface {
	GetToken(ctx context.Context, req *GetTokenReq, opts ...http.CallOption) (rsp *Token, err error)
	ListTokenBalance(ctx context.Context, req *ListTokenBalanceReq, opts ...http.CallOption) (rsp *ListTokenBalanceReply, err error)
	NetworkList(ctx context.Context, req *NetworkListReq, opts ...http.CallOption) (rsp *NetworkListReply, err error)
	QueryTxLatestToken(ctx context.Context, req *QueryTxLatestTokenReq, opts ...http.CallOption) (rsp *QueryTxLatestTokenReply, err error)
	ReportInternalTxn(ctx context.Context, req *ReportInternalTxnReq, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	TokenList(ctx context.Context, req *TokenListReq, opts ...http.CallOption) (rsp *TokenListReply, err error)
	TransactionInfo(ctx context.Context, req *TransactionReq, opts ...http.CallOption) (rsp *Transaction, err error)
	Transactions(ctx context.Context, req *TransactionsReq, opts ...http.CallOption) (rsp *TransactionsReply, err error)
	TransactionsByAddress(ctx context.Context, req *TransactionsByAddressReq, opts ...http.CallOption) (rsp *TransactionsReply, err error)
}

type WalletSrvHTTPClientImpl struct {
	cc *http.Client
}

func NewWalletSrvHTTPClient(client *http.Client) WalletSrvHTTPClient {
	return &WalletSrvHTTPClientImpl{client}
}

func (c *WalletSrvHTTPClientImpl) GetToken(ctx context.Context, in *GetTokenReq, opts ...http.CallOption) (*Token, error) {
	var out Token
	pattern := "/v1/getToken"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWalletSrvGetToken))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WalletSrvHTTPClientImpl) ListTokenBalance(ctx context.Context, in *ListTokenBalanceReq, opts ...http.CallOption) (*ListTokenBalanceReply, error) {
	var out ListTokenBalanceReply
	pattern := "/v1/listTokenBalance"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWalletSrvListTokenBalance))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WalletSrvHTTPClientImpl) NetworkList(ctx context.Context, in *NetworkListReq, opts ...http.CallOption) (*NetworkListReply, error) {
	var out NetworkListReply
	pattern := "/v1/networkList"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWalletSrvNetworkList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WalletSrvHTTPClientImpl) QueryTxLatestToken(ctx context.Context, in *QueryTxLatestTokenReq, opts ...http.CallOption) (*QueryTxLatestTokenReply, error) {
	var out QueryTxLatestTokenReply
	pattern := "/v1/queryTxLatestToken"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWalletSrvQueryTxLatestToken))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WalletSrvHTTPClientImpl) ReportInternalTxn(ctx context.Context, in *ReportInternalTxnReq, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/reportInternalTxn"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWalletSrvReportInternalTxn))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WalletSrvHTTPClientImpl) TokenList(ctx context.Context, in *TokenListReq, opts ...http.CallOption) (*TokenListReply, error) {
	var out TokenListReply
	pattern := "/v1/tokenList"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWalletSrvTokenList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WalletSrvHTTPClientImpl) TransactionInfo(ctx context.Context, in *TransactionReq, opts ...http.CallOption) (*Transaction, error) {
	var out Transaction
	pattern := "/v1/transactionInfo"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWalletSrvTransactionInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WalletSrvHTTPClientImpl) Transactions(ctx context.Context, in *TransactionsReq, opts ...http.CallOption) (*TransactionsReply, error) {
	var out TransactionsReply
	pattern := "/v1/transactionList"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWalletSrvTransactions))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WalletSrvHTTPClientImpl) TransactionsByAddress(ctx context.Context, in *TransactionsByAddressReq, opts ...http.CallOption) (*TransactionsReply, error) {
	var out TransactionsReply
	pattern := "/v1/transactionsByAddress"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWalletSrvTransactionsByAddress))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
