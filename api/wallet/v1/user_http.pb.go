// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.0
// - protoc             (unknown)
// source: api/wallet/v1/user.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationUserSrvGetWalletIds = "/api.wallet.v1.UserSrv/GetWalletIds"
const OperationUserSrvUpdateWalletID = "/api.wallet.v1.UserSrv/UpdateWalletID"
const OperationUserSrvUserRegister = "/api.wallet.v1.UserSrv/UserRegister"

type UserSrvHTTPServer interface {
	// GetWalletIds 获取wallet Id
	GetWalletIds(context.Context, *GetWalletIdReq) (*GeWalletIdsReply, error)
	// UpdateWalletID 更新wallet Id
	UpdateWalletID(context.Context, *UpdateWalletIDReq) (*UpdateWalletIDReply, error)
	// UserRegister 生成wallet Id
	UserRegister(context.Context, *UserRegisterReq) (*UserRegisterReply, error)
}

func RegisterUserSrvHTTPServer(s *http.Server, srv UserSrvHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/wallet/user_register", _UserSrv_UserRegister0_HTTP_Handler(srv))
	r.POST("/v1/wallet/update_wallet_id", _UserSrv_UpdateWalletID0_HTTP_Handler(srv))
	r.GET("/v1/wallet/ids", _UserSrv_GetWalletIds0_HTTP_Handler(srv))
}

func _UserSrv_UserRegister0_HTTP_Handler(srv UserSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UserRegisterReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserSrvUserRegister)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserRegister(ctx, req.(*UserRegisterReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserRegisterReply)
		return ctx.Result(200, reply)
	}
}

func _UserSrv_UpdateWalletID0_HTTP_Handler(srv UserSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateWalletIDReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserSrvUpdateWalletID)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateWalletID(ctx, req.(*UpdateWalletIDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateWalletIDReply)
		return ctx.Result(200, reply)
	}
}

func _UserSrv_GetWalletIds0_HTTP_Handler(srv UserSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetWalletIdReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserSrvGetWalletIds)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetWalletIds(ctx, req.(*GetWalletIdReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GeWalletIdsReply)
		return ctx.Result(200, reply)
	}
}

type UserSrvHTTPClient interface {
	GetWalletIds(ctx context.Context, req *GetWalletIdReq, opts ...http.CallOption) (rsp *GeWalletIdsReply, err error)
	UpdateWalletID(ctx context.Context, req *UpdateWalletIDReq, opts ...http.CallOption) (rsp *UpdateWalletIDReply, err error)
	UserRegister(ctx context.Context, req *UserRegisterReq, opts ...http.CallOption) (rsp *UserRegisterReply, err error)
}

type UserSrvHTTPClientImpl struct {
	cc *http.Client
}

func NewUserSrvHTTPClient(client *http.Client) UserSrvHTTPClient {
	return &UserSrvHTTPClientImpl{client}
}

func (c *UserSrvHTTPClientImpl) GetWalletIds(ctx context.Context, in *GetWalletIdReq, opts ...http.CallOption) (*GeWalletIdsReply, error) {
	var out GeWalletIdsReply
	pattern := "/v1/wallet/ids"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserSrvGetWalletIds))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserSrvHTTPClientImpl) UpdateWalletID(ctx context.Context, in *UpdateWalletIDReq, opts ...http.CallOption) (*UpdateWalletIDReply, error) {
	var out UpdateWalletIDReply
	pattern := "/v1/wallet/update_wallet_id"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserSrvUpdateWalletID))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserSrvHTTPClientImpl) UserRegister(ctx context.Context, in *UserRegisterReq, opts ...http.CallOption) (*UserRegisterReply, error) {
	var out UserRegisterReply
	pattern := "/v1/wallet/user_register"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserSrvUserRegister))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
