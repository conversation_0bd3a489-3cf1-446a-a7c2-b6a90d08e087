syntax = "proto3";

package api.wallet.v1;

import "buf/validate/validate.proto";
import "google/api/annotations.proto";
import "api/wallet/v1/wallet.proto";

option go_package = "byd_wallet/api/wallet/v1;v1";

service DappSrv {
  // ListNavigation 获取dapp导航栏配置
  rpc ListNavigation(ListNavigationReq) returns (ListNavigationReply) {
    option (google.api.http) = {get: "/v1/dapp/navigation"};
  }
  // ListDappIndex 获取dapp首页列表
  rpc ListDappIndex(ListDappIndexReq) returns (ListDappIndexReply) {
    option (google.api.http) = {get: "/v1/dapp/index"};
  }
  // GetDappTopic 获取专题详情
  rpc GetDappTopic(GetDappTopicReq) returns (GetDappTopicReply) {
    option (google.api.http) = {get: "/v1/dapp/topic/{id}"};
  }
  // GetDappCategory 获取分类详情
  rpc GetDappCategory(GetDappCategoryReq) returns (GetDappCategoryReply) {
    option (google.api.http) = {get: "/v1/dapp/category/{id}"};
  }
  // SearchDapp 搜索dapp
  rpc SearchDapp(SearchDappReq) returns (SearchDappReply) {
    option (google.api.http) = {get: "/v1/dapp"};
  }
  // ListApprovedAddresses 根据用户地址筛选出已授权的地址
  rpc ListApprovedAddresses(ListApprovedAddressesReq) returns (ListApprovedAddressesReply) {
    option (google.api.http) = {post: "/v1/dapp/approved_addresses", body: "*"};
  }
  // ListApprovalByUserAddress 查询单个用户钱包地址授权记录
  rpc ListApprovalByUserAddress(ListApprovalByUserAddressReq) returns (ListApprovalByUserAddressReply) {
    option (google.api.http) = {post: "/v1/dapp/address/approval", body: "*"};
  }
  // ListApprovedDappsByUserAddresses 根据用户地址查询已授权的dapp
  rpc ListApprovedDappsByUserAddresses(ListApprovalByUserAddressesReq) returns (ListApprovedDappsByUserAddressesReply) {
    option (google.api.http) = {post: "/v1/dapp/addresses/approval", body: "*"};
  }
  // ListTokenApprovalsByDapp 根据dapp应用查询授权信息
  rpc ListTokenApprovalsByDapp(ListTokenApprovalsByDappReq) returns (ListTokenApprovalsByDappReply) {
    option (google.api.http) = {post: "/v1/dapp/blockchain/approval", body: "*"};
  }
}

message ListApprovedAddressesReq {
  repeated UserAddress addresses = 1;
}

message ListApprovedAddressesReply {
  repeated UserAddress list = 1;
}

message ListTokenApprovalsByDappReq {
  string spender_address = 1 [(buf.validate.field).string.min_len = 1];
  int64 chain_index = 2;
  // 用户钱包地址
  repeated string addresses = 3;
}

message ListTokenApprovalsByDappReply {
  repeated TokenApproval list = 1;
}

message ListApprovedDappsByUserAddressesReply {
  repeated UserApprovedDapp list = 1;
}

message ListApprovalByUserAddressesReq {
  repeated UserAddress addresses = 1;
}

message UserAddress {
  string address = 1;
  int64 chain_index = 2;
}
message ListApprovalByUserAddressReq {
  // 用户钱包地址
  string address = 1 [(buf.validate.field).string.min_len = 1];
  int64 chain_index = 2;
}

message ListApprovalByUserAddressReply {
  UserAddress address = 1;
  BlockchainNetwork network = 2;
  repeated TokenDappApproval list = 3;
}

message SearchDappReq {
  // 搜索关键词
  string key = 1;
}

message SearchDappReply {
  repeated Dapp list = 1;
}

message GetDappCategoryReq {
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
  int64 chain_index = 2;
  string chain_id = 3;
}

message GetDappCategoryReply {
  repeated Dapp list = 1;
}

message GetDappTopicReq {
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
}

message GetDappTopicReply {
  DappTopic topic = 1;
}

message ListDappIndexReq {}

message Dapp {
  uint64 id = 1;
  string logo = 2;
  string link = 3;
  string tags = 4;
  bool hot = 5;
  repeated DappI18N i18ns = 6;
  repeated BlockchainNetwork networks = 7;
}

message DappI18N {
  string name = 1;
  string summary = 2;
}

message BlockchainNetwork {
  string chain_id = 1;
}

message DappCategory {
  uint64 id = 1;
  string name = 2;
  string summary = 3;
  string language = 4;
  repeated Dapp dapps = 5;
}

message DappTopic {
  uint64 id = 1;
  string background_url = 2;
  string name = 3;
  string summary = 4;
  string language = 5;
  string title = 6;
  string top_title = 7;
  string bottom_title = 8;
  repeated Dapp dapps = 9;
}

message DappIndex {
  DappTopic topic = 1;
  DappCategory category = 2;
}

message ListDappIndexReply {
  repeated DappIndex list = 1;
}

message ListNavigationReq {}

message ListNavigationReply {
  repeated DappNavigation list = 1;
}

message DappNavigation {
  uint64 dapp_category_id = 1;
  string name = 2;
}

message Approval {
  int64 chain_index = 1;
  string owner_address = 2;
  string spender_address = 3;
  string token_address = 4;
  string value = 5;
}

message TokenDappApproval {
  Token token = 1;
  Dapp dapp = 2;
  Approval approval = 3;
}

message UserApprovedDapp {
  repeated string addresses = 1;
  Dapp dapp = 2;
  BlockchainNetwork network = 3;
  string spender = 4;
}

message TokenApproval {
  Token token = 1;
  Approval approval = 2;
}
