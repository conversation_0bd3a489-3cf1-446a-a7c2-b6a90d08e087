// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: api/wallet/v1/rent.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	TronSrv_AddTronRentRecord_FullMethodName = "/api.wallet.v1.TronSrv/AddTronRentRecord"
	TronSrv_QueryPreorderInfo_FullMethodName = "/api.wallet.v1.TronSrv/QueryPreorderInfo"
	TronSrv_UploadHash_FullMethodName        = "/api.wallet.v1.TronSrv/UploadHash"
)

// TronSrvClient is the client API for TronSrv service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TronSrvClient interface {
	// AddTronRentRecord 创建能量买单
	AddTronRentRecord(ctx context.Context, in *AddTronRentRecordReq, opts ...grpc.CallOption) (*AddTronRentRecordReply, error)
	// QueryPreorderInfo 查询预订单信息，用于预估租赁费用
	QueryPreorderInfo(ctx context.Context, in *QueryPreorderInfoReq, opts ...grpc.CallOption) (*QueryPreorderInfoReply, error)
	// UploadHash 上传买单哈希
	UploadHash(ctx context.Context, in *UploadHashReq, opts ...grpc.CallOption) (*UploadHashReply, error)
}

type tronSrvClient struct {
	cc grpc.ClientConnInterface
}

func NewTronSrvClient(cc grpc.ClientConnInterface) TronSrvClient {
	return &tronSrvClient{cc}
}

func (c *tronSrvClient) AddTronRentRecord(ctx context.Context, in *AddTronRentRecordReq, opts ...grpc.CallOption) (*AddTronRentRecordReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddTronRentRecordReply)
	err := c.cc.Invoke(ctx, TronSrv_AddTronRentRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tronSrvClient) QueryPreorderInfo(ctx context.Context, in *QueryPreorderInfoReq, opts ...grpc.CallOption) (*QueryPreorderInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryPreorderInfoReply)
	err := c.cc.Invoke(ctx, TronSrv_QueryPreorderInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tronSrvClient) UploadHash(ctx context.Context, in *UploadHashReq, opts ...grpc.CallOption) (*UploadHashReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UploadHashReply)
	err := c.cc.Invoke(ctx, TronSrv_UploadHash_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TronSrvServer is the server API for TronSrv service.
// All implementations must embed UnimplementedTronSrvServer
// for forward compatibility.
type TronSrvServer interface {
	// AddTronRentRecord 创建能量买单
	AddTronRentRecord(context.Context, *AddTronRentRecordReq) (*AddTronRentRecordReply, error)
	// QueryPreorderInfo 查询预订单信息，用于预估租赁费用
	QueryPreorderInfo(context.Context, *QueryPreorderInfoReq) (*QueryPreorderInfoReply, error)
	// UploadHash 上传买单哈希
	UploadHash(context.Context, *UploadHashReq) (*UploadHashReply, error)
	mustEmbedUnimplementedTronSrvServer()
}

// UnimplementedTronSrvServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTronSrvServer struct{}

func (UnimplementedTronSrvServer) AddTronRentRecord(context.Context, *AddTronRentRecordReq) (*AddTronRentRecordReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddTronRentRecord not implemented")
}
func (UnimplementedTronSrvServer) QueryPreorderInfo(context.Context, *QueryPreorderInfoReq) (*QueryPreorderInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPreorderInfo not implemented")
}
func (UnimplementedTronSrvServer) UploadHash(context.Context, *UploadHashReq) (*UploadHashReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadHash not implemented")
}
func (UnimplementedTronSrvServer) mustEmbedUnimplementedTronSrvServer() {}
func (UnimplementedTronSrvServer) testEmbeddedByValue()                 {}

// UnsafeTronSrvServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TronSrvServer will
// result in compilation errors.
type UnsafeTronSrvServer interface {
	mustEmbedUnimplementedTronSrvServer()
}

func RegisterTronSrvServer(s grpc.ServiceRegistrar, srv TronSrvServer) {
	// If the following call pancis, it indicates UnimplementedTronSrvServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&TronSrv_ServiceDesc, srv)
}

func _TronSrv_AddTronRentRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTronRentRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TronSrvServer).AddTronRentRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TronSrv_AddTronRentRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TronSrvServer).AddTronRentRecord(ctx, req.(*AddTronRentRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TronSrv_QueryPreorderInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryPreorderInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TronSrvServer).QueryPreorderInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TronSrv_QueryPreorderInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TronSrvServer).QueryPreorderInfo(ctx, req.(*QueryPreorderInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TronSrv_UploadHash_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadHashReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TronSrvServer).UploadHash(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TronSrv_UploadHash_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TronSrvServer).UploadHash(ctx, req.(*UploadHashReq))
	}
	return interceptor(ctx, in, info, handler)
}

// TronSrv_ServiceDesc is the grpc.ServiceDesc for TronSrv service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TronSrv_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.wallet.v1.TronSrv",
	HandlerType: (*TronSrvServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddTronRentRecord",
			Handler:    _TronSrv_AddTronRentRecord_Handler,
		},
		{
			MethodName: "QueryPreorderInfo",
			Handler:    _TronSrv_QueryPreorderInfo_Handler,
		},
		{
			MethodName: "UploadHash",
			Handler:    _TronSrv_UploadHash_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/wallet/v1/rent.proto",
}
