// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: api/wallet/v1/market.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TokenWithMarket struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 链索引
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 合约地址
	Address string `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	// 币名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 币符号
	Symbol string `protobuf:"bytes,4,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// 币精度位数
	Decimals int64 `protobuf:"varint,5,opt,name=decimals,proto3" json:"decimals,omitempty"`
	// 流动性
	CirculatingSupply string `protobuf:"bytes,6,opt,name=circulating_supply,json=circulatingSupply,proto3" json:"circulating_supply,omitempty"`
	// 24小时成交额
	TradingVolume_24H string `protobuf:"bytes,7,opt,name=trading_volume_24h,json=tradingVolume24h,proto3" json:"trading_volume_24h,omitempty"`
	// 24小时涨跌幅（百分比）
	PriceChangePercentage_24H string `protobuf:"bytes,8,opt,name=price_change_percentage_24h,json=priceChangePercentage24h,proto3" json:"price_change_percentage_24h,omitempty"`
	// 币图标
	LogoUrl string `protobuf:"bytes,9,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	// 币价格(usd)
	Price string `protobuf:"bytes,10,opt,name=price,proto3" json:"price,omitempty"`
}

func (x *TokenWithMarket) Reset() {
	*x = TokenWithMarket{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_market_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TokenWithMarket) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenWithMarket) ProtoMessage() {}

func (x *TokenWithMarket) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_market_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenWithMarket.ProtoReflect.Descriptor instead.
func (*TokenWithMarket) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_market_proto_rawDescGZIP(), []int{0}
}

func (x *TokenWithMarket) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *TokenWithMarket) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *TokenWithMarket) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TokenWithMarket) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *TokenWithMarket) GetDecimals() int64 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *TokenWithMarket) GetCirculatingSupply() string {
	if x != nil {
		return x.CirculatingSupply
	}
	return ""
}

func (x *TokenWithMarket) GetTradingVolume_24H() string {
	if x != nil {
		return x.TradingVolume_24H
	}
	return ""
}

func (x *TokenWithMarket) GetPriceChangePercentage_24H() string {
	if x != nil {
		return x.PriceChangePercentage_24H
	}
	return ""
}

func (x *TokenWithMarket) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *TokenWithMarket) GetPrice() string {
	if x != nil {
		return x.Price
	}
	return ""
}

type SearchTokenReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*TokenWithMarket `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *SearchTokenReply) Reset() {
	*x = SearchTokenReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_market_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchTokenReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchTokenReply) ProtoMessage() {}

func (x *SearchTokenReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_market_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchTokenReply.ProtoReflect.Descriptor instead.
func (*SearchTokenReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_market_proto_rawDescGZIP(), []int{1}
}

func (x *SearchTokenReply) GetList() []*TokenWithMarket {
	if x != nil {
		return x.List
	}
	return nil
}

type ListPopularTokenReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 页码
	Page int64 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	// 页容量
	Limit int64 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	// 关键字(搜索范围 代币符号 和 合约地址)
	Keyword *string `protobuf:"bytes,3,opt,name=keyword,proto3,oneof" json:"keyword,omitempty"`
	// 链索引(0,60,...支持查询多个链,查询全部就传空或不传)
	ChainIndexes string `protobuf:"bytes,4,opt,name=chain_indexes,json=chainIndexes,proto3" json:"chain_indexes,omitempty"`
}

func (x *ListPopularTokenReq) Reset() {
	*x = ListPopularTokenReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_market_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPopularTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPopularTokenReq) ProtoMessage() {}

func (x *ListPopularTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_market_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPopularTokenReq.ProtoReflect.Descriptor instead.
func (*ListPopularTokenReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_market_proto_rawDescGZIP(), []int{2}
}

func (x *ListPopularTokenReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListPopularTokenReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ListPopularTokenReq) GetKeyword() string {
	if x != nil && x.Keyword != nil {
		return *x.Keyword
	}
	return ""
}

func (x *ListPopularTokenReq) GetChainIndexes() string {
	if x != nil {
		return x.ChainIndexes
	}
	return ""
}

type ListPopularTokenReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*TokenWithMarket `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 总记录数
	TotalCount int64 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *ListPopularTokenReply) Reset() {
	*x = ListPopularTokenReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_market_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPopularTokenReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPopularTokenReply) ProtoMessage() {}

func (x *ListPopularTokenReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_market_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPopularTokenReply.ProtoReflect.Descriptor instead.
func (*ListPopularTokenReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_market_proto_rawDescGZIP(), []int{3}
}

func (x *ListPopularTokenReply) GetList() []*TokenWithMarket {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListPopularTokenReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type QuerySimpleKlineReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 查询指定的token
	TokenIds []*TokenID `protobuf:"bytes,1,rep,name=token_ids,json=tokenIds,proto3" json:"token_ids,omitempty"`
}

func (x *QuerySimpleKlineReq) Reset() {
	*x = QuerySimpleKlineReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_market_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuerySimpleKlineReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuerySimpleKlineReq) ProtoMessage() {}

func (x *QuerySimpleKlineReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_market_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuerySimpleKlineReq.ProtoReflect.Descriptor instead.
func (*QuerySimpleKlineReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_market_proto_rawDescGZIP(), []int{4}
}

func (x *QuerySimpleKlineReq) GetTokenIds() []*TokenID {
	if x != nil {
		return x.TokenIds
	}
	return nil
}

type SimpleKline struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 链索引
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 合约地址
	Address string `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	// k线时间（时间戳，单位秒） // 升序
	Times []int64 `protobuf:"varint,3,rep,packed,name=times,proto3" json:"times,omitempty"`
	// 收盘价(USD) // 按时间升序
	ClosePrices []string `protobuf:"bytes,4,rep,name=close_prices,json=closePrices,proto3" json:"close_prices,omitempty"`
}

func (x *SimpleKline) Reset() {
	*x = SimpleKline{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_market_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SimpleKline) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimpleKline) ProtoMessage() {}

func (x *SimpleKline) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_market_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimpleKline.ProtoReflect.Descriptor instead.
func (*SimpleKline) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_market_proto_rawDescGZIP(), []int{5}
}

func (x *SimpleKline) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *SimpleKline) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *SimpleKline) GetTimes() []int64 {
	if x != nil {
		return x.Times
	}
	return nil
}

func (x *SimpleKline) GetClosePrices() []string {
	if x != nil {
		return x.ClosePrices
	}
	return nil
}

type QuerySimpleKlineReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*SimpleKline `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *QuerySimpleKlineReply) Reset() {
	*x = QuerySimpleKlineReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_market_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuerySimpleKlineReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuerySimpleKlineReply) ProtoMessage() {}

func (x *QuerySimpleKlineReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_market_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuerySimpleKlineReply.ProtoReflect.Descriptor instead.
func (*QuerySimpleKlineReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_market_proto_rawDescGZIP(), []int{6}
}

func (x *QuerySimpleKlineReply) GetList() []*SimpleKline {
	if x != nil {
		return x.List
	}
	return nil
}

type QueryCurrencyUSDRateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 法币 // cny,jpy,...
	Currencies string `protobuf:"bytes,1,opt,name=currencies,proto3" json:"currencies,omitempty"`
}

func (x *QueryCurrencyUSDRateReq) Reset() {
	*x = QueryCurrencyUSDRateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_market_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryCurrencyUSDRateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryCurrencyUSDRateReq) ProtoMessage() {}

func (x *QueryCurrencyUSDRateReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_market_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryCurrencyUSDRateReq.ProtoReflect.Descriptor instead.
func (*QueryCurrencyUSDRateReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_market_proto_rawDescGZIP(), []int{7}
}

func (x *QueryCurrencyUSDRateReq) GetCurrencies() string {
	if x != nil {
		return x.Currencies
	}
	return ""
}

type CurrencyUSDRate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// key:法币(cny,jpy等小写) value:汇率
	Value map[string]string `protobuf:"bytes,1,rep,name=value,proto3" json:"value,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *CurrencyUSDRate) Reset() {
	*x = CurrencyUSDRate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_market_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CurrencyUSDRate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CurrencyUSDRate) ProtoMessage() {}

func (x *CurrencyUSDRate) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_market_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CurrencyUSDRate.ProtoReflect.Descriptor instead.
func (*CurrencyUSDRate) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_market_proto_rawDescGZIP(), []int{8}
}

func (x *CurrencyUSDRate) GetValue() map[string]string {
	if x != nil {
		return x.Value
	}
	return nil
}

type TokenID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 链索引
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 合约地址
	Address string `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
}

func (x *TokenID) Reset() {
	*x = TokenID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_market_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TokenID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenID) ProtoMessage() {}

func (x *TokenID) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_market_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenID.ProtoReflect.Descriptor instead.
func (*TokenID) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_market_proto_rawDescGZIP(), []int{9}
}

func (x *TokenID) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *TokenID) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type QueryCoinPriceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 查询指定的token
	TokenIds []*TokenID `protobuf:"bytes,1,rep,name=token_ids,json=tokenIds,proto3" json:"token_ids,omitempty"`
}

func (x *QueryCoinPriceReq) Reset() {
	*x = QueryCoinPriceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_market_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryCoinPriceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryCoinPriceReq) ProtoMessage() {}

func (x *QueryCoinPriceReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_market_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryCoinPriceReq.ProtoReflect.Descriptor instead.
func (*QueryCoinPriceReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_market_proto_rawDescGZIP(), []int{10}
}

func (x *QueryCoinPriceReq) GetTokenIds() []*TokenID {
	if x != nil {
		return x.TokenIds
	}
	return nil
}

type CoinPrice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 链索引
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 合约地址
	Address string `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	// 价格(USD)
	Price string `protobuf:"bytes,3,opt,name=price,proto3" json:"price,omitempty"`
	// 更新时间（时间戳，单位秒）
	LastUpdatedAt int64 `protobuf:"varint,4,opt,name=last_updated_at,json=lastUpdatedAt,proto3" json:"last_updated_at,omitempty"`
}

func (x *CoinPrice) Reset() {
	*x = CoinPrice{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_market_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CoinPrice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoinPrice) ProtoMessage() {}

func (x *CoinPrice) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_market_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoinPrice.ProtoReflect.Descriptor instead.
func (*CoinPrice) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_market_proto_rawDescGZIP(), []int{11}
}

func (x *CoinPrice) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *CoinPrice) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *CoinPrice) GetPrice() string {
	if x != nil {
		return x.Price
	}
	return ""
}

func (x *CoinPrice) GetLastUpdatedAt() int64 {
	if x != nil {
		return x.LastUpdatedAt
	}
	return 0
}

type QueryCoinPriceReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*CoinPrice `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *QueryCoinPriceReply) Reset() {
	*x = QueryCoinPriceReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_wallet_v1_market_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryCoinPriceReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryCoinPriceReply) ProtoMessage() {}

func (x *QueryCoinPriceReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_market_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryCoinPriceReply.ProtoReflect.Descriptor instead.
func (*QueryCoinPriceReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_market_proto_rawDescGZIP(), []int{12}
}

func (x *QueryCoinPriceReply) GetList() []*CoinPrice {
	if x != nil {
		return x.List
	}
	return nil
}

var File_api_wallet_v1_market_proto protoreflect.FileDescriptor

var file_api_wallet_v1_market_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2f, 0x76, 0x31, 0x2f,
	0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x61, 0x70,
	0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x62, 0x75, 0x66, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe1, 0x02, 0x0a, 0x0f, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x57, 0x69, 0x74, 0x68, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x68,
	0x61, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x18, 0x0a, 0x07, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d,
	0x62, 0x6f, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f,
	0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x73, 0x12, 0x2d, 0x0a,
	0x12, 0x63, 0x69, 0x72, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x75, 0x70,
	0x70, 0x6c, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x63, 0x69, 0x72, 0x63, 0x75,
	0x6c, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x70, 0x70, 0x6c, 0x79, 0x12, 0x2c, 0x0a, 0x12,
	0x74, 0x72, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x5f, 0x32,
	0x34, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x74, 0x72, 0x61, 0x64, 0x69, 0x6e,
	0x67, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x32, 0x34, 0x68, 0x12, 0x3d, 0x0a, 0x1b, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x32, 0x34, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x18, 0x70, 0x72, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x65, 0x72, 0x63,
	0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x32, 0x34, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x6f, 0x67,
	0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x6f, 0x67,
	0x6f, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x22, 0x46, 0x0a, 0x10, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x32,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x52, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x22, 0xa3, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6f, 0x70, 0x75, 0x6c,
	0x61, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xba, 0x48, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1f, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x09, 0xba, 0x48, 0x06, 0x22, 0x04, 0x18, 0x14, 0x20,
	0x00, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x1d, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77,
	0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x6b, 0x65, 0x79,
	0x77, 0x6f, 0x72, 0x64, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x65, 0x73, 0x42, 0x0a, 0x0a, 0x08,
	0x5f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x22, 0x6c, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x6f, 0x70, 0x75, 0x6c, 0x61, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x32, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x52,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x54, 0x0a, 0x13, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53,
	0x69, 0x6d, 0x70, 0x6c, 0x65, 0x4b, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x12, 0x3d, 0x0a,
	0x09, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x44, 0x42, 0x08, 0xba, 0x48, 0x05, 0x92, 0x01, 0x02,
	0x08, 0x01, 0x52, 0x08, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x64, 0x73, 0x22, 0x81, 0x01, 0x0a,
	0x0b, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x4b, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x18, 0x0a,
	0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x05, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x21, 0x0a,
	0x0c, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x73,
	0x22, 0x47, 0x0a, 0x15, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x4b,
	0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2e, 0x0a, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x4b, 0x6c,
	0x69, 0x6e, 0x65, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x39, 0x0a, 0x17, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x55, 0x53, 0x44, 0x52, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x69,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x69, 0x65, 0x73, 0x22, 0x8c, 0x01, 0x0a, 0x0f, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x79, 0x55, 0x53, 0x44, 0x52, 0x61, 0x74, 0x65, 0x12, 0x3f, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x55, 0x53, 0x44, 0x52, 0x61, 0x74, 0x65, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x1a, 0x38, 0x0a, 0x0a, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x44, 0x0a, 0x07, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x44, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12,
	0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x52, 0x0a, 0x11, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x43, 0x6f, 0x69, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x3d,
	0x0a, 0x09, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x44, 0x42, 0x08, 0xba, 0x48, 0x05, 0x92, 0x01,
	0x02, 0x08, 0x01, 0x52, 0x08, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x64, 0x73, 0x22, 0x84, 0x01,
	0x0a, 0x09, 0x43, 0x6f, 0x69, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x18, 0x0a, 0x07,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x26, 0x0a, 0x0f,
	0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x22, 0x43, 0x0a, 0x13, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x69,
	0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2c, 0x0a, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x69, 0x6e, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x32, 0xa6, 0x04, 0x0a, 0x09, 0x4d, 0x61,
	0x72, 0x6b, 0x65, 0x74, 0x53, 0x72, 0x76, 0x12, 0x7e, 0x0a, 0x0e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x43, 0x6f, 0x69, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43,
	0x6f, 0x69, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x43, 0x6f, 0x69, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x3a, 0x01, 0x2a, 0x22, 0x1b, 0x2f, 0x76, 0x31, 0x2f,
	0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x69,
	0x6e, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x89, 0x01, 0x0a, 0x14, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x55, 0x53, 0x44, 0x52, 0x61, 0x74, 0x65,
	0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x55, 0x53,
	0x44, 0x52, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x79, 0x55, 0x53, 0x44, 0x52, 0x61, 0x74, 0x65, 0x22, 0x29, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23,
	0x12, 0x21, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2f, 0x71, 0x75, 0x65,
	0x72, 0x79, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x75, 0x73, 0x64, 0x72,
	0x61, 0x74, 0x65, 0x12, 0x86, 0x01, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x69, 0x6d,
	0x70, 0x6c, 0x65, 0x4b, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x69,
	0x6d, 0x70, 0x6c, 0x65, 0x4b, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x4b, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x3a, 0x01, 0x2a, 0x22, 0x1d, 0x2f,
	0x76, 0x31, 0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f,
	0x73, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x5f, 0x6b, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x83, 0x01, 0x0a,
	0x10, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6f, 0x70, 0x75, 0x6c, 0x61, 0x72, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6f, 0x70, 0x75, 0x6c, 0x61, 0x72, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x61, 0x6c, 0x6c,
	0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6f, 0x70, 0x75, 0x6c, 0x61,
	0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x25, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x1f, 0x12, 0x1d, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2f,
	0x6c, 0x69, 0x73, 0x74, 0x5f, 0x70, 0x6f, 0x70, 0x75, 0x6c, 0x61, 0x72, 0x5f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x42, 0x93, 0x01, 0x0a, 0x11, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x42, 0x0b, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x1b, 0x62, 0x79, 0x64, 0x5f, 0x77, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2f, 0x76,
	0x31, 0x3b, 0x76, 0x31, 0xa2, 0x02, 0x03, 0x41, 0x57, 0x58, 0xaa, 0x02, 0x0d, 0x41, 0x70, 0x69,
	0x2e, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x56, 0x31, 0xca, 0x02, 0x0d, 0x41, 0x70, 0x69,
	0x5c, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x5c, 0x56, 0x31, 0xe2, 0x02, 0x19, 0x41, 0x70, 0x69,
	0x5c, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x5c, 0x56, 0x31, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0xea, 0x02, 0x0f, 0x41, 0x70, 0x69, 0x3a, 0x3a, 0x57, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x3a, 0x3a, 0x56, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_wallet_v1_market_proto_rawDescOnce sync.Once
	file_api_wallet_v1_market_proto_rawDescData = file_api_wallet_v1_market_proto_rawDesc
)

func file_api_wallet_v1_market_proto_rawDescGZIP() []byte {
	file_api_wallet_v1_market_proto_rawDescOnce.Do(func() {
		file_api_wallet_v1_market_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_wallet_v1_market_proto_rawDescData)
	})
	return file_api_wallet_v1_market_proto_rawDescData
}

var file_api_wallet_v1_market_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_api_wallet_v1_market_proto_goTypes = []any{
	(*TokenWithMarket)(nil),         // 0: api.wallet.v1.TokenWithMarket
	(*SearchTokenReply)(nil),        // 1: api.wallet.v1.SearchTokenReply
	(*ListPopularTokenReq)(nil),     // 2: api.wallet.v1.ListPopularTokenReq
	(*ListPopularTokenReply)(nil),   // 3: api.wallet.v1.ListPopularTokenReply
	(*QuerySimpleKlineReq)(nil),     // 4: api.wallet.v1.QuerySimpleKlineReq
	(*SimpleKline)(nil),             // 5: api.wallet.v1.SimpleKline
	(*QuerySimpleKlineReply)(nil),   // 6: api.wallet.v1.QuerySimpleKlineReply
	(*QueryCurrencyUSDRateReq)(nil), // 7: api.wallet.v1.QueryCurrencyUSDRateReq
	(*CurrencyUSDRate)(nil),         // 8: api.wallet.v1.CurrencyUSDRate
	(*TokenID)(nil),                 // 9: api.wallet.v1.TokenID
	(*QueryCoinPriceReq)(nil),       // 10: api.wallet.v1.QueryCoinPriceReq
	(*CoinPrice)(nil),               // 11: api.wallet.v1.CoinPrice
	(*QueryCoinPriceReply)(nil),     // 12: api.wallet.v1.QueryCoinPriceReply
	nil,                             // 13: api.wallet.v1.CurrencyUSDRate.ValueEntry
}
var file_api_wallet_v1_market_proto_depIdxs = []int32{
	0,  // 0: api.wallet.v1.SearchTokenReply.list:type_name -> api.wallet.v1.TokenWithMarket
	0,  // 1: api.wallet.v1.ListPopularTokenReply.list:type_name -> api.wallet.v1.TokenWithMarket
	9,  // 2: api.wallet.v1.QuerySimpleKlineReq.token_ids:type_name -> api.wallet.v1.TokenID
	5,  // 3: api.wallet.v1.QuerySimpleKlineReply.list:type_name -> api.wallet.v1.SimpleKline
	13, // 4: api.wallet.v1.CurrencyUSDRate.value:type_name -> api.wallet.v1.CurrencyUSDRate.ValueEntry
	9,  // 5: api.wallet.v1.QueryCoinPriceReq.token_ids:type_name -> api.wallet.v1.TokenID
	11, // 6: api.wallet.v1.QueryCoinPriceReply.list:type_name -> api.wallet.v1.CoinPrice
	10, // 7: api.wallet.v1.MarketSrv.QueryCoinPrice:input_type -> api.wallet.v1.QueryCoinPriceReq
	7,  // 8: api.wallet.v1.MarketSrv.QueryCurrencyUSDRate:input_type -> api.wallet.v1.QueryCurrencyUSDRateReq
	4,  // 9: api.wallet.v1.MarketSrv.QuerySimpleKline:input_type -> api.wallet.v1.QuerySimpleKlineReq
	2,  // 10: api.wallet.v1.MarketSrv.ListPopularToken:input_type -> api.wallet.v1.ListPopularTokenReq
	12, // 11: api.wallet.v1.MarketSrv.QueryCoinPrice:output_type -> api.wallet.v1.QueryCoinPriceReply
	8,  // 12: api.wallet.v1.MarketSrv.QueryCurrencyUSDRate:output_type -> api.wallet.v1.CurrencyUSDRate
	6,  // 13: api.wallet.v1.MarketSrv.QuerySimpleKline:output_type -> api.wallet.v1.QuerySimpleKlineReply
	3,  // 14: api.wallet.v1.MarketSrv.ListPopularToken:output_type -> api.wallet.v1.ListPopularTokenReply
	11, // [11:15] is the sub-list for method output_type
	7,  // [7:11] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_api_wallet_v1_market_proto_init() }
func file_api_wallet_v1_market_proto_init() {
	if File_api_wallet_v1_market_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_wallet_v1_market_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*TokenWithMarket); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_market_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*SearchTokenReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_market_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*ListPopularTokenReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_market_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*ListPopularTokenReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_market_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*QuerySimpleKlineReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_market_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*SimpleKline); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_market_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*QuerySimpleKlineReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_market_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*QueryCurrencyUSDRateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_market_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*CurrencyUSDRate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_market_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*TokenID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_market_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*QueryCoinPriceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_market_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*CoinPrice); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_wallet_v1_market_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*QueryCoinPriceReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_wallet_v1_market_proto_msgTypes[2].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_wallet_v1_market_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_wallet_v1_market_proto_goTypes,
		DependencyIndexes: file_api_wallet_v1_market_proto_depIdxs,
		MessageInfos:      file_api_wallet_v1_market_proto_msgTypes,
	}.Build()
	File_api_wallet_v1_market_proto = out.File
	file_api_wallet_v1_market_proto_rawDesc = nil
	file_api_wallet_v1_market_proto_goTypes = nil
	file_api_wallet_v1_market_proto_depIdxs = nil
}
