GOHOSTOS:=$(shell go env GOHOSTOS)
GOPATH:=$(shell go env GOPATH)
VERSION=$(shell git describe --tags --always)

ifeq ($(GOHOSTOS), windows)
	#the `find.exe` is different from `find` in bash/shell.
	#to see https://docs.microsoft.com/en-us/windows-server/administration/windows-commands/find.
	#changed to use git-bash.exe to run find cli or other cli friendly, caused of every developer has a Git.
	#Git_Bash= $(subst cmd\,bin\bash.exe,$(dir $(shell where git)))
	Git_Bash=$(subst \,/,$(subst cmd\,bin\bash.exe,$(dir $(shell where git))))
	INTERNAL_PROTO_FILES=$(shell $(Git_Bash) -c "find internal -name *.proto")
	API_PROTO_FILES=$(shell $(Git_Bash) -c "find api -name *.proto")
	ERR_PROTO_FILES=$(shell $(Git_Bash) -c "find errors -name *.proto")
else
	INTERNAL_PROTO_FILES=$(shell find internal -name *.proto)
	API_PROTO_FILES=$(shell find api -name *.proto)
	ERR_PROTO_FILES=$(shell find errors -name *.proto)
endif

.PHONY: init
# init env
init:
	go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
	go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest
	go install github.com/go-kratos/kratos/cmd/kratos/v2@latest
	go install github.com/go-kratos/kratos/cmd/protoc-gen-go-http/v2@latest
	go install github.com/google/gnostic/cmd/protoc-gen-openapi@latest
	go install github.com/google/wire/cmd/wire@latest
	go install github.com/go-kratos/kratos/cmd/protoc-gen-go-errors/v2

.PHONY: proto
# generate all proto
proto:
	buf generate
	buf generate --path api/wallet \
				--template '{"version":"v2","plugins":[{"local":"protoc-gen-openapi","out":"api/wallet","strategy":"all","opt":["naming=proto","default_response=false","fq_schema_naming=true"]}]}'
	buf generate --path api/walletadmin \
				--template '{"version":"v2","plugins":[{"local":"protoc-gen-openapi","out":"api/walletadmin","strategy":"all","opt":["naming=proto","default_response=false","fq_schema_naming=true"]}]}'

.PHONY: deps
# buf 增加依赖时需要执行
deps:
	buf dep update

.PHONY: sync-openapi
# sync OpenAPI files to embedded directory
sync-openapi:
	./scripts/sync-openapi.sh

.PHONY: build
# build
build:
	./scripts/sync-openapi.sh
	mkdir -p bin/ && go build -ldflags "-X main.Version=$(VERSION)" -o ./bin/ ./...

.PHONY: generate
# generate
generate:
	go generate ./...
	go mod tidy

.PHONY: test
# go test(no cache)
test:
	go test -count=1 -v ./...

.PHONY: all
# generate all
all:
	make proto;
	make generate;

.PHONY: config
# generate internal proto
config:
	buf generate --path internal
# show help
help:
	@echo ''
	@echo 'Usage:'
	@echo ' make [target]'
	@echo ''
	@echo 'Targets:'
	@awk '/^[a-zA-Z\-\_0-9]+:/ { \
	helpMessage = match(lastLine, /^# (.*)/); \
		if (helpMessage) { \
			helpCommand = substr($$1, 0, index($$1, ":")); \
			helpMessage = substr(lastLine, RSTART + 2, RLENGTH); \
			printf "\033[36m%-22s\033[0m %s\n", helpCommand,helpMessage; \
		} \
	} \
	{ lastLine = $$0 }' $(MAKEFILE_LIST)

.DEFAULT_GOAL := help