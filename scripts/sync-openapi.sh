#!/bin/bash

# 同步OpenAPI文件到嵌入目录
# Sync OpenAPI files to embedded directory

set -e

echo "Syncing OpenAPI files to embedded directory..."

# 确保嵌入目录存在
# Ensure embedded directory exists
mkdir -p embedded

# 复制钱包API文件
# Copy wallet API file
if [ -f "api/wallet/openapi.yaml" ]; then
    cp api/wallet/openapi.yaml embedded/wallet-openapi.yaml
    echo "✓ Copied wallet OpenAPI file"
else
    echo "✗ Warning: api/wallet/openapi.yaml not found"
fi

# 复制管理员API文件
# Copy admin API file
if [ -f "api/walletadmin/openapi.yaml" ]; then
    cp api/walletadmin/openapi.yaml embedded/admin-openapi.yaml
    echo "✓ Copied admin OpenAPI file"
else
    echo "✗ Warning: api/walletadmin/openapi.yaml not found"
fi

echo "OpenAPI files sync completed!"
