// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"byd_wallet/internal/biz"
	"byd_wallet/internal/biz/dapp"
	"byd_wallet/internal/conf"
	"byd_wallet/internal/data"
	"byd_wallet/internal/server"
	"byd_wallet/internal/service/admin"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(confServer *conf.Server, confData *conf.Data, logger log.Logger) (*kratos.App, func(), error) {
	db, cleanup, err := data.NewGormDB(confData)
	if err != nil {
		return nil, nil, err
	}
	universalClient, cleanup2, err := data.NewRedisClient(confData)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	dataData := data.NewData(db, universalClient)
	adminRepo := data.NewAdminRepo(dataData)
	adminJwt, err := data.NewAdminJwt(confData)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	adminUsecase := biz.NewAdminUsecase(logger, adminRepo, adminJwt)
	adminService := admin.NewAdminService(adminUsecase)
	userAddressRepo := data.NewUserAddressRepo(dataData)
	userAddressUsecase := biz.NewUserAddressUsecase(logger, userAddressRepo)
	addressService := admin.NewAddressService(userAddressUsecase)
	userRepo := data.NewUserRepo(dataData)
	eventPublisher, cleanup3, err := data.NewEventPublisher(confData)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	userUsecase := biz.NewUserUsecase(logger, db, universalClient, userRepo, eventPublisher)
	userService := admin.NewUserService(userUsecase)
	blockchainNetworkRepo := data.NewBlockchainNetworkRepo(dataData)
	blockchainNetworkUsecase := biz.NewBlockchainNetworkUsecase(logger, blockchainNetworkRepo)
	s3Repo, err := data.NewS3Repo(dataData)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	chainService := admin.NewChainService(blockchainNetworkUsecase, s3Repo)
	client, err := data.NewOKXClient(dataData, logger)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	okxChainIndexDto := data.NewOKXChainIndexDto()
	tokenAssetRepo := data.NewTokenAssetRepo(logger, dataData, client, okxChainIndexDto)
	tokenAssetUsecase := biz.NewTokenAssetUsecase(logger, tokenAssetRepo)
	tokenAssetStarRepo := data.NewTokenAssetStarRepo(logger, dataData)
	tokenAssetStarUsecase := biz.NewTokenAssetStarUsecase(logger, tokenAssetStarRepo)
	coinService := admin.NewCoinService(tokenAssetUsecase, s3Repo, tokenAssetStarUsecase)
	transactionRepo := data.NewTransactionRepo(dataData)
	transactionUsecase := biz.NewTransactionUsecase(logger, transactionRepo)
	txService := admin.NewTxService(transactionUsecase)
	dappAdminRepo := data.NewDappAdminRepo(dataData)
	dbTx := data.NewDBTx(dataData)
	dappAdminUsecase := dapp.NewAdminUsecase(dappAdminRepo, dbTx, s3Repo, logger)
	dappService := admin.NewDappService(dappAdminUsecase)
	fileUsecase := biz.NewFileUsecase(s3Repo)
	fileService := admin.NewFileService(fileUsecase)
	swapAdminRepo := data.NewSwapAdminRepo(dataData)
	sortRepo := data.NewCommonSortRepo(dataData)
	swapAdminUsecase := biz.NewSwapAdminUsecase(logger, swapAdminRepo, dbTx, sortRepo)
	swapService := admin.NewSwapService(swapAdminUsecase)
	appVersionAdminRepo := data.NewAppVersionAdminRepo(dataData)
	appVersionAdminUsecase := biz.NewAppVersionUsecase(appVersionAdminRepo)
	appVersionService := admin.NewAppVersionService(appVersionAdminUsecase)
	adminHttpServer := server.NewAdminHttpServer(confServer, logger, adminService, addressService, userService, chainService, coinService, txService, dappService, fileService, swapService, appVersionService)
	app := newApp(logger, adminHttpServer)
	return app, func() {
		cleanup3()
		cleanup2()
		cleanup()
	}, nil
}
