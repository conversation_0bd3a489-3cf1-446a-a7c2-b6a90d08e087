package hot

import (
	"byd_wallet/common"
	"byd_wallet/internal/data"
	"byd_wallet/model"
	"context"
	"github.com/urfave/cli/v3"
	"gorm.io/gorm"
)

var Cmd = &cli.Command{
	Name:  "hot",
	Usage: "兑换热门代币数据修复",
	Flags: []cli.Flag{
		&cli.StringFlag{
			Name:     "conf",
			Usage:    "数据库配置文件路径",
			Required: true,
		},
	},
	Action: run,
}

func run(ctx context.Context, cmd *cli.Command) error {
	bc, err := common.NewBootstrapConfig(cmd.String("conf"))
	if err != nil {
		return err
	}
	db, cleanup, err := data.NewGormDB(bc.Data)
	if err != nil {
		return err
	}
	defer cleanup()

	var allTokens []*model.SwappableHotToken
	if err := db.WithContext(ctx).Model(&model.SwappableHotToken{}).Where("is_all=?", true).Order("sort_order asc").Find(&allTokens).Error; err != nil {
		return err
	}
	if err := reorderTokens(ctx, db, allTokens); err != nil {
		return err
	}
	var chains []*model.SwappableBlockchainNetwork
	if err := db.WithContext(ctx).Model(&model.SwappableBlockchainNetwork{}).Preload("BlockchainNetwork").Find(&chains).Error; err != nil {
		return err
	}
	for _, chain := range chains {
		var tokens []*model.SwappableHotToken
		if err := db.WithContext(ctx).Model(&model.SwappableHotToken{}).
			Where("is_all=? and chain_index=?", false, chain.BlockchainNetwork.ChainIndex).
			Order("sort_order asc").Find(&tokens).Error; err != nil {
			return err
		}
		if err := reorderTokens(ctx, db, tokens); err != nil {
			return err
		}
	}

	return nil
}

func reorderTokens(ctx context.Context, db *gorm.DB, tokens []*model.SwappableHotToken) error {
	for i, token := range tokens {
		if err := db.WithContext(ctx).Model(&model.SwappableHotToken{}).Where("id=?", token.ID).Update("sort_order", i+1).Error; err != nil {
			return err
		}
	}
	return nil
}
