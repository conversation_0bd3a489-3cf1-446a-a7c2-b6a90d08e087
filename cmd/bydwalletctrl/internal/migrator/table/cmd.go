package table

import (
	"byd_wallet/internal/conf"
	"byd_wallet/internal/data"
	"context"
	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/file"
	"github.com/urfave/cli/v3"
)

var Cmd = &cli.Command{
	Name:  "tables",
	Usage: "初始化表",
	Flags: []cli.Flag{
		&cli.StringFlag{
			Name:     "conf",
			Usage:    "数据库配置文件路径",
			Required: true,
		},
	},
	Action: run,
}

func run(ctx context.Context, cmd *cli.Command) error {
	c := config.New(
		config.WithSource(
			file.NewSource(cmd.String("conf")),
		),
	)
	if err := c.Load(); err != nil {
		return err
	}

	var bc conf.Bootstrap
	if err := c.<PERSON>an(&bc); err != nil {
		return err
	}
	m, cleanup, err := data.NewMigrator(bc.Data)
	if err != nil {
		return err
	}
	defer cleanup()
	return m.AutoMigrate(ctx)
}
