package rent

import (
	"byd_wallet/common"
	"byd_wallet/internal/data"
	"byd_wallet/model"
	"context"
	"errors"
	"github.com/urfave/cli/v3"
)

var Cmd = &cli.Command{
	Name:  "rent",
	Usage: "波场租赁数据初始化",
	Flags: []cli.Flag{
		&cli.StringFlag{
			Name:     "conf",
			Usage:    "数据库配置文件路径",
			Required: true,
		},
	},
	Action: run,
}

func run(ctx context.Context, cmd *cli.Command) error {
	bc, err := common.NewBootstrapConfig(cmd.String("conf"))
	if err != nil {
		return err
	}
	db, cleanup, err := data.NewGormDB(bc.Data)
	if err != nil {
		return err
	}
	defer cleanup()
	var count int64
	if err := db.WithContext(ctx).Model(&model.TronRentConfig{}).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return errors.New("TronRentConfig 配置已存在，请勿重复添加！")
	}
	if err := db.WithContext(ctx).Create(&model.TronRentConfig{
		TradeType:  "fastTrade",
		SourceFlag: "BossWallet",
		OrderType:  "ENERGY",
		PledgeHour: 3,
	}).Error; err != nil {
		return err
	}
	return nil
}
