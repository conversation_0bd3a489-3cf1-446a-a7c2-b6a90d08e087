package approval

import (
	"byd_wallet/common"
	"byd_wallet/internal/biz"
	"byd_wallet/internal/data"
	"byd_wallet/model"
	"context"
	"github.com/urfave/cli/v3"
)

var Cmd = &cli.Command{
	Name:  "approval",
	Usage: "授权数据初始化",
	Flags: []cli.Flag{
		&cli.StringFlag{
			Name:     "conf",
			Usage:    "数据库配置文件路径",
			Required: true,
		},
	},
	Action: run,
}

func run(ctx context.Context, cmd *cli.Command) error {
	bc, err := common.NewBootstrapConfig(cmd.String("conf"))
	if err != nil {
		return err
	}
	db, cleanup, err := data.NewGormDB(bc.Data)
	if err != nil {
		return err
	}
	defer cleanup()
	var users []*model.User
	if err := db.WithContext(ctx).Model(&model.User{}).Find(&users).Error; err != nil {
		return err
	}
	publisher, cleanup2, err := data.NewEventPublisher(bc.Data)
	if err != nil {
		return err
	}
	defer func() {
		cleanup()
		cleanup2()
	}()
	for _, user := range users {
		var uas []model.UserAddress
		if err = db.WithContext(ctx).Model(&model.UserAddress{}).Where("user_id=?", user.ID).Find(&uas).Error; err != nil {
			return err
		}
		if err = publisher.Publish(ctx, &biz.UserRegisterEvent{
			Addresses: uas,
		}); err != nil {
			return err
		}
	}
	return nil
}
