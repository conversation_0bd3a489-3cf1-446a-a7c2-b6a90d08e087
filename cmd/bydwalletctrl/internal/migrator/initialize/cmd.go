package initialize

import (
	"byd_wallet/cmd/bydwalletctrl/internal/migrator/initialize/approval"
	"byd_wallet/cmd/bydwalletctrl/internal/migrator/initialize/coindata"
	"byd_wallet/cmd/bydwalletctrl/internal/migrator/initialize/rent"
	"byd_wallet/cmd/bydwalletctrl/internal/migrator/initialize/solaccount"
	"byd_wallet/cmd/bydwalletctrl/internal/migrator/initialize/swap"

	"github.com/urfave/cli/v3"
)

var Cmd = &cli.Command{
	Name:  "initialize",
	Usage: "初始化数据",
	Commands: []*cli.Command{
		rent.Cmd,
		coindata.Cmd,
		solaccount.Cmd,
		approval.Cmd,
		swap.Cmd,
	},
}
