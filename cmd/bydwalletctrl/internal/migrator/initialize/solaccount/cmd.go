package solaccount

import (
	"byd_wallet/common"
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz/syncer/chain/solana"
	"byd_wallet/internal/data"
	"byd_wallet/model"
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/urfave/cli/v3"
	"os"
)

var Cmd = &cli.Command{
	Name:  "addsplaccount",
	Usage: "更新Solana地址account",
	Flags: []cli.Flag{
		&cli.StringFlag{
			Name:     "conf",
			Usage:    "配置文件路径",
			Required: true,
		},
	},
	Action: run,
}

func run(ctx context.Context, cmd *cli.Command) error {
	logger := log.With(log.NewStdLogger(os.Stdout),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
	)
	bc, err := common.NewBootstrapConfig(cmd.String("conf"))
	if err != nil {
		return err
	}

	db, cleanup, err := data.NewGormDB(bc.Data)
	if err != nil {
		return err
	}
	defer cleanup()
	rd, cleanup1, err := data.NewRedisClient(bc.Data)
	if err != nil {
		return err
	}
	defer cleanup1()

	dtData := data.NewData(db, nil)
	rpc := data.NewRPCEndpointRepo(dtData)
	solRpcList, err := rpc.GetRpcListByChainIndex(constant.SolChainIndex, "http")
	if err != nil {
		return err
	}
	f, _, err := solana.NewTransactionsFetcher(log.NewHelper(logger), constant.SolChainIndex, solRpcList)
	if err != nil {
		return err
	}
	var userAddresses []model.UserAddress
	err = db.Model(model.UserAddress{}).Where("chain_index = ?", constant.SolChainIndex).Find(&userAddresses).Error
	if err != nil {
		return err
	}

	for _, userAddress := range userAddresses {
		accounts, err := f.GetTokenAccountsByOwner(userAddress.Address, constant.TokenProgramID)
		if err != nil {
			continue
		}
		for _, account := range accounts {
			redisKey := common.GetSolOwner(account.Account.Owner.String())
			rd.Set(ctx, redisKey, userAddress.Address, 0)
		}

	}
	return nil
}
