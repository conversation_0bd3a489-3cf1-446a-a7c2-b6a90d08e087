// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package swap

import (
	"byd_wallet/internal/biz"
	"byd_wallet/internal/biz/syncer/chain/tron"
	"byd_wallet/internal/conf"
	"byd_wallet/internal/data"
	"byd_wallet/internal/data/metapath"
	"github.com/go-kratos/kratos/v2/log"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireUsecase(confData *conf.Data, logger log.Logger) (*biz.SwapUsecase, func(), error) {
	db, cleanup, err := data.NewGormDB(confData)
	if err != nil {
		return nil, nil, err
	}
	universalClient, cleanup2, err := data.NewRedisClient(confData)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	dataData := data.NewData(db, universalClient)
	swapRepo := data.NewSwapRepo(dataData)
	config, err := data.NewMetapathConfig(dataData)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	client := metapath.NewClient(config, logger)
	rpcEndpointRepo := data.NewRPCEndpointRepo(dataData)
	roundRobinClient, cleanup3, err := data.NewTronChainClient(rpcEndpointRepo)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	tokenSwapper := metapath.NewTokenSwapper(client, roundRobinClient, logger)
	okxClient, err := data.NewOKXClient(dataData, logger)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	okxChainIndexDto := data.NewOKXChainIndexDto()
	tokenAssetRepo := data.NewTokenAssetRepo(logger, dataData, okxClient, okxChainIndexDto)
	transactionRepo := data.NewTransactionRepo(dataData)
	swapTokenFetcher := metapath.NewSwapTokenFetcher(client, logger)
	multiChainClient, cleanup4, err := data.NewEvmChainClient(rpcEndpointRepo)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	smartNodeSelectionClient, cleanup5, err := data.NewSolanaChainClient(rpcEndpointRepo)
	if err != nil {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	tokenContractRepo := data.NewTokenContractRepo(logger, multiChainClient, roundRobinClient, smartNodeSelectionClient)
	s3Repo, err := data.NewS3Repo(dataData)
	if err != nil {
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	tokenCollector := biz.NewTokenCollector(tokenAssetRepo, tokenContractRepo, s3Repo)
	coinDataThirdAPI, err := data.NewCoinDataThirdAPI(dataData)
	if err != nil {
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	blockchainNetworkRepo := data.NewBlockchainNetworkRepo(dataData)
	transactionFetcher := tron.NewTransactionFetcher(roundRobinClient)
	bizTransactionFetcher := biz.NewTransactionFetcher(transactionFetcher)
	swapUsecase := biz.NewSwapUsecase(logger, swapRepo, tokenSwapper, tokenAssetRepo, transactionRepo, swapTokenFetcher, tokenCollector, coinDataThirdAPI, blockchainNetworkRepo, bizTransactionFetcher)
	return swapUsecase, func() {
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
	}, nil
}
