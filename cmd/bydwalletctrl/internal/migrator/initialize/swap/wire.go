//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package swap

import (
	"byd_wallet/internal/biz"
	"byd_wallet/internal/conf"
	"byd_wallet/internal/data"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
)

// wireApp init kratos application.
func wireUsecase(*conf.Data, log.Logger) (*biz.SwapUsecase, func(), error) {
	panic(wire.Build(data.ProviderSet, biz.ProviderSet))
}
