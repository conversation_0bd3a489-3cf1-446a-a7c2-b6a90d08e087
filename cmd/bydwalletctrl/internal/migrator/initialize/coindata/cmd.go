package coindata

import (
	"fmt"

	"github.com/urfave/cli/v3"
)

var Cmd = &cli.Command{
	Name:  "coindata",
	Usage: "market数据初始化",
	Flags: []cli.Flag{
		&cli.StringFlag{
			Name:     "conf",
			Usage:    "配置文件路径",
			Required: true,
		},
	},
	Commands: []*cli.Command{
		{
			Name:   "info",
			Usage:  "同步coin_infos表数据",
			Action: runInfo,
		},
		{
			Name:   "logo",
			Usage:  "同步coin_logos表数据",
			Action: runLogo,
		},
		{
			Name:   "token",
			Usage:  "同步token_assets表数据",
			Action: runToken,
		},
		{
			Name:   "tokenlogo",
			Usage:  "上传logo到s3并赋值token_assets表logo_url字段",
			Action: runTokenLogo,
		},
		{
			Name:   "market",
			Usage:  "手动同步market数据",
			Action: runMarket,
			Flags: []cli.Flag{
				&cli.StringFlag{
					Name:     "datatype",
					Usage:    fmt.Sprintf("同步数据类型. values: %v", datatypeMarkets),
					Required: true,
					Validator: func(s string) error {
						_, ok := datatypeMarketChecker[s]
						if !ok {
							return fmt.Errorf("datatype %s is not supported", s)
						}
						return nil
					},
				},
			},
		},
	},
}
