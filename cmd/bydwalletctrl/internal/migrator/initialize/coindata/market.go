package coindata

import (
	"byd_wallet/common"
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz/coindata"
	"byd_wallet/internal/data"
	"context"
	"os"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/urfave/cli/v3"
)

const (
	marketCurrencyRate            = "rate"
	marketPopularCoinMarketData   = "cmdp"
	marketNoPopularCoinMarketData = "cmdnp"
	marketCoinOHLC                = "ohlc"
)

var datatypeMarketChecker = map[string]struct{}{
	marketCurrencyRate:            {},
	marketPopularCoinMarketData:   {},
	marketNoPopularCoinMarketData: {},
	marketCoinOHLC:                {},
}

var datatypeMarkets = []string{
	marketCurrencyRate,
	marketPopularCoinMarketData,
	marketNoPopularCoinMarketData,
	marketCoinOHLC,
}

func runMarket(ctx context.Context, cmd *cli.Command) error {
	logger := log.With(log.NewStdLogger(os.Stdout),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
	)

	bc, err := common.NewBootstrapConfig(cmd.String("conf"))
	if err != nil {
		return err
	}
	db, cleanup, err := data.NewGormDB(bc.Data)
	if err != nil {
		return err
	}
	defer cleanup()

	rd, cleanup, err := data.NewRedisClient(bc.Data)
	if err != nil {
		return err
	}
	defer cleanup()

	dd := data.NewData(db, rd)

	cdtApi, err := data.NewCoinDataThirdAPI(dd)
	if err != nil {
		return err
	}

	crApi, err := data.NewCurrencyRateAPI(dd)
	if err != nil {
		return err
	}

	s := coindata.NewSyncer(logger, cdtApi, db, rd, crApi)

	switch cmd.String("datatype") {
	case marketCurrencyRate:
		s.SyncCurrencyUSDRate(ctx, []string{
			constant.CurrencyCNY,
			constant.CurrencyJPY,
		})
	case marketCoinOHLC:
		s.SyncCoinOHLCs(ctx)
	case marketNoPopularCoinMarketData:
		s.SyncCoinMarketDataByNoPopular(ctx)
	case marketPopularCoinMarketData:
		s.SyncCoinMarketDataByPopular(ctx)

	}

	return nil
}
