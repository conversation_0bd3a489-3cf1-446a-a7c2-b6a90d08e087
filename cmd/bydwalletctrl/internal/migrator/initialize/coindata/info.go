package coindata

import (
	"byd_wallet/common"
	"byd_wallet/internal/biz/coindata"
	"byd_wallet/internal/data"
	"context"
	"os"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/urfave/cli/v3"
)

func runInfo(ctx context.Context, cmd *cli.Command) error {
	logger := log.With(log.NewStdLogger(os.Stdout),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
	)

	bc, err := common.NewBootstrapConfig(cmd.String("conf"))
	if err != nil {
		return err
	}
	db, cleanup, err := data.NewGormDB(bc.Data)
	if err != nil {
		return err
	}
	defer cleanup()

	rd, cleanup, err := data.NewRedisClient(bc.Data)
	if err != nil {
		return err
	}
	defer cleanup()

	dd := data.NewData(db, rd)

	api, err := data.NewCoinDataThirdAPI(dd)
	if err != nil {
		return err
	}

	s := coindata.NewInitCtrl(logger, api, db, nil, nil)
	return s.InitCoinInfos(ctx)
}
