# MetaPath Swap接口代理测试完整指南

本指南详细介绍如何使用代理测试工具来测试Swap接口在有代理和无代理情况下的表现，特别关注metapath第三方调用是否受代理影响。

## 📋 目录

1. [快速开始](#快速开始)
2. [工具概述](#工具概述)
3. [基础测试工具](#基础测试工具)
4. [高级测试工具](#高级测试工具)
5. [配置文件详解](#配置文件详解)
6. [测试场景说明](#测试场景说明)
7. [结果分析](#结果分析)
8. [故障排除](#故障排除)
9. [最佳实践](#最佳实践)

## 🚀 快速开始

### 1. 编译项目

```bash
cd /Users/<USER>/work/dwallet/byd-wallet-go
go build -o bydwalletctrl ./cmd/bydwalletctrl
```

### 2. 查看可用命令

```bash
./bydwalletctrl --help
```

### 3. 运行基础测试

```bash
# 测试无代理情况
./bydwalletctrl proxy-test --base-url="https://api.metapath.com"

# 测试代理对比
./bydwalletctrl proxy-test --base-url="https://api.metapath.com" --proxy-url="http://your-proxy:8080"
```

### 4. 运行高级测试

```bash
# 生成配置文件模板
cp ./cmd/bydwalletctrl/internal/proxy/config.example.yaml ./config.yaml

# 编辑配置文件
vim ./config.yaml

# 运行高级测试
./bydwalletctrl proxy-test-advanced --config="./config.yaml"
```

## 🔧 工具概述

本项目提供了两个测试工具：

| 工具 | 命令 | 适用场景 | 特点 |
|------|------|----------|------|
| 基础测试工具 | `proxy-test` | 快速验证 | 简单易用，命令行参数 |
| 高级测试工具 | `proxy-test-advanced` | 全面测试 | 配置文件驱动，多场景测试 |

## 📊 基础测试工具

### 命令格式

```bash
./bydwalletctrl proxy-test [OPTIONS]
```

### 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--base-url` | string | `https://api.metapath.com` | MetaPath API基础URL |
| `--proxy-url` | string | 无 | 代理服务器URL |
| `--timeout` | string | `30s` | 请求超时时间 |
| `--debug` | bool | `false` | 启用调试模式 |
| `--source` | string | `test_source` | 通道唯一ID |
| `--test-count` | int | `3` | 测试次数 |

### 使用示例

```bash
# 基础测试
./bydwalletctrl proxy-test

# 自定义参数测试
./bydwalletctrl proxy-test \
  --base-url="https://api.metapath.com" \
  --proxy-url="http://proxy.example.com:8080" \
  --timeout="60s" \
  --test-count=5 \
  --debug

# 使用示例脚本
chmod +x ./cmd/bydwalletctrl/internal/proxy/example_test.sh
./cmd/bydwalletctrl/internal/proxy/example_test.sh
```

## 🎯 高级测试工具

### 命令格式

```bash
./bydwalletctrl proxy-test-advanced [OPTIONS]
```

### 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--config` | string | `./config.yaml` | 配置文件路径 |
| `--generate-config` | bool | `false` | 生成示例配置文件 |
| `--output` | string | 无 | 测试报告输出文件路径 |
| `--format` | string | `console` | 输出格式 (console, json) |

### 使用示例

```bash
# 使用默认配置
./bydwalletctrl proxy-test-advanced

# 指定配置文件
./bydwalletctrl proxy-test-advanced --config="./my-config.yaml"

# 输出JSON报告到文件
./bydwalletctrl proxy-test-advanced \
  --config="./config.yaml" \
  --format="json" \
  --output="./test-report.json"
```

## ⚙️ 配置文件详解

配置文件使用YAML格式，包含以下主要部分：

### API配置

```yaml
api:
  base_url: "https://api.metapath.com"  # API基础URL
  timeout: "30s"                        # 请求超时
  source: "test_source"                 # 通道ID
  debug: false                          # 调试模式
```

### 代理配置

```yaml
proxy:
  url: "http://proxy.example.com:8080"  # 代理URL
  username: "proxy_user"                # 代理用户名（可选）
  password: "proxy_pass"                # 代理密码（可选）
```

### 测试配置

```yaml
test:
  count: 3                              # 默认测试次数
  interval: "1s"                        # 测试间隔
  scenarios:                            # 测试场景配置
    basic:
      enabled: true
      description: "基础API连通性测试"
    proxy_comparison:
      enabled: true
      description: "有代理vs无代理对比测试"
    high_frequency:
      enabled: true
      description: "高频调用稳定性测试"
      count: 10
    timeout_test:
      enabled: true
      description: "短超时时间测试"
      timeout: "5s"
    concurrent:
      enabled: false
      description: "并发调用测试"
      concurrency: 5
```

### 测试数据配置

```yaml
test_data:
  from_token_address: "******************************************"
  to_token_address: "******************************************"
  from_address: "******************************************"
  to_address: "******************************************"
  amount: "1.0"
  slippage: "0.01"  # 1%
  dex: "uniswap"
  from_chain: "ethereum"
  to_chain: "ethereum"
```

### 告警配置

```yaml
alerts:
  success_rate_threshold: 90.0          # 成功率阈值
  response_time_threshold: "10s"        # 响应时间阈值
  proxy_performance_threshold: 2.0      # 代理性能影响阈值
```

## 🧪 测试场景说明

### 1. 基础测试 (basic)
- **目的**: 验证API基本连通性和功能
- **方法**: 发送标准Swap请求
- **评估**: 成功率和响应时间

### 2. 代理对比测试 (proxy_comparison)
- **目的**: 对比有代理和无代理的性能差异
- **方法**: 同时测试两种配置
- **评估**: 成功率差异、响应时间差异

### 3. 高频测试 (high_frequency)
- **目的**: 测试API在高频调用下的稳定性
- **方法**: 短间隔连续发送多个请求
- **评估**: 稳定性和性能衰减

### 4. 超时测试 (timeout_test)
- **目的**: 测试网络异常情况下的表现
- **方法**: 使用较短的超时时间
- **评估**: 超时处理和错误恢复

### 5. 并发测试 (concurrent)
- **目的**: 测试并发调用的处理能力
- **方法**: 同时发送多个请求
- **评估**: 并发性能和资源竞争

## 📈 结果分析

### 成功率分析

- **优秀**: ≥95%
- **良好**: 90-95%
- **一般**: 80-90%
- **较差**: <80%

### 响应时间分析

- **优秀**: <2秒
- **良好**: 2-5秒
- **一般**: 5-10秒
- **较差**: >10秒

### 代理影响评估

- **无影响**: 响应时间增加<50%，成功率差异<5%
- **轻微影响**: 响应时间增加50-100%，成功率差异5-10%
- **显著影响**: 响应时间增加>100%，成功率差异>10%

### 告警触发条件

1. **成功率告警**: 低于配置的阈值
2. **响应时间告警**: 超过配置的阈值
3. **代理性能告警**: 代理响应时间超过直连的指定倍数

## 🔍 故障排除

### 常见问题

#### 1. 连接失败

**症状**: 所有请求都失败，错误信息包含"connection refused"或"timeout"

**可能原因**:
- API服务不可用
- 网络连接问题
- 防火墙阻止

**解决方案**:
```bash
# 检查网络连接
ping api.metapath.com

# 检查端口连通性
telnet api.metapath.com 443

# 使用curl测试
curl -v https://api.metapath.com
```

#### 2. 代理连接失败

**症状**: 无代理测试成功，有代理测试失败

**可能原因**:
- 代理服务器配置错误
- 代理认证失败
- 代理服务器不可用

**解决方案**:
```bash
# 测试代理连通性
curl -x http://proxy.example.com:8080 https://api.metapath.com

# 检查代理认证
curl -x http://user:<EMAIL>:8080 https://api.metapath.com
```

#### 3. API调用失败

**症状**: 连接成功但API返回错误

**可能原因**:
- API密钥无效
- 请求参数错误
- API限流

**解决方案**:
- 启用调试模式查看详细日志
- 检查API文档确认参数格式
- 减少请求频率

### 调试技巧

1. **启用调试模式**:
   ```bash
   ./bydwalletctrl proxy-test --debug
   ```

2. **查看详细日志**:
   ```bash
   ./bydwalletctrl proxy-test --debug 2>&1 | tee debug.log
   ```

3. **单次测试**:
   ```bash
   ./bydwalletctrl proxy-test --test-count=1
   ```

4. **增加超时时间**:
   ```bash
   ./bydwalletctrl proxy-test --timeout="60s"
   ```

## 💡 最佳实践

### 测试策略

1. **渐进式测试**:
   - 先运行基础测试确认API可用
   - 再进行代理对比测试
   - 最后执行压力测试

2. **多环境测试**:
   - 在不同网络环境下测试
   - 使用不同的代理服务器
   - 测试不同时间段的性能

3. **定期监控**:
   - 建立定期测试计划
   - 监控性能趋势
   - 及时发现问题

### 配置建议

1. **超时设置**:
   - 生产环境: 30-60秒
   - 测试环境: 10-30秒
   - 快速验证: 5-10秒

2. **测试次数**:
   - 快速验证: 1-3次
   - 常规测试: 3-5次
   - 稳定性测试: 10-20次

3. **告警阈值**:
   - 成功率: 90-95%
   - 响应时间: 根据业务需求设定
   - 代理影响: 2-3倍

### 安全注意事项

1. **敏感信息保护**:
   - 不要在配置文件中硬编码密码
   - 使用环境变量存储敏感信息
   - 定期轮换API密钥

2. **网络安全**:
   - 使用HTTPS连接
   - 验证代理服务器证书
   - 避免在不安全网络中传输敏感数据

3. **测试数据**:
   - 使用测试专用的地址和参数
   - 避免使用真实的交易数据
   - 确保测试不会影响生产环境

## 📞 技术支持

如果在使用过程中遇到问题，可以：

1. 查看本文档的故障排除部分
2. 启用调试模式获取详细日志
3. 检查配置文件格式和参数
4. 联系技术支持团队

## 📝 更新日志

- **v1.0.0**: 初始版本，包含基础和高级测试工具
- 支持代理对比测试
- 支持多种测试场景
- 支持配置文件驱动
- 支持JSON格式报告输出

---

**注意**: 本工具仅用于测试目的，请确保在使用前已获得相应的API访问权限，并遵守相关的使用条款和限制。