package metapath

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"net/url"
	"sync"
	"time"
)

// MetaPathTestConfig 测试配置
type MetaPathTestConfig struct {
	BaseURL        string
	ProxyURL       string
	Debug          bool
	TestCount      int
	Concurrency    int
	RequestTimeout time.Duration // 单个请求超时时间
}

// MetaPathTester MetaPath测试器
type MetaPathTester struct {
	config     *MetaPathTestConfig
	httpClient *http.Client
}

// TestResult 测试结果
type TestResult struct {
	Success      bool
	ResponseTime time.Duration
	Error        error
	StatusCode   int
	ResponseSize int
}

// TestSummary 测试汇总
type TestSummary struct {
	TotalTests   int
	SuccessCount int
	FailureCount int
	SuccessRate  float64
	AvgResponse  time.Duration
	MinResponse  time.Duration
	MaxResponse  time.Duration
	TotalTime    time.Duration
}

// NewMetaPathTester 创建MetaPath测试器
func NewMetaPathTester(config *MetaPathTestConfig) (*MetaPathTester, error) {
	return &MetaPathTester{
		config: config,
	}, nil
}

// createHTTPClient 创建HTTP客户端，根据是否使用代理配置不同的Transport
func (t *MetaPathTester) createHTTPClient(useProxy bool) *http.Client {
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}

	if useProxy && t.config.ProxyURL != "" {
		proxyURL, err := url.Parse(t.config.ProxyURL)
		if err == nil {
			transport.Proxy = http.ProxyURL(proxyURL)
		}
	}

	return &http.Client{
		Transport: transport,
		// 不设置客户端级别的超时，使用 context 超时
	}
}

// Close 关闭测试器
func (t *MetaPathTester) Close() {
	// 无需清理资源
}

// RunComparison 运行对比测试
func (t *MetaPathTester) RunComparison(ctx context.Context) error {
	fmt.Println("=== 开始无代理测试 ===")
	noProxyResults, err := t.runTest(ctx, false)
	if err != nil {
		return fmt.Errorf("无代理测试失败: %v", err)
	}

	fmt.Println("\n=== 开始有代理测试 ===")
	proxyResults, err := t.runTest(ctx, true)
	if err != nil {
		return fmt.Errorf("有代理测试失败: %v", err)
	}

	fmt.Println("\n=== 测试结果对比 ===")
	t.compareResults(noProxyResults, proxyResults)

	return nil
}

// runTest 运行测试
func (t *MetaPathTester) runTest(ctx context.Context, useProxy bool) (*TestSummary, error) {
	// 创建对应的HTTP客户端
	t.httpClient = t.createHTTPClient(useProxy)

	proxyStatus := "无代理"
	if useProxy {
		proxyStatus = "有代理"
	}
	fmt.Printf("当前配置: %s\n", proxyStatus)

	// 并发测试
	results := make([]TestResult, 0, t.config.TestCount)
	resultsChan := make(chan TestResult, t.config.TestCount)
	semaphore := make(chan struct{}, t.config.Concurrency)

	var wg sync.WaitGroup
	startTime := time.Now()

	for i := 0; i < t.config.TestCount; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()
			semaphore <- struct{}{}        // 获取信号量
			defer func() { <-semaphore }() // 释放信号量

			// 为每个请求创建新的带超时的 context
			requestCtx, cancel := context.WithTimeout(context.Background(), t.config.RequestTimeout)
			defer cancel()

			result := t.executeRequest(requestCtx, index+1)
			resultsChan <- result
		}(i)
	}

	// 等待所有goroutine完成
	go func() {
		wg.Wait()
		close(resultsChan)
	}()

	// 收集结果
	for result := range resultsChan {
		results = append(results, result)
		if result.Success {
			fmt.Printf("✓ 请求成功 - 响应时间: %v\n", result.ResponseTime)
		} else {
			fmt.Printf("✗ 请求失败 - 错误: %v\n", result.Error)
		}
	}

	totalTime := time.Since(startTime)
	return t.calculateSummary(results, totalTime), nil
}

// executeRequest 执行HTTP请求
func (t *MetaPathTester) executeRequest(ctx context.Context, index int) TestResult {
	startTime := time.Now()
	n := rand.Intn(100)
	amount := fmt.Sprintf("%d000000", n)
	// 构建请求体 - 使用新的API格式
	requestBody := map[string]interface{}{
		"equipmentNo":      "CJBcN1trUGMSVeiPnGHtiI2A42Zz5523",
		"source":           "bosswallet",
		"fromTokenAddress": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",
		"toTokenAddress":   "******************************************",
		"fromTokenAmount":  amount,
		"fromTokenChain":   "TRON",
		"toTokenChain":     "BSC",
		"toAddress":        "",
		"userAddr":         "",
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return TestResult{
			Success:      false,
			ResponseTime: time.Since(startTime),
			Error:        fmt.Errorf("序列化请求体失败: %v", err),
		}
	}

	// 创建HTTP请求 - 使用新的API地址
	url := "https://api-swap.paths.finance/api/multiQuote"
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return TestResult{
			Success:      false,
			ResponseTime: time.Since(startTime),
			Error:        fmt.Errorf("创建请求失败: %v", err),
		}
	}

	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := t.httpClient.Do(req)
	responseTime := time.Since(startTime)

	if err != nil {
		return TestResult{
			Success:      false,
			ResponseTime: responseTime,
			Error:        fmt.Errorf("请求失败: %v", err),
		}
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return TestResult{
			Success:      false,
			ResponseTime: responseTime,
			Error:        fmt.Errorf("读取响应失败: %v", err),
			StatusCode:   resp.StatusCode,
		}
	}

	success := resp.StatusCode >= 200 && resp.StatusCode < 300
	var resultError error
	if !success {
		resultError = fmt.Errorf("HTTP状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	if t.config.Debug {
		fmt.Printf("[请求%d] 状态码: %d, 响应时间: %v, 响应大小: %d bytes\n",
			index, resp.StatusCode, responseTime, len(body))
		if t.config.Debug && len(body) > 0 {
			fmt.Printf("[请求%d] 响应内容: %s\n", index, string(body))
		}
	}

	return TestResult{
		Success:      success,
		ResponseTime: responseTime,
		Error:        resultError,
		StatusCode:   resp.StatusCode,
		ResponseSize: len(body),
	}
}

// calculateSummary 计算测试汇总
func (t *MetaPathTester) calculateSummary(results []TestResult, totalTime time.Duration) *TestSummary {
	summary := &TestSummary{
		TotalTests: len(results),
		TotalTime:  totalTime,
	}

	if len(results) == 0 {
		return summary
	}

	var totalResponseTime time.Duration
	summary.MinResponse = time.Hour // 初始化为一个很大的值

	for _, result := range results {
		if result.Success {
			summary.SuccessCount++
		} else {
			summary.FailureCount++
		}

		totalResponseTime += result.ResponseTime

		if result.ResponseTime < summary.MinResponse {
			summary.MinResponse = result.ResponseTime
		}
		if result.ResponseTime > summary.MaxResponse {
			summary.MaxResponse = result.ResponseTime
		}
	}

	summary.SuccessRate = float64(summary.SuccessCount) / float64(summary.TotalTests) * 100
	summary.AvgResponse = totalResponseTime / time.Duration(summary.TotalTests)

	if summary.MinResponse == time.Hour {
		summary.MinResponse = 0
	}

	return summary
}

// compareResults 对比测试结果
func (t *MetaPathTester) compareResults(noProxy, proxy *TestSummary) {
	fmt.Printf("\n📊 测试结果对比\n")
	fmt.Printf("========================================\n")
	fmt.Printf("%-15s | %-10s | %-10s | %-10s\n", "指标", "无代理", "有代理", "差异")
	fmt.Printf("----------------------------------------\n")

	// 成功率对比
	fmt.Printf("%-15s | %8.1f%% | %8.1f%% | %+7.1f%%\n",
		"成功率", noProxy.SuccessRate, proxy.SuccessRate, proxy.SuccessRate-noProxy.SuccessRate)

	// 平均响应时间对比
	fmt.Printf("%-15s | %8s | %8s | %+8s\n",
		"平均响应时间", noProxy.AvgResponse.Round(time.Millisecond),
		proxy.AvgResponse.Round(time.Millisecond),
		(proxy.AvgResponse - noProxy.AvgResponse).Round(time.Millisecond))

	// 最小响应时间对比
	fmt.Printf("%-15s | %8s | %8s | %+8s\n",
		"最小响应时间", noProxy.MinResponse.Round(time.Millisecond),
		proxy.MinResponse.Round(time.Millisecond),
		(proxy.MinResponse - noProxy.MinResponse).Round(time.Millisecond))

	// 最大响应时间对比
	fmt.Printf("%-15s | %8s | %8s | %+8s\n",
		"最大响应时间", noProxy.MaxResponse.Round(time.Millisecond),
		proxy.MaxResponse.Round(time.Millisecond),
		(proxy.MaxResponse - noProxy.MaxResponse).Round(time.Millisecond))

	// 总测试时间对比
	fmt.Printf("%-15s | %8s | %8s | %+8s\n",
		"总测试时间", noProxy.TotalTime.Round(time.Millisecond),
		proxy.TotalTime.Round(time.Millisecond),
		(proxy.TotalTime - noProxy.TotalTime).Round(time.Millisecond))

	fmt.Printf("========================================\n")

	// 结论
	fmt.Printf("\n📋 测试结论:\n")
	if proxy.SuccessRate > noProxy.SuccessRate {
		fmt.Printf("✓ 代理模式成功率更高 (+%.1f%%)\n", proxy.SuccessRate-noProxy.SuccessRate)
	} else if proxy.SuccessRate < noProxy.SuccessRate {
		fmt.Printf("✗ 代理模式成功率较低 (%.1f%%)\n", proxy.SuccessRate-noProxy.SuccessRate)
	} else {
		fmt.Printf("= 代理模式和无代理模式成功率相同\n")
	}

	if proxy.AvgResponse < noProxy.AvgResponse {
		fmt.Printf("✓ 代理模式响应更快 (-%s)\n", (noProxy.AvgResponse - proxy.AvgResponse).Round(time.Millisecond))
	} else if proxy.AvgResponse > noProxy.AvgResponse {
		fmt.Printf("✗ 代理模式响应较慢 (+%s)\n", (proxy.AvgResponse - noProxy.AvgResponse).Round(time.Millisecond))
	} else {
		fmt.Printf("= 代理模式和无代理模式响应时间相同\n")
	}
}
