# MetaPath API 代理测试工具

这个工具通过 HTTP Transport 配置代理来测试 MetaPath API 在有代理和无代理情况下的表现对比。

## 功能特性

- ✅ 通过 HTTP Transport 动态配置代理设置
- ✅ 支持并发测试（可配置并发数）
- ✅ 直接调用 `https://api-swap.paths.finance/api/multiQuote` API
- ✅ 自动对比有无代理的成功率和响应时间
- ✅ 详细的测试统计和分析报告
- ✅ 调试模式支持
- ✅ 轻量级设计，无需数据库依赖

## 使用方法

### 基本用法

```bash
# 编译工具
go build -o bydwalletctrl ./cmd/bydwalletctrl

# 运行测试（使用默认参数）
./bydwalletctrl test-metapath
```

### 完整参数示例

```bash
./bydwalletctrl test-metapath \
  --proxy-url http://127.0.0.1:7890 \
  --test-count 20 \
  --concurrency 5 \
  --timeout 30s \
  --debug
```

## 参数说明

| 参数 | 说明 | 默认值 | 必填 |
|------|------|--------|------|
| `--proxy-url` | 代理服务器URL | `http://127.0.0.1:7890` | ❌ |
| `--test-count` | 测试次数 | `100` | ❌ |
| `--concurrency` | 并发数 | `4` | ❌ |
| `--timeout` | 单个请求超时时间 | `30s` | ❌ |
| `--debug` | 启用调试模式 | `false` | ❌ |

## 工作原理

1. **无代理测试**: 创建不使用代理的 HTTP 客户端，执行测试
2. **有代理测试**: 创建使用指定代理的 HTTP 客户端，执行测试
3. **结果对比**: 自动对比两种模式的成功率、响应时间等指标

## 测试流程

```
开始测试
    ↓
=== 无代理测试 ===
创建无代理 HTTP 客户端
    ↓
并发执行 HTTP 请求
    ↓
收集测试结果
    ↓
=== 有代理测试 ===
创建有代理 HTTP 客户端
    ↓
并发执行 HTTP 请求
    ↓
收集测试结果
    ↓
=== 结果对比 ===
分析并输出对比报告
```

## 输出示例

```
开始MetaPath代理对比测试
API地址: https://api-swap.paths.finance/api/multiQuote
代理URL: http://127.0.0.1:7890
测试次数: 10
并发数: 3
请求超时: 30s
调试模式: false
========================================

=== 开始无代理测试 ===
当前配置: 无代理
✓ 请求成功 - 响应时间: 245ms
✓ 请求成功 - 响应时间: 198ms
✗ 请求失败 - 错误: context deadline exceeded
...

=== 开始有代理测试 ===
当前配置: 有代理
✓ 请求成功 - 响应时间: 156ms
✓ 请求成功 - 响应时间: 178ms
✓ 请求成功 - 响应时间: 134ms
...

=== 测试结果对比 ===
📊 测试结果对比
========================================
指标            | 无代理     | 有代理     | 差异
----------------------------------------
成功率          |     70.0% |     90.0% |   +20.0%
平均响应时间    |     245ms |     156ms |    -89ms
最小响应时间    |     156ms |     134ms |    -22ms
最大响应时间    |     456ms |     234ms |   -222ms
总测试时间      |    2.1s   |    1.8s   |   -300ms
========================================

📋 测试结论:
✓ 代理模式成功率更高 (+20.0%)
✓ 代理模式响应更快 (-89ms)
```

## 注意事项

1. **代理服务**: 如果使用代理，确保代理服务可用
2. **网络连接**: 测试前确保能够访问目标 API
3. **并发限制**: 根据网络和服务器性能调整并发数，避免过载
4. **SSL证书**: 工具默认跳过SSL证书验证，适用于测试环境

## 故障排除

### 常见问题

1. **网络连接失败**
   - 检查网络连接是否正常
   - 确保能够访问 `https://api-swap.paths.finance`
   - 使用 `--debug` 查看详细错误信息

2. **代理连接失败**
   - 检查 `--proxy-url` 是否正确
   - 确保代理服务可用
   - 尝试先测试无代理模式

3. **请求超时**
   - 检查网络延迟
   - 增加 `--timeout` 参数值（如 `--timeout 60s`）
   - 减少并发数
   - 检查代理服务性能

### 调试技巧

1. 使用 `--debug` 参数查看详细日志和响应内容
2. 减少 `--test-count` 进行快速测试
3. 设置较小的 `--concurrency` 避免过载
4. 根据网络情况调整 `--timeout` 参数