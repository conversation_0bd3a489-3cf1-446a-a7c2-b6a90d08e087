package metapath

import (
	"context"
	"fmt"
	"time"

	"github.com/urfave/cli/v3"
)

// Cmd MetaPath代理测试命令
var Cmd = &cli.Command{
	Name:  "metapath",
	Usage: "测试MetaPath接口在有代理和无代理情况下的表现（支持并发测试）",
	Flags: []cli.Flag{
		&cli.StringFlag{
			Name:  "proxy-url",
			Usage: "代理服务器URL (例如: http://127.0.0.1:7890)",
			Value: "http://127.0.0.1:7890",
		},
		&cli.BoolFlag{
			Name:  "debug",
			Usage: "启用调试模式",
			Value: false,
		},
		&cli.IntFlag{
			Name:  "test-count",
			Usage: "测试次数",
			Value: 100,
		},
		&cli.IntFlag{
			Name:  "concurrency",
			Usage: "并发数",
			Value: 8,
		},
		&cli.DurationFlag{
			Name:  "timeout",
			Usage: "单个请求超时时间 (例如: 30s, 1m)",
			Value: 5 * time.Second,
		},
	},
	Action: runMetaPathTest,
}

// runMetaPathTest 执行MetaPath测试
func runMetaPathTest(ctx context.Context, cmd *cli.Command) error {
	proxyURL := cmd.String("proxy-url")
	debug := cmd.Bool("debug")
	testCount := cmd.Int("test-count")
	concurrency := cmd.Int("concurrency")
	timeout := cmd.Duration("timeout")

	fmt.Printf("开始MetaPath代理对比测试\n")
	fmt.Printf("API地址: https://api-swap.paths.finance/api/multiQuote\n")
	fmt.Printf("代理URL: %s\n", proxyURL)
	fmt.Printf("测试次数: %d\n", testCount)
	fmt.Printf("并发数: %d\n", concurrency)
	fmt.Printf("请求超时: %v\n", timeout)
	fmt.Printf("调试模式: %v\n", debug)
	fmt.Printf("========================================\n\n")

	// 创建测试器
	tester, err := NewMetaPathTester(&MetaPathTestConfig{
		ProxyURL:       proxyURL,
		Debug:          debug,
		TestCount:      testCount,
		Concurrency:    concurrency,
		RequestTimeout: timeout,
	})
	if err != nil {
		return fmt.Errorf("创建测试器失败: %v", err)
	}
	defer tester.Close()

	// 执行对比测试
	return tester.RunComparison(ctx)
}
