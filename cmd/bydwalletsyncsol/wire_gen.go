// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"byd_wallet/internal/biz"
	"byd_wallet/internal/biz/syncer/chain/bitcoin"
	"byd_wallet/internal/biz/syncer/chain/evm"
	"byd_wallet/internal/biz/syncer/chain/evm/evm_l2"
	"byd_wallet/internal/biz/syncer/chain/solana"
	"byd_wallet/internal/biz/syncer/chain/tron"
	"byd_wallet/internal/conf"
	"byd_wallet/internal/data"
	"byd_wallet/internal/server"
	"byd_wallet/internal/service/task"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(confServer *conf.Server, confData *conf.Data, logger log.Logger) (*kratos.App, func(), error) {
	db, cleanup, err := data.NewGormDB(confData)
	if err != nil {
		return nil, nil, err
	}
	universalClient, cleanup2, err := data.NewRedisClient(confData)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	dataData := data.NewData(db, universalClient)
	rpcEndpointRepo := data.NewRPCEndpointRepo(dataData)
	blockFetcher := bitcoin.NewBlockFetcher(logger)
	tronBlockFetcher := tron.NewBlockFetcher(logger)
	evmBlockFetcher := evm.NewBlockFetcher(logger)
	evm_l2BlockFetcher := evm_l2.NewBlockFetcher(logger)
	solanaBlockFetcher := solana.NewBlockFetcher(logger)
	blockchainNetworkRepo := data.NewBlockchainNetworkRepo(dataData)
	blockFetchers, cleanup3, err := biz.NewBlockFetchers(rpcEndpointRepo, blockFetcher, tronBlockFetcher, evmBlockFetcher, evm_l2BlockFetcher, solanaBlockFetcher, blockchainNetworkRepo, confServer)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	txProcessor := data.NewTxProcessor(dataData)
	transactionBatcher := data.NewTransactionBatcher(dataData)
	approvalRepo := data.NewApprovalRepo(dataData)
	chainSyncerRepo := data.NewChainSyncerRepo(dataData)
	eventPublisher, cleanup4, err := data.NewEventPublisher(confData)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	tronRentRepo := data.NewTronRentRepo(dataData)
	chainSyncer, err := biz.NewChainSyncer(logger, blockFetchers, txProcessor, transactionBatcher, approvalRepo, chainSyncerRepo, blockchainNetworkRepo, eventPublisher, tronRentRepo, confServer)
	if err != nil {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	solTimerServer, err := server.NewSolTimerServer(logger, chainSyncer)
	if err != nil {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	solanaWebSocketService := task.NewSolanaWebSocketService(db, universalClient, rpcEndpointRepo)
	solanaWebSocketServer := server.NewSolanaWebSocketServer(solanaWebSocketService)
	app := newApp(logger, solTimerServer, solanaWebSocketServer)
	return app, func() {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
	}, nil
}
