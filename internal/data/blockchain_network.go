package data

import (
	"byd_wallet/common"
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"
	"encoding/json"
	"fmt"

	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

type blockchainNetworkRepo struct {
	data *Data
}

func NewBlockchainNetworkRepo(data *Data) biz.BlockchainNetworkRepo {
	return &blockchainNetworkRepo{data}
}

func (repo *blockchainNetworkRepo) ListEVM(ctx context.Context) ([]*model.BlockchainNetwork, error) {
	var list []*model.BlockchainNetwork
	if err := repo.data.DB(ctx).Model(&model.BlockchainNetwork{}).Where("is_evm = true").Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (repo *blockchainNetworkRepo) UpdateSortOrder(ctx context.Context, id uint, current, target int64) error {
	return repo.data.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var ts []*model.BlockchainNetwork
		err := tx.Model(model.BlockchainNetworkStub).Where("sort_order = ?", target).Find(&ts).Error
		if err != nil {
			return err
		}
		switch len(ts) {
		case 0:
			return tx.Model(model.BlockchainNetworkStub).Where("id=? and sort_order=?", id, current).Update("sort_order", target).Error
		case 1:
			s := ts[0]
			err = tx.Model(model.BlockchainNetworkStub).Where("id=? and sort_order=?", s.ID, target).Update("sort_order", current).Error
			if err != nil {
				return err
			}
			return tx.Model(model.BlockchainNetworkStub).Where("id=? and sort_order=?", id, current).Update("sort_order", target).Error
		default:
			return fmt.Errorf("too many blockchain network with sort order %d", target)
		}
	})
}
func (repo *blockchainNetworkRepo) All(ctx context.Context) ([]*model.BlockchainNetwork, error) {
	var list []*model.BlockchainNetwork
	err := repo.data.db.WithContext(ctx).Order("sort_order desc").Find(&list).Error
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (repo *blockchainNetworkRepo) FindByIndex(ctx context.Context, chainIndex int64) (*model.BlockchainNetwork, error) {
	var output model.BlockchainNetwork
	if err := repo.data.DB(ctx).Model(&model.BlockchainNetwork{}).Where("chain_index=?", chainIndex).Take(&output).Error; err != nil {
		return nil, err
	}
	return &output, nil
}

func (repo *blockchainNetworkRepo) ListByFilter(ctx context.Context, filter *biz.BlockchainNetworkFilter) (
	list []*model.BlockchainNetwork, err error) {
	sql := repo.data.db.WithContext(ctx).Model(model.BlockchainNetworkStub)

	if filter.ChainName != "" {
		sql = sql.Where("chain_name=?", filter.ChainName)
	}

	err = sql.Order("sort_order desc").Find(&list).Error
	return
}

func (repo *blockchainNetworkRepo) UpdateByUpdates(ctx context.Context, id uint, updates map[string]interface{}) error {
	return repo.data.db.WithContext(ctx).Model(model.BlockchainNetworkStub).Where("id=?", id).Updates(updates).Error
}

type blockchainNetworkCache struct {
	data *Data
}

func NewBlockchainNetworkCache(data *Data) biz.BlockchainNetworkCache {
	return &blockchainNetworkCache{data}
}

func (b *blockchainNetworkCache) GetChainByIndex(chainIndex int64) (*model.BlockchainNetwork, error) {
	blockchain, _ := b.GetChainByIndexForCache(chainIndex)
	if blockchain != nil {
		return blockchain, nil
	}
	var blockchainNetwork model.BlockchainNetwork
	err := b.data.db.Model(&model.BlockchainNetwork{}).Where("chain_index = ?", chainIndex).First(&blockchainNetwork).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	key := common.GetBlockChainInfoKey(chainIndex)
	blkByte, err := json.Marshal(blockchainNetwork)
	if err != nil {
		return nil, err
	}
	b.data.rd.Set(context.Background(), key, string(blkByte), 0)
	return &blockchainNetwork, nil
}

func (b *blockchainNetworkCache) GetChainByIndexForCache(chainIndex int64) (*model.BlockchainNetwork, error) {
	key := common.GetBlockChainInfoKey(chainIndex)
	str, err := b.data.rd.Get(context.Background(), key).Result()
	if err != nil && err != redis.Nil {
		return nil, err
	}
	var blockchain model.BlockchainNetwork
	err = json.Unmarshal([]byte(str), &blockchain)
	if err != nil {
		return nil, err
	}
	return &blockchain, nil
}
