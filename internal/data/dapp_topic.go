package data

import (
	"byd_wallet/common/lang"
	"byd_wallet/model"
	"context"
	"gorm.io/gorm"
)

func (d *DappRepo) GetDappTopic(ctx context.Context, id uint, dappLimit ...int) (*model.DappTopic, error) {
	language := lang.FromContext(ctx)
	var out model.DappTopic
	if err := d.DB(ctx).Model(&model.DappTopic{}).
		Preload("DappTopicI18Ns", "language = ?", language).
		Preload("DappTopicRels", func(db *gorm.DB) *gorm.DB {
			db = db.Order("dapp_topic_rel.sort_order")
			if len(dappLimit) > 0 && dappLimit[0] > 0 {
				db = db.Limit(dappLimit[0])
			}
			return db
		}).
		Preload("DappTopicRels.Dapp", "show = ?", true).
		Preload("DappTopicRels.Dapp.DappI18Ns", "language = ?", language).
		Preload("DappTopicRels.Dapp.DappBlockchainNetworks.BlockchainNetwork").
		Take(&out, "id = ? AND show = ?", id, true).Error; err != nil {
		return nil, err
	}
	return &out, nil
}
