package data

import (
	"context"
	"errors"
	"os"
	"path/filepath"
	"testing"

	"byd_wallet/model"

	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// newTestDappAdminRepo initializes a test DB and dappAdminRepo for DappCategory related tests.
func newTestDappAdminRepo(t *testing.T) *dappAdminRepo {
	db, err := gorm.Open(sqlite.Open(filepath.Join(os.TempDir(), uuid.New().String()+"_dapp_category.db")), &gorm.Config{})
	assert.NoError(t, err)

	autoMigrate(t, db)

	dataInstance := &Data{db: db}
	return &dappAdminRepo{Data: dataInstance}
}

func TestDappAdminRepo_CreateDappCategory(t *testing.T) {
	repo := newTestDappAdminRepo(t)
	ctx := context.Background()

	category := &model.DappCategory{
		Show: true,
		DappCategoryI18Ns: []*model.DappCategoryI18N{
			{Name: "Test Category EN", Summary: "Test Summary EN", Language: "EN"},
			{Name: "测试分类 CN", Summary: "测试摘要 CN", Language: "CN"},
		},
	}

	err := repo.CreateDappCategory(ctx, category)
	assert.NoError(t, err)
	assert.NotZero(t, category.ID)
	assert.Len(t, category.DappCategoryI18Ns, 2)
	assert.NotZero(t, category.DappCategoryI18Ns[0].ID)
	assert.Equal(t, category.ID, category.DappCategoryI18Ns[0].DappCategoryID)
	assert.NotZero(t, category.DappCategoryI18Ns[1].ID)
	assert.Equal(t, category.ID, category.DappCategoryI18Ns[1].DappCategoryID)

	// Verify by getting
	retrievedCategory, err := repo.GetDappCategory(ctx, category.ID)
	assert.NoError(t, err)
	assert.NotNil(t, retrievedCategory)
	assert.Equal(t, category.Show, retrievedCategory.Show)
	assert.Len(t, retrievedCategory.DappCategoryI18Ns, 2)
}

func TestDappAdminRepo_GetDappCategory(t *testing.T) {
	repo := newTestDappAdminRepo(t)
	ctx := context.Background()

	// 1. Create a category first
	initialCategory := &model.DappCategory{
		Show: true,
		DappCategoryI18Ns: []*model.DappCategoryI18N{
			{Name: "Category For Get Test", Language: "EN"},
		},
	}
	err := repo.CreateDappCategory(ctx, initialCategory)
	assert.NoError(t, err)
	assert.NotZero(t, initialCategory.ID)

	t.Run("Get existing DappCategory", func(t *testing.T) {
		category, err := repo.GetDappCategory(ctx, initialCategory.ID)
		assert.NoError(t, err)
		assert.NotNil(t, category)
		assert.Equal(t, initialCategory.ID, category.ID)
		assert.Equal(t, initialCategory.Show, category.Show)
		assert.Len(t, category.DappCategoryI18Ns, 1)
		assert.Equal(t, "Category For Get Test", category.DappCategoryI18Ns[0].Name)
	})

	t.Run("Get non-existent DappCategory", func(t *testing.T) {
		category, err := repo.GetDappCategory(ctx, 99999) // Non-existent ID
		assert.Error(t, err)
		assert.Nil(t, category)
		assert.True(t, errors.Is(err, gorm.ErrRecordNotFound))
	})
}

func TestDappAdminRepo_UpdateDappCategory(t *testing.T) {
	repo := newTestDappAdminRepo(t)
	ctx := context.Background()

	// 1. Create initial DappCategory with I18N
	initialCategory := &model.DappCategory{
		Show: true,
		DappCategoryI18Ns: []*model.DappCategoryI18N{
			{Name: "Initial Category Name EN", Summary: "Initial Summary EN", Language: "EN"},
			{Name: "初始分类名 CN", Summary: "初始摘要 CN", Language: "CN"},
		},
	}
	err := repo.CreateDappCategory(ctx, initialCategory)
	assert.NoError(t, err)
	assert.NotZero(t, initialCategory.ID)

	t.Run("Successfully update DappCategory and its I18Ns", func(t *testing.T) {
		updatedCategory := &model.DappCategory{}
		retrievedCategory, err := repo.GetDappCategory(ctx, initialCategory.ID)
		assert.NoError(t, err)
		assert.NotNil(t, retrievedCategory)

		copier.Copy(updatedCategory, retrievedCategory)
		updatedCategory.Show = false
		var i18nToUpdate *model.DappCategoryI18N
		for _, i18n := range updatedCategory.DappCategoryI18Ns {
			if i18n.Language == "EN" {
				i18nToUpdate = i18n
				break
			}
		}
		assert.NotNil(t, i18nToUpdate, "EN I18N record not found")
		i18nToUpdate.Name = "Updated Category Name EN"
		i18nToUpdate.Summary = "Updated Summary EN"

		err = repo.UpdateDappCategory(ctx, updatedCategory)
		assert.NoError(t, err)

		verifiedCategory, err := repo.GetDappCategory(ctx, initialCategory.ID)
		assert.NoError(t, err)
		assert.NotNil(t, verifiedCategory)
		assert.Equal(t, false, verifiedCategory.Show)
		foundUpdatedI18N := false
		for _, i18n := range verifiedCategory.DappCategoryI18Ns {
			if i18n.Language == "EN" {
				assert.Equal(t, "Updated Category Name EN", i18n.Name)
				assert.Equal(t, "Updated Summary EN", i18n.Summary)
				foundUpdatedI18N = true
			}
		}
		assert.True(t, foundUpdatedI18N)
	})

	t.Run("Attempt to update DappCategory with ID 0", func(t *testing.T) {
		categoryWithZeroID := &model.DappCategory{Show: true}
		err := repo.UpdateDappCategory(ctx, categoryWithZeroID)
		assert.Error(t, err)
		assert.Equal(t, "DappCategory id should not be zero", err.Error())
	})

	t.Run("Update DappCategory by adding a new I18N", func(t *testing.T) {
		categoryToUpdate, err := repo.GetDappCategory(ctx, initialCategory.ID)
		assert.NoError(t, err)
		newI18N := &model.DappCategoryI18N{Name: "Nouvelle Catégorie FR", Summary: "Résumé FR", Language: "FR"}
		categoryToUpdate.DappCategoryI18Ns = append(categoryToUpdate.DappCategoryI18Ns, newI18N)

		err = repo.UpdateDappCategory(ctx, categoryToUpdate)
		assert.NoError(t, err)

		verifiedCategory, err := repo.GetDappCategory(ctx, initialCategory.ID)
		assert.NoError(t, err)
		assert.Len(t, verifiedCategory.DappCategoryI18Ns, 3)
		foundNewI18N := false
		for _, i18n := range verifiedCategory.DappCategoryI18Ns {
			if i18n.Language == "FR" {
				assert.Equal(t, "Nouvelle Catégorie FR", i18n.Name)
				foundNewI18N = true
			}
		}
		assert.True(t, foundNewI18N)
	})

	t.Run("Update DappCategory by removing an I18N", func(t *testing.T) {
		categoryToUpdate, err := repo.GetDappCategory(ctx, initialCategory.ID)
		assert.NoError(t, err)
		var i18nsToKeep []*model.DappCategoryI18N
		var removedI18NID uint
		for _, i18n := range categoryToUpdate.DappCategoryI18Ns {
			if i18n.Language != "CN" {
				i18nsToKeep = append(i18nsToKeep, i18n)
			} else {
				removedI18NID = i18n.ID
			}
		}
		categoryToUpdate.DappCategoryI18Ns = i18nsToKeep
		assert.NotZero(t, removedI18NID)

		err = repo.UpdateDappCategory(ctx, categoryToUpdate)
		assert.NoError(t, err)

		verifiedCategory, err := repo.GetDappCategory(ctx, initialCategory.ID)
		assert.NoError(t, err)
		assert.Len(t, verifiedCategory.DappCategoryI18Ns, 2) // EN and FR should remain
		cnStillExists := false
		for _, i18n := range verifiedCategory.DappCategoryI18Ns {
			if i18n.Language == "CN" {
				cnStillExists = true
				break
			}
		}
		assert.False(t, cnStillExists, "CN I18N should have been removed")
	})

	t.Run("Update DappCategory without providing DappCategoryRels", func(t *testing.T) {
		// 0. Create a Dapp for relation
		dapp := &model.Dapp{Logo: "logo.png", Link: "https://example.com"}
		err := repo.DB(ctx).Create(dapp).Error
		assert.NoError(t, err)
		assert.NotZero(t, dapp.ID)

		// 1. Create a DappCategory with DappCategoryRels
		categoryWithRels := &model.DappCategory{
			Show: true,
			DappCategoryI18Ns: []*model.DappCategoryI18N{
				{Name: "Category with Rels", Language: "EN"},
			},
			DappCategoryRels: []*model.DappCategoryRel{
				{DappID: dapp.ID, SortOrder: 1},
			},
		}
		err = repo.CreateDappCategory(ctx, categoryWithRels)
		assert.NoError(t, err)
		assert.NotZero(t, categoryWithRels.ID)
		assert.Len(t, categoryWithRels.DappCategoryRels, 1)
		assert.NotZero(t, categoryWithRels.DappCategoryRels[0].ID)

		// Verify Rels are created
		var count int64
		repo.DB(ctx).Model(&model.DappCategoryRel{}).Where("dapp_category_id = ?", categoryWithRels.ID).Count(&count)
		assert.EqualValues(t, 1, count)

		// 2. Get the category, clear its Rels, and update (simulating not providing Rels in an update payload)
		categoryToUpdate, err := repo.GetDappCategory(ctx, categoryWithRels.ID)
		assert.NoError(t, err)
		assert.NotNil(t, categoryToUpdate)

		updatePayload := &model.DappCategory{
			BaseModelNoDeleted: model.BaseModelNoDeleted{ID: categoryToUpdate.ID},
			Show:               false,                              // Change some other field
			DappCategoryI18Ns:  categoryToUpdate.DappCategoryI18Ns, // Keep I18Ns or update them as needed
		}

		err = repo.UpdateDappCategory(ctx, updatePayload)
		assert.NoError(t, err)

		// 3. Verify DappCategoryRels are deleted
		verifiedCategory, err := repo.GetDappCategory(ctx, categoryWithRels.ID)
		assert.NoError(t, err)
		assert.NotNil(t, verifiedCategory)
		assert.False(t, verifiedCategory.Show) // Check the updated field

		repo.DB(ctx).Model(&model.DappCategoryRel{}).Where("dapp_category_id = ?", categoryWithRels.ID).Count(&count)
		assert.EqualValues(t, 1, count)
	})

}

func TestDappAdminRepo_CreateDappCategoryRels(t *testing.T) {
	repo := newTestDappAdminRepo(t)
	db := repo.Data.db
	ctx := context.Background()

	// Need to create a DappCategory and Dapps first for foreign key constraints
	// For simplicity, we'll assume they exist or mock them if direct creation is complex
	// Here, we'll create them directly for a self-contained test.

	// Create a DappCategory
	category := &model.DappCategory{Show: true}
	err := db.Create(category).Error
	assert.NoError(t, err)
	assert.NotZero(t, category.ID)

	// Create Dapps (simplified, assuming Dapp model exists and is simple for this test)
	// In a real scenario, you might need to migrate model.Dapp as well.
	// For this specific test, we only need Dapp IDs.
	// Let's assume Dapp model is simple or we just use IDs.
	// To make this runnable, we should migrate Dapp as well in newTestDappAdminRepo or create them.
	// For now, let's ensure Dapp model is part of migration in newTestDappAdminRepo if not already.
	// Let's add Dapp to newTestDappAdminRepo migration for this test.
	// Modify newTestDappAdminRepo to include &model.Dapp{} in AutoMigrate.

	dapp1 := &model.Dapp{Link: "dapp1.com"} // Add minimal required fields for Dapp
	err = db.Create(dapp1).Error
	assert.NoError(t, err)
	assert.NotZero(t, dapp1.ID)

	dapp2 := &model.Dapp{Link: "dapp2.com"}
	err = db.Create(dapp2).Error
	assert.NoError(t, err)
	assert.NotZero(t, dapp2.ID)

	rels := []*model.DappCategoryRel{
		{DappCategoryID: category.ID, DappID: dapp1.ID, SortOrder: 1},
		{DappCategoryID: category.ID, DappID: dapp2.ID, SortOrder: 2},
	}

	err = repo.CreateDappCategoryRels(ctx, rels)
	assert.NoError(t, err)
	assert.NotZero(t, rels[0].ID)
	assert.NotZero(t, rels[1].ID)

	// Verify creation
	var createdRels []*model.DappCategoryRel
	err = db.Where("dapp_category_id = ?", category.ID).Order("sort_order asc").Find(&createdRels).Error
	assert.NoError(t, err)
	assert.Len(t, createdRels, 2)
	assert.Equal(t, dapp1.ID, createdRels[0].DappID)
	assert.Equal(t, dapp2.ID, createdRels[1].DappID)
}

func TestDappAdminRepo_ListDappCategory(t *testing.T) {
	repo := newTestDappAdminRepo(t)
	ctx := context.Background()

	// 1. Create some DappCategories
	category1 := &model.DappCategory{
		Show: true,
		DappCategoryI18Ns: []*model.DappCategoryI18N{
			{Name: "Category One EN", Language: "EN", Summary: "Summary One EN"},
			{Name: "分类一 CN", Language: "CN", Summary: "摘要一 CN"},
		},
	}
	err := repo.CreateDappCategory(ctx, category1)
	assert.NoError(t, err)
	assert.NotZero(t, category1.ID)

	category2 := &model.DappCategory{
		Show: false,
		DappCategoryI18Ns: []*model.DappCategoryI18N{
			{Name: "Category Two EN", Language: "EN", Summary: "Summary Two EN"},
		},
	}
	err = repo.CreateDappCategory(ctx, category2)
	assert.NoError(t, err)
	assert.NotZero(t, category2.ID)

	category3NoI18n := &model.DappCategory{
		Show: true,
	}
	err = repo.CreateDappCategory(ctx, category3NoI18n)
	assert.NoError(t, err)
	assert.NotZero(t, category3NoI18n.ID)

	t.Run("List all DappCategories", func(t *testing.T) {
		adminCategories, err := repo.ListDappCategory(ctx)
		assert.NoError(t, err)
		assert.Len(t, adminCategories, 3, "Should retrieve all 3 created categories")

		// Verify details of each category
		foundCat1 := false
		foundCat2 := false
		foundCat3 := false

		for _, adminCat := range adminCategories {
			switch adminCat.ID {
			case category1.ID:
				foundCat1 = true
				assert.Equal(t, category1.Show, adminCat.Show)
				assert.Len(t, adminCat.DappCategoryI18Ns, 2)
				// Check one of the I18Ns for correctness
				var foundCat1EN bool
				for _, i18n := range adminCat.DappCategoryI18Ns {
					if i18n.Language == "EN" {
						assert.Equal(t, "Category One EN", i18n.Name)
						foundCat1EN = true
					}
				}
				assert.True(t, foundCat1EN, "EN I18N for category 1 not found or incorrect")
			case category2.ID:
				foundCat2 = true
				assert.Equal(t, category2.Show, adminCat.Show)
				assert.Len(t, adminCat.DappCategoryI18Ns, 1)
				assert.Equal(t, "Category Two EN", adminCat.DappCategoryI18Ns[0].Name)
			case category3NoI18n.ID:
				foundCat3 = true
				assert.Equal(t, category3NoI18n.Show, adminCat.Show)
				assert.Len(t, adminCat.DappCategoryI18Ns, 0, "Category 3 should have no I18N records")
			}
		}

		assert.True(t, foundCat1, "Category 1 not found in list")
		assert.True(t, foundCat2, "Category 2 not found in list")
		assert.True(t, foundCat3, "Category 3 (no I18N) not found in list")
	})

	t.Run("List DappCategories when none exist", func(t *testing.T) {
		// Create a new repo with a fresh in-memory DB for this sub-test
		emptyRepo := newTestDappAdminRepo(t)
		adminCategories, err := emptyRepo.ListDappCategory(ctx)
		assert.NoError(t, err)
		assert.Len(t, adminCategories, 0, "Should return an empty list when no categories exist")
	})
}
