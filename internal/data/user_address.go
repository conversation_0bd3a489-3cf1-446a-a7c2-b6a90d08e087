package data

import (
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"
)

type userAddressRepo struct {
	data *Data
}

func NewUserAddressRepo(data *Data) biz.UserAddressRepo {
	return &userAddressRepo{data}
}

func (repo *userAddressRepo) FindByChainIndexAndAddress(ctx context.Context, chainIndex int64, address string) (*model.UserAddress, error) {
	ua := &model.UserAddress{}
	if err := repo.data.DB(ctx).Model(model.UserAddressStub).
		Where("chain_index = ? AND address = ?", chainIndex, address).
		Take(ua).Error; err != nil {
		return nil, err
	}
	return ua, nil
}

func (repo *userAddressRepo) ListByFilter(ctx context.Context, filter *biz.UserAddressFilter) (list []*model.UserAddress, totalCount int64, err error) {
	sql := repo.data.db.WithContext(ctx).Model(model.UserAddressStub)
	if filter.OrderBy != "" {
		sql = sql.Order(filter.OrderBy)
	}

	if filter.ChainIndex != -1 {
		sql = sql.Where("chain_index=?", filter.ChainIndex)
	}
	if filter.Address != "" {
		sql = sql.Where("address=?", filter.Address)
	}

	if filter.ChainIndex != -1 && filter.Address != "" {
		// unique index
		// chain_index,address
		err = sql.Preload("User").Find(&list).Error
		if err == nil {
			totalCount = int64(len(list))
		}
		return
	}

	if err = sql.Count(&totalCount).Error; err != nil {
		return
	}

	offset := (filter.Page - 1) * filter.PageSize
	err = sql.Offset(int(offset)).Limit(int(filter.PageSize)).Preload("User").Find(&list).Error
	return
}
