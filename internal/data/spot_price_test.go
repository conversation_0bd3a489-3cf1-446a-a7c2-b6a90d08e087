package data

import (
	"byd_wallet/internal/conf"
	"byd_wallet/model"
	"context"
	"os"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func TestNewSpotPriceRepo(t *testing.T) {
	os.Setenv("HTTP_PROXY", "")
	os.Setenv("HTTPS_PROXY", "")

	ctx := context.Background()
	s := assert.New(t)
	dsn := "host=127.0.0.1 user=root password=root dbname=wallet_test port=5432 sslmode=disable"
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	s.NoError(err)

	rd, cf, err := NewRedisClient(&conf.Data{
		Redis: &conf.Data_Redis{
			Addrs: []string{"127.0.0.1:6379"},
		},
	})
	s.NoError(err)
	defer cf()

	s.NoError(db.AutoMigrate(model.APIConfigStub))
	s.NoError(db.Exec("delete from " + model.APIConfigStub.TableName()).Error)
	s.NoError(rd.FlushAll(t.Context()).Err())

	err = db.Create(&model.APIConfig{
		API:    "spot_price",
		Config: []byte(`{"pairs": ["BTCUSDT", "ETHUSDT"]}`),
	}).Error
	s.NoError(err)

	repo, err := NewSpotPriceRepo(log.DefaultLogger, NewData(db, rd))
	s.NoError(err)

	s.NoError(repo.Subscribe(t.Context()))
	defer repo.Unsubscribe(t.Context())
	time.Sleep(time.Second * 3)

	p, err := repo.FindByTradingPair(ctx, "BTCUSDT")
	s.NoError(err)
	s.Equal("BTCUSDT", p.TradingPair)
	s.True(p.Price.IsPositive())
	t.Logf("BTCUSDT: %+v", p)

	ps, err := repo.ListByTradingPairs(ctx, []string{"ETHUSDT"})
	s.NoError(err)
	s.Len(ps, 1)
	p = ps[0]
	s.Equal("ETHUSDT", p.TradingPair)
	s.True(p.Price.IsPositive())
	t.Logf("ETHUSDT: %+v", p)
}
