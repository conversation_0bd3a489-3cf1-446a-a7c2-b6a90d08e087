package data

import (
	"byd_wallet/model"
	"context"
	"errors"
	"gorm.io/gorm"
)

func (d *DappRepo) ListDappIndex(ctx context.Context, dappCategoryLimit, dappTopicLimit int) ([]any, error) {
	var indexes []model.DappIndex
	if err := d.DB(ctx).Model(&model.DappIndex{}).Order("sort_order DESC").Find(&indexes).Error; err != nil {
		return nil, err
	}
	var result []any
	for _, idx := range indexes {
		switch idx.OwnerType {
		case model.DappCategory{}.TableName():
			data, err := d.GetDappCategoryForApp(ctx, idx.OwnerID, dappCategoryLimit)
			if err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					continue
				}
				return nil, err
			}
			result = append(result, data)
		case model.DappTopic{}.TableName():
			data, err := d.GetDappTopic(ctx, idx.OwnerID, dappTopicLimit)
			if err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					continue
				}
				return nil, err
			}
			result = append(result, data)
		}
	}
	return result, nil
}
