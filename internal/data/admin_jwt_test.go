package data

import (
	"byd_wallet/internal/conf"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestAdminJwt(t *testing.T) {
	s := assert.New(t)

	aj, err := NewAdminJwt(&conf.Data{
		Adminjwt: &conf.Data_Jwt{
			PrivKey: `-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VwBCIEIOjPmaSpAAs+hIayXHSjaEVboXe5Zm/pxGxTkCRe27E5
-----END PRIVATE KEY-----`,
			Expires: "30m",
		},
	})

	if !s.NoError(err) {
		return
	}

	adminID := uint(3218)
	jtStr, err := aj.GenerateJwtToken(adminID)
	if !s.NoError(err) {
		return
	}

	t.Log(jtStr)

	pAdminID, err := aj.VerifyJwtToken(jtStr)
	if !s.NoError(err) {
		return
	}

	s.Equal(adminID, pAdminID)

}
