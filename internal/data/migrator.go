package data

import (
	"byd_wallet/internal/conf"
	"byd_wallet/model"
	"context"

	"gorm.io/gorm"
)

type Migrator struct {
	*gorm.DB
}

func NewMigrator(c *conf.Data) (*Migrator, func(), error) {
	db, cleanup, err := NewGormDB(c)
	return &Migrator{db}, cleanup, err
}

func (m Migrator) AutoMigrate(ctx context.Context) error {
	if err := m.migrate(ctx); err != nil {
		return err
	}
	return m.migrateTxs(ctx)
}

func (m Migrator) migrate(ctx context.Context) error {
	return m.DB.WithContext(ctx).AutoMigrate(
		&model.BlockchainNetwork{},
		&model.TronRentRecord{},
		&model.TronRentConfig{},
		model.CoinInfoStub,
		model.CoinLogoStub,
		model.APIConfigStub,
		&model.Dapp{},
		&model.DappI18N{},
		&model.DappBlockchainNetwork{},
		&model.DappCategory{},
		&model.DappCategoryRel{},
		&model.DappCategoryI18N{},
		&model.DappTopic{},
		&model.DappTopicRel{},
		&model.DappTopicI18N{},
		&model.DappNavigation{},
		&model.DappIndex{},
		model.TokenAssetStub,
		model.TokenAssetRankStub,
		model.TokenAssetStarStub,
		model.AdminStub,
		&model.MissingBlock{},
		&model.Approval{},
		model.RPCEndpointStub,
		&model.User{},
		&model.UserAddress{},
		&model.MissTransaction{},
		model.UserHoldTokenStub,
		&model.SwapChannel{},
		&model.SwappableBlockchainNetwork{},
		&model.SwappableToken{},
		&model.SwapRecord{},
		&model.SwapDetail{},
		&model.SwappableTokenChannel{},
		&model.SwappableHotToken{},
		&model.SwapConfig{},
		model.GasPoolDepositAddressStub,
		model.GasPoolDepositTokenStub,
		model.GasPoolStub,
		model.GasPoolFlowStub,
		model.GasPoolSponsorTxStub,
		&model.AppVersion{},
		&model.AppVersionI18N{},
	)
}

func (m Migrator) migrateTxs(ctx context.Context) error {
	var networks []model.BlockchainNetwork
	if err := m.DB.WithContext(ctx).Model(&model.BlockchainNetwork{}).Find(&networks).Error; err != nil {
		return err
	}
	for _, network := range networks {
		tableName := network.TxTableName()
		if err := m.DB.WithContext(ctx).Table(tableName).AutoMigrate(&model.Transaction{}); err != nil {
			return err
		}
	}
	return nil
}
