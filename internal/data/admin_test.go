package data

import (
	"byd_wallet/model"
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func TestAdmin(t *testing.T) {
	s := assert.New(t)

	// init data
	dsn := "host=127.0.0.1 user=root password=root dbname=wallet_test port=5432 sslmode=disable"
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if !s.NoError(err) {
		return
	}

	if !s.NoError(db.AutoMigrate(
		model.AdminStub,
	)) {
		return
	}

	if !s.NoError(db.Exec("truncate table "+model.AdminStub.TableName()).Error) {
		return
	}

	// logic
	repo := NewAdminRepo(&Data{
		db: db,
	})

	// admin not exist
	admin, err := repo.GetByUsernameAndPassword(context.Background(), "123123", "321hjbcvdfz")
	if !s.NoError(err) {
		return
	}
	if !s.Nil(admin) {
		return
	}

	insertAdmin := &model.Admin{
		Username: "bhjasghzxc",
		Password: "chajshjd",
	}

	if !s.NoError(db.Model(model.AdminStub).Create(insertAdmin).Error) {
		return
	}

	// admin exist
	admin, err = repo.GetByUsernameAndPassword(context.Background(), insertAdmin.Username, insertAdmin.Password)
	if !s.NoError(err) {
		return
	}
	if !s.NotNil(admin) {
		return
	}
	s.Equal(insertAdmin.Username, admin.Username)
	s.Equal(insertAdmin.Password, admin.Password)
}
