package data

import (
	"byd_wallet/internal/biz/dapp"
	"byd_wallet/model"
	"context"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (d *dappAdminRepo) ListTopicDapp(ctx context.Context, topicID uint) ([]*model.Dapp, error) {
	var out []*model.Dapp
	var dappTopic model.DappTopic
	if err := d.DB(ctx).Model(&model.DappTopic{}).
		Preload("DappTopicRels", func(db *gorm.DB) *gorm.DB {
			return db.Order("dapp_topic_rel.sort_order desc")
		}).
		Preload("DappTopicRels.Dapp").
		Preload("DappTopicRels.Dapp.DappI18Ns").
		Preload("DappTopicRels.Dapp.DappBlockchainNetworks.BlockchainNetwork").
		Take(&dappTopic, topicID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return out, nil
		}
		return nil, err
	}
	for _, rel := range dappTopic.DappTopicRels {
		if rel.Dapp == nil {
			continue
		}
		out = append(out, rel.Dapp)
	}

	return out, nil
}

func (d *dappAdminRepo) CreateDappTopic(ctx context.Context, topic *model.DappTopic) error {
	return d.DB(ctx).Create(topic).Error
}

func (d *dappAdminRepo) DeleteDappTopic(ctx context.Context, id uint) error {
	m := &model.DappTopic{}
	m.ID = id
	return d.DB(ctx).Delete(m).Error
}

func (d *dappAdminRepo) CreateDappTopicRel(ctx context.Context, rel *model.DappTopicRel) error {
	return d.DB(ctx).Create(rel).Error
}

func (d *dappAdminRepo) LastDappTopicRelSortOrder(ctx context.Context, topicID uint) (int, error) {
	var out model.DappTopicRel
	if err := d.DB(ctx).Model(&model.DappTopicRel{}).
		Where("dapp_topic_id = ?", topicID).
		Order("sort_order DESC").
		First(&out).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, nil
		}
		return 0, err
	}
	return out.SortOrder, nil
}

func (d *dappAdminRepo) DeleteDappTopicRel(ctx context.Context, topicID, dappID uint) error {
	return d.DB(ctx).Delete(&model.DappTopicRel{}, "dapp_topic_id=? AND dapp_id=?", topicID, dappID).Error
}

// GetDappTopic 获取单个DAPP专题
func (d *dappAdminRepo) GetDappTopic(ctx context.Context, id uint) (*model.DappTopic, error) {
	var out model.DappTopic
	if err := d.DB(ctx).Model(&model.DappTopic{}).
		Preload("DappTopicI18Ns").
		Take(&out, id).Error; err != nil {
		return nil, err
	}
	return &out, nil
}

// UpdateDappTopic 更新DAPP专题
func (d *dappAdminRepo) UpdateDappTopic(ctx context.Context, m *model.DappTopic) error {
	var dappTopic model.DappTopic
	if err := d.DB(ctx).Model(&model.DappTopic{}).Where("id = ?", m.ID).Take(&dappTopic).Error; err != nil {
		return err
	}
	m.CreatedAt = dappTopic.CreatedAt
	m.UpdatedAt = time.Now()
	return d.DB(ctx).Session(&gorm.Session{FullSaveAssociations: true}).Save(m).Error
}

// ListDappTopic 查询DAPP专题列表
func (d *dappAdminRepo) ListDappTopic(ctx context.Context) ([]*dapp.AdminDappTopic, error) {
	var topics []*model.DappTopic
	if err := d.DB(ctx).Model(&model.DappTopic{}).
		Preload("DappTopicI18Ns").
		Order("id DESC").
		Find(&topics).Error; err != nil {
		return nil, err
	}

	var adminTopics []*dapp.AdminDappTopic
	for _, topic := range topics {
		adminTopic := &dapp.AdminDappTopic{
			ID:             topic.ID,
			Show:           topic.Show,
			BackgroundUrl:  topic.BackgroundUrl,
			DappTopicI18Ns: topic.DappTopicI18Ns,
			CreatedAt:      topic.CreatedAt.Unix(),
		}

		// Calculate AppCount - 计算专题下的dapp数量
		var appCount int64
		if err := d.DB(ctx).Model(&model.DappTopicRel{}).Where("dapp_topic_id = ?", topic.ID).Count(&appCount).Error; err != nil {
			return nil, err
		}
		adminTopic.AppCount = int(appCount)

		adminTopics = append(adminTopics, adminTopic)
	}

	return adminTopics, nil
}
