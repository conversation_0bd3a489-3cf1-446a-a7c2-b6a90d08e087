package data

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"os"
	"path/filepath"
	"testing"
	"time"
)

func newTestSwapDB(t *testing.T) *gorm.DB {
	db, err := gorm.Open(sqlite.Open(filepath.Join(os.TempDir(), uuid.New().String())), &gorm.Config{})
	assert.NoError(t, err)
	return db
}

func migrateSwapTables(t *testing.T, db *gorm.DB) {
	err := db.AutoMigrate(
		&model.BlockchainNetwork{},
		&model.SwappableBlockchainNetwork{},
		&model.TokenAsset{},
		&model.SwappableToken{},
		&model.SwapChannel{},
		&model.SwapRecord{},
		&model.SwapDetail{},
		&model.SwappableHotToken{},
		&model.SwappableTokenChannel{},
	)
	assert.NoError(t, err)
}

func TestSwapRepo(t *testing.T) {
	db := newTestSwapDB(t)
	migrateSwapTables(t, db)
	repo := &swapRepo{
		Data: &Data{
			db: db,
		},
	}
	channel := &model.SwapChannel{
		Name:   "test",
		Enable: true,
	}
	db.Create(channel)
	db.Create([]*model.BlockchainNetwork{
		{Name: "test1", ChainIndex: 1},
		{Name: "test2", ChainIndex: 2},
		{Name: "test3", ChainIndex: 3},
	})
	db.Create([]*model.SwappableBlockchainNetwork{
		{BlockchainNetworkID: 1, SwapChannelID: channel.ID},
		{BlockchainNetworkID: 3, SwapChannelID: channel.ID},
	})
	ctx := context.Background()
	list, err := repo.ListBlockchainNetwork(ctx)
	assert.NoError(t, err)
	assert.Len(t, list, 2)
	assert.Equal(t, "test1", list[0].Name)
	assert.Equal(t, "test3", list[1].Name)

	db.Create([]*model.TokenAsset{
		{ChainIndex: 0, Address: "aaa"},
		{ChainIndex: 123, Address: "bbb"},
		{ChainIndex: 222, Address: "ccc"},
		{ChainIndex: 222, Address: "ddd", Symbol: "USDTaaa"},
	})
	db.Create([]*model.SwappableToken{
		{TokenAssetID: 1, ChainIndex: 222, Enable: true, Display: true},
		{TokenAssetID: 3, ChainIndex: 123},
		{TokenAssetID: 4, ChainIndex: 222, Enable: true, Display: true},
	})
	db.Create([]*model.SwappableHotToken{
		{SwappableTokenID: 1, ChainIndex: 222, SortOrder: 1},
		{SwappableTokenID: 2, ChainIndex: 123, SortOrder: 2},
		{SwappableTokenID: 3, ChainIndex: 222, SortOrder: 3},
	})
	tokens, err := repo.ListToken(ctx, constant.AllChainIndex)
	assert.NoError(t, err)
	assert.Len(t, tokens, 3)
	assert.Equal(t, "aaa", tokens[0].Address)
	assert.Equal(t, "ccc", tokens[1].Address)
	tokens, err = repo.ListToken(ctx, 222)
	assert.NoError(t, err)
	assert.Len(t, tokens, 2)
	assert.Equal(t, "ccc", tokens[0].Address)
	assert.Equal(t, "ddd", tokens[1].Address)

	hotTokens, err := repo.ListHotTokens(ctx, 222)
	assert.NoError(t, err)
	assert.Len(t, hotTokens, 2)
	for _, token := range hotTokens {
		assert.NotNil(t, token.SwappableToken)
		assert.NotNil(t, token.SwappableToken.TokenAsset)
	}

	tokens, err = repo.FilterToken(ctx, biz.SwapTokenFilter{
		ChainIndex: -1,
		SearchKey:  "USD",
	})
	assert.NoError(t, err)
	assert.Len(t, tokens, 1)

	record := &model.SwapRecord{
		Hash:             "123",
		SwapChannelID:    channel.ID,
		SwappedAt:        time.Now(),
		Status:           "pending",
		FromTokenAssetID: 1,
		ToTokenAssetID:   2,
		Details: []*model.SwapDetail{
			{
				ChainIndex:  60,
				ExplorerURL: "",
				Hash:        "test1",
				Status:      "pending",
			},
		},
		FinishedAt: nil,
	}
	err = repo.CreateSwapRecord(ctx, record)
	assert.NoError(t, err)
	record2, err := repo.GetSwapRecord(ctx, record.ID)
	assert.NoError(t, err)
	assert.Len(t, record2.Details, 1)
	exist, err := repo.ExistsSwapRecord(ctx, channel.ID, record.Hash)
	assert.NoError(t, err)
	assert.True(t, exist)
}
