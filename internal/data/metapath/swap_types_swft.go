package metapath

import "encoding/json"

type SWFTSwapResultData struct {
	TxData *SWFTSwapTxData `json:"txData"`
}

type SWFTSwapTxData struct {
	Data               string      `json:"data"`
	OrderId            string      `json:"orderId"`
	SwftCoinFeeRate    string      `json:"swftCoinFeeRate"`
	XrpInfo            interface{} `json:"xrpInfo"`
	BtcMemo            interface{} `json:"btcMemo"`
	DepositCoinAmt     string      `json:"depositCoinAmt"`
	IsDiscount         string      `json:"isDiscount"`
	OrderState         string      `json:"orderState"`
	DeveloperId        string      `json:"developerId"`
	DepositCoinCode    string      `json:"depositCoinCode"`
	KycUrl             string      `json:"kycUrl"`
	DealFinishTime     interface{} `json:"dealFinishTime"`
	ChainFee           string      `json:"chainFee"`
	DepositCoinFeeAmt  string      `json:"depositCoinFeeAmt"`
	RefundDepositTxid  string      `json:"refundDepositTxid"`
	DepositFeeRate     string      `json:"depositFeeRate"`
	FromAddress        string      `json:"fromAddress"`
	RefundSwftAmt      string      `json:"refundSwftAmt"`
	DepositTxid        string      `json:"depositTxid"`
	ReceiveCoinCode    string      `json:"receiveCoinCode"`
	Value              string      `json:"value"`
	ReceiveCoinAmt     string      `json:"receiveCoinAmt"`
	NoGasTxInfo        interface{} `json:"noGasTxInfo"`
	SwftCoinState      string      `json:"swftCoinState"`
	TxInfo             interface{} `json:"txInfo"`
	ToType             string      `json:"toType"`
	SwftRefundAddr     string      `json:"swftRefundAddr"`
	DetailState        string      `json:"detailState"`
	BurnRate           string      `json:"burnRate"`
	ChangeType         string      `json:"changeType"`
	DepositCoinFeeRate string      `json:"depositCoinFeeRate"`
	TransactionId      string      `json:"transactionId"`
	OrderModel         string      `json:"orderModel"`
	RefundAddr         string      `json:"refundAddr"`
	CreateTime         string      `json:"createTime"`
	ChoiseFeeType      string      `json:"choiseFeeType"`
	DestinationAddr    string      `json:"destinationAddr"`
	PlatformAddr       string      `json:"platformAddr"`
	To                 string      `json:"to"`
	InstantRate        string      `json:"instantRate"`
	RefundCoinMinerFee string      `json:"refundCoinMinerFee"`
	RefundCoinAmt      string      `json:"refundCoinAmt"`
	ReceiveSwftAmt     string      `json:"receiveSwftAmt"`
	DepositCoinState   string      `json:"depositCoinState"`
	SwftReceiveAddr    string      `json:"swftReceiveAddr"`
	ApproveAddress     string      `json:"approveAddress"`
}

func (s SWFTSwapTxData) ToAddress() string {
	if s.To != "" {
		return s.To
	}
	return s.PlatformAddr
}

func (s SWFTSwapTxData) GetTransferData() *TransferData {
	return nil
}

func (s SWFTSwapTxData) IsValidate() bool {
	return s.OrderId != ""
}

func (r *SwapResponse) GetSWFTSwapTxData() (*SWFTSwapTxData, error) {
	var result SWFTSwapResultData
	err := json.Unmarshal(r.Data, &result)
	return result.TxData, err
}
