package metapath

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"testing"
)

func TestClient_GetTransactionDetail(t *testing.T) {
	client := NewClient(&Config{
		BaseURL: "https://api-swap.paths.finance",
		Debug:   true,
	}, log.DefaultLogger)

	// 8527022000000000000
	// 创建上下文
	ctx := context.Background()

	client.GetTransactionDetail(ctx, &GetTransactionDetailRequest{
		Hash: "3cZcjmttEZkWFWhtEKMhHf9j2NxreoUViH1XfZAt2Lwxnx9QLJzqR5VJVLyQaL55wpNRj3m5HyyXL9zEnDdpLGzP",
	})
}
