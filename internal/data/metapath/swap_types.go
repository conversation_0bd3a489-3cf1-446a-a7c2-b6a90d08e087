package metapath

import (
	"encoding/json"
	"github.com/shopspring/decimal"
)

type SwapResult interface {
	GetTransferData() *TransferData
	IsValidate() bool
}

// SwapRequest Swap接口请求结构
type SwapRequest struct {
	// 必填字段
	EquipmentNo      string            `json:"equipmentNo" validate:"required"`      // 设备号
	FromTokenAddress string            `json:"fromTokenAddress" validate:"required"` // 原始代币合约地址
	ToTokenAddress   string            `json:"toTokenAddress" validate:"required"`   // 目标代币合约地址
	FromAddress      string            `json:"fromAddress" validate:"required"`      // 发送方地址
	ToAddress        string            `json:"toAddress" validate:"required"`        // 接收方地址
	AmountOutMin     decimal.Decimal   `json:"amountOutMin"`                         // 最小接收数量
	RouterPath       []any             `json:"routerPath"`                           // 交换路由
	Amounts          []decimal.Decimal `json:"amounts"`                              // 最小接收数量数组
	Dex              string            `json:"dex" validate:"required"`              // 交换平台名称
	FromTokenChain   string            `json:"fromTokenChain" validate:"required"`   // 原始链
	ToTokenChain     string            `json:"toTokenChain" validate:"required"`     // 目标链
	FromTokenAmount  decimal.Decimal   `json:"fromTokenAmount"`                      // 原始代币数量
	Slippage         decimal.Decimal   `json:"slippage"`                             // 滑点
	Source           string            `json:"source" validate:"required"`           // 通道唯一ID

	// 可选字段
	IsNoGas      *bool  `json:"isNoGas,omitempty"`      // 是否免gas（仅SWFT通道有效）
	MidTokenCode string `json:"midTokenCode,omitempty"` // 仅Glue通道有效
}

func (r SwapRequest) IsSameChain() bool {
	return r.FromTokenChain == r.ToTokenChain
}

func (r SwapRequest) IsSameSolanaTx() bool {
	return r.IsSameChain() && r.FromTokenChain == SOLANA
}

func (r SwapRequest) IsSolanaTx() bool {
	return r.FromTokenChain == SOLANA || r.ToTokenChain == SOLANA
}

func (r SwapRequest) IsSameTRONTx() bool {
	return r.IsSameChain() && r.FromTokenChain == TRON
}

func (r SwapRequest) IsTRONTx() bool {
	return r.FromTokenChain == TRON || r.ToTokenChain == TRON
}

// SwapResponse Swap接口响应结构
type SwapResponse struct {
	BaseResponse
}

type CommonSwapResultData struct {
	TxData CommonSwapTxData `json:"txData"`
}

type CommonSwapTxData struct {
	Data any `json:"data"`
}

// IsStringOfTxData 交易数据data是否为字符串
func (r *SwapResponse) IsStringOfTxData() bool {
	var result CommonSwapResultData
	if err := json.Unmarshal(r.Data, &result); err != nil {
		return true
	}

	switch result.TxData.Data.(type) {
	case string:
		return true
	default:
		return false
	}
}

type TransferData struct {
	FromChainId      int    `json:"fromChainId"`
	FromTokenAmount  string `json:"fromTokenAmount"`
	ToChainId        int    `json:"toChainId"`
	FromAddress      string `json:"fromAddress"`
	FromTokenAddress string `json:"fromTokenAddress"`
	Timestamp        int64  `json:"timestamp"`
}
