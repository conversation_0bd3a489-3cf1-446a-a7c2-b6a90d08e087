package metapath

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/shopspring/decimal"
	"net/http"
)

// MultiQuoteRequest 多重报价请求结构
type MultiQuoteRequest struct {
	// 必填字段
	EquipmentNo      string          `json:"equipmentNo" validate:"required"`      // 设备号
	FromTokenAddress string          `json:"fromTokenAddress" validate:"required"` // 原始代币合约地址
	UserAddr         string          `json:"userAddr"`                             // 发送方地址
	ToTokenAddress   string          `json:"toTokenAddress" validate:"required"`   // 目标代币合约地址
	ToAddress        string          `json:"toAddress"`                            // 接收方地址
	FromTokenChain   string          `json:"fromTokenChain" validate:"required"`   // 原始链
	ToTokenChain     string          `json:"toTokenChain" validate:"required"`     // 目标链
	FromTokenAmount  decimal.Decimal `json:"fromTokenAmount"`                      // 原始代币数量
	Slippage         decimal.Decimal `json:"slippage"`                             // 滑点
	Source           string          `json:"source" validate:"required"`           // 通道唯一ID

	// 可选字段
	IsNoGas      *bool  `json:"isNoGas,omitempty"`      // 是否免gas（仅SWFT通道有效）
	MidTokenCode string `json:"midTokenCode,omitempty"` // 仅Glue通道有效
}

// MultiQuoteResponse 多重报价响应结构
type MultiQuoteResponse struct {
	BaseResponse
}

// PriceInquiryData 价格查询数据
type PriceInquiryData struct {
	TxData []RouteInfo `json:"txData"`
}

func (p PriceInquiryData) PrettyString() string {
	b, _ := json.MarshalIndent(p.TxData, "", "	")
	return string(b)
}

// RouteInfo 路由信息
type RouteInfo struct {
	// 基础信息
	FromTokenAmount    decimal.Decimal `json:"fromTokenAmount"`    // 原始代币数量，乘以小数位
	FromTokenDecimal   int             `json:"fromTokenDecimal"`   // 原始代币小数位
	ToTokenAmount      decimal.Decimal `json:"toTokenAmount"`      // 目标代币数量，除以小数位
	ToTokenDecimal     int             `json:"toTokenDecimal"`     // 目标代币小数位
	Dex                string          `json:"dex"`                // 交换平台
	Fee                decimal.Decimal `json:"fee"`                // 交易费用，与原始代币相同
	FeeToken           string          `json:"feeToken"`           // 交易费用结算代币符号
	ReceiveTokenAmount decimal.Decimal `json:"receiveTokenAmount"` // 预估目标代币接收数量，除以小数位
	LogoUrl            string          `json:"logoUrl"`            // 交换平台logo
	EstimatedTime      int             `json:"estimatedTime"`      // 预估时间：1(1-3分钟)，2(1-10分钟)，3(5-30分钟)
	Path               []any           `json:"path"`               // 交换路径列表

	// 存款限制（仅有存款范围的通道可用）
	DepositMax decimal.Decimal `json:"depositMax,omitempty"` // 最大原始代币
	DepositMin decimal.Decimal `json:"depositMin,omitempty"` // 最小原始代币

	// KYC信息（仅当有与KYC配额相关的通道时可用）
	KycInfo *KycInfo `json:"kycInfo,omitempty"` // KYC信息

	// SWFT通道特有
	IsSupportNoGas *bool `json:"isSupportNoGas,omitempty"` // 是否支持免gas（仅SWFT通道显示）

	// 特定通道地址
	ContractAddress string `json:"contractAddress,omitempty"` // 仅Bridgers和Aggregator通道显示，交换地址
	ApproveAddress  string `json:"approveAddress,omitempty"`  // 仅Glue通道显示，授权地址
	DexAddress      string `json:"dexAddress,omitempty"`      // 仅Glue通道显示，交换地址

	// 费用详情
	AmountOutMin     decimal.Decimal `json:"amountOutMin,omitempty"`     // 最小输出数量
	FromAmountOutMin decimal.Decimal `json:"fromAmountOutMin,omitempty"` // 原始代币最小输出数量
	ChainFee         decimal.Decimal `json:"chainFee,omitempty"`         // 链费用
	BridgeFee        decimal.Decimal `json:"bridgeFee,omitempty"`        // 跨链费用
	BridgeFeeToken   string          `json:"bridgeFeeToken,omitempty"`   // 跨链费用代币
	GasFee           decimal.Decimal `json:"gasFee,omitempty"`           // Gas费用
	GasFeeToken      string          `json:"gasFeeToken,omitempty"`      // Gas费用代币

	// Glue通道特有
	MidDex         string          `json:"midDex,omitempty"`         // 中间DEX
	MidDexLogo     string          `json:"midDexLogo,omitempty"`     // 中间DEX logo
	MidTokenCode   string          `json:"midTokenCode,omitempty"`   // 中间代币代码
	MidTokenAmount decimal.Decimal `json:"midTokenAmount,omitempty"` // 中间代币数量

	// 显示信息
	DexDisplayName string `json:"dexDisplayName,omitempty"` // DEX显示名称
	DexName        string `json:"dexName,omitempty"`        // DEX名称
	DexStatus      string `json:"dexStatus,omitempty"`      // DEX状态
	FromChain      string `json:"fromChain,omitempty"`      // 原始链
	ToChain        string `json:"toChain,omitempty"`        // 目标链

	// 跨链详情
	CrossDetails *CrossDetails `json:"crossDetails,omitempty"` // 跨链详情
}

func (r RouteInfo) CalcAmountOutMin() decimal.Decimal {
	// amountOutMin高了，agg渠道价格价格变化较频繁，最好你们询价的结果再下降2个点对调兑换
	if r.Dex == Aggregator {
		return r.AmountOutMin.Mul(decimal.NewFromFloat(0.98)).Truncate(0)
	}
	return r.AmountOutMin
}

// KycInfo KYC信息
type KycInfo struct {
	CnUrl  string `json:"cnUrl"`
	EnUrl  string `json:"enUrl"`
	IsDapp bool   `json:"isDapp"`
	Status bool   `json:"status"`
}

// CrossDetails 跨链详情
type CrossDetails struct {
	From CrossDetailItem `json:"from"` // 原始链详情
	To   CrossDetailItem `json:"to"`   // 目标链详情
}

// CrossDetailItem 跨链详情项
type CrossDetailItem struct {
	Path any    `json:"path"` // 路径
	Dex  string `json:"dex"`  // DEX
}

// GetDataAsPriceInquiry 获取data字段作为价格查询结果
func (r *MultiQuoteResponse) GetDataAsPriceInquiry() (*PriceInquiryData, error) {
	var result PriceInquiryData
	err := json.Unmarshal(r.Data, &result)
	return &result, err
}

// MultiQuote 获取多重报价
func (c *Client) MultiQuote(ctx context.Context, req *MultiQuoteRequest) ([]RouteInfo, error) {
	// 参数验证
	if err := ValidateStruct(req); err != nil {
		return nil, fmt.Errorf("参数验证失败: %w", err)
	}

	var response MultiQuoteResponse
	if err := c.call(ctx, http.MethodPost, "/api/multiQuote", req, &response); err != nil {
		return nil, err
	}
	if err := response.Error(); err != nil {
		return nil, err
	}
	data, err := response.GetDataAsPriceInquiry()
	if err != nil {
		return nil, err
	}
	return data.TxData, nil
}
