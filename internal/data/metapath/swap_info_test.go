package metapath

import (
	"context"
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
)

func TestGetSwapInfoList(t *testing.T) {
	// 创建客户端，使用真实的MetaPath API
	logger := log.DefaultLogger
	client := NewClient(&Config{
		BaseURL: "https://api-swap.paths.finance",
		Debug:   true,
	}, logger)

	// 创建上下文
	ctx := context.Background()

	// 测试1：获取交换信息列表
	t.Run("GetSwapInfoList with valid address", func(t *testing.T) {
		resp, err := client.GetSwapInfoList(ctx, &GetSwapInfoListRequest{
			FromAddress: "0xF5a4170C83D9267B9344eb284D3fC31329e72843", // 使用文档中的示例地址
			PageSize:    5,
			PageNo:      1,
		})

		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.Equal(t, float64(100), resp.ResCode)
		assert.Equal(t, "success", resp.ResMsg)

		// 解析交换信息列表数据
		data, err := resp.GetDataAsSwapInfoList()
		assert.NoError(t, err)
		assert.NotNil(t, data)

		// 打印结果用于验证
		t.Logf("总记录数: %d", data.Total)
		t.Logf("当前页码: %d", data.PageNo)
		t.Logf("当前页记录数: %d", len(data.List))

		// 打印前几条记录的详细信息
		for i, item := range data.List {
			if i >= 3 { // 只打印前3条
				break
			}
			t.Logf("记录 %d: %s -> %s, 状态: %s, 时间: %s",
				i+1, item.FromTokenSymbol, item.ToTokenSymbol, item.Status, item.CreateTime)
		}
	})

	// 测试2：参数验证
	t.Run("GetSwapInfoList with invalid parameters", func(t *testing.T) {
		// 测试空地址
		_, err := client.GetSwapInfoList(ctx, &GetSwapInfoListRequest{
			FromAddress: "",
			PageSize:    5,
			PageNo:      1,
		})
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "FromAddress")

		// 测试无效页面大小
		_, err = client.GetSwapInfoList(ctx, &GetSwapInfoListRequest{
			FromAddress: "0xF5a4170C83D9267B9344eb284D3fC31329e72843",
			PageSize:    0,
			PageNo:      1,
		})
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "PageSize")

		// 测试无效页码
		_, err = client.GetSwapInfoList(ctx, &GetSwapInfoListRequest{
			FromAddress: "0xF5a4170C83D9267B9344eb284D3fC31329e72843",
			PageSize:    5,
			PageNo:      0,
		})
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "PageNo")
	})

	// 测试3：带交易哈希的查询
	t.Run("GetSwapInfoList with txHash", func(t *testing.T) {
		resp, err := client.GetSwapInfoList(ctx, &GetSwapInfoListRequest{
			FromAddress: "0xF5a4170C83D9267B9344eb284D3fC31329e72843",
			PageSize:    5,
			PageNo:      1,
			TxHash:      "0xb299ba2f7fb2c038244b76c227b70deaf8706ce88bec3732f6833ce92a457696", // 使用文档中的示例哈希
		})

		if err != nil {
			t.Logf("带交易哈希的查询返回错误: %v", err)
		} else {
			t.Logf("响应码: %d, 消息: %s", resp.ResCode, resp.ResMsg)
			if resp.ResCode == 100 {
				data, parseErr := resp.GetDataAsSwapInfoList()
				if parseErr == nil {
					t.Logf("查询到 %d 条记录", len(data.List))
				}
			}
		}
	})
}
