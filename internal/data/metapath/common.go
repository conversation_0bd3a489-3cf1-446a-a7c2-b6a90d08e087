package metapath

import (
	"encoding/json"
	"errors"
	"strings"

	"github.com/go-playground/validator/v10"
)

// 全局验证器实例
var validate *validator.Validate

func init() {
	validate = validator.New()
}

// BaseResponse 基础响应结构
type BaseResponse struct {
	ResCode interface{}     `json:"resCode"` // 响应码，可能是int或string
	ResMsg  string          `json:"resMsg"`  // 响应消息
	Data    json.RawMessage `json:"data"`    // 响应数据
}

func (r *BaseResponse) Error() error {
	if !r.IsSuccess() {
		data, _ := r.GetDataAsString()
		if data != "" {
			return errors.New(data)
		}
		return errors.New(r.ResMsg)
	}
	return nil
}

// IsSuccess 检查响应是否成功
func (r *BaseResponse) IsSuccess() bool {
	switch code := r.ResCode.(type) {
	case int:
		return code == 100
	case string:
		return code == "100"
	case float64:
		return code == 100
	default:
		return false
	}
}

// GetDataAsString 获取data字段作为字符串（错误情况）
func (r *BaseResponse) GetDataAsString() (string, error) {
	if r.Data == nil {
		return "", nil
	}
	var str string
	err := json.Unmarshal(r.Data, &str)
	return str, err
}

// ValidateStruct 验证结构体
func ValidateStruct(s interface{}) error {
	return validate.Struct(s)
}

// GenerateEquipmentNo 根据地址生成设备号
// 如果地址长度小于等于32，则用'x'填充到32位
// 如果地址长度大于32，则截取前32位
func GenerateEquipmentNo(address string) string {
	if len(address) <= 32 {
		n := 32 - len(address)
		equipmentNo := strings.Repeat("x", n) + address
		return equipmentNo
	}
	return address[:32]
}

func encodePath(path []any) string {
	if path == nil {
		return ""
	}
	r, _ := json.Marshal(path)
	return string(r)
}

func decodePath(path string) []any {
	if path == "" || path == "[]" {
		return nil
	}
	var r []any
	_ = json.Unmarshal([]byte(path), &r)
	return r
}
