package metapath

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func TestAddSwapInfo(t *testing.T) {
	// 这是一个集成测试示例，实际使用时需要有效的API端点
	config := &Config{
		BaseURL: "https://api-swap.paths.finance",
		Debug:   true,
	}

	logger := log.DefaultLogger
	client := NewClient(config, logger)

	req := &AddSwapInfoRequest{
		EquipmentNo:      "0x18fc9d463b1e40bd0ed7c1c1d79513",
		FromTokenAddress: "0xeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee",
		ToTokenAddress:   "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",
		FromAddress:      "0x18fc9d463b1e40bd0ed7c1c1d79513af1b72db98",
		ToAddress:        "TV21rLfdCU9kdQciTKStA9wkCieQtA9tcn",
		FromTokenAmount:  decimal.RequireFromString("6351589000000000000"),
		Slippage:         decimal.NewFromInt(1),
		FromChain:        "BSC",
		ToChain:          "TRON",
		Hash:             "0x574f4fe5908740034da25e0adca87bfb26f7e20b83bf3f6721a6a4c6b5e5bb79",
		ToTokenAmount:    decimal.RequireFromString("1315.139317"),
		DexName:          "Bridgers",
		IsNoGas:          "0",
		Source:           "globalm",
		ToTokenAmountWD:  "1315139317",
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 注意：这个测试需要真实的API端点才能通过
	// 在实际环境中，你可能需要模拟HTTP响应或使用测试服务器
	data, err := client.AddSwapInfo(ctx, req)
	assert.NoError(t, err)
	t.Logf("Success data: %+v", data)
}
