package metapath

import "encoding/json"

type EvmSwapResultData struct {
	TxData *EvmSwapTxData `json:"txData"`
}

type EvmSwapTxData struct {
	TransferData   *TransferData `json:"transferData"`
	ApproveAddress string        `json:"approveAddress"`
	ToType         string        `json:"toType"`
	Data           string        `json:"data"`
	Gas            string        `json:"gas"`
	From           string        `json:"from"`
	To             string        `json:"to"`
	Value          string        `json:"value"`
	GasPrice       string        `json:"gasPrice"`
	PlatformAddr   string        `json:"platformAddr"`
}

func (e EvmSwapTxData) GetTransferData() *TransferData {
	return e.TransferData
}

func (e EvmSwapTxData) IsValidate() bool {
	return e.ToType != ""
}

func (r *SwapResponse) GetEvmSwapTxData() (*EvmSwapTxData, error) {
	var result EvmSwapResultData
	err := json.Unmarshal(r.Data, &result)
	return result.TxData, err
}
