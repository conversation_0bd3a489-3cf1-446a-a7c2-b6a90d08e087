package metapath

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestSwapResponse_IsStringOfTxData(t *testing.T) {
	type fields struct {
		BaseResponse BaseResponse
	}
	tests := []struct {
		name   string
		fields fields
		want   bool
	}{
		{
			name: "evm",
			fields: fields{
				BaseResponse: BaseResponse{
					Data: []byte(`{"txData":{"transferData":{"fromChainId":1,"fromTokenAmount":"10000000000000000","toChainId":999999,"fromAddress":"0x1cAbA0e8Fa24Da977E1ed97392e628d6C3B0D43a","fromTokenAddress":"0xeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee","timestamp":1753672730338},"approveAddress":"0x12179f77a02522c01f6d3a5d38a1a5d2b9834f19","toType":"2","data":"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","gas":"","from":"0x1cAbA0e8Fa24Da977E1ed97392e628d6C3B0D43a","to":"0x12179f77a02522c01f6d3a5d38a1a5d2b9834f19","value":"0x2386f26fc10000","gasPrice":""}}`),
				},
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &SwapResponse{
				BaseResponse: tt.fields.BaseResponse,
			}
			assert.Equalf(t, tt.want, r.IsStringOfTxData(), "IsStringOfTxData()")
		})
	}
}
