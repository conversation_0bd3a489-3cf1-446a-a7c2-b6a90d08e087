package metapath

import (
	"context"
	"encoding/json"
	"net/http"
)

// GetTokenListRequest 获取代币列表请求参数
type GetTokenListRequest struct {
	Chain string `json:"chain,omitempty"` // 可选，指定区块链名称
}

// TokenInfo 代币信息
type TokenInfo struct {
	Symbol        string `json:"symbol"`        // 代币符号
	Name          string `json:"name"`          // 代币名称
	Address       string `json:"address"`       // 代币合约地址
	Decimals      int    `json:"decimals"`      // 代币精度
	LogoURI       string `json:"logoURI"`       // 代币图标URI
	IsCrossEnable bool   `json:"isCrossEnable"` // 是否支持跨链
}

// GetTokenListResponse 获取代币列表响应
type GetTokenListResponse struct {
	BaseResponse
}

// TokenListData 代币列表数据（当没有chain参数时）
type TokenListData struct {
	Tokens      map[string][]TokenInfo `json:"tokens"`      // 按链分组的代币列表
	QuoteSwitch bool                   `json:"quoteSwitch"` // 价格查询开关
}

// ChainTokenListData 单链代币列表数据（当有chain参数时）
type ChainTokenListData struct {
	Tokens      []TokenInfo `json:"tokens"`      // 代币列表
	QuoteSwitch bool        `json:"quoteSwitch"` // 价格查询开关
}

// GetDataAsTokenList 将data字段解析为代币列表数据（无chain参数）
func (r *GetTokenListResponse) GetDataAsTokenList() (*TokenListData, error) {
	var data TokenListData
	err := json.Unmarshal(r.Data, &data)
	return &data, err
}

// GetDataAsChainTokenList 将data字段解析为单链代币列表数据（有chain参数）
func (r *GetTokenListResponse) GetDataAsChainTokenList() (*ChainTokenListData, error) {
	var data ChainTokenListData
	err := json.Unmarshal(r.Data, &data)
	return &data, err
}

// GetTokenList 获取支持的链和对应的代币信息
// API地址: https://api-swap.paths.finance/api/getBaseInfo
func (c *Client) GetTokenList(ctx context.Context, req *GetTokenListRequest) (*GetTokenListResponse, error) {
	var response GetTokenListResponse
	if err := c.call(ctx, http.MethodGet, "/api/getBaseInfo", req, &response); err != nil {
		return nil, err
	}
	if err := response.Error(); err != nil {
		return nil, err
	}
	return &response, nil
}

func (c *Client) ListAllToken(ctx context.Context) (*TokenListData, error) {
	response, err := c.GetTokenList(ctx, &GetTokenListRequest{})
	if err != nil {
		return nil, err
	}
	data, err := response.GetDataAsTokenList()
	if err != nil {
		return nil, err
	}

	return data, nil
}

func (c *Client) ListTokenByChain(ctx context.Context, chain string) (*ChainTokenListData, error) {
	response, err := c.GetTokenList(ctx, &GetTokenListRequest{Chain: chain})
	if err != nil {
		return nil, err
	}
	data, err := response.GetDataAsChainTokenList()
	if err != nil {
		return nil, err
	}

	return data, nil
}
