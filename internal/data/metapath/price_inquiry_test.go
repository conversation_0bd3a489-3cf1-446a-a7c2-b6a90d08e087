package metapath

import (
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestRouteInfo_CalcAmountOutMin(t *testing.T) {
	type fields struct {
		Dex          string
		AmountOutMin decimal.Decimal
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{
			name: "Aggregator",
			fields: fields{
				Dex:          "Aggregator",
				AmountOutMin: decimal.NewFromFloat(100),
			},
			want: "98",
		},
		{
			name: "Other",
			fields: fields{
				Dex:          "XX",
				AmountOutMin: decimal.NewFromFloat(100),
			},
			want: "100",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := RouteInfo{
				Dex:          tt.fields.Dex,
				AmountOutMin: tt.fields.AmountOutMin,
			}
			assert.Equalf(t, tt.want, r.CalcAmountOutMin().String(), "CalcAmountOutMin()")
		})
	}
}
