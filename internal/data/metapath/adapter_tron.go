package metapath

import (
	"byd_wallet/internal/biz/syncer/chain/tron"
	"encoding/json"
	"github.com/fbsobreira/gotron-sdk/pkg/proto/core"
)

type tronAdapter struct {
	tronCli  *tron.RoundRobinClient
	tronABIs map[string]*core.SmartContract_ABI
}

func newTronAdapter(tronCli *tron.RoundRobinClient) *tronAdapter {
	return &tronAdapter{tronCli: tronCli, tronABIs: make(map[string]*core.SmartContract_ABI)}
}

func (t *tronAdapter) client() *tron.GRPCClient {
	return t.tronCli.Last()
}

func (t *tronAdapter) GetContractABIJSON(contractAddr string) (string, error) {
	tronCli := t.client()
	abi, ok := t.tronABIs[contractAddr]
	if !ok {
		scABI, err := tronCli.GetContractABI(contractAddr)
		if err != nil {
			return "", err
		}
		abi = scABI
		t.tronABIs[contractAddr] = abi
	}

	jsonB, err := json.Marshal(&abi.Entrys)
	if err != nil {
		return "", err
	}
	return string(jsonB), nil
}
