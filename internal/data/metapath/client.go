package metapath

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/go-querystring/query"
)

// Client MetaPath API客户端
type Client struct {
	log    *log.Helper
	config *Config
}

// Config MetaPath API配置
type Config struct {
	BaseURL  string `json:"base_url"`
	Source   string `json:"source"`
	Debug    bool   `json:"debug"`
	ProxyURL string `json:"proxy_url"`
}

// NewClient 创建新的MetaPath API客户端
func NewClient(conf *Config, logger log.Logger) *Client {
	return &Client{
		log:    log.NewHelper(logger),
		config: conf,
	}
}

func (c *Client) call(ctx context.Context, method, reqPath string, req, reply any) error {
	reqURL := c.config.BaseURL + reqPath

	var (
		queryStr string
		body     []byte
	)
	if req != nil {
		switch method {
		case http.MethodGet:
			vs, err := query.Values(req)
			if err != nil {
				return fmt.Errorf("failed to encode query string: %w", err)
			}
			queryStr = "?" + vs.Encode()
			reqURL += queryStr
		case http.MethodPost:
			bts, err := json.Marshal(req)
			if err != nil {
				return fmt.Errorf("failed to encode request body: %w", err)
			}
			body = bts
		default:
			return fmt.Errorf("unsupported method: %s", method)
		}
	}

	httpReq, err := http.NewRequestWithContext(ctx, method, reqURL, bytes.NewBuffer(body))
	if err != nil {
		return fmt.Errorf("failed to create http request: %w", err)
	}
	headers := http.Header{}
	headers.Set("Content-Type", "application/json")
	headers.Set("accept", "application/json")
	httpReq.Header = headers

	// 创建 HTTP 客户端并配置代理
	cli := &http.Client{}
	if c.config.ProxyURL != "" {
		proxyURL, err := url.Parse(c.config.ProxyURL)
		if err != nil {
			return fmt.Errorf("failed to parse proxy URL: %w", err)
		}
		cli.Transport = &http.Transport{
			Proxy: http.ProxyURL(proxyURL),
		}
		if c.config.Debug {
			c.log.Debugf("using proxy: %s", c.config.ProxyURL)
		}
	}

	resp, err := cli.Do(httpReq)
	if err != nil {
		return fmt.Errorf("failed to send http request: %w", err)
	}
	defer resp.Body.Close()

	respBodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}
	if c.config.Debug {
		c.log.Debugf("metapath api request: url: %s \n req: %s \n resp: %s", reqURL, body, respBodyBytes)
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("metapath API request failed with status code %d", resp.StatusCode)
	}

	if err := json.Unmarshal(respBodyBytes, reply); err != nil {
		return fmt.Errorf("failed to unmarshal response body: %w", err)
	}

	return nil
}
