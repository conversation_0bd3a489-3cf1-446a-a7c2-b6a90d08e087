package metapath

import (
	"byd_wallet/model"
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/shopspring/decimal"
)

// GetTransactionDetailRequest 获取交易详细信息请求参数
type GetTransactionDetailRequest struct {
	Hash string `json:"hash" validate:"required"` // 交易哈希（必填）
}

// GetTransactionDetailResponse 获取交易详细信息响应
type GetTransactionDetailResponse struct {
	BaseResponse
}

// TransactionDetailData 交易详细信息数据
type TransactionDetailData struct {
	// 基础信息
	TxHash           string          `json:"txHash"`           // 交易哈希
	FromTokenAddress string          `json:"fromTokenAddress"` // 原始代币合约地址
	FromTokenSymbol  string          `json:"fromTokenSymbol"`  // 原始代币符号
	FromTokenUrl     string          `json:"fromTokenUrl"`     // 原始代币logo地址
	ToTokenAddress   string          `json:"toTokenAddress"`   // 目标合约地址
	ToTokenSymbol    string          `json:"toTokenSymbol"`    // 目标代币符号
	ToTokenUrl       string          `json:"toTokenUrl"`       // 目标代币logo地址
	FromAmount       decimal.Decimal `json:"fromAmount"`       // 原始代币数量，除以小数位
	ToTokenAmount    decimal.Decimal `json:"toTokenAmount"`    // 目标代币数量，除以小数位
	FinalStatus      string          `json:"finalStatus"`      // 当前状态：pending：处理中，success：成功，fail：失败，wait_kyc：等待kyc，wait_refund：等待退款，refund_complete：退款完成，wait_deposit_send_fail：存币失败，timeout：超时未存币，wait_for_information：异常订单
	CreateTime       string          `json:"createTime"`       // 创建时间
	CreateTimeT      int64           `json:"createTimeT"`      // 创建时间戳

	// 链信息
	FromTokenChain string `json:"fromTokenChain"` // 原始代币链
	ToTokenChain   string `json:"toTokenChain"`   // 目标代币链

	// 地址信息
	ReceiveAddress string `json:"receiveAddress"` // 接收地址
	RefundAddress  string `json:"refundAddress"`  // 退款地址

	// 交换信息
	DexName       string `json:"dexName"`       // 交换平台名称
	LogoUrl       string `json:"logoUrl"`       // 交换平台logo
	EstimatedTime int    `json:"estimatedTime"` // 预估时间：1(1-3分钟)，2(1-10分钟)，3(5-30分钟)
	IsNoGas       string `json:"isNoGas"`       // 是否免gas：0(使用)；1(不使用)

	// 交换状态，第一个元素是原始链交换状态，第二个元素是目标链交换状态
	Status []TransactionStatus `json:"status"`
}

func (t TransactionDetailData) LocalSwapStatus() model.SwapStatus {
	return model.SwapStatus(t.FinalStatus)
}

func (t TransactionDetailData) FromChainIndex() (int64, error) {
	return chainName2Index(t.FromTokenChain)
}

func (t TransactionDetailData) ToChainIndex() (int64, error) {
	return chainName2Index(t.ToTokenChain)
}

func chainName2Index(chainName string) (int64, error) {
	chainIndex, ok := chainNameToIndex[chainName]
	if !ok {
		return 0, fmt.Errorf("chain %s not exist", chainName)
	}
	return chainIndex, nil
}

// TransactionStatus 交易状态
type TransactionStatus struct {
	Chain  string `json:"chain"`  // 链名称
	TxHash string `json:"txHash"` // 交易哈希
	Url    string `json:"url"`    // 区块链浏览器URL
	Status string `json:"status"` // 状态
}

// GetDataAsTransactionDetail 将data字段解析为交易详细信息数据
func (r *GetTransactionDetailResponse) GetDataAsTransactionDetail() (*TransactionDetailData, error) {
	var data TransactionDetailData
	err := json.Unmarshal(r.Data, &data)
	return &data, err
}

// GetTransactionDetail 获取交易详细信息
// API地址: https://api-swap.paths.finance/api/exchangeRecord/getTransDetail
func (c *Client) GetTransactionDetail(ctx context.Context, req *GetTransactionDetailRequest) (*TransactionDetailData, error) {
	if req == nil {
		return nil, fmt.Errorf("request cannot be nil")
	}

	// 参数验证
	if err := ValidateStruct(req); err != nil {
		return nil, fmt.Errorf("参数验证失败: %w", err)
	}

	var response GetTransactionDetailResponse
	if err := c.call(ctx, http.MethodPost, "/api/exchangeRecord/getTransDetail", req, &response); err != nil {
		return nil, err
	}
	if err := response.Error(); err != nil {
		return nil, err
	}
	data, err := response.GetDataAsTransactionDetail()
	if err != nil {
		return nil, err
	}

	return data, nil
}
