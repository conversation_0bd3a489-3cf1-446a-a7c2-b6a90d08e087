package metapath

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestEncodePath(t *testing.T) {
	path := []any{"0xdac17f958d2ee523a2206206994597c13d831ec7", "0xdac17f958d2ee523a2206206994597c13d831ec7", 123.1}
	str := encodePath(path)
	assert.Equal(t, `["0xdac17f958d2ee523a2206206994597c13d831ec7","0xdac17f958d2ee523a2206206994597c13d831ec7",123.1]`, str)
	path2 := decodePath(str)
	assert.Equal(t, path, path2)

	path3 := decodePath("[]")
	assert.Nil(t, path3)
	//assert.Equal(t, []any{}, path3)

	path4 := decodePath("")
	assert.Nil(t, path4)
}

func TestGenerateEquipmentNo(t *testing.T) {
	tests := []struct {
		name     string
		address  string
		expected string
	}{
		{
			name:     "空地址",
			address:  "",
			expected: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", // 32个x
		},
		{
			name:     "短地址需要填充",
			address:  "abc123",
			expected: "xxxxxxxxxxxxxxxxxxxxxxxxxxabc123", // 26个x + abc123
		},
		{
			name:     "正好32位地址",
			address:  "12345678901234567890123456789012",
			expected: "12345678901234567890123456789012",
		},
		{
			name:     "超过32位地址需要截取",
			address:  "123456789012345678901234567890123456789",
			expected: "12345678901234567890123456789012", // 前32位
		},
		{
			name:     "单个字符",
			address:  "a",
			expected: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxa", // 31个x + a
		},
		{
			name:     "31位地址",
			address:  "1234567890123456789012345678901",
			expected: "x1234567890123456789012345678901", // 1个x + 31位地址
		},
		{
			name:     "33位地址",
			address:  "123456789012345678901234567890123",
			expected: "12345678901234567890123456789012", // 截取前32位
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GenerateEquipmentNo(tt.address)
			if result != tt.expected {
				t.Errorf("GenerateEquipmentNo(%q) = %q, expected %q", tt.address, result, tt.expected)
			}

			// 验证结果长度总是32位
			if len(result) != 32 {
				t.Errorf("GenerateEquipmentNo(%q) length = %d, expected 32", tt.address, len(result))
			}
		})
	}
}

// 基准测试
func BenchmarkGenerateEquipmentNo(b *testing.B) {
	testCases := []string{
		"",
		"abc123",
		"12345678901234567890123456789012",
		"123456789012345678901234567890123456789",
	}

	for _, address := range testCases {
		b.Run("len_"+string(rune(len(address))), func(b *testing.B) {
			for i := 0; i < b.N; i++ {
				GenerateEquipmentNo(address)
			}
		})
	}
}
