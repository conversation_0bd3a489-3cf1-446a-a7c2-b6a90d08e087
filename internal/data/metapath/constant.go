package metapath

const (
	ETH      = "ETH"
	SOLANA   = "SOLANA"
	BSC      = "BSC"
	POLYGON  = "POLYGON"
	BASE     = "BASE"
	ARBITRUM = "ARBITRUM"
	OPTIMISM = "OPTIMISM"
	TRON     = "TRON"
)

const (
	ToTypeCommonAddr         = "0"
	ToTypeCommonAddrHaveData = "1"
	ToTypeContractHaveData   = "2"
)

// NativeTokenAddress 原始代币token地址
const NativeTokenAddress = "******************************************"

const (
	Bridgers   = "Bridgers"
	Aggregator = "Aggregator"
	SWFT       = "SWFT"
	SunSwapV2  = "sunSwapV2"
)
