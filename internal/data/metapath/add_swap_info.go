package metapath

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/shopspring/decimal"
)

// AddSwapInfoRequest 添加交换信息请求结构
type AddSwapInfoRequest struct {
	// 必填字段
	EquipmentNo      string          `json:"equipmentNo" validate:"required"`       // 设备ID
	FromTokenAddress string          `json:"fromTokenAddress" validate:"required"`  // 原始代币合约地址
	ToTokenAddress   string          `json:"toTokenAddress" validate:"required"`    // 目标代币合约地址
	FromAddress      string          `json:"fromAddress" validate:"required"`       // 发送方地址
	ToAddress        string          `json:"toAddress" validate:"required"`         // 接收方地址
	FromTokenAmount  decimal.Decimal `json:"fromTokenAmount"`                       // 原始代币数量(带小数)
	Slippage         decimal.Decimal `json:"slippage"`                              // 最大滑点，%
	FromChain        string          `json:"fromChain" validate:"required"`         // 原始链
	ToChain          string          `json:"toChain" validate:"required"`           // 目标链
	Hash             string          `json:"hash" validate:"required"`              // 交易哈希
	ToTokenAmount    decimal.Decimal `json:"toTokenAmount"`                         // 目标代币数量，除以小数位
	DexName          string          `json:"dexName" validate:"required"`           // 交换平台名称
	IsNoGas          string          `json:"isNoGas" validate:"required,oneof=0 1"` // 是否使用无gas：0(使用)；1(不使用)

	// 可选字段
	MidTokenCode    string `json:"midTokenCode,omitempty"`    // 仅Glue通道，通用交换请求参数midTokenCode
	ToTokenAmountWD string `json:"toTokenAmountWD,omitempty"` // 仅Glue通道，目标代币数量(带小数)
	OrderId         string `json:"orderId,omitempty"`         // 订单ID，如果没有填写Null
	OrderType       string `json:"orderType,omitempty"`       // 订单类型，如没有填写null字符串
	TransferData    string `json:"transferData,omitempty"`    // 交换自定义流程信息，如果没有填写null
	Source          string `json:"source,omitempty"`          // 通道的唯一ID
}

// AddSwapInfoResponse 添加交换信息响应结构
type AddSwapInfoResponse struct {
	BaseResponse
}

// GetDataAsObject 获取data字段作为对象（成功情况）
func (r *AddSwapInfoResponse) GetDataAsObject() (*AddSwapInfoResultData, error) {
	var dataObj AddSwapInfoResultData
	err := json.Unmarshal(r.Data, &dataObj)
	return &dataObj, err
}

// AddSwapInfoResultData 交换信息结果数据
type AddSwapInfoResultData struct {
	TxHash           string                  `json:"txHash"`           // 交易哈希
	FromTokenAddress string                  `json:"fromTokenAddress"` // 原始代币合约地址
	FromTokenSymbol  string                  `json:"fromTokenSymbol"`  // 原始代币符号
	FromTokenUrl     string                  `json:"fromTokenUrl"`     // 原始代币logo地址
	ToTokenAddress   string                  `json:"toTokenAddress"`   // 目标合约地址
	ToTokenSymbol    string                  `json:"toTokenSymbol"`    // 目标代币符号
	ToTokenUrl       string                  `json:"toTokenUrl"`       // 目标代币logo地址
	FromAmount       decimal.Decimal         `json:"fromAmount"`       // 原始代币数量，除以小数位
	ToTokenAmount    decimal.Decimal         `json:"toTokenAmount"`    // 目标代币数量，除以小数位
	FinalStatus      string                  `json:"finalStatus"`      // 当前状态：pending：处理中，success：成功，fail：失败，wait_kyc：等待kyc，wait_refund：等待退款，refund_complete：退款完成
	CreateTime       string                  `json:"createTime"`       // 创建时间
	Status           []SwapTransactionStatus `json:"status"`           // 交换状态，第一个元素是原始链交换状态，第二个元素是目标链交换状态
}

// SwapTransactionStatus 交换交易状态
type SwapTransactionStatus struct {
	Chain  string `json:"chain"`  // 链名称
	TxHash string `json:"txHash"` // 交易哈希
	Url    string `json:"url"`    // 区块链浏览器URL
	Status string `json:"status"` // 状态
}

// AddSwapInfo 添加交换信息
func (c *Client) AddSwapInfo(ctx context.Context, req *AddSwapInfoRequest) (*AddSwapInfoResultData, error) {
	// 参数验证
	if err := ValidateStruct(req); err != nil {
		return nil, fmt.Errorf("参数验证失败: %w", err)
	}

	var response AddSwapInfoResponse
	if err := c.call(ctx, http.MethodPost, "/api/exchangeRecord/addTransData", req, &response); err != nil {
		return nil, err
	}

	if err := response.Error(); err != nil {
		return nil, err
	}
	data, err := response.GetDataAsObject()
	if err != nil {
		return nil, err
	}

	return data, nil
}
