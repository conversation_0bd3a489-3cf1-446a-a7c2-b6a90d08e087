package metapath

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
)

// GetSwapInfoListRequest 获取交换信息列表请求参数
type GetSwapInfoListRequest struct {
	FromAddress string `json:"fromAddress" validate:"required"`            // 用户地址（必填）
	PageSize    int    `json:"pageSize" validate:"required,min=1,max=100"` // 每页数量（必填）
	PageNo      int    `json:"pageNo" validate:"required,min=1"`           // 页码（必填）
	TxHash      string `json:"txHash,omitempty"`                           // 交易哈希（可选）
}

// SwapInfoItem 交换信息项
type SwapInfoItem struct {
	FromTokenSymbol  string `json:"fromTokenSymbol"`  // 原始代币符号
	FromAmount       string `json:"fromAmount"`       // 原始代币数量
	FromTokenUrl     string `json:"fromTokenUrl"`     // 原始代币logo地址
	FromTokenAddress string `json:"fromTokenAddress"` // 原始代币合约地址
	FromTokenChain   string `json:"fromTokenChain"`   // 原始代币链
	ToTokenAddress   string `json:"toTokenAddress"`   // 目标合约地址
	ToTokenAmount    string `json:"toTokenAmount"`    // 目标代币数量
	ToTokenSymbol    string `json:"toTokenSymbol"`    // 目标代币符号
	ToTokenUrl       string `json:"toTokenUrl"`       // 目标代币logo地址
	ToTokenChain     string `json:"toTokenChain"`     // 目标代币链
	TxHash           string `json:"txHash"`           // 交易哈希
	Status           string `json:"status"`           // 当前状态
	CreateTime       string `json:"createTime"`       // 创建时间
	CreateTimeT      int64  `json:"createTimeT"`      // 创建时间戳
	ReleaseStatus    bool   `json:"releaseStatus"`    // 释放状态
}

// SwapInfoListData 交换信息列表数据
type SwapInfoListData struct {
	Total  int            `json:"total"`  // 总数
	PageNo int            `json:"pageNo"` // 页码
	List   []SwapInfoItem `json:"list"`   // 交换信息列表
}

// GetSwapInfoListResponse 获取交换信息列表响应
type GetSwapInfoListResponse struct {
	BaseResponse
}

// GetDataAsSwapInfoList 将data字段解析为交换信息列表数据
func (r *GetSwapInfoListResponse) GetDataAsSwapInfoList() (*SwapInfoListData, error) {
	var data SwapInfoListData
	err := json.Unmarshal(r.Data, &data)
	return &data, err
}

// GetSwapInfoList 获取交换信息列表
// API地址: https://api-swap.paths.finance/api/exchangeRecord/getTransData
func (c *Client) GetSwapInfoList(ctx context.Context, req *GetSwapInfoListRequest) (*GetSwapInfoListResponse, error) {
	if req == nil {
		return nil, fmt.Errorf("request cannot be nil")
	}

	// 参数验证
	if err := ValidateStruct(req); err != nil {
		return nil, fmt.Errorf("参数验证失败: %w", err)
	}

	var response GetSwapInfoListResponse
	if err := c.call(ctx, http.MethodPost, "/api/exchangeRecord/getTransData", req, &response); err != nil {
		return nil, err
	}
	
	return &response, nil
}
