package metapath

import (
	"context"
	"encoding/json"
	"github.com/ethereum/go-ethereum/common"
	"github.com/fbsobreira/gotron-sdk/pkg/address"
	"github.com/gagliardetto/solana-go"
	"os"
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
)

func TestGetTokenList(t *testing.T) {
	// 创建客户端，使用真实的MetaPath API
	logger := log.NewStdLogger(nil)
	client := NewClient(&Config{
		BaseURL: "https://api-swap.paths.finance",
	}, logger)

	// 创建上下文
	ctx := context.Background()

	// 测试1：无chain参数，获取所有链的代币列表
	t.Run("ListAllToken without chain parameter", func(t *testing.T) {
		data, err := client.ListAllToken(ctx)
		assert.NoError(t, err)
		file, err := os.Create("../../../tmp/token_list.json")
		assert.NoError(t, err)
		err = json.NewEncoder(file).Encode(data)
		assert.NoError(t, err)

		// 检查是否有代币数据
		assert.NotEmpty(t, data.Tokens)
		t.Logf("获取到 %d 个链的代币信息", len(data.Tokens))

		// 打印部分结果用于验证
		for chainName, tokens := range data.Tokens {
			for _, token := range tokens {
				if token.Address == "" || token.Address == NativeTokenAddress {
					continue
				}
				var standardAddress string
				switch chainName {
				case ETH, BSC, POLYGON, BASE, ARBITRUM, OPTIMISM:
					standardAddress = common.HexToAddress(token.Address).Hex()
				case SOLANA:
					standardAddress = solana.MustPublicKeyFromBase58(token.Address).String()
				case TRON:
					addr, err := address.Base64ToAddress(token.Address)
					assert.NoError(t, err)
					standardAddress = addr.String()
				default:
					continue
				}
				assert.Equal(t, token.Address, standardAddress, "chain_name", chainName)
			}

			t.Logf("链 %s 有 %d 个代币", chainName, len(tokens))
			if len(tokens) > 0 {
				t.Logf("第一个代币: %s (%s)", tokens[0].Symbol, tokens[0].Name)
			}
			break // 只打印第一个链的信息
		}
	})

	// 测试2：有chain参数，获取特定链的代币列表
	t.Run("ListAllToken with chain parameter", func(t *testing.T) {
		data, err := client.ListTokenByChain(ctx, "ETH")
		assert.NoError(t, err)
		assert.NotNil(t, data)

		// 检查代币列表
		assert.NotEmpty(t, data.Tokens)
		t.Logf("ETH链获取到 %d 个代币", len(data.Tokens))

		// 打印前几个代币信息
		for i, token := range data.Tokens {
			if i >= 3 { // 只打印前3个
				break
			}
			t.Logf("代币 %d: %s (%s) - %s", i+1, token.Symbol, token.Name, token.Address)
		}
	})

}
