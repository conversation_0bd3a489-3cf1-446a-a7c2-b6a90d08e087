package data

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz"
	"byd_wallet/internal/biz/syncer/chain/evm"
	"byd_wallet/internal/biz/syncer/chain/solana"
	"byd_wallet/internal/biz/syncer/chain/tron"
	"context"
	"fmt"
)

func NewEvmChainClient(rpcRepo biz.RPCEndpointRepo) (*evm.MultiChainClient, func(), error) {
	evmChainIndexes := []int64{
		constant.EthChainIndex,
		constant.BaseChainIndex,
		constant.ArbChainIndex,
		constant.OptimismChainIndex,
		constant.PolChainIndex,
		constant.BscChainIndex,
	}
	evmRpcs, err := rpcRepo.ListRPCEndpointsByChainIndexes(context.Background(), evmChainIndexes, "http")
	if err != nil {
		return nil, nil, err
	}
	evmCli, err := evm.NewMultiChainClient(evmRpcs)
	if err != nil {
		return nil, nil, err
	}

	return evmCli, func() {
		evmCli.Close()
	}, nil
}

func NewTronChainClient(rpcRepo biz.RPCEndpointRepo) (*tron.RoundRobinClient, func(), error) {
	rpcs, err := rpcRepo.GetRpcListByChainIndexForCache(constant.TronChainIndex, "http")
	if err != nil {
		return nil, nil, fmt.Errorf("GetRpcListByChainIndexForCache: tron: %w", err)
	}
	tronCli, err := tron.NewRoundRobinClient(rpcs)
	if err != nil {
		return nil, nil, err
	}
	return tronCli, func() {
		tronCli.Close()
	}, nil
}

func NewSolanaChainClient(rpcRepo biz.RPCEndpointRepo) (*solana.SmartNodeSelectionClient, func(), error) {
	rpcs, err := rpcRepo.GetRpcListByChainIndexForCache(constant.SolChainIndex, "http")
	if err != nil {
		return nil, nil, fmt.Errorf("GetRpcListByChainIndexForCache: sol: %w", err)
	}
	solCli, cf, err := solana.NewSmartNodeSelectionClient(rpcs)
	if err != nil {
		return nil, nil, err
	}
	return solCli, cf, nil
}
