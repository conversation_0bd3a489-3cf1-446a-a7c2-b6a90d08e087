package data

import (
	v1 "byd_wallet/api/wallet/v1"
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz"
	"byd_wallet/internal/biz/base"
	"byd_wallet/internal/data/metapath"
	"byd_wallet/model"
	"context"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

func NewSwapAdminRepo(data *Data) biz.SwapAdminRepo {
	return &swapAdminRepo{Data: data}
}

type swapAdminRepo struct {
	*Data
	mpCli *metapath.Client
}

func (s *swapAdminRepo) GetOriginSwapRecordByHash(ctx context.Context, channelID uint, hash string) (*model.SwapRecord, error) {
	var channel model.SwapChannel
	if err := s.DB(ctx).Model(&model.SwapChannel{}).Where("id=?", channelID).Take(&channel).Error; err != nil {
		return nil, err
	}
	var record *model.SwapRecord
	switch channel.Name {
	case model.SwapChannelNameMetapath:
		data, err := s.mpCli.GetTransactionDetail(ctx, &metapath.GetTransactionDetailRequest{Hash: hash})
		if err != nil {
			return nil, err
		}
		s.DB(ctx).Model(&model.TokenAsset{})
		record = &model.SwapRecord{
			SwapChannelID:    channelID,
			SwappedAt:        time.UnixMilli(data.CreateTimeT),
			Status:           data.LocalSwapStatus(),
			FromTokenAssetID: 0,
			FromTokenAsset:   nil,
			ToTokenAssetID:   0,
			ToTokenAsset:     nil,
			FromTokenAmount:  "",
			ToTokenAmount:    "",
			FromAddress:      "",
			ToAddress:        "",
			GasFee:           "",
			FeeRate:          "",
			Hash:             "",
			ApprovalHash:     "",
			BlockNumber:      0,
			Dex:              "",
			DexLogo:          "",
			SwapPrice:        "",
			Details:          nil,
			FinishedAt:       nil,
		}
	default:
		return nil, fmt.Errorf("swap channel name %s not exist", channel.Name)
	}
	return record, nil
}

func (s *swapAdminRepo) GetSwapRecord(ctx context.Context, id uint) (*model.SwapRecord, error) {
	var out model.SwapRecord
	if err := s.DB(ctx).Model(&model.SwapRecord{}).Take(&out, id).Error; err != nil {
		return nil, err
	}
	return &out, nil
}

func (s *swapAdminRepo) ListSwapRecord(ctx context.Context, filter biz.AdminSwapRecordFilter) ([]*model.SwapRecord, int64, error) {
	db := s.DB(ctx).Model(&model.SwapRecord{}).
		Joins("FromTokenAsset").
		Joins("ToTokenAsset").
		Preload("SwapChannel").
		Preload("Details")

	if filter.ChannelID > 0 {
		db = db.Where("swap_channel_id = ?", filter.ChannelID)
	}
	if filter.Address != "" {
		db = db.Clauses(clause.OrConditions{
			Exprs: []clause.Expression{
				clause.Eq{
					Column: "FromTokenAsset.address",
					Value:  filter.Address,
				},
				clause.Eq{
					Column: "ToTokenAsset.address",
					Value:  filter.Address,
				},
			},
		})
	}
	if filter.Symbol != "" {
		db = db.Clauses(clause.OrConditions{
			Exprs: []clause.Expression{
				clause.Eq{
					Column: "FromTokenAsset.symbol",
					Value:  filter.Symbol,
				},
				clause.Eq{
					Column: "ToTokenAsset.symbol",
					Value:  filter.Symbol,
				},
			},
		})
	}
	if filter.Status != "" {
		db = db.Where("status = ?", filter.Status)
	}
	if filter.FromAddress != "" {
		db = db.Where("from_address = ?", filter.FromAddress)
	}
	var tokens []*model.SwapRecord
	var count int64
	if err := db.Scopes(Paginate(filter.Pagination)).Order("id DESC").
		Find(&tokens).Offset(-1).Count(&count).
		Error; err != nil {
		return nil, 0, err
	}
	return tokens, count, nil
}

func (s *swapAdminRepo) DeleteHotToken(ctx context.Context, id uint) error {
	m := &model.SwappableHotToken{}
	m.ID = id
	return s.DB(ctx).Unscoped().Delete(m).Error
}

func (s *swapAdminRepo) GetToken(ctx context.Context, id uint) (*model.SwappableToken, error) {
	var out model.SwappableToken
	if err := s.DB(ctx).Model(&model.SwappableToken{}).Take(&out, id).Error; err != nil {
		return nil, err
	}
	return &out, nil
}

func (s *swapAdminRepo) GetHotToken(ctx context.Context, id uint) (*model.SwappableHotToken, error) {
	var out model.SwappableHotToken
	if err := s.DB(ctx).Model(&model.SwappableHotToken{}).Take(&out, id).Error; err != nil {
		return nil, err
	}
	return &out, nil
}

func (s *swapAdminRepo) CreateHotToken(ctx context.Context, token *model.SwappableHotToken) error {
	var count int64
	if err := s.DB(ctx).Model(&model.SwappableHotToken{}).
		Where("swappable_token_id=? AND is_all=?", token.SwappableTokenID, token.IsAll).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return v1.ErrorDataExists("hot token already exists")
	}
	return s.DB(ctx).Create(token).Error
}

func (s *swapAdminRepo) LastHotTokenSortOrder(ctx context.Context) (int, error) {
	var out model.SwappableHotToken
	if err := s.DB(ctx).Model(&model.SwappableHotToken{}).Order("sort_order DESC").First(&out).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, nil
		}
		return 0, err
	}
	return out.SortOrder, nil
}

func (s *swapAdminRepo) UpdateToken(ctx context.Context, token *model.SwappableToken) error {
	return s.DB(ctx).Model(&model.SwappableToken{}).Where("id = ?", token.ID).Updates(map[string]any{
		"display": token.Display,
	}).Error
}

func (s *swapAdminRepo) ListHotToken(ctx context.Context, filter biz.AdminHotTokenFilter) ([]*model.SwappableHotToken, int64, error) {
	db := s.DB(ctx).Model(&model.SwappableHotToken{}).
		Joins("SwappableToken").
		Joins("SwappableToken.TokenAsset").
		Preload("SwappableToken.BlockchainNetwork").
		Preload("SwappableToken.Channels.SwapChannel")

	if filter.Symbol != "" {
		db = db.Clauses(clause.Eq{
			Column: "SwappableToken__TokenAsset.symbol",
			Value:  filter.Symbol,
		})
	}
	if filter.Address != "" {
		db = db.Clauses(clause.Eq{
			Column: "SwappableToken__TokenAsset.address",
			Value:  filter.Address,
		})
	}
	if filter.ChainIndex >= 0 {
		db = db.Clauses(clause.Eq{
			Column: clause.Column{
				Table: clause.CurrentTable,
				Name:  "chain_index",
			},
			Value: filter.ChainIndex,
		}, clause.Eq{
			Column: clause.Column{
				Table: clause.CurrentTable,
				Name:  "is_all",
			},
			Value: false,
		})
	} else {
		db = db.Clauses(clause.Eq{
			Column: clause.Column{
				Table: clause.CurrentTable,
				Name:  "is_all",
			},
			Value: true,
		})
	}
	var tokens []*model.SwappableHotToken
	var count int64
	if err := db.Scopes(Paginate(filter.Pagination)).
		Order("sort_order DESC").
		Find(&tokens).Offset(-1).Count(&count).
		Error; err != nil {
		return nil, 0, err
	}
	return tokens, count, nil
}

func (s *swapAdminRepo) UpdateSwapChannel(ctx context.Context, sc *model.SwapChannel) error {
	return s.DB(ctx).Model(&model.SwapChannel{}).Where("id = ?", sc.ID).Updates(map[string]any{
		"enable": sc.Enable,
	}).Error
}

func (s *swapAdminRepo) ListToken(ctx context.Context, filter biz.AdminSwapTokenFilter) ([]*model.SwappableToken, int64, error) {
	db := s.DB(ctx).Model(&model.SwappableToken{}).
		Joins("TokenAsset").
		Joins("BlockchainNetwork").
		Preload("Channels.SwapChannel")

	if filter.Symbol != "" {
		db = db.Clauses(clause.Eq{
			Column: "TokenAsset.symbol",
			Value:  filter.Symbol,
		})
	}
	if filter.Address != "" {
		db = db.Clauses(clause.Eq{
			Column: "TokenAsset.address",
			Value:  filter.Address,
		})
	}
	if filter.ChainIndex >= 0 {
		db = db.Clauses(clause.Eq{
			Column: "TokenAsset.chain_index",
			Value:  filter.ChainIndex,
		})
	}
	if filter.Native {
		db = db.Clauses(clause.Eq{
			Column: "TokenAsset.token_type",
			Value:  constant.NativeTokenType,
		})
	}
	if filter.ChannelID > 0 {
		var channels []model.SwappableTokenChannel
		if err := s.DB(ctx).Model(&model.SwappableTokenChannel{}).Where("swap_channel_id = ?", filter.ChannelID).Find(&channels).Error; err != nil {
			return nil, 0, err
		}
		tokenIDs := make([]any, len(channels))
		for i, channel := range channels {
			tokenIDs[i] = channel.SwappableTokenID
		}
		db = db.Clauses(clause.IN{
			Column: clause.Column{
				Table: clause.CurrentTable,
				Name:  "id",
			},
			Values: tokenIDs,
		})
	}
	var tokens []*model.SwappableToken
	var count int64
	if err := db.Scopes(Paginate(filter.Pagination)).
		Find(&tokens).Offset(-1).Count(&count).
		Error; err != nil {
		return nil, 0, err
	}
	return tokens, count, nil
}

func (s *swapAdminRepo) ListSwapChannel(ctx context.Context, pagination base.Pagination) ([]*model.SwapChannel, int64, error) {
	var out []*model.SwapChannel
	var total int64
	if err := s.DB(ctx).Model(&model.SwapChannel{}).Scopes(Paginate(pagination)).Find(&out).Offset(-1).Count(&total).Error; err != nil {
		return nil, 0, err
	}
	return out, total, nil
}
