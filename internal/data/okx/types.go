package okx

import "fmt"

const (
	ApprovalStatusSuccess   = "1" // 成功
	ApprovalStatusCanceling = "2" // 取消中
	ApprovalStatusApproving = "3" // 授权中
)

// GetApprovalsRequest represents the request body for the OKX Get Approvals API.
type GetApprovalsRequest struct {
	AddressList []*Address `json:"addressList"`
	Limit       string     `json:"limit,omitempty"`  // Default 50, max 100
	Cursor      string     `json:"cursor,omitempty"` // Default to first page
}

type Address struct {
	Address    string `json:"address"`
	ChainIndex string `json:"chainIndex"`
}

// GetApprovalsResponse represents the response from the OKX Get Approvals API.
// Based on: https://web3.okx.com/zh-hans/build/docs/waas/walletapi-api-get-approval-detail
type GetApprovalsResponse struct {
	BaseResponse
	Data []*GetApprovalsData `json:"data"`
}

type GetApprovalsData struct {
	ChainIndex       string             `json:"chainIndex"`
	Cursor           string             `json:"cursor"`
	ApprovalProjects []*ApprovalProject `json:"approvalProjects"`
}

type Token struct {
	TokenAddress string `json:"tokenAddress"`
	RemainAmount string `json:"remainAmount"`
	Status       string `json:"status"`
}

type ApprovalProject struct {
	Address         string   `json:"address"`
	ApprovalAddress string   `json:"approvalAddress"`
	ProtocolIcon    string   `json:"protocolIcon"`
	ProtocolName    string   `json:"protocolName"`
	TokenList       []*Token `json:"tokenList"`
}

type BaseResponse struct {
	Code string `json:"code"`
	Msg  string `json:"msg"`
}

func (b BaseResponse) IsSuccess() bool {
	return b.Code == "0"
}

func (b BaseResponse) Error() string {
	return fmt.Sprintf("okx api: %s, %s", b.Code, b.Msg)
}

type SupportedChains struct {
	Name       string `json:"name"`
	LogoUrl    string `json:"logoUrl"`
	ShortName  string `json:"shortName"`
	ChainIndex string `json:"chainIndex"`
}
type SupportedChainsResponse struct {
	BaseResponse
	Data []*SupportedChains `json:"data"`
}

type AllTokenBalanceByAddressReq struct {
	Chains  string `json:"chains" url:"chains"` // 筛选需要查询资产明细的链，多条链以","分隔。最多支持 50 个
	Address string `json:"address" url:"address"`
	Filter  string `json:"filter" url:"filter"` // 0: 过滤风险空投币 1: 不过滤风险空投币 默认过滤
}

type TokenAssetBalance struct {
	ChainIndex      string `json:"chainIndex"`
	TokenAddress    string `json:"tokenAddress"`
	Symbol          string `json:"symbol"`
	Balance         string `json:"balance"`
	TokenPrice      string `json:"tokenPrice"`
	TokenType       string `json:"tokenType"`
	IsRiskToken     bool   `json:"isRiskToken"`
	TransferAmount  string `json:"transferAmount"`
	AvailableAmount string `json:"availableAmount"`
	RawBalance      string `json:"rawBalance"`
	Address         string `json:"address"`
}

type AllTokenBalanceByAddressData struct {
	TokenAssets []*TokenAssetBalance `json:"tokenAssets"`
}

type AllTokenBalanceByAddressResponse struct {
	BaseResponse
	Data []*AllTokenBalanceByAddressData `json:"data"`
}
