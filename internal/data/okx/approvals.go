package okx

import (
	"context"
	"net/http"
)

const (
	getApprovalsPath = "/api/v5/wallet/security/approvals"
)

// GetApprovals 调用 OKX API 查询授权信息
func (r *Client) GetApprovals(ctx context.Context, req *GetApprovalsRequest) (*GetApprovalsResponse, error) {
	var apiResp GetApprovalsResponse
	if err := r.call(ctx, http.MethodPost, getApprovalsPath, req, &apiResp); err != nil {
		return nil, err
	}
	if !apiResp.IsSuccess() {
		return nil, apiResp
	}
	return &apiResp, nil
}
