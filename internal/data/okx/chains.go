package okx

import (
	"context"
	"net/http"
)

const (
	getChainIndexPath = "/api/v5/wallet/chain/supported-chains"
)

func (r *Client) SupportedChains(ctx context.Context) (*SupportedChainsResponse, error) {
	var apiResp SupportedChainsResponse
	if err := r.call(ctx, http.MethodGet, getChainIndexPath, nil, &apiResp); err != nil {
		return nil, err
	}
	if !apiResp.IsSuccess() {
		return nil, apiResp
	}
	return &apiResp, nil
}
