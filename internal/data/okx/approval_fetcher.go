package okx

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"
	"fmt"
	"github.com/alitto/pond/v2"
)

type ApprovalFetcher struct {
	*Client
	pool pond.ResultPool[[]*model.Approval]
}

func NewApprovalFetcher(cli *Client) biz.ApprovalFetcher {
	return &ApprovalFetcher{
		Client: cli,
		pool:   pond.NewResultPool[[]*model.Approval](cli.config.MaxConcurrency),
	}
}

// https://web3.okx.com/zh-hans/build/docs/waas/walletapi-resources-supported-networks
var chainIndexMapper = map[int64]string{
	constant.EthChainIndex:      "1",
	constant.BscChainIndex:      "56",
	constant.PolChainIndex:      "137",
	constant.BaseChainIndex:     "8453",
	constant.ArbChainIndex:      "42161",
	constant.OptimismChainIndex: "10",
	constant.TronChainIndex:     "195",
}

func (a *ApprovalFetcher) GetApprovalsByAddresses(ctx context.Context, addresses []model.UserAddress) ([]*model.Approval, error) {
	group := a.pool.NewGroup()

	for _, address := range addresses {
		ownerAddress := address.Address
		chainIndex, ok := chainIndexMapper[address.ChainIndex]
		if !ok {
			continue
		}
		group.SubmitErr(func() ([]*model.Approval, error) {
			var approvals []*model.Approval
			reply, err := a.GetApprovals(ctx, &GetApprovalsRequest{
				AddressList: []*Address{
					{Address: ownerAddress, ChainIndex: chainIndex},
				},
				Limit: "100",
			})
			if err != nil {
				return nil, err
			}
			if len(reply.Data) != 1 {
				return nil, fmt.Errorf("okx GetApprovals reply data len is not 1. address=%s, chainIndex=%s", ownerAddress, chainIndex)
			}
			for _, project := range reply.Data[0].ApprovalProjects {
				for _, token := range project.TokenList {
					if token.Status != ApprovalStatusSuccess {
						continue
					}
					approvals = append(approvals, &model.Approval{
						ChainIndex:     address.ChainIndex,
						OwnerAddress:   ownerAddress,
						SpenderAddress: project.ApprovalAddress,
						TokenAddress:   token.TokenAddress,
						Value:          token.RemainAmount,
					})
				}
			}
			return approvals, nil
		})
	}
	results, err := group.Wait()
	if err != nil {
		return nil, err
	}
	var approvals []*model.Approval
	for _, result := range results {
		approvals = append(approvals, result...)
	}
	return approvals, nil
}
