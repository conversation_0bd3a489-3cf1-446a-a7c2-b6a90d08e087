package okx

import (
	"context"
	"net/http"
)

const (
	allTokenBalanceByAddressPath = "/api/v5/wallet/asset/all-token-balances-by-address"
)

func (r *Client) AllTokenBalanceByAddress(ctx context.Context, req *AllTokenBalanceByAddressReq) (*AllTokenBalanceByAddressResponse, error) {
	var apiResp AllTokenBalanceByAddressResponse
	if err := r.call(ctx, http.MethodGet, allTokenBalanceByAddressPath, req, &apiResp); err != nil {
		return nil, err
	}
	if !apiResp.IsSuccess() {
		return nil, apiResp
	}
	return &apiResp, nil
}
