package okx

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/require"
	"testing"
)

func TestClient_SupportedChains(t *testing.T) {
	cli, err := NewClient(&Config{
		BaseURL:    "https://web3.okx.com",
		APIKey:     "f2f528f6-5720-4800-be46-38d03f7cc732",
		SecretKey:  "5D4695712A18F097822504040A54B36F",
		Passphrase: "r!KetW#=&LJ1gbb",
		ProjectID:  "ae1352666d6a98698588e0b665c493fb",
		ProxyURL:   "http://127.0.0.1:7890",
	}, log.DefaultLogger)
	require.NoError(t, err)
	resp, err := cli.SupportedChains(context.Background())
	require.NoError(t, err)
	b, _ := json.Marshal(resp)
	fmt.Println(string(b))
}
