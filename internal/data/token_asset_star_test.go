package data

import (
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"
	"fmt"
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

type TokenAssetStarRepoTestSuite struct {
	suite.Suite
	data *Data
	repo *tokenAssetStarRepo
}

func TestTokenAssetStarRepoTestSuite(t *testing.T) {
	suite.Run(t, new(TokenAssetStarRepoTestSuite))
}

func (s *TokenAssetStarRepoTestSuite) SetupSuite() {
	dsn := "host=127.0.0.1 user=root password=root dbname=wallet_test port=5432 sslmode=disable"
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	s.NoError(err)
	s.NoError(db.AutoMigrate(
		model.TokenAssetStub,
		model.TokenAssetStarStub,
	))
	rd := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
	})

	s.data = NewData(db, rd)
	s.repo = NewTokenAssetStarRepo(log.DefaultLogger, s.data).(*tokenAssetStarRepo)
}

func (s *TokenAssetStarRepoTestSuite) SetupTest() {
	for _, tb := range []string{
		model.TokenAssetStub.TableName(),
		model.TokenAssetStarStub.TableName(),
	} {
		s.data.db.Exec(fmt.Sprintf("delete from %s", tb))
	}
	s.data.rd.FlushAll(context.Background())
}

func (s *TokenAssetStarRepoTestSuite) TestSave() {
	ctx := s.T().Context()

	s.Run("save first record", func() {
		tas, err := s.repo.Save(ctx, &model.TokenAssetStar{
			TokenAssetID: 1,
		})
		s.NoError(err)
		s.Equal(int64(-1), tas.SortOrder)
	})

	s.Run("save second record", func() {
		tas, err := s.repo.Save(ctx, &model.TokenAssetStar{
			TokenAssetID: 2,
		})
		s.NoError(err)
		s.Equal(int64(-2), tas.SortOrder)
	})
}

func (s *TokenAssetStarRepoTestSuite) TestListViewByFilter() {
	ctx := s.T().Context()

	tas := []*model.TokenAsset{
		{
			Model: gorm.Model{
				ID: 1,
			},
			Address:     "0x1",
			ChainIndex:  1,
			CoinID:      "coin_id_1",
			Decimals:    18,
			IsDisplay:   true,
			LogoUrl:     "logo_url_1",
			Name:        "name_11",
			Symbol:      "ETH",
			TokenType:   "token_type_1",
			TotalSupply: "total_supply_1",
		},
		{
			Model: gorm.Model{
				ID: 2,
			},
			Address:     "0x2",
			ChainIndex:  1,
			CoinID:      "coin_id_1",
			Decimals:    18,
			IsDisplay:   true,
			LogoUrl:     "logo_url_1",
			Name:        "name_12",
			Symbol:      "ETH",
			TokenType:   "token_type_1",
			TotalSupply: "total_supply_1",
		},
		{
			Model: gorm.Model{
				ID: 3,
			},
			Address:     "0x1",
			ChainIndex:  2,
			CoinID:      "coin_id_1",
			Decimals:    8,
			IsDisplay:   true,
			LogoUrl:     "logo_url_1",
			Name:        "name_21",
			Symbol:      "BTC",
			TokenType:   "token_type_1",
			TotalSupply: "total_supply_1",
		},
	}
	err := s.data.DB(ctx).CreateInBatches(tas, 200).Error
	s.NoError(err)

	tass := []*model.TokenAssetStar{
		{
			ID:           1,
			TokenAssetID: 1,
			SortOrder:    -2,
		},
		{
			ID:           2,
			TokenAssetID: 2,
			SortOrder:    -1,
		},
		{
			ID:           3,
			TokenAssetID: 3,
			SortOrder:    -3,
		},
	}
	err = s.data.DB(ctx).CreateInBatches(tass, 200).Error
	s.NoError(err)

	s.Run("list by all chain", func() {
		list, totalCount, err := s.repo.ListViewByFilter(ctx, &biz.TokenAssetStarViewFilter{
			Page:       1,
			PageSize:   1,
			ChainIndex: -1,
		})
		s.NoError(err)
		s.Equal(int64(3), totalCount)
		s.Len(list, 1)
	})

	s.Run("list by chain", func() {
		list, totalCount, err := s.repo.ListViewByFilter(ctx, &biz.TokenAssetStarViewFilter{
			Page:       1,
			PageSize:   10,
			ChainIndex: 1,
		})
		s.NoError(err)
		s.Equal(int64(2), totalCount)
		s.Len(list, 2)

		list, totalCount, err = s.repo.ListViewByFilter(ctx, &biz.TokenAssetStarViewFilter{
			Page:       1,
			PageSize:   10,
			ChainIndex: -99,
		})
		s.NoError(err)
		s.Zero(totalCount)
		s.Len(list, 0)
	})

	s.Run("list by symbol", func() {
		list, totalCount, err := s.repo.ListViewByFilter(ctx, &biz.TokenAssetStarViewFilter{
			Page:       1,
			PageSize:   10,
			ChainIndex: -1,
			Symbol:     "ETH",
		})
		s.NoError(err)
		s.Equal(int64(2), totalCount)
		s.Equal(2, len(list))

		list, totalCount, err = s.repo.ListViewByFilter(ctx, &biz.TokenAssetStarViewFilter{
			Page:       1,
			PageSize:   10,
			ChainIndex: -1,
			Symbol:     "BTC",
		})
		s.NoError(err)
		s.Equal(int64(1), totalCount)
		s.Equal(1, len(list))
	})

	s.Run("list by address", func() {
		list, totalCount, err := s.repo.ListViewByFilter(ctx, &biz.TokenAssetStarViewFilter{
			Page:       1,
			PageSize:   10,
			ChainIndex: -1,
			Address:    "0x1",
		})
		s.NoError(err)
		s.Equal(int64(2), totalCount)
		s.Equal(2, len(list))

		list, totalCount, err = s.repo.ListViewByFilter(ctx, &biz.TokenAssetStarViewFilter{
			Page:       1,
			PageSize:   10,
			ChainIndex: -1,
			Address:    "0x2",
		})
		s.NoError(err)
		s.Equal(int64(1), totalCount)
		s.Equal(1, len(list))
	})

	s.Run("list view by sort order", func() {
		list, totalCount, err := s.repo.ListViewByFilter(ctx, &biz.TokenAssetStarViewFilter{
			Page:       1,
			PageSize:   1,
			ChainIndex: -1,
			OrderBy:    "sort_order desc",
		})
		s.NoError(err)
		s.Len(list, 1)
		s.Equal(int64(3), totalCount)
		s.Equal(int64(-1), list[0].SortOrder)
	})

	s.Run("delete", func() {
		tas := &model.TokenAssetStar{}
		err := s.data.db.Create(tas).Error
		s.NoError(err)
		err = s.repo.Delete(ctx, tas.ID)
		s.NoError(err)
	})

	s.Run("update sort order not exist swap", func() {
		err := s.repo.UpdateSortOrder(ctx, 3, -3, -4)
		s.NoError(err)
		tas := &model.TokenAssetStar{}
		err = s.data.db.Where("id=?", 3).Take(tas).Error
		s.NoError(err)
		s.Equal(int64(-4), tas.SortOrder)
	})

	s.Run("update sort order exist swap", func() {
		err := s.repo.UpdateSortOrder(ctx, 1, -2, -1)
		s.NoError(err)
		tas := &model.TokenAssetStar{}
		err = s.data.db.Where("id=?", 1).Take(tas).Error
		s.NoError(err)
		s.Equal(int64(-1), tas.SortOrder)
		tas = &model.TokenAssetStar{}
		err = s.data.db.Where("id=?", 2).Take(tas).Error
		s.NoError(err)
		s.Equal(int64(-2), tas.SortOrder)
	})
}
