package data

import (
	"byd_wallet/internal/biz/dapp"
	"byd_wallet/model"
	"context"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (d *dappAdminRepo) ListCategoryDapp(ctx context.Context, categoryID uint) ([]*model.Dapp, error) {
	var out []*model.Dapp
	var dappCategory model.DappCategory
	if err := d.DB(ctx).Model(&model.DappCategory{}).
		Preload("DappCategoryRels", func(db *gorm.DB) *gorm.DB {
			return db.Order("dapp_category_rel.sort_order desc")
		}).
		Preload("DappCategoryRels.Dapp").
		Preload("DappCategoryRels.Dapp.DappI18Ns").
		Preload("DappCategoryRels.Dapp.DappBlockchainNetworks.BlockchainNetwork").
		Take(&dappCategory, categoryID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return out, nil
		}
		return nil, err
	}
	for _, rel := range dappCategory.DappCategoryRels {
		if rel.Dapp == nil {
			continue
		}
		out = append(out, rel.Dapp)
	}

	return out, nil
}

func (d *dappAdminRepo) CreateDappCategory(ctx context.Context, category *model.DappCategory) error {
	return d.DB(ctx).Create(category).Error
}

func (d *dappAdminRepo) DeleteDappCategory(ctx context.Context, id uint) error {
	m := &model.DappCategory{}
	m.ID = id
	return d.DB(ctx).Delete(m).Error
}

func (d *dappAdminRepo) GetDappCategory(ctx context.Context, id uint) (*model.DappCategory, error) {
	var out model.DappCategory
	if err := d.DB(ctx).Model(&model.DappCategory{}).
		Preload("DappCategoryI18Ns").
		Take(&out, id).Error; err != nil {
		return nil, err
	}
	return &out, nil
}

func (d *dappAdminRepo) UpdateDappCategory(ctx context.Context, m *model.DappCategory) error {
	var oldDappCategory model.DappCategory
	if err := d.DB(ctx).Model(&model.DappCategory{}).Where("id = ?", m.ID).Take(&oldDappCategory).Error; err != nil {
		return err
	}
	m.CreatedAt = oldDappCategory.CreatedAt
	m.UpdatedAt = time.Now()
	return d.DB(ctx).Session(&gorm.Session{FullSaveAssociations: true}).Save(m).Error
}

func (d *dappAdminRepo) CreateDappCategoryRels(ctx context.Context, rels []*model.DappCategoryRel) error {
	return d.DB(ctx).Create(rels).Error
}

// ListDappCategory .
// TODO 关注性能问题
func (d *dappAdminRepo) ListDappCategory(ctx context.Context) ([]*dapp.AdminDappCategory, error) {
	var categories []*model.DappCategory
	if err := d.DB(ctx).Model(&model.DappCategory{}).
		Preload("DappCategoryI18Ns").
		Order("id DESC").
		Find(&categories).Error; err != nil {
		return nil, err
	}

	var adminCategories []*dapp.AdminDappCategory
	for _, category := range categories {
		adminCategory := &dapp.AdminDappCategory{
			ID:                category.ID,
			Show:              category.Show,
			DappCategoryI18Ns: category.DappCategoryI18Ns,
		}

		// Calculate AppCount
		var appCount int64
		if err := d.DB(ctx).Model(&model.DappCategoryRel{}).Where("dapp_category_id = ?", category.ID).Count(&appCount).Error; err != nil {
			return nil, err
		}
		adminCategory.AppCount = int(appCount)

		// Calculate HotCount
		var hotCount int64
		// This query joins DappCategoryRel with Dapp to count hot dapps in the category
		if err := d.DB(ctx).Model(&model.DappCategoryRel{}).
			Joins("JOIN dapp ON dapp.id = dapp_category_rel.dapp_id").
			Where("dapp_category_rel.dapp_category_id = ? AND dapp.hot = ?", category.ID, true).
			Count(&hotCount).Error; err != nil {
			return nil, err
		}
		adminCategory.HotCount = int(hotCount)

		// Check Navigation status
		var navigation model.DappNavigation
		if err := d.DB(ctx).Model(&model.DappNavigation{}).Where("dapp_category_id = ? AND show = ?", category.ID, true).First(&navigation).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				adminCategory.Navigation = false
			} else {
				return nil, err
			}
		} else {
			adminCategory.Navigation = true
		}

		adminCategories = append(adminCategories, adminCategory)
	}

	return adminCategories, nil
}

func (d *dappAdminRepo) CreateDappCategoryRel(ctx context.Context, rel *model.DappCategoryRel) error {
	return d.DB(ctx).Create(rel).Error
}

func (d *dappAdminRepo) LastDappCategoryRelSortOrder(ctx context.Context, categoryID uint) (int, error) {
	var out model.DappCategoryRel
	if err := d.DB(ctx).Model(&model.DappCategoryRel{}).
		Where("dapp_category_id = ?", categoryID).
		Order("sort_order DESC").
		First(&out).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, nil
		}
		return 0, err
	}
	return out.SortOrder, nil
}

func (d *dappAdminRepo) DeleteDappCategoryRel(ctx context.Context, categoryID, dappID uint) error {
	return d.DB(ctx).Delete(&model.DappCategoryRel{}, "dapp_category_id=? AND dapp_id=?", categoryID, dappID).Error
}
