package data

import (
	"byd_wallet/internal/thirdapi/megafuel"
	"encoding/json"
	"fmt"
)

func GetMegafuelConfig(data *Data) (*megafuel.Config, error) {
	cfg, err := getAPIConfig(data, "megafuel")
	if err != nil {
		return nil, fmt.Errorf("get megafuel config fail: %w", err)
	}
	apiCfg := &megafuel.Config{}
	err = json.Unmarshal(cfg.Config, apiCfg)
	if err != nil {
		return nil, fmt.Errorf("parse megafuel config fail: %w", err)
	}
	return apiCfg, nil
}

// NewMegaFuelPaymaster 创建MegaFuel paymaster客户端
// 通过Wire依赖注入提供给BSC paymaster使用
func NewMegaFuelPaymaster(data *Data) (*megafuel.Paymaster, error) {
	// 获取MegaFuel配置
	config, err := GetMegafuelConfig(data)
	if err != nil {
		return nil, fmt.Errorf("获取MegaFuel配置失败: %w", err)
	}

	// 创建MegaFuel客户端
	megafuelClient, err := megafuel.NewClient(config.ApiUrl)
	if err != nil {
		return nil, fmt.Errorf("创建MegaFuel客户端失败: %w", err)
	}

	return megafuelClient, nil
}
