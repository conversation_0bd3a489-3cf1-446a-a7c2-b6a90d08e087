package data

import (
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"
	"fmt"
	"testing"

	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

type UserHoldTokenRepoTestSuite struct {
	suite.Suite
	data *Data
	repo *userHoldTokenRepo
	ctx  context.Context
}

func TestUserHoldTokenRepoTestSuite(t *testing.T) {
	suite.Run(t, new(UserHoldTokenRepoTestSuite))
}

func (s *UserHoldTokenRepoTestSuite) SetupSuite() {
	dsn := "host=127.0.0.1 user=root password=root dbname=wallet_test port=5432 sslmode=disable"
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	s.NoError(err)
	s.NoError(db.AutoMigrate(
		model.UserStub,
		model.TokenAssetStub,
		model.UserAddressStub,
		model.UserHoldTokenStub,
	))
	rd := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
	})

	s.data = NewData(db, rd)
	s.repo = NewUserHoldTokenRepo(s.data).(*userHoldTokenRepo)
	s.ctx = s.T().Context()
}

func (s *UserHoldTokenRepoTestSuite) SetupTest() {
	for _, tb := range []string{
		model.UserAddressStub.TableName(),
		model.TokenAssetStub.TableName(),
		model.UserHoldTokenStub.TableName(),
		model.UserStub.TableName(),
	} {
		s.data.db.Exec(fmt.Sprintf("delete from public.%s", tb))
	}
	s.data.rd.FlushAll(context.Background())
}

func (s *UserHoldTokenRepoTestSuite) TestBatchSave() {
	err := s.repo.BatchSave(s.ctx, []*model.UserHoldToken{
		{
			UserAddressID: 1,
			TokenAssetID:  2,
		},
		{
			UserAddressID: 1,
			TokenAssetID:  2,
		},
		{
			UserAddressID: 2,
			TokenAssetID:  2,
		},
	})
	s.NoError(err)
	var count int64
	err = s.data.db.Model(model.UserHoldTokenStub).Count(&count).Error
	s.NoError(err)
	s.EqualValues(2, count)
}

func (s *UserHoldTokenRepoTestSuite) TestListViewByFilter() {
	user := &model.User{
		Username: "test",
	}
	s.NoError(s.data.db.Create(user).Error)
	uas := []*model.UserAddress{
		{
			Model: gorm.Model{
				ID: 1,
			},
			UserID:     user.ID,
			ChainIndex: 1,
			Address:    "0xu111",
		},
		{
			Model: gorm.Model{
				ID: 2,
			},
			UserID:     user.ID,
			ChainIndex: 2,
			Address:    "0xu222",
		},
	}
	s.NoError(s.data.db.Model(model.UserAddressStub).CreateInBatches(uas, 200).Error)
	tas := []*model.TokenAsset{
		{
			Model: gorm.Model{
				ID: 1,
			},
			ChainIndex:      1,
			ChainId:         "1",
			Address:         "0xta111",
			Symbol:          "1",
			Name:            "1",
			Decimals:        1,
			LogoUrl:         "1",
			TokenType:       "1",
			TotalSupply:     "1",
			IsDisplay:       false,
			CoinID:          "1",
			TokenDeployedAt: 0,
		},
		{
			Model: gorm.Model{
				ID: 2,
			},
			ChainIndex:      2,
			ChainId:         "2",
			Address:         "0xta222",
			Symbol:          "2",
			Name:            "2",
			Decimals:        2,
			LogoUrl:         "2",
			TokenType:       "2",
			TotalSupply:     "2",
			IsDisplay:       false,
			CoinID:          "2",
			TokenDeployedAt: 0,
		},
	}
	s.NoError(s.data.db.Model(model.TokenAssetStub).CreateInBatches(tas, 200).Error)
	s.NoError(s.repo.BatchSave(s.ctx, []*model.UserHoldToken{
		{
			UserAddressID: uas[0].ID,
			TokenAssetID:  tas[0].ID,
		},
		{
			UserAddressID: uas[0].ID,
			TokenAssetID:  tas[1].ID,
		},
		{
			UserAddressID: uas[1].ID,
			TokenAssetID:  tas[1].ID,
		},
	}))

	// case 1: single result
	list, err := s.repo.ListViewByFilter(s.ctx, &biz.UserHoldTokenViewFilter{
		UserAddrs: []struct {
			ChainIndex int64
			Address    string
		}{
			{
				ChainIndex: uas[1].ChainIndex,
				Address:    uas[1].Address,
			},
		},
	})
	s.NoError(err)
	s.Len(list, 1)
	s.Equal(uas[1].Address, list[0].WalletAddress)
	s.Equal(tas[1].Address, list[0].Address)

	// case 2: multi result
	list, err = s.repo.ListViewByFilter(s.ctx, &biz.UserHoldTokenViewFilter{
		UserAddrs: []struct {
			ChainIndex int64
			Address    string
		}{
			{
				ChainIndex: uas[0].ChainIndex,
				Address:    uas[0].Address,
			},
			{
				ChainIndex: uas[1].ChainIndex,
				Address:    uas[1].Address,
			},
		},
	})
	s.NoError(err)
	s.Len(list, 3)
	s.Equal(tas[1].Address, list[0].Address)
	s.Equal(tas[1].Address, list[1].Address)
	s.Equal(tas[0].Address, list[2].Address)
}
