package data

import (
	"byd_wallet/internal/biz"
	"byd_wallet/internal/conf"
	"crypto"
	"crypto/ed25519"
	"errors"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

type adminJwt struct {
	signerMethod jwt.SigningMethod
	edPrivKey    crypto.PrivateKey
	edPubKey     crypto.PublicKey
	expires      time.Duration
}

func NewAdminJwt(c *conf.Data) (j biz.AdminJwt, err error) {
	expires, err := time.ParseDuration(c.Adminjwt.Expires)
	if err != nil {
		err = fmt.Errorf("parse admin jwt expire: %w", err)
		return
	}
	if expires < time.Hour {
		expires = time.Hour
	}
	fmt.Println("admin jwt expires:", expires.String())

	privKey, err := jwt.ParseEdPrivateKeyFromPEM([]byte(c.Adminjwt.PrivKey))
	if err != nil {
		err = fmt.Errorf("parse admin jwt private key: %w", err)
		return
	}

	edPrivKey, ok := privKey.(ed25519.PrivateKey)
	if !ok {
		err = errors.New("private key is not ed25519")
		return
	}

	j = &adminJwt{
		signerMethod: jwt.SigningMethodEdDSA,
		edPrivKey:    edPrivKey,
		edPubKey:     edPrivKey.Public(),
		expires:      expires,
	}
	return
}

type adminJwtClaims struct {
	jwt.RegisteredClaims
	AdminID uint
}

func (j *adminJwt) GenerateJwtToken(adminID uint) (jwtToken string, err error) {
	nt := time.Now()
	jwtToken, err = jwt.NewWithClaims(j.signerMethod, adminJwtClaims{
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(nt.Add(j.expires)),
			IssuedAt:  jwt.NewNumericDate(nt),
			NotBefore: jwt.NewNumericDate(nt),
		},
		AdminID: adminID,
	}).
		SignedString(j.edPrivKey)
	return
}

func (j *adminJwt) VerifyJwtToken(jwtToken string) (adminID uint, err error) {
	jtObj, err := jwt.ParseWithClaims(jwtToken, &adminJwtClaims{}, func(t *jwt.Token) (interface{}, error) {
		return j.edPubKey, nil
	})
	if err != nil {
		return
	}

	if !jtObj.Valid {
		err = biz.ErrAdminInvalidJwtToken
		return
	}

	c, ok := jtObj.Claims.(*adminJwtClaims)
	if !ok {
		err = biz.ErrAdminInvalidJwtToken
		return
	}
	adminID = c.AdminID
	return
}
