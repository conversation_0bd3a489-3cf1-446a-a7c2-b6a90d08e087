package data

import (
	"byd_wallet/internal/biz/gaspool/paymaster/evm"
	"context"
	"encoding/json"
	"fmt"
	"strconv"
)

// evmPaymasterRepo EVM paymaster数据访问层实现
// 支持按链索引区分不同EVM链的gas转账等待确认记录
type evmPaymasterRepo struct {
	*Data
	chainIndex int64 // 链索引，用于区分不同的EVM链
}

// NewEvmPaymasterRepo 创建EVM paymaster数据访问层实例
// 参数:
//   - data: 数据访问层基础实例
//   - chainIndex: 链索引，用于区分不同的EVM链
func NewEvmPaymasterRepo(data *Data, chainIndex int64) evm.Repo {
	return &evmPaymasterRepo{
		Data:       data,
		chainIndex: chainIndex,
	}
}

// getEvmGasTransferHashKey 获取基于链索引的Redis哈希键
// 格式: "evm_gas_transfer_wait_confirm_records:{chainIndex}"
// 例如: "evm_gas_transfer_wait_confirm_records:1" (Ethereum)
//
//	"evm_gas_transfer_wait_confirm_records:137" (Polygon)
func (r *evmPaymasterRepo) getEvmGasTransferHashKey() string {
	return fmt.Sprintf("evm_gas_transfer_wait_confirm_records:%d", r.chainIndex)
}

// AllEvmGasTransferWaitConfirmRecord 获取所有EVM gas转账等待确认记录
// 从Redis哈希表中获取当前链的所有等待确认记录
func (r *evmPaymasterRepo) AllEvmGasTransferWaitConfirmRecord(ctx context.Context) ([]*evm.EvmGasTransferWaitConfirmRecord, error) {
	hashKey := r.getEvmGasTransferHashKey()
	data, err := r.rd.HGetAll(ctx, hashKey).Result()
	if err != nil {
		return nil, fmt.Errorf("get chainIdex:%d gas transfer waiting fail: %w", r.chainIndex, err)
	}

	records := make([]*evm.EvmGasTransferWaitConfirmRecord, 0, len(data))
	for _, v := range data {
		record := &evm.EvmGasTransferWaitConfirmRecord{}
		if err := json.Unmarshal([]byte(v), record); err != nil {
			return nil, fmt.Errorf("get chainIdex:%d gas transfer waiting fail: %w", r.chainIndex, err)
		}
		records = append(records, record)
	}

	return records, nil
}

// CreateEvmGasTransferWaitConfirmRecord 创建EVM gas转账等待确认记录
// 将记录序列化为JSON并存储到当前链的Redis哈希表中
func (r *evmPaymasterRepo) CreateEvmGasTransferWaitConfirmRecord(ctx context.Context, record *evm.EvmGasTransferWaitConfirmRecord) error {
	if record == nil {
		return fmt.Errorf("record is empty")
	}

	bts, err := json.Marshal(record)
	if err != nil {
		return fmt.Errorf("marshal chain_index:%d gas transfer waiting fail: %w", r.chainIndex, err)
	}

	// 使用交易ID作为哈希字段名
	field := strconv.Itoa(int(record.TxID))
	hashKey := r.getEvmGasTransferHashKey()
	if err := r.rd.HSet(ctx, hashKey, field, string(bts)).Err(); err != nil {
		return fmt.Errorf("chain_index:%d gas transfer waiting fail: %w", r.chainIndex, err)
	}

	return nil
}

// DeleteEvmGasTransferWaitConfirmRecord 删除EVM gas转账等待确认记录
// 根据交易ID从当前链的Redis哈希表中删除对应的记录
func (r *evmPaymasterRepo) DeleteEvmGasTransferWaitConfirmRecord(ctx context.Context, txID uint) error {
	field := strconv.Itoa(int(txID))
	hashKey := r.getEvmGasTransferHashKey()
	if err := r.rd.HDel(ctx, hashKey, field).Err(); err != nil {
		return fmt.Errorf("delete chain_index:%d gas transfer waiting fail (TxID: %d): %w", r.chainIndex, txID, err)
	}

	return nil
}

// NewEvmPaymasterRepoFactory 创建EVM paymaster repo工厂函数
// 用于Wire依赖注入，为每个EVM链创建独立的repo实例
func NewEvmPaymasterRepoFactory(data *Data) evm.RepoFactory {
	return func(chainIndex int64) evm.Repo {
		return NewEvmPaymasterRepo(data, chainIndex)
	}
}
