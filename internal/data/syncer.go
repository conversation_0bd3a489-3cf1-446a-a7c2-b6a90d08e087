package data

import (
	taskCommon "byd_wallet/common"
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"
	"errors"

	"github.com/redis/go-redis/v9"
	"gorm.io/gorm/clause"
)

func NewTxProcessor(data *Data) biz.TxProcessor {
	return &SyncerTxProcessor{Data: data}
}

type SyncerTxProcessor struct {
	*Data
}

func (t SyncerTxProcessor) IsTronRentTx(ctx context.Context, tx *model.Transaction) (bool, error) {
	var count int64
	if err := t.DB(ctx).Model(model.TronRentRecordStub).Where("tx_hash=?", tx.TxHash).Count(&count).Error; err != nil {
		return false, err
	}
	return count > 0, nil
}

func (t SyncerTxProcessor) Skip(ctx context.Context, tx *model.Transaction) (bool, error) {
	if tx == nil {
		return true, nil
	}
	key := taskCommon.GetTrackedAddress(tx.ChainIndex)
	exists, err := t.rd.SMIsMember(ctx, key, tx.FromAddress, tx.ToAddress).Result()
	if err != nil {
		return false, err
	}
	for _, exist := range exists {
		if exist {
			return false, nil
		}
	}
	return true, nil
}

type SyncerTransactionBatcher struct {
	*Data
}

func NewTransactionBatcher(data *Data) biz.TransactionBatcher {
	return &SyncerTransactionBatcher{Data: data}
}

func (t *SyncerTransactionBatcher) Create(ctx context.Context, tableName string, txs []*model.Transaction) error {
	return t.DB(ctx).Table(tableName).Clauses(clause.OnConflict{DoNothing: true}).Create(txs).Error
}

type ChainSyncerRepo struct {
	*Data
}

func NewChainSyncerRepo(data *Data) biz.ChainSyncerRepo {
	return &ChainSyncerRepo{Data: data}
}

func (c *ChainSyncerRepo) ListMissingBlock(ctx context.Context, chainIndex int64) ([]*model.MissingBlock, error) {
	var ms []*model.MissingBlock
	if err := c.DB(ctx).Model(&model.MissingBlock{}).Where("chain_index = ?", chainIndex).Find(&ms).Error; err != nil {
		return nil, err
	}
	return ms, nil
}

func (c *ChainSyncerRepo) DeleteMissingBlock(ctx context.Context, id uint) error {
	return c.DB(ctx).Delete(&model.MissingBlock{}, id).Error
}

func (c *ChainSyncerRepo) CreateMissingBlock(ctx context.Context, ms *model.MissingBlock) error {
	return c.DB(ctx).Create(ms).Error
}

func (c *ChainSyncerRepo) GetSyncedBlockHeight(ctx context.Context, chainIndex int64) (int64, error) {
	key := taskCommon.GetSyncBlockNumberKey(chainIndex)
	height, err := c.rd.Get(ctx, key).Int64()
	if err != nil && !errors.Is(err, redis.Nil) {
		return 0, err
	}
	return height, nil
}

func (c *ChainSyncerRepo) SaveSyncedBlockHeight(ctx context.Context, chainIndex, height int64) error {
	key := taskCommon.GetSyncBlockNumberKey(chainIndex)
	return c.rd.Set(ctx, key, height, 0).Err()
}

func (c *ChainSyncerRepo) ListMissTxn(ctx context.Context, chainIndex int64) ([]*model.MissTransaction, error) {
	var ms []*model.MissTransaction
	err := c.DB(ctx).Model(&model.MissTransaction{}).Where("chain_index = ?", chainIndex).Find(&ms).Error
	if err != nil {
		return nil, err
	}
	return ms, nil
}

func (c *ChainSyncerRepo) DeleteMissTxn(ctx context.Context, id uint) error {
	return c.DB(ctx).Unscoped().Where("id = ?", id).Delete(&model.MissTransaction{}).Error
}
