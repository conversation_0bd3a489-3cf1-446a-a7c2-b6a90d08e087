package data

import (
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"
	"errors"
	"fmt"
	"gorm.io/gorm"
)

type transactionRepo struct {
	data *Data
}

func NewTransactionRepo(data *Data) biz.TransactionRepo {
	return &transactionRepo{data}
}
func (repo *transactionRepo) ListViewByFilter(ctx context.Context, filter *biz.TransactionViewFilter) (list []*model.TransactionView, totalCount int64, err error) {
	tn := model.TransactionStub.TableName(filter.ChainIndex)
	if tn == "" {
		return nil, 0, fmt.Errorf("invalid chainIndex: %d", filter.ChainIndex)
	}

	sql := repo.data.db.WithContext(ctx).
		Table(tn+" as tx").
		Select("tx.*, ta.decimals as token_decimals, ta.name as token_name, ta.symbol as token_symbol").
		Joins("left join token_assets as ta on ta.chain_index=? and ta.address=tx.program_id", filter.ChainIndex)

	if filter.OrderBy != "" {
		sql = sql.Order(filter.OrderBy)
	}

	if filter.FromAddress != "" {
		sql = sql.Where("from_address=?", filter.FromAddress)
	}
	if filter.ToAddress != "" {
		sql = sql.Where("to_address=?", filter.ToAddress)
	}
	if filter.TxHash != "" {
		sql = sql.Where("tx_hash=?", filter.TxHash)
	}

	if err = sql.Count(&totalCount).Error; err != nil {
		return
	}

	offset := (filter.Page - 1) * filter.PageSize
	err = sql.Offset(int(offset)).Limit(int(filter.PageSize)).Find(&list).Error
	return
}
func (repo *transactionRepo) ListByFilter(ctx context.Context, filter *biz.TransactionFilter) (list []*model.Transaction, totalCount int64, err error) {
	sql := repo.data.db.WithContext(ctx).Table(model.TransactionStub.TableName(filter.ChainIndex))

	if filter.FromAddress != "" {
		sql = sql.Where("from_address=?", filter.FromAddress)
	}
	if filter.ToAddress != "" {
		sql = sql.Where("to_address=?", filter.ToAddress)
	}
	if filter.TxHash != "" {
		sql = sql.Where("tx_hash=?", filter.TxHash)
	}

	if err = sql.Count(&totalCount).Error; err != nil {
		return
	}

	offset := (filter.Page - 1) * filter.PageSize
	err = sql.Offset(int(offset)).Limit(int(filter.PageSize)).Find(&list).Error
	return
}

func (repo *transactionRepo) GetByHash(ctx context.Context, chainIndex int64, hash string) (*model.Transaction, error) {
	var tx model.Transaction
	if err := repo.data.DB(ctx).Table(model.TransactionStub.TableName(chainIndex)).
		Where("tx_hash=?", hash).
		Take(&tx).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &tx, nil
}
