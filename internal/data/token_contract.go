package data

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz"
	"byd_wallet/internal/biz/coindata"
	"byd_wallet/internal/biz/syncer/chain/evm"
	"byd_wallet/internal/biz/syncer/chain/evm/chain"
	"byd_wallet/internal/biz/syncer/chain/solana"
	"byd_wallet/internal/biz/syncer/chain/tron"
	"context"
	"errors"
	"fmt"
	"math/big"

	"github.com/blocto/solana-go-sdk/program/metaplex/token_metadata"
	"github.com/gagliardetto/solana-go/rpc"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/ethclient"
	tcommon "github.com/fbsobreira/gotron-sdk/pkg/common"
	solcommon "github.com/gagliardetto/solana-go"
	"github.com/go-kratos/kratos/v2/log"
)

type tokenContractRepo struct {
	log      *log.Helper
	erc20ABI abi.ABI

	evmCli  *evm.MultiChainClient
	tronCli *tron.RoundRobinClient
	solCli  *solana.SmartNodeSelectionClient
}

func NewTokenContractRepo(logger log.Logger,
	evmCli *evm.MultiChainClient,
	tronCli *tron.RoundRobinClient,
	solCli *solana.SmartNodeSelectionClient) biz.TokenContractRepo {
	return &tokenContractRepo{
		log:      log.NewHelper(logger),
		erc20ABI: constant.ERC20ABI,

		evmCli:  evmCli,
		tronCli: tronCli,
		solCli:  solCli,
	}
}

func (repo *tokenContractRepo) FindViewByChainIndexAndAddress(ctx context.Context, chainIndex int64, address string) (*biz.TokenContract, error) {
	if address == "" {
		return nil, errors.New("address is empty")
	}
	switch chainIndex {
	case constant.EthChainIndex, constant.BscChainIndex,
		constant.ArbChainIndex, constant.BaseChainIndex,
		constant.PolChainIndex, constant.OptimismChainIndex:
		return repo.getEvmTokenContract(ctx, chainIndex, address)
	case constant.SolChainIndex:
		return repo.getSolTokenContract(ctx, address)
	case constant.TronChainIndex:
		return repo.getTrxTokenContract(ctx, address)
	default:
		return nil, fmt.Errorf("not support chain index: %d", chainIndex)
	}
}

func (repo *tokenContractRepo) getTrxTokenContract(ctx context.Context, address string) (*biz.TokenContract, error) {
	cli := repo.tronCli.Next()
	name, err := cli.TRC20GetName(address)
	if err != nil {
		return nil, fmt.Errorf("TRC20GetName: %w: %s", err, address)
	}
	symbol, err := cli.TRC20GetSymbol(address)
	if err != nil {
		return nil, fmt.Errorf("TRC20GetSymbol: %w: %s", err, address)
	}
	decimals, err := cli.TRC20GetDecimals(address)
	if err != nil {
		return nil, fmt.Errorf("TRC20GetSymbol: %w: %s", err, address)
	}

	getTRC20TotalSupply := func() (string, error) {
		result, err := cli.TRC20Call("", address, "0x18160ddd", true, 0)
		if err != nil {
			return "", fmt.Errorf("TRC20Call: GetTotalSupply: %w: %s", err, address)
		}
		data := tcommon.BytesToHexString(result.GetConstantResult()[0])
		totalSupply, err := cli.ParseTRC20NumericProperty(data)
		if err != nil {
			return "", fmt.Errorf("ParseTRC20NumericProperty: %w", err)
		}
		return totalSupply.String(), nil
	}
	totalSupply, err := getTRC20TotalSupply()
	if err != nil {
		repo.log.Warnf("getTRC20TotalSupply error: %v: %s", err, address)
	}

	return &biz.TokenContract{
		Symbol:      symbol,
		Name:        name,
		Decimals:    decimals.Int64(),
		TotalSupply: totalSupply,
	}, nil
}

func (repo *tokenContractRepo) getSolTokenContract(ctx context.Context, address string) (*biz.TokenContract, error) {
	mintAddr, err := solcommon.PublicKeyFromBase58(address) // mint address = token address
	if err != nil {
		return nil, fmt.Errorf("parse token address error: %w", err)
	}
	metaAcc, _, err := solcommon.FindTokenMetadataAddress(mintAddr)
	if err != nil {
		return nil, fmt.Errorf("parse meta address error: %w", err)
	}

	cli := repo.solCli.Select()

	mintAccInfo, err := cli.GetAccountInfo(ctx, mintAddr)
	if err != nil {
		return nil, fmt.Errorf("get token mint account info: %w", err)
	}
	data := mintAccInfo.GetBinary()
	if len(data) < 45 {
		return nil, fmt.Errorf("invalid mint account data length: %d", len(data))
	}

	decimals := int64(data[44])

	metaAccInfo, err := cli.GetAccountInfo(ctx, metaAcc)
	if err != nil {
		return nil, fmt.Errorf("get token metadata account info: %w", err)
	}

	metadata, err := token_metadata.MetadataDeserialize(metaAccInfo.GetBinary())
	if err != nil {
		return nil, fmt.Errorf("deserialize token metadata: %w", err)
	}
	return &biz.TokenContract{
		Symbol:   metadata.Data.Symbol,
		Name:     metadata.Data.Name,
		Decimals: decimals,
	}, nil
}

func (repo *tokenContractRepo) getEvmTokenContract(ctx context.Context, chainIndex int64, address string) (*biz.TokenContract, error) {
	cli, err := repo.evmCli.Select(chainIndex)
	if err != nil {
		return nil, fmt.Errorf("select evm client: %w", err)
	}
	var (
		symbol      string
		name        string
		decimals    *big.Int
		totalSupply *big.Int
	)
	addr := common.HexToAddress(address)
	err = repo.callERC20Function(ctx, cli, addr, "symbol", &symbol)
	if err != nil {
		return nil, fmt.Errorf("CallERC20Function: symbol: %w", err)
	}
	err = repo.callERC20Function(ctx, cli, addr, "name", &name)
	if err != nil {
		return nil, fmt.Errorf("CallERC20Function: name: %w", err)
	}
	err = repo.callERC20Function(ctx, cli, addr, "decimals", &decimals)
	if err != nil {
		return nil, fmt.Errorf("CallERC20Function: decimals: %w", err)
	}
	err = repo.callERC20Function(ctx, cli, addr, "totalSupply", &totalSupply)
	if err != nil {
		repo.log.Warnf("CallERC20Function: totalSupply: %w: %d: %s", err, chainIndex, address)
	}
	var totalSupplyStr string
	if totalSupply != nil {
		totalSupplyStr = totalSupply.String()
	}
	return &biz.TokenContract{
		Symbol:      symbol,
		Name:        name,
		Decimals:    decimals.Int64(),
		TotalSupply: totalSupplyStr,
	}, nil
}

func (repo *tokenContractRepo) callERC20Function(ctx context.Context, cli *ethclient.Client, address common.Address, method string, out interface{}) error {
	input, err := repo.erc20ABI.Pack(method)
	if err != nil {
		return err
	}
	msg := ethereum.CallMsg{
		To:   &address,
		Data: input,
	}
	output, err := cli.CallContract(ctx, msg, nil)
	if err != nil {
		return err
	}
	return repo.erc20ABI.UnpackIntoInterface(out, method, output)
}

type tokenContractAPI struct {
	evmChains map[int64]*chain.EvmChain
	solChain  *rpc.Client
	trxChain  *tron.RoundRobinClient
	erc20ABI  abi.ABI
}

func NewTokenContractAPI(rpcRepo biz.RPCEndpointRepo) (coindata.TokenContractAPI, func(), error) {
	evmChains := map[int64]*chain.EvmChain{}

	var cs func()

	for _, evmChainIndex := range []int64{
		constant.EthChainIndex,
		constant.BaseChainIndex,
		constant.ArbChainIndex,
		constant.OptimismChainIndex,
		constant.PolChainIndex,
		constant.BscChainIndex,
	} {
		rpcs, err := rpcRepo.GetRpcListByChainIndexForCache(evmChainIndex, "http")
		if err != nil {
			if cs != nil {
				cs()
			}
			return nil, nil, fmt.Errorf("GetRpcListByChainIndexForCache: %w: %d", err, evmChainIndex)
		}
		c, err := chain.NewEvmChain(evmChainIndex, rpcs...)
		if err != nil {
			if cs != nil {
				cs()
			}
			return nil, nil, fmt.Errorf("NewEvmChain: %w", err)
		}
		evmChains[evmChainIndex] = c
		if cs == nil {
			cs = func() {
				c.Client.Close()
			}
		} else {
			oldCS := cs
			cs = func() {
				oldCS()
				c.Client.Close()
			}
		}
	}

	solRpcs, err := rpcRepo.GetRpcListByChainIndexForCache(constant.SolChainIndex, "http")
	if err != nil {
		if cs != nil {
			cs()
		}
		return nil, nil, fmt.Errorf("GetRpcListByChainIndexForCache: %w: %d", err, constant.SolChainIndex)
	}
	if len(solRpcs) == 0 {
		if cs != nil {
			cs()
		}
		return nil, nil, errors.New("sol rpc endpoint is empty")
	}

	solChain := rpc.New(solRpcs[0])

	trxRpcs, err := rpcRepo.GetRpcListByChainIndexForCache(constant.TronChainIndex, "http")
	if err != nil {
		if cs != nil {
			cs()
		}
		return nil, nil, fmt.Errorf("GetRpcListByChainIndexForCache: %w: %d", err, constant.TronChainIndex)
	}
	if len(trxRpcs) == 0 {
		if cs != nil {
			cs()
		}
		return nil, nil, errors.New("trx rpc endpoint is empty")
	}

	trxChain, err := tron.NewRoundRobinClient(trxRpcs)
	if err != nil {
		if cs != nil {
			cs()
		}
		return nil, nil, fmt.Errorf("trx NewGRPCClientFromURLs: %w", err)
	}
	oldCS := cs
	cs = func() {
		oldCS()
		trxChain.Close()
	}

	return &tokenContractAPI{
		evmChains: evmChains,
		solChain:  solChain,
		trxChain:  trxChain,
		erc20ABI:  constant.ERC20ABI,
	}, cs, nil
}

func (repo *tokenContractAPI) GetTokenContract(ctx context.Context, chainIndex int64, address string) (*coindata.TokenContract, error) {
	switch chainIndex {
	case constant.EthChainIndex, constant.BscChainIndex,
		constant.ArbChainIndex, constant.BaseChainIndex,
		constant.PolChainIndex, constant.OptimismChainIndex:
		return repo.getEvmTokenContract(ctx, chainIndex, address)
	case constant.SolChainIndex:
		return repo.getSolTokenContract(ctx, address)
	case constant.TronChainIndex:
		return repo.getTrxTokenContract(ctx, address)
	default:
		return nil, fmt.Errorf("not support chain index: %d", chainIndex)
	}
}

func (repo *tokenContractAPI) getEvmTokenContract(ctx context.Context, chainIndex int64, address string) (*coindata.TokenContract, error) {
	cc, ok := repo.evmChains[chainIndex]
	if !ok {
		return nil, fmt.Errorf("evm chain not found: %d", chainIndex)
	}
	var (
		decimals    *big.Int
		totalSupply *big.Int
	)
	addr := common.HexToAddress(address)
	err := cc.CallERC20Function(repo.erc20ABI, addr, "decimals", &decimals)
	if err != nil {
		return nil, fmt.Errorf("CallERC20Function: decimals: %w", err)
	}
	err = cc.CallERC20Function(repo.erc20ABI, addr, "totalSupply", &totalSupply)
	if err != nil {
		return nil, fmt.Errorf("CallERC20Function: totalSupply: %w", err)
	}
	return &coindata.TokenContract{
		Decimals:    decimals.Int64(),
		TotalSupply: totalSupply.String(),
	}, nil
}

func (repo *tokenContractAPI) getSolTokenContract(ctx context.Context, address string) (*coindata.TokenContract, error) {
	out, err := solcommon.PublicKeyFromBase58(address)
	if err != nil {
		return nil, fmt.Errorf("solcommon.PublicKeyFromBase58: %w", err)
	}
	accInfo, err := repo.solChain.GetAccountInfo(ctx, out)
	if err != nil {
		return nil, fmt.Errorf("get account info: %w: %s", err, address)
	}
	data := accInfo.Value.Data.GetBinary()
	if len(data) < 45 {
		return nil, fmt.Errorf("account info data length < 45: %s", address)
	}
	return &coindata.TokenContract{
		Decimals:    int64(data[44]),
		TotalSupply: "",
	}, nil
}

func (repo *tokenContractAPI) getTrxTokenContract(ctx context.Context, address string) (*coindata.TokenContract, error) {
	cli := repo.trxChain.Next()
	decimals, err := cli.TRC20GetDecimals(address)
	if err != nil {
		return nil, fmt.Errorf("TRC20GetDecimals: %w: %s", err, address)
	}

	result, err := cli.TRC20Call("", address, "0x18160ddd", true, 0)
	if err != nil {
		return nil, fmt.Errorf("TRC20Call: GetTotalSupply: %w: %s", err, address)
	}
	data := tcommon.BytesToHexString(result.GetConstantResult()[0])
	totalSupply, err := cli.ParseTRC20NumericProperty(data)
	if err != nil {
		return nil, fmt.Errorf("ParseTRC20NumericProperty: %w", err)
	}

	return &coindata.TokenContract{
		Decimals:    decimals.Int64(),
		TotalSupply: totalSupply.String(),
	}, nil
}
