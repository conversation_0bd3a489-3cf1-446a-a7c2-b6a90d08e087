package covalenthq

import (
	"context"
	"fmt"
	"time"
)

func (c *Client) GetTokenApprovals(ctx context.Context, chainName, walletAddress string) (*GetTokenApprovalsReply, error) {
	reqPath := fmt.Sprintf("/%s/approvals/%s/", chainName, walletAddress)
	var reply GetTokenApprovalsReply
	if err := c.get(ctx, reqPath, &reply); err != nil {
		return nil, err
	}
	if reply.Error {
		return nil, fmt.Errorf("%s#%s", reply.ErrorCode, reply.ErrorMessage)
	}
	return &reply, nil
}

type GetTokenApprovalsReply struct {
	Data         TokenApprovals `json:"data"`
	Error        bool           `json:"error"`
	ErrorMessage string         `json:"error_message"`
	ErrorCode    string         `json:"error_code"`
}

type TokenApprovals struct {
	Address       string     `json:"address"`
	UpdatedAt     time.Time  `json:"updated_at"`
	QuoteCurrency string     `json:"quote_currency"`
	ChainId       int        `json:"chain_id"`
	ChainName     string     `json:"chain_name"`
	Items         []Approval `json:"items"`
}

type Approval struct {
	TokenAddress           string    `json:"token_address"`
	TokenAddressLabel      string    `json:"token_address_label"`
	TickerSymbol           string    `json:"ticker_symbol"`
	ContractDecimals       int       `json:"contract_decimals"`
	LogoUrl                string    `json:"logo_url"`
	QuoteRate              float64   `json:"quote_rate"`
	Balance                string    `json:"balance"`
	BalanceQuote           float64   `json:"balance_quote"`
	PrettyBalanceQuote     string    `json:"pretty_balance_quote"`
	ValueAtRisk            string    `json:"value_at_risk"`
	ValueAtRiskQuote       float64   `json:"value_at_risk_quote"`
	PrettyValueAtRiskQuote string    `json:"pretty_value_at_risk_quote"`
	Spenders               []Spender `json:"spenders"`
}

type Spender struct {
	BlockHeight            int         `json:"block_height"`
	TxOffset               int         `json:"tx_offset"`
	LogOffset              int         `json:"log_offset"`
	BlockSignedAt          time.Time   `json:"block_signed_at"`
	TxHash                 string      `json:"tx_hash"`
	SpenderAddress         string      `json:"spender_address"`
	SpenderAddressLabel    interface{} `json:"spender_address_label"`
	Allowance              string      `json:"allowance"`
	AllowanceQuote         *float64    `json:"allowance_quote"`
	PrettyAllowanceQuote   *string     `json:"pretty_allowance_quote"`
	ValueAtRisk            string      `json:"value_at_risk"`
	ValueAtRiskQuote       float64     `json:"value_at_risk_quote"`
	PrettyValueAtRiskQuote string      `json:"pretty_value_at_risk_quote"`
	RiskFactor             string      `json:"risk_factor"`
}
