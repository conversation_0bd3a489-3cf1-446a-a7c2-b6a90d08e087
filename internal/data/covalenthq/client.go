package covalenthq

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"io"
	"net/http"
)

// Client 定义了与OKX API交互的仓库
type Client struct {
	httpClient *http.Client
	config     *Config
	log        *log.Helper
}

type Config struct {
	BaseURL        string `json:"base_url"`
	APIKey         string `json:"api_key"`
	MaxConcurrency int    `json:"max_concurrency"`
}

// NewClient 创建一个新的 Client 实例
func NewClient(conf *Config, logger log.Logger) *Client {
	return &Client{
		httpClient: http.DefaultClient,
		config:     conf,
		log:        log.NewHelper(logger),
	}
}

func (c *Client) get(ctx context.Context, reqPath string, reply any) error {
	url := c.config.BaseURL + reqPath

	httpReq, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return fmt.Errorf("failed to create http request: %w", err)
	}
	httpReq.Header.Add("content-type", "application/json")
	httpReq.Header.Add("Authorization", "Bearer "+c.config.APIKey)

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return fmt.Errorf("failed to send http request: %w", err)
	}
	defer resp.Body.Close()

	respBodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}
	c.log.Debugf("covalenthq api get request: url: %s \n resp: %s", url, respBodyBytes)

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("covalenthq API request failed with status code %d: %s", resp.StatusCode, string(respBodyBytes))
	}

	if err := json.Unmarshal(respBodyBytes, reply); err != nil {
		return fmt.Errorf("failed to unmarshal response body: %w", err)
	}

	return nil
}
