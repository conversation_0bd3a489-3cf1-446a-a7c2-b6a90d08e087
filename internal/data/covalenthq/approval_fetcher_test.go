package covalenthq

import (
	"byd_wallet/model"
	"context"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestApprovalFetcher_GetApprovalsByAddresses(t *testing.T) {
	t.Skip()
	cli := NewClient(&Config{
		BaseURL:        "https://api.covalenthq.com/v1",
		APIKey:         "xxx",
		MaxConcurrency: 10,
	}, log.DefaultLogger)
	f := NewApprovalFetcher(cli)
	addresses := []string{
		"******************************************",
		"******************************************",
		"******************************************",
		"******************************************",
		"******************************************",
		"******************************************",
	}
	var uas []model.UserAddress
	for chainIndex := range chainNamesMapper {
		for _, address := range addresses {
			uas = append(uas, model.UserAddress{
				ChainIndex: chainIndex,
				Address:    address,
			})
		}
	}
	approvals, err := f.GetApprovalsByAddresses(context.Background(), uas)
	assert.NoError(t, err)
	for _, approval := range approvals {
		fmt.Println(approval)
	}
}
