package covalenthq

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"
	"github.com/alitto/pond/v2"
)

type ApprovalFetcher struct {
	*Client
	pool pond.ResultPool[[]*model.Approval]
}

func NewApprovalFetcher(cli *Client) biz.ApprovalFetcher {
	return &ApprovalFetcher{
		Client: cli,
		pool:   pond.NewResultPool[[]*model.Approval](cli.config.MaxConcurrency),
	}
}

const (
	chainNameSuffix = "-mainnet"
	maxUint256      = "115792089237316195423570985008687907853269984665640564039457584007913129639935"
)

var chainNamesMapper = map[int64]string{
	constant.EthChainIndex: "eth",
	constant.BscChainIndex: "bsc",
	// TODO 不知道quicknode的polygon的chainName是什么
	//constant.PolChainIndex:      "",
	constant.BaseChainIndex:     "base",
	constant.ArbChainIndex:      "arbitrum",
	constant.OptimismChainIndex: "optimism",
}

func (a *ApprovalFetcher) GetApprovalsByAddresses(ctx context.Context, addresses []model.UserAddress) ([]*model.Approval, error) {
	group := a.pool.NewGroup()

	for _, address := range addresses {
		ownerAddress := address.Address
		chainIndex := address.ChainIndex
		chainName, ok := chainNamesMapper[chainIndex]
		if !ok {
			continue
		}
		chainName += chainNameSuffix
		group.SubmitErr(func() ([]*model.Approval, error) {
			var approvals []*model.Approval
			reply, err := a.GetTokenApprovals(ctx, chainName, ownerAddress)
			if err != nil {
				return nil, err
			}
			for _, item := range reply.Data.Items {
				for _, spender := range item.Spenders {
					if spender.Allowance == "UNLIMITED" {
						spender.Allowance = maxUint256
					}
					approvals = append(approvals, &model.Approval{
						ChainIndex:     chainIndex,
						OwnerAddress:   ownerAddress,
						SpenderAddress: spender.SpenderAddress,
						TokenAddress:   item.TokenAddress,
						Value:          spender.Allowance,
					})
				}
			}
			return approvals, nil
		})
	}
	results, err := group.Wait()
	if err != nil {
		return nil, err
	}
	var approvals []*model.Approval
	for _, result := range results {
		approvals = append(approvals, result...)
	}
	return approvals, nil
}
