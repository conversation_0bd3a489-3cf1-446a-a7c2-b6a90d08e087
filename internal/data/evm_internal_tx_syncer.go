package data

import (
	taskCommon "byd_wallet/common"
	"byd_wallet/internal/biz"
	"context"
	"errors"
	"github.com/redis/go-redis/v9"
)

type EVMInternalTxSyncerRepo struct {
	*Data
}

func NewEVMInternalTxSyncerRepo(data *Data) biz.EVMInternalTxSyncerRepo {
	return &EVMInternalTxSyncerRepo{Data: data}
}

func (e *EVMInternalTxSyncerRepo) GetSyncedBlockHeight(ctx context.Context, chainIndex int64) (int64, error) {
	key := taskCommon.GetSyncEvmInternalBlockNumberKey(chainIndex)
	height, err := e.rd.Get(ctx, key).Int64()
	if err != nil && !errors.Is(err, redis.Nil) {
		return 0, err
	}
	return height, nil
}

func (e *EVMInternalTxSyncerRepo) SaveSyncedBlockHeight(ctx context.Context, chainIndex, height int64) error {
	key := taskCommon.GetSyncEvmInternalBlockNumberKey(chainIndex)
	return e.rd.Set(ctx, key, height, 0).Err()
}
