package data

import (
	"byd_wallet/internal/biz/base"
	"byd_wallet/utils"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/feature/s3/manager"
	"github.com/aws/aws-sdk-go-v2/service/s3"
)

type S3Repo struct {
	uploader *manager.Uploader
	conf     *S3Config
	pCli     *s3.PresignClient
	cli      *s3.Client
}

type S3Config struct {
	AccessKey     string `json:"access_key"`
	SecretKey     string `json:"secret_key"`
	Bucket        string `json:"bucket"`
	Region        string `json:"region"`
	KeyPrefix     string `json:"key_prefix"`
	AppAccessAddr string `json:"app_access_addr"`
}

func NewS3Repo(data *Data) (base.S3Repo, error) {
	s3Conf, err := getS3Config(data)
	if err != nil {
		return nil, err
	}
	return NewS3RepoFromConfig(s3Conf)
}

func NewS3RepoFromConfig(s3Conf *S3Config) (*S3Repo, error) {
	cli := s3.NewFromConfig(aws.Config{
		Region:      s3Conf.Region,
		Credentials: credentials.NewStaticCredentialsProvider(s3Conf.AccessKey, s3Conf.SecretKey, ""),
	})
	uploader := manager.NewUploader(cli)
	return &S3Repo{
		uploader: uploader,
		conf:     s3Conf,
		cli:      cli,
		pCli:     s3.NewPresignClient(cli),
	}, nil
}

func getS3Config(data *Data) (*S3Config, error) {
	cfg, err := getAPIConfig(data, "s3")
	if err != nil {
		return nil, fmt.Errorf("get s3 config fail: %w", err)
	}
	var s3Conf S3Config
	if err = json.Unmarshal(cfg.Config, &s3Conf); err != nil {
		return nil, fmt.Errorf("parse s3 config fail: %w", err)
	}
	return &s3Conf, nil
}

func (s S3Repo) fullKey(key string) *string {
	kk := s.conf.KeyPrefix + key
	return &kk
}

func (s S3Repo) Upload(ctx context.Context, key string, body io.Reader) (string, error) {
	return s.upload(ctx, key, body)
}

func (s S3Repo) upload(ctx context.Context, key string, body io.Reader) (string, error) {
	result, err := s.uploader.Upload(ctx, &s3.PutObjectInput{
		Bucket: aws.String(s.conf.Bucket),
		Key:    s.fullKey(key),
		Body:   body,
	})
	if err != nil {
		return "", err
	}
	return result.Location, nil
}

func (s S3Repo) UploadFileByFileUrl(ctx context.Context, fileUrl, key string) (string, error) {
	cli := utils.NewHttpClient(fileUrl, "GET")
	rspBody, err := cli.Send()
	if err != nil {
		return "", fmt.Errorf("failed to upload file by URL, %v", err)
	}
	return s.upload(ctx, key, bytes.NewBuffer(rspBody))
}

func (s S3Repo) UploadImageFileByFileUrl(ctx context.Context, fileUrl, key string) (string, error) {
	cli := utils.NewHttpClient(fileUrl, "GET")
	rspBody, err := cli.Send()
	if err != nil {
		return "", fmt.Errorf("failed to download image file by URL, %w", err)
	}

	result, err := s.uploader.Upload(ctx, &s3.PutObjectInput{
		Bucket:             aws.String(s.conf.Bucket),
		Key:                s.fullKey(key),
		Body:               bytes.NewBuffer(rspBody),
		ContentType:        aws.String(http.DetectContentType(rspBody)),
		ContentDisposition: aws.String("inline"),
	})
	if err != nil {
		return "", err
	}
	return result.Location, nil
}

// GeneratePresignedRequest 生成预签名的请求
// 相关文档: https://docs.aws.amazon.com/AmazonS3/latest/API/s3_example_s3_Scenario_PresignedUrl_section.html
func (s S3Repo) GeneratePresignedRequest(ctx context.Context, key string, expires time.Duration) (*base.PresignedHTTPRequest, error) {
	result, err := s.pCli.PresignPutObject(ctx, &s3.PutObjectInput{
		Bucket: aws.String(s.conf.Bucket),
		Key:    s.fullKey(key),
	}, s3.WithPresignExpires(expires))
	if err != nil {
		return nil, err
	}
	return &base.PresignedHTTPRequest{
		URL:          result.URL,
		Method:       result.Method,
		SignedHeader: result.SignedHeader,
	}, nil
}

func (s S3Repo) GeneratePresignedRequest4ImagePng(ctx context.Context, key string, expires time.Duration) (*base.PresignedHTTPRequest, error) {
	result, err := s.pCli.PresignPutObject(ctx, &s3.PutObjectInput{
		Bucket:             aws.String(s.conf.Bucket),
		Key:                s.fullKey(key),
		ContentDisposition: aws.String("inline"),
		ContentType:        aws.String("image/png"),
	}, s3.WithPresignExpires(expires))
	if err != nil {
		return nil, err
	}
	return &base.PresignedHTTPRequest{
		URL:          result.URL,
		Method:       result.Method,
		SignedHeader: result.SignedHeader,
	}, nil
}

// DeleteObject delete s3 object by full key
// NOTE: dont invoke S3Repo.awsKey()
func (s S3Repo) DeleteObject(ctx context.Context, fullKey string) error {
	_, err := s.cli.DeleteObject(ctx, &s3.DeleteObjectInput{
		Bucket: aws.String(s.conf.Bucket),
		Key:    aws.String(fullKey),
	})
	return err
}

func (s S3Repo) ToAppAccessUrl(ctx context.Context, s3Url string) string {
	if s.conf.AppAccessAddr == "" || s3Url == "" {
		return s3Url
	}
	if strings.HasPrefix(s3Url, s.conf.AppAccessAddr) {
		return s3Url
	}

	u, err := url.Parse(s3Url)
	if err != nil {
		return s3Url
	}
	key := strings.TrimPrefix(u.Path, "/")
	return fmt.Sprintf("%s/%s", s.conf.AppAccessAddr, key)
}
