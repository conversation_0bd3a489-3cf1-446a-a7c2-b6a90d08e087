package data

import (
	"byd_wallet/common/lang"
	"byd_wallet/model"
	"context"
	"gorm.io/gorm"
)

func (d *DappRepo) GetDappCategoryForApp(ctx context.Context, id uint, dappLimit ...int) (*model.DappCategory, error) {
	language := lang.FromContext(ctx)
	var out model.DappCategory
	if err := d.DB(ctx).Model(&model.DappCategory{}).
		Preload("DappCategoryI18Ns", "language = ?", language).
		Preload("DappCategoryRels", func(db *gorm.DB) *gorm.DB {
			db = db.Order("dapp_category_rel.sort_order")
			if len(dappLimit) > 0 && dappLimit[0] > 0 {
				db = db.Limit(dappLimit[0])
			}
			return db
		}).
		Preload("DappCategoryRels.Dapp", "show = ?", true).
		Preload("DappCategoryRels.Dapp.DappI18Ns", "language = ?", language).
		Preload("DappCategoryRels.Dapp.DappBlockchainNetworks.BlockchainNetwork").
		Take(&out, "id = ? AND show = ?", id, true).Error; err != nil {
		return nil, err
	}
	return &out, nil
}
