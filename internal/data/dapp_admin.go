package data

import (
	"byd_wallet/internal/biz/dapp"
	"byd_wallet/model"
	"context"
	"errors"
	"gorm.io/gorm"
)

func NewDappAdminRepo(data *Data) dapp.AdminRepo {
	return &dappAdminRepo{data}
}

type dappAdminRepo struct {
	*Data
}

func (d *dappAdminRepo) CreateDapp(ctx context.Context, dapp *model.Dapp) error {
	return d.DB(ctx).Create(dapp).Error
}

func (d *dappAdminRepo) CreateDapps(ctx context.Context, dapps []*model.Dapp) error {
	return d.DB(ctx).Create(&dapps).Error
}

func (d *dappAdminRepo) UpdateDapp(ctx context.Context, m *model.Dapp) error {
	if m.ID == 0 {
		return errors.New("dapp id should not be zero")
	}
	return d.DB(ctx).Session(&gorm.Session{FullSaveAssociations: true}).Save(m).Error
}

func (d *dappAdminRepo) DeleteDapp(ctx context.Context, id uint) error {
	m := &model.Dapp{}
	m.ID = id
	return d.DB(ctx).Delete(m).Error
}

func (d *dappAdminRepo) ListDapp(ctx context.Context) ([]*model.Dapp, error) {
	var out []*model.Dapp
	if err := d.DB(ctx).Model(&model.Dapp{}).
		Preload("DappI18Ns").
		Preload("DappBlockchainNetworks").
		Preload("DappBlockchainNetworks.BlockchainNetwork"). // Preload nested BlockchainNetwork
		Order("id DESC").
		Find(&out).Error; err != nil {
		return nil, err
	}
	return out, nil
}

func (d *dappAdminRepo) GetDapp(ctx context.Context, id uint) (*model.Dapp, error) {
	var out model.Dapp
	if err := d.DB(ctx).Model(&model.Dapp{}).
		Preload("DappI18Ns").
		Preload("DappBlockchainNetworks").
		Preload("DappBlockchainNetworks.BlockchainNetwork"). // Preload nested BlockchainNetwork
		Take(&out, "id = ?", id).Error; err != nil {
		return nil, err
	}
	return &out, nil
}

func (d *dappAdminRepo) CreateDappBlockchainNetworks(ctx context.Context, networks []*model.DappBlockchainNetwork) error {
	return d.DB(ctx).Create(networks).Error
}

func (d *dappAdminRepo) DeleteDappBlockchainNetworks(ctx context.Context, dappID uint) error {
	return d.DB(ctx).Delete(&model.DappBlockchainNetwork{}, "dapp_id = ?", dappID).Error
}

// Sort dapp通用排序方法
// 需要在事务中进行
func (d *dappAdminRepo) Sort(ctx context.Context, sort *dapp.Sort) error {
	// 获取当前排序数据
	var curSort BaseSort
	if err := d.DB(ctx).Model(sort.Model).Where("id = ?", sort.ID).First(&curSort).Error; err != nil {
		return err
	}

	// 根据方向查找需要交换的排序数据
	var targetSort BaseSort
	var err error

	if sort.Direction {
		// 向上移动：找到sort_order比当前大的最小值
		err = d.DB(ctx).Model(sort.Model).Where("sort_order > ?", curSort.SortOrder).
			Order("sort_order ASC").First(&targetSort).Error
	} else {
		// 向下移动：找到sort_order比当前小的最大值
		err = d.DB(ctx).Model(sort.Model).Where("sort_order < ?", curSort.SortOrder).
			Order("sort_order DESC").First(&targetSort).Error
	}

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 已经在边界位置，无法移动
			return nil
		}
		return err
	}

	// 交换sort_order值
	currentSortOrder := curSort.SortOrder
	targetSortOrder := targetSort.SortOrder

	// 更新当前导航项的sort_order
	if err := d.DB(ctx).Model(sort.Model).
		Where("id = ?", curSort.ID).
		Update("sort_order", targetSortOrder).Error; err != nil {
		return err
	}

	// 更新目标导航项的sort_order
	if err := d.DB(ctx).Model(sort.Model).
		Where("id = ?", targetSort.ID).
		Update("sort_order", currentSortOrder).Error; err != nil {
		return err
	}

	return nil
}
