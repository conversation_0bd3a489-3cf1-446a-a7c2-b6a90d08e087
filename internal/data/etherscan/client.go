package etherscan

import (
	"context"
	"fmt"
	"resty.dev/v3"
)

type MultiChainClient struct {
	clients map[string]*client
}

func NewMultiChainClient(conf *Config) *MultiChainClient {
	mc := &MultiChainClient{
		clients: make(map[string]*client),
	}
	if conf == nil {
		return mc
	}
	for chainID, apiKey := range conf.APIKeys {
		mc.clients[chainID] = &client{
			cli:    resty.New().SetBaseURL(conf.BaseURL),
			apiKey: apiKey,
		}
	}
	return mc
}

func (m MultiChainClient) get(chainID string) (*client, error) {
	cli, ok := m.clients[chainID]
	if !ok {
		return cli, fmt.Errorf("%s client not exists", chainID)
	}
	return cli, nil
}

type client struct {
	// https://api.etherscan.io/v2/api
	cli    *resty.Client
	apiKey string
}

type Config struct {
	BaseURL string `json:"base_url"`
	// key: chainID
	// value: api<PERSON>ey
	APIKeys map[string]string `json:"api_keys"`
}

func (c *client) ListInternalTx(ctx context.Context, params *ListInternalTxParams) (*ListInternalTxResult, error) {
	var result ListInternalTxResult
	response, err := c.cli.R().WithContext(ctx).SetQueryParams(map[string]string{
		"chainid":    params.ChainID,
		"module":     "account",
		"action":     "txlistinternal",
		"startblock": fmt.Sprintf("%d", params.StartBlock),
		"endblock":   fmt.Sprintf("%d", params.EndBlock),
		"page":       fmt.Sprintf("%d", params.Page),
		"offset":     fmt.Sprintf("%d", params.Offset),
		"sort":       "asc",
		"apikey":     c.apiKey,
	}).SetResult(&result).
		Get("")
	if err != nil {
		return nil, err
	}
	if !response.IsSuccess() {
		return nil, fmt.Errorf("code: %d, data: %s", response.StatusCode(), response.String())
	}
	return &result, nil
}
