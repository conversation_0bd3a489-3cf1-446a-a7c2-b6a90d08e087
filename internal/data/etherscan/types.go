package etherscan

import "fmt"

type InternalTransaction struct {
	BlockNumber     string `json:"blockNumber"`
	TimeStamp       string `json:"timeStamp"`
	Hash            string `json:"hash"`
	From            string `json:"from"`
	To              string `json:"to"`
	Value           string `json:"value"`
	ContractAddress string `json:"contractAddress"`
	Input           string `json:"input"`
	Type            string `json:"type"`
	Gas             string `json:"gas"`
	GasUsed         string `json:"gasUsed"`
	TraceId         string `json:"traceId"`
	// The isError field returns 0 for successful transactions and 1 for rejected/cancelled transactions.
	IsError string `json:"isError"`
	ErrCode string `json:"errCode"`
}

func (i InternalTransaction) Success() bool {
	return i.IsError == "0"
}

type ListInternalTxResult struct {
	Status  string                 `json:"status"`
	Message string                 `json:"message"`
	Result  []*InternalTransaction `json:"result"`
}

func (l ListInternalTxResult) Success() bool {
	return l.Status == "1"
}

func (l ListInternalTxResult) IsNotFound() bool {
	return l.Status == "0" && l.Message == "No transactions found"
}

func (l ListInternalTxResult) Error() string {
	return fmt.Sprintf("%s: %s", l.Status, l.Message)
}

type ListInternalTxParams struct {
	ChainID              string
	StartBlock, EndBlock int64
	Page, Offset         int
}
