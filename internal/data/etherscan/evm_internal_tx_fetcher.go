package etherscan

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"strconv"
)

type evmInternalTxFetcher struct {
	log *log.Helper
	*MultiChainClient
}

func NewEvmInternalTxFetcher(logger log.Logger, cli *MultiChainClient) biz.EvmInternalTxFetcher {
	return &evmInternalTxFetcher{
		log:              log.NewHelper(logger),
		MultiChainClient: cli,
	}
}

func (e *evmInternalTxFetcher) GetEvmInternalTx(ctx context.Context, chainIndex int64, chainID string, height int64) ([]*model.Transaction, error) {
	cli, err := e.get(chainID)
	if err != nil {
		return nil, err
	}
	size := 10000
	result, err := cli.ListInternalTx(ctx, &ListInternalTxParams{
		ChainID:    chainID,
		StartBlock: height,
		EndBlock:   height,
		Page:       1,
		Offset:     size,
	})
	if err != nil {
		return nil, err
	}
	if !result.Success() {
		if result.IsNotFound() {
			return nil, nil
		}
		return nil, result
	}
	if len(result.Result) == size {
		e.log.Errorf("evm 内部交易数量超过 %d, 请注意验证数据是否遗漏。chainID: %s, height: %d", size, chainID, height)
	}
	var transactions []*model.Transaction
	for _, internalTx := range result.Result {
		blockNum, err := strconv.ParseInt(internalTx.BlockNumber, 10, 64)
		if err != nil {
			return nil, err
		}
		ts, err := strconv.ParseInt(internalTx.TimeStamp, 10, 64)
		if err != nil {
			return nil, err
		}
		status := constant.TransactionStatusFail
		if internalTx.Success() {
			status = constant.TransactionStatusSuccess
		}
		transactions = append(transactions, &model.Transaction{
			TxHash:      internalTx.Hash,
			BlockNumber: blockNum,
			ChainIndex:  chainIndex,
			FromAddress: internalTx.From,
			ToAddress:   internalTx.To,
			Value:       internalTx.Value,
			Fee:         internalTx.GasUsed,
			Method:      internalTx.Type,
			ProgramID:   internalTx.ContractAddress,
			Status:      status,
			Timestamp:   ts,
		})
	}
	return transactions, nil
}
