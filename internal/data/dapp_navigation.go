package data

import (
	"byd_wallet/common/lang"
	"byd_wallet/model"
	"context"
)

func (d *DappRepo) ListDappNavigation(ctx context.Context) ([]*model.DappNavigation, error) {
	var list []*model.DappNavigation
	language := lang.FromContext(ctx)
	if err := d.DB(ctx).Model(&model.DappNavigation{}).Where("show = ?", true).Order("sort_order").
		Preload("DappCategory", "show = ?", true).
		Preload("DappCategory.DappCategoryI18Ns", "language = ?", language).
		Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}
