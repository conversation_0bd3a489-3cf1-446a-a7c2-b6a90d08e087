package data

import (
	"byd_wallet/common/lang"
	"byd_wallet/internal/biz/dapp"
	"byd_wallet/model"
	"context"
	"errors"

	"gorm.io/gorm"
)

func (d *dappAdminRepo) CreateDappNavigation(ctx context.Context, navigation *model.DappNavigation) error {
	return d.DB(ctx).Create(navigation).Error
}

func (d *dappAdminRepo) LastDappNavigationSortOrder(ctx context.Context) (int, error) {
	var out model.DappNavigation
	if err := d.DB(ctx).Model(&model.DappNavigation{}).Order("sort_order DESC").First(&out).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, nil
		}
		return 0, err
	}
	return out.SortOrder, nil
}

func (d *dappAdminRepo) BatchUpdateDappNavigation(ctx context.Context, navs []*model.DappNavigation) error {
	for _, nav := range navs {
		if err := d.DB(ctx).Model(&model.DappNavigation{}).
			Where("id=?", nav.ID).
			Updates(map[string]any{"sort_order": nav.SortOrder}).
			Error; err != nil {
			return err
		}
	}
	return nil
}

func (d *dappAdminRepo) DeleteDappNavigation(ctx context.Context, id uint) error {
	return d.DB(ctx).Delete(&model.DappNavigation{}, id).Error
}

func (d *dappAdminRepo) ListDappNavigation(ctx context.Context) ([]*dapp.AdminDappNavigation, error) {
	language := lang.FromContext(ctx)
	var navs []*model.DappNavigation
	if err := d.DB(ctx).Model(&model.DappNavigation{}).
		Preload("DappCategory").
		Preload("DappCategory.DappCategoryI18Ns", "language = ?", language).
		Order("sort_order DESC").
		Find(&navs).Error; err != nil {
		return nil, err
	}
	var list []*dapp.AdminDappNavigation
	for _, nav := range navs {
		adminNav := &dapp.AdminDappNavigation{
			ID:                nav.ID,
			Show:              nav.Show,
			DappCategoryI18Ns: nav.DappCategory.DappCategoryI18Ns,
			SortOrder:         nav.SortOrder,
		}
		// Calculate AppCount
		var appCount int64
		if err := d.DB(ctx).Model(&model.DappCategoryRel{}).Where("dapp_category_id = ?", nav.DappCategoryID).Count(&appCount).Error; err != nil {
			return nil, err
		}
		adminNav.AppCount = int(appCount)

		// Calculate HotCount
		var hotCount int64
		// This query joins DappCategoryRel with Dapp to count hot dapps in the category
		if err := d.DB(ctx).Model(&model.DappCategoryRel{}).
			Joins("JOIN dapp ON dapp.id = dapp_category_rel.dapp_id").
			Where("dapp_category_rel.dapp_category_id = ? AND dapp.hot = ?", nav.DappCategoryID, true).
			Count(&hotCount).Error; err != nil {
			return nil, err
		}
		adminNav.HotCount = int(hotCount)

		list = append(list, adminNav)
	}
	return list, nil
}
