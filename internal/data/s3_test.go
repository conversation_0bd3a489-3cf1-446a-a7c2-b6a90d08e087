package data

import (
	"byd_wallet/internal/biz/base"
	"context"
	"fmt"
	"io"
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

var testS3Config = &S3Config{
	AccessKey: os.<PERSON>env("S3_KEY"),
	SecretKey: os.<PERSON>env("S3_SECRET"),
	Bucket:    os.Getenv("S3_BUCKET"),
	Region:    os.Getenv("S3_REGION"),
}

func TestS3Repo_UploadFileByFileUrl(t *testing.T) {
	if testS3Config.AccessKey == "" {
		t.Skip("no s3 config")
	}
	repo, err := NewS3RepoFromConfig(testS3Config)
	assert.NoError(t, err)
	key := "data/test/test-coinlogo.png"
	location, err := repo.UploadFileByFileUrl(
		context.Background(),
		"https://coin-images.coingecko.com/coins/images/1/large/bitcoin.png?1696501400",
		key,
	)
	assert.NoError(t, err)
	assert.Equal(t, "https://byb-wallet.s3.ap-east-1.amazonaws.com/data/test/test-coinlogo.png", location)
	err = repo.DeleteObject(context.Background(), key)
	assert.NoError(t, err)
}

func TestS3Repo_UploadImageFileByFileUrl(t *testing.T) {
	if testS3Config.AccessKey == "" {
		t.Skip("no s3 config")
	}
	repo, err := NewS3RepoFromConfig(testS3Config)
	assert.NoError(t, err)

	key := "data/test/test-coinlogo"

	err = repo.DeleteObject(context.Background(), key)
	assert.NoError(t, err)

	location, err := repo.UploadImageFileByFileUrl(
		context.Background(),
		"https://coin-images.coingecko.com/coins/images/1/large/bitcoin.png?1696501400",
		key,
	)
	assert.NoError(t, err)
	assert.Equal(t, "https://byb-wallet.s3.ap-east-1.amazonaws.com/data/test/test-coinlogo", location)
}

func TestS3Repo_GeneratePresignedRequest(t *testing.T) {
	if testS3Config.AccessKey == "" {
		t.Skip("no s3 config")
	}
	repo, err := NewS3RepoFromConfig(testS3Config)
	assert.NoError(t, err)
	key := "data/test/test1.png"
	// https://byb-wallet.s3.ap-east-1.amazonaws.com/data/test/test1.png
	request, err := repo.GeneratePresignedRequest4ImagePng(context.Background(), key, time.Second*10)
	assert.NoError(t, err)
	fmt.Println(request)
	//fmt.Printf("https://%s/%s\n", request.SignedHeader.Get("Host"), key)
	body, err := httpRequest(request)
	assert.NoError(t, err)
	fmt.Println(string(body))
	err = repo.DeleteObject(context.Background(), key)
	assert.NoError(t, err)
}

func TestS3Repo_AppAccessUrl(t *testing.T) {
	repo := &S3Repo{
		conf: &S3Config{
			AppAccessAddr: "https://xxxxx.xx:8080",
		},
	}
	url := repo.ToAppAccessUrl(context.Background(), "https://aaa.aa:8081/a/b/c/d/dccc")
	assert.Equal(t, "https://xxxxx.xx:8080/a/b/c/d/dccc", url)
	url = repo.ToAppAccessUrl(context.Background(), "https://xxxxx.xx:8080/a/b/c/d/dccc")
	assert.Equal(t, "https://xxxxx.xx:8080/a/b/c/d/dccc", url)
}

func httpRequest(request *base.PresignedHTTPRequest) ([]byte, error) {
	// &{https://byb-wallet.s3.ap-east-1.amazonaws.com/data/test/test1.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIATT5KPLHI47NCCMUH%2F20250612%2Fap-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250612T061306Z&X-Amz-Expires=10&X-Amz-SignedHeaders=content-disposition%3Bhost&x-id=PutObject&X-Amz-Signature=0dba1f44abca60a6c3b328ab49672f6d8c7289e318f3c2e0d2d4d6816f2cb713 PUT map[Content-Disposition:[inline] Host:[byb-wallet.s3.ap-east-1.amazonaws.com]]}
	file, err := os.Open("testdata/test.png")
	if err != nil {
		return nil, err
	}
	fileInfo, err := file.Stat()
	if err != nil {
		return nil, err
	}
	req, err := http.NewRequest(request.Method, request.URL, file)
	if err != nil {
		return nil, err
	}
	req.Header = request.SignedHeader
	req.ContentLength = fileInfo.Size()
	req.Header = request.SignedHeader
	response, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, err
	}
	body, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, err
	}
	return body, nil
}
