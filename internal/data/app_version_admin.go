package data

import (
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"
)

// AppVersionAdminRepo app版本仓库实现
type AppVersionAdminRepo struct {
	*Data
}

// NewAppVersionAdminRepo 创建app版本仓库
func NewAppVersionAdminRepo(data *Data) biz.AppVersionAdminRepo {
	return &AppVersionAdminRepo{
		data,
	}
}

// CreateAppVersion 创建版本
func (r *AppVersionAdminRepo) CreateAppVersion(ctx context.Context, version *model.AppVersion) error {
	return r.DB(ctx).Create(version).Error
}

// UpdateAppVersion 更新版本
func (r *AppVersionAdminRepo) UpdateAppVersion(ctx context.Context, version *model.AppVersion) error {
	return r.DB(ctx).Save(version).Error
}

// DeleteAppVersion 删除版本
func (r *AppVersionAdminRepo) DeleteAppVersion(ctx context.Context, id uint) error {
	m := &model.AppVersion{}
	m.ID = id
	return r.DB(ctx).Delete(m).Error
}

// GetAppVersion 获取版本详情
func (r *AppVersionAdminRepo) GetAppVersion(ctx context.Context, id uint) (*model.AppVersion, error) {
	var version model.AppVersion
	err := r.DB(ctx).
		Preload("AppVersionI18Ns").
		Take(&version, id).Error
	if err != nil {
		return nil, err
	}
	return &version, nil
}

// ListAppVersion 版本列表
func (r *AppVersionAdminRepo) ListAppVersion(ctx context.Context, filter biz.AdminAppVersionFilter) ([]*model.AppVersion, int64, error) {
	var versions []*model.AppVersion
	var total int64

	query := r.DB(ctx).Model(&model.AppVersion{})

	// 应用类型过滤
	if filter.AppType != "" {
		query = query.Where("app_type = ?", filter.AppType)
	}

	err := query.Scopes(Paginate(filter.Pagination)).Preload("AppVersionI18Ns").
		Order("created_at DESC").
		Find(&versions).Offset(-1).Count(&total).Error

	return versions, total, err
}

// ListPublishedVersions 获取已发布的版本号列表
func (r *AppVersionAdminRepo) ListPublishedVersions(ctx context.Context, appType string) ([]string, error) {
	var versions []string

	query := r.DB(ctx).Model(&model.AppVersion{}).
		Select("version").
		Order("created_at DESC")

	if appType != "" {
		query = query.Where("app_type = ?", appType)
	}

	err := query.Pluck("version", &versions).Error
	return versions, err
}
