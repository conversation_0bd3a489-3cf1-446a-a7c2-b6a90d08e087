package data

import (
	"byd_wallet/common"
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"
	"encoding/json"
	"time"
)

type rpcEndpointRepo struct {
	data *Data
}

func NewRPCEndpointRepo(data *Data) biz.RPCEndpointRepo {
	return &rpcEndpointRepo{data}
}

func (endpoint *rpcEndpointRepo) GetRpcListByChainIndexForCache(chainIndex int64, rpcType string) (list []string, err error) {
	cacheKey := common.GetChainIndexRpcKey(chainIndex, rpcType)
	ctx := context.Background()
	// 1. 尝试从 Redis 获取缓存
	cached, err := endpoint.data.rd.Get(ctx, cacheKey).Result()
	if err == nil {
		// 成功拿到缓存，反序列化
		err = json.Unmarshal([]byte(cached), &list)
		if err == nil {
			return list, nil
		}
	}

	list, err = endpoint.GetRpcListByChainIndex(chainIndex, rpcType)
	if err != nil {
		return nil, err
	}
	// 3. 缓存到 Redis，设置过期时间
	data, _ := json.Marshal(list)
	endpoint.data.rd.Set(ctx, cacheKey, data, 30*time.Minute)

	return
}

func (endpoint *rpcEndpointRepo) GetRpcListByChainIndex(chainIndex int64, rpcType string) (list []string, err error) {
	var rpcList []model.RPCEndpoint
	err = endpoint.data.db.Model(&model.RPCEndpoint{}).Where("chain_index = ? and url_type = ?", chainIndex, rpcType).Find(&rpcList).Error
	if err != nil {
		return nil, err
	}
	for _, rpc := range rpcList {
		list = append(list, rpc.URL)
	}
	return
}

func (endpoint *rpcEndpointRepo) ListRPCEndpointsByChainIndexes(ctx context.Context, chains []int64, rpcType string) ([]*model.RPCEndpoint, error) {
	var list []*model.RPCEndpoint
	if err := endpoint.data.DB(ctx).Model(&model.RPCEndpoint{}).
		Where("chain_index IN (?)", chains).
		Where("url_type = ?", rpcType).
		Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}
