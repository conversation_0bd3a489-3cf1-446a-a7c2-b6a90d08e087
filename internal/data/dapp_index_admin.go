package data

import (
	"byd_wallet/common/lang"
	"byd_wallet/internal/biz/dapp"
	"byd_wallet/model"
	"context"
	"errors"
	"fmt"
	"gorm.io/gorm"
)

func (d *dappAdminRepo) DeleteDappIndex(ctx context.Context, id uint) error {
	return d.DB(ctx).Delete(&model.DappIndex{}, id).Error
}

func (d *dappAdminRepo) CreateDappIndex(ctx context.Context, index *model.DappIndex) error {
	return d.DB(ctx).Create(index).Error
}

func (d *dappAdminRepo) LastDappIndexSortOrder(ctx context.Context) (int, error) {
	var out model.DappIndex
	if err := d.DB(ctx).Model(&model.DappIndex{}).Order("sort_order DESC").First(&out).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, nil
		}
		return 0, err
	}
	return out.SortOrder, nil
}

func (d *dappAdminRepo) ListDappIndex(ctx context.Context) ([]*dapp.AdminDappIndex, error) {
	var indexes []model.DappIndex
	if err := d.DB(ctx).Model(&model.DappIndex{}).Order("sort_order DESC").Find(&indexes).Error; err != nil {
		return nil, err
	}
	language := lang.FromContext(ctx)
	var result []*dapp.AdminDappIndex
	for _, idx := range indexes {
		index := &dapp.AdminDappIndex{
			ID:        idx.ID,
			SortOrder: idx.SortOrder,
			OwnerType: idx.OwnerType,
		}
		switch idx.OwnerType {
		case model.DappCategory{}.TableName():
			var category model.DappCategoryI18N
			if err := d.DB(ctx).Model(&model.DappCategoryI18N{}).
				Where("dapp_category_id = ?", idx.OwnerID).
				Where("language = ?", language).
				Take(&category).Error; err != nil {
				return nil, err
			}
			dapps, err := d.ListCategoryDapp(ctx, idx.OwnerID)
			if err != nil {
				return nil, err
			}
			index.Name = category.Name
			index.Dapps = dapps
			result = append(result, index)
		case model.DappTopic{}.TableName():
			var topic model.DappTopicI18N
			if err := d.DB(ctx).Model(&model.DappTopicI18N{}).
				Where("dapp_topic_id = ?", idx.OwnerID).
				Where("language = ?", language).
				Take(&topic).Error; err != nil {
				return nil, err
			}
			dapps, err := d.ListTopicDapp(ctx, idx.OwnerID)
			if err != nil {
				return nil, err
			}
			index.Name = topic.Title
			index.Dapps = dapps
			result = append(result, index)
		default:
			return nil, fmt.Errorf("unknown owner type: %s", idx.OwnerType)
		}
	}
	return result, nil
}

func (d *dappAdminRepo) BatchUpdateDappIndex(ctx context.Context, indexes []*model.DappIndex) error {
	for _, index := range indexes {
		if err := d.DB(ctx).Model(&model.DappIndex{}).
			Where("id=?", index.ID).
			Updates(map[string]any{"sort_order": index.SortOrder}).
			Error; err != nil {
			return err
		}
	}
	return nil
}
