package data

import (
	"byd_wallet/common/lang"
	"byd_wallet/internal/biz/dapp"
	"byd_wallet/internal/data/okx"
	"byd_wallet/model"
	"context"
	"errors"

	"gorm.io/gorm"
)

func NewDappRepo(data *Data, clien *okx.Client, dto *OKXChainIndexDto) dapp.Repo {
	return &DappRepo{Data: data, Client: clien, OKXChainIndexDto: dto}
}

type DappRepo struct {
	*Data
	*okx.Client
	*OKXChainIndexDto
}

func (d *DappRepo) FilterDapps(ctx context.Context, filter dapp.DappsFilter) ([]*model.Dapp, error) {
	var out []*model.Dapp
	if filter.DappCategoryID > 0 {
		language := lang.FromContext(ctx)
		var dappCategory model.DappCategory
		if err := d.DB(ctx).Model(&model.DappCategory{}).
			Preload("DappCategoryRels", func(db *gorm.DB) *gorm.DB {
				return db.Order("dapp_category_rel.sort_order DESC")
			}).
			Preload("DappCategoryRels.Dapp", "show = ?", true).
			Preload("DappCategoryRels.Dapp.DappI18Ns", "language = ?", language).
			Preload("DappCategoryRels.Dapp.DappBlockchainNetworks.BlockchainNetwork").
			Take(&dappCategory, "id = ? AND show = ?", filter.DappCategoryID, true).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return out, nil
			}
			return nil, err
		}
		var networkID uint
		if filter.FilterChain() {
			var network model.BlockchainNetwork
			if err := d.DB(ctx).Model(&model.BlockchainNetwork{}).
				Where("chain_index = ? AND chain_id = ?", filter.ChainIndex, filter.ChainID).
				Take(&network).Error; err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					return out, nil
				} else {
					return nil, err
				}
			}
			networkID = network.ID
		}
		for _, rel := range dappCategory.DappCategoryRels {
			if rel.Dapp == nil {
				continue
			}
			if networkID > 0 {
				for _, network := range rel.Dapp.DappBlockchainNetworks {
					if network.BlockchainNetworkID == networkID {
						out = append(out, rel.Dapp)
						break
					}
				}
			} else {
				out = append(out, rel.Dapp)
			}
		}

	}

	return out, nil
}

func (d *DappRepo) SearchDappByKey(ctx context.Context, key string) ([]*model.Dapp, error) {
	language := lang.FromContext(ctx)
	var out []*model.Dapp
	likeStr := "%" + key + "%"
	if err := d.DB(ctx).Model(&model.Dapp{}).
		Where("link LIKE ?", likeStr).
		Where("show = ?", true).
		Preload("DappI18Ns", "language = ?", language).
		Preload("DappBlockchainNetworks.BlockchainNetwork"). // Preload nested BlockchainNetwork
		Find(&out).Error; err != nil {
		return nil, err
	}
	if len(out) > 0 {
		return out, nil
	}
	var di18ns []*model.DappI18N
	if err := d.DB(ctx).Model(&model.DappI18N{}).
		Where("language = ?", language).
		Where("name LIKE ?", likeStr).
		Preload("Dapp", "show = ?", true).
		Preload("Dapp.DappBlockchainNetworks.BlockchainNetwork"). // Preload nested BlockchainNetwork
		Find(&di18ns).Error; err != nil {
		return nil, err
	}
	for _, di18n := range di18ns {
		if di18n.Dapp == nil {
			continue
		}
		di18n.Dapp.DappI18Ns = []*model.DappI18N{di18n}
		out = append(out, di18n.Dapp)
	}
	return out, nil
}
