package data

import (
	"byd_wallet/internal/biz"
	"byd_wallet/internal/biz/coindata"
	"byd_wallet/model"
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/shopspring/decimal"
)

type marketRepo struct {
	log *log.Helper

	data *Data
	capi coindata.CoinDataThirdAPI

	pmdCache  *MarketLocalCache
	npmdCache *MarketLocalCache
}

func NewMarketRepo(data *Data, logger log.Logger,
	capi coindata.CoinDataThirdAPI) biz.MarketRepo {
	pmdCache := newMarketLocalCache(logger, data.rd, model.CoinMarketDataPopularCacheKey, 10*time.Minute)
	npmdCache := newMarketLocalCache(logger, data.rd, model.CoinMarketDataNoPopularCacheKey, 60*time.Minute)

	return &marketRepo{
		log:  log.<PERSON>elper(logger),
		data: data,
		capi: capi,

		pmdCache:  pmdCache,
		npmdCache: npmdCache,
	}
}

func (repo *marketRepo) ListTokenByPopular(ctx context.Context, filter *biz.PopularTokenFilter) (
	list []*biz.TokenWithMarket, totalCount int64, err error) {

	// only show popular token asset
	if filter.Keyword == "" {
		ptas := []*model.TokenAssetWithRank{}

		sql := repo.data.db.WithContext(ctx).
			Table(model.TokenAssetRankStub.TableName() + " AS tar").
			Joins("LEFT JOIN token_assets AS ta ON tar.token_asset_id=ta.id").
			Select("ta.*, tar.rank")

		if len(filter.ChainIndexes) == 1 {
			sql = sql.Where("chain_index=?", filter.ChainIndexes[0])
		} else if len(filter.ChainIndexes) > 1 {
			sql = sql.Where("chain_index IN ?", filter.ChainIndexes)
		}

		if err = sql.Count(&totalCount).Error; err != nil {
			return
		}

		offset := (filter.Page - 1) * filter.PageSize
		err = sql.Offset(int(offset)).
			Limit(int(filter.PageSize)).
			Order("rank asc").
			Find(&ptas).Error
		if err != nil {
			return
		}

		list = make([]*biz.TokenWithMarket, 0, len(ptas))
		for _, pta := range ptas {
			twm := &biz.TokenWithMarket{
				ChainIndex: pta.ChainIndex,
				Address:    pta.Address,
				Name:       pta.Name,
				Symbol:     pta.Symbol,
				Decimals:   pta.Decimals,
				LogoUrl:    pta.LogoUrl,
			}
			marketData := repo.pmdCache.SafeGet(ctx, pta.CoinID)
			if marketData != nil {
				twm.PriceChangePercentage24H = marketData.PriceChangePercentage24h
				twm.TradingVolume24H = marketData.TradingVolume24h
				twm.CirculatingSupply = marketData.CirculatingSupply
				twm.Price = marketData.Price
			}
			list = append(list, twm)
		}

		return
	}

	// query all token asset
	tas := []*model.TokenAsset{}

	query := "%" + filter.Keyword + "%"
	sql := repo.data.db.WithContext(ctx).Model(model.TokenAssetStub).
		Where("address LIKE ? OR name LIKE ? OR symbol LIKE ?", query, query, query)

	if err = sql.Count(&totalCount).Error; err != nil {
		return
	}

	offset := (filter.Page - 1) * filter.PageSize
	err = sql.Offset(int(offset)).Limit(int(filter.PageSize)).Find(&tas).Error
	if err != nil {
		return
	}

	list = make([]*biz.TokenWithMarket, 0, len(tas))
	for _, ta := range tas {
		twm := &biz.TokenWithMarket{
			ChainIndex: ta.ChainIndex,
			Address:    ta.Address,
			Name:       ta.Name,
			Symbol:     ta.Symbol,
			Decimals:   ta.Decimals,
			LogoUrl:    ta.LogoUrl,
		}
		marketData := repo.pmdCache.SafeGet(ctx, ta.CoinID)
		if marketData == nil {
			marketData = repo.npmdCache.SafeGet(ctx, ta.CoinID)
		}
		if marketData != nil {
			twm.PriceChangePercentage24H = marketData.PriceChangePercentage24h
			twm.TradingVolume24H = marketData.TradingVolume24h
			twm.CirculatingSupply = marketData.CirculatingSupply
			twm.Price = marketData.Price
		}
		list = append(list, twm)
	}
	return
}

// FIXME: opz
func (repo *marketRepo) ListSimpleKlineByTokenIDs(ctx context.Context, tokenIDs []*biz.TokenID) ([]*biz.SimpleKline, error) {
	rel, err := repo.GetTokenIDRelCacheByTokenIDs(ctx, tokenIDs)
	if err != nil {
		return nil, fmt.Errorf("GetTokenIDRelCacheByTokenIDs: %w", err)
	}
	r, err := repo.data.rd.HMGet(ctx, model.CoinOHLCsCacheKey, rel.UniqueIDs...).Result()
	if err != nil {
		return nil, fmt.Errorf("query coin ohlcs cache: %w", err)
	}
	res := make([]*biz.SimpleKline, 0, len(r))
	for _, v := range r {
		vs, ok := v.(string)
		if !ok {
			continue
		}

		var ohlcs []*model.CoinOHLC
		err = json.Unmarshal([]byte(vs), &ohlcs)
		if err != nil {
			return nil, fmt.Errorf("parse result json: %w", err)
		}
		if len(ohlcs) == 0 {
			continue
		}

		times := make([]int64, 0, len(ohlcs))
		closePrices := make([]string, 0, len(ohlcs))
		for _, ohlc := range ohlcs {
			times = append(times, ohlc.LastUpdatedAt)
			closePrices = append(closePrices, ohlc.Close.String())
		}

		for _, t := range rel.CoinID2TokenID[ohlcs[0].CoinID] {
			res = append(res, &biz.SimpleKline{
				ChainIndex:  t.ChainIndex,
				Address:     t.Address,
				Times:       times,
				ClosePrices: closePrices,
			})
		}
	}
	return res, nil
}

// FIXME: opz
func (repo *marketRepo) GetTokenIDRelCacheByTokenIDs(ctx context.Context, tokenIDs []*biz.TokenID) (*biz.CoinTokenRel, error) {
	conds := [][]interface{}{}
	for _, v := range tokenIDs {
		conds = append(conds, []interface{}{v.ChainIndex, v.Address})
	}
	var list []*model.CoinInfo
	err := repo.data.db.WithContext(ctx).Model(model.CoinInfoStub).
		Where("(chain_index, address) in (?)", conds).
		Find(&list).Error
	if err != nil {
		return nil, err
	}
	r := &biz.CoinTokenRel{
		CoinID2TokenID: map[string][]*biz.TokenID{},
		UniqueIDs:      make([]string, 0, len(list)),
	}
	for _, v := range list {
		ts, ok := r.CoinID2TokenID[v.CoinID]
		if !ok {
			r.CoinID2TokenID[v.CoinID] = []*biz.TokenID{
				{
					ChainIndex: v.ChainIndex,
					Address:    v.Address,
				},
			}
			r.UniqueIDs = append(r.UniqueIDs, v.CoinID)
			continue
		}
		r.CoinID2TokenID[v.CoinID] = append(ts, &biz.TokenID{
			ChainIndex: v.ChainIndex,
			Address:    v.Address,
		})
	}
	return r, nil
}

// FIXME: opz
func (repo *marketRepo) GetTokenIDRelCacheByChainIndex(ctx context.Context, chainIndex int64) (*biz.CoinTokenRel, error) {
	var list []*model.CoinInfo
	err := repo.data.db.WithContext(ctx).Model(model.CoinInfoStub).
		Where("chain_index = ?", chainIndex).
		Find(&list).Error
	if err != nil {
		return nil, err
	}
	r := &biz.CoinTokenRel{
		CoinID2TokenID: map[string][]*biz.TokenID{},
		UniqueIDs:      make([]string, 0, len(list)),
	}
	for _, v := range list {
		ts, ok := r.CoinID2TokenID[v.CoinID]
		if !ok {
			r.CoinID2TokenID[v.CoinID] = []*biz.TokenID{
				{
					ChainIndex: v.ChainIndex,
					Address:    v.Address,
				},
			}
			r.UniqueIDs = append(r.UniqueIDs, v.CoinID)
			continue
		}
		r.CoinID2TokenID[v.CoinID] = append(ts, &biz.TokenID{
			ChainIndex: v.ChainIndex,
			Address:    v.Address,
		})
	}
	return r, nil
}

// FIXME: opz
func (repo *marketRepo) ListCoinMarketDataByCoinIDs(ctx context.Context, coinIDs []string) ([]*model.CoinMarketData, error) {
	r, err := repo.data.rd.HMGet(ctx, model.CoinMarketDataPopularCacheKey, coinIDs...).Result()
	if err != nil {
		return nil, fmt.Errorf("query coin market data cache: %w", err)
	}
	res := make([]*model.CoinMarketData, 0, len(r))
	for _, v := range r {
		vs, ok := v.(string)
		if !ok {
			continue
		}

		var vv model.CoinMarketData
		err = json.Unmarshal([]byte(vs), &vv)
		if err != nil {
			return nil, fmt.Errorf("parse result json: %w", err)
		}
		res = append(res, &vv)
	}
	return res, nil
}

func (repo *marketRepo) ListCurrencyUSDRate(ctx context.Context, bases []string) (map[string]decimal.Decimal, error) {
	// NOTE: opz
	lbases := make([]string, 0, len(bases))
	for _, base := range bases {
		base = strings.ToLower(base)
		lbases = append(lbases, base)
	}
	data, err := repo.data.rd.HMGet(ctx, model.CurrencyRatesCacheKey, lbases...).Result()
	if err != nil {
		return nil, fmt.Errorf("query currency rates cache: %w", err)
	}

	r := make(map[string]decimal.Decimal)
	dl := len(data)
	for i, base := range lbases {
		if i >= dl {
			break
		}
		vs, ok := data[i].(string)
		if !ok {
			continue
		}
		rate, _ := decimal.NewFromString(vs)
		r[base] = rate
	}
	return r, nil
}
