package weidubot

import (
	"byd_wallet/utils"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

type BuyEnergyReq struct {
	Address string // energy receiver
	Count   int64  // rent number // required: > 30000
	Period  string // rent duration // 1h,1day,3day
}
type BuyEnergyReply struct {
	OrderSn string  `json:"order_sn"`
	Price   float64 `json:"price"`   // 计算出的单位能量价格 (SUN)
	Fee     float64 `json:"fee"`     // 额外手续费 (TRX)
	Amount  float64 `json:"amount"`  // 订单总费用 (TRX)
	Balance float64 `json:"balance"` // 操作后账户余额 (可能不准确或不提供)。
}

type QueryEnergyPriceReply struct {
	Price  float64 `json:"price"`  // 单位价格 (SUN / 每百万资源 / 每周期)
	Amount float64 `json:"amount"` // 订单总费用 (TRX)
	Fee    float64 `json:"fee"`    // 额外的手续费 (单位：TRX)，例如小额订单费用
}

type Client struct {
	log *log.Helper
	cfg *ClientConf
}

type ClientConf struct {
	ApiUrl    string `json:"api_url"`
	ApiKey    string `json:"api_key"`
	ApiSecret string `json:"api_secret"`
}

func NewClient(logger log.Logger, cfg *ClientConf) (*Client, error) {
	if cfg == nil {
		return nil, errors.New("api config is nil")
	}
	if cfg.ApiUrl == "" {
		return nil, errors.New("apiUrl is empty")
	}
	if cfg.ApiKey == "" {
		return nil, errors.New("apiKey is empty")
	}
	if cfg.ApiSecret == "" {
		return nil, errors.New("apiSecret is empty")
	}
	cli := &Client{
		log: log.NewHelper(logger),
		cfg: cfg,
	}
	return cli, nil
}

const defaultTronRentPeriod = "1h"

func (c *Client) QueryEnergyPrice(ctx context.Context, count int64, period string) (*QueryEnergyPriceReply, error) {
	cli := utils.NewHttpClient(c.cfg.ApiUrl+"/api/v2/query_price", "GET")
	cli.SetHeader("x-api-key", c.cfg.ApiKey)
	sig, unix := c.generateSign()
	cli.SetHeader("x-timestamp", unix)
	cli.SetHeader("x-signature", sig)

	cli.SetParam("type", "energy")
	cli.SetParam("count", strconv.Itoa(int(count)))
	cli.SetParam("period", period)

	body, err := cli.Send()
	if err != nil {
		return nil, fmt.Errorf("send fail: %v", err)
	}
	var reply struct {
		Code int                    `json:"code"`
		Msg  string                 `json:"msg"`
		Time string                 `json:"time"`
		Data map[string]interface{} `json:"data"`
	}
	err = json.Unmarshal(body, &reply)
	if err != nil {
		return nil, fmt.Errorf("parse json: %v: %s", err, string(body))
	}
	if reply.Code != 1 {
		return nil, fmt.Errorf("request fail: %d: %s", reply.Code, reply.Msg)
	}

	price, err := takeValue[float64](reply.Data, "price")
	if err != nil {
		return nil, err
	}
	feeStr, err := takeValue[string](reply.Data, "fee")
	if err != nil {
		return nil, err
	}
	fee, err := strconv.ParseFloat(feeStr, 64)
	if err != nil {
		return nil, fmt.Errorf("parse fee: %w: %s", err, feeStr)
	}
	amountStr, err := takeValue[string](reply.Data, "amount")
	if err != nil {
		return nil, err
	}
	amount, err := strconv.ParseFloat(amountStr, 64)
	if err != nil {
		return nil, fmt.Errorf("parse amount: %w: %s", err, amountStr)
	}

	result := &QueryEnergyPriceReply{
		Price:  price,
		Fee:    fee,
		Amount: amount,
	}
	return result, nil
}

const minEnergyCount = 30000

func (c *Client) BuyEnergy(ctx context.Context, req *BuyEnergyReq) (*BuyEnergyReply, error) {
	c.log.Infof("buy energy: req=%+v", req)

	if req.Count < minEnergyCount {
		return nil, fmt.Errorf("energy count must be greater than %d", minEnergyCount)
	}

	cli := utils.NewHttpClient(c.cfg.ApiUrl+"/api/v2/buy_energy", "POST")
	cli.SetHeader("Content-Type", "application/x-www-form-urlencoded")
	cli.SetHeader("x-api-key", c.cfg.ApiKey)
	sig, unix := c.generateSign()
	cli.SetHeader("x-timestamp", unix)
	cli.SetHeader("x-signature", sig)

	reqUrlValues := url.Values{}
	reqUrlValues.Set("address", req.Address)
	reqUrlValues.Set("count", strconv.Itoa(int(req.Count)))
	reqUrlValues.Set("period", req.Period)
	cli.SetBody(strings.NewReader(reqUrlValues.Encode()))

	body, err := cli.Send()
	if err != nil {
		return nil, fmt.Errorf("send fail: %v", err)
	}
	var reply struct {
		Code int                    `json:"code"`
		Msg  string                 `json:"msg"`
		Time string                 `json:"time"`
		Data map[string]interface{} `json:"data"`
	}
	err = json.Unmarshal(body, &reply)
	if err != nil {
		return nil, fmt.Errorf("parse json: %v: %s", err, string(body))
	}
	if reply.Code != 1 {
		return nil, fmt.Errorf("request fail: %d: %s", reply.Code, reply.Msg)
	}
	orderSn, err := takeValue[string](reply.Data, "order_sn")
	if err != nil {
		return nil, err
	}
	price, err := takeValue[float64](reply.Data, "price")
	if err != nil {
		return nil, err
	}
	fee, err := takeValue[float64](reply.Data, "fee")
	if err != nil {
		// docs say fee is float64, but actually is string
		fee2, err := takeValue[string](reply.Data, "fee")
		if err != nil {
			return nil, err
		}
		fee, err = strconv.ParseFloat(fee2, 64)
		if err != nil {
			return nil, fmt.Errorf("parse fee: %w: %s", err, fee2)
		}
	}
	amount, err := takeValue[float64](reply.Data, "amount")
	if err != nil {
		return nil, err
	}
	balance, err := takeValue[float64](reply.Data, "balance")
	if err != nil {
		return nil, err
	}

	result := &BuyEnergyReply{
		OrderSn: orderSn,
		Price:   price,
		Fee:     fee,
		Amount:  amount,
		Balance: balance,
	}
	return result, nil
}

func takeValue[V any](data map[string]interface{}, key string) (V, error) {
	v, ok := data[key]
	if !ok {
		var vv V
		return vv, fmt.Errorf("%s not found", key)
	}
	vv, ok := v.(V)
	if !ok {
		return vv, fmt.Errorf("%s is not %T: %v", key, vv, reflect.TypeOf(v).String())
	}
	return vv, nil
}

func (c *Client) generateSign() (sig, unix string) {
	unix = strconv.Itoa(int(time.Now().Unix()))
	hmac := hmac.New(sha256.New, []byte(c.cfg.ApiSecret))
	hmac.Write([]byte(c.cfg.ApiKey + unix))
	sigbts := hmac.Sum(nil)
	sig = strings.ToLower(hex.EncodeToString(sigbts))
	return
}
