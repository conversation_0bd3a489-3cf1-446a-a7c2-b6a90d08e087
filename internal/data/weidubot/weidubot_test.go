package weidubot

import (
	"os"
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
)

var (
	apiUrl         = os.Getenv("RENT_API_URL")
	apiKey         = os.Getenv("RENT_API_KEY")
	apiSecret      = os.Getenv("RENT_API_SECRET")
	receiveTrxAddr = os.Getenv("RENT_RECEIVE_TRX_ADDR")
	tronRPC        = os.Getenv("TRON_RPC")
	fromAddress    = os.Getenv("TRON_FROM_ADDRESS")
	toAddress      = os.Getenv("TRON_TO_ADDRESS")
	fromPrivateKey = os.Getenv("TRON_FROM_PRIVATE_KEY")
)

func TestQueryEnergyPrice(t *testing.T) {
	if apiSecret == "" {
		t.Skip("no api secret")
	}
	cfg := &ClientConf{
		ApiKey:    apiKey,
		ApiSecret: apiSecret,
		ApiUrl:    apiUrl,
	}
	cli, err := NewClient(log.DefaultLogger, cfg)
	assert.NoError(t, err)
	reply, err := cli.QueryEnergyPrice(t.Context(), minEnergyCount, defaultTronRentPeriod)
	assert.NoError(t, err)
	assert.NotNil(t, reply)
	t.Logf("reply: %+v", reply)
}

func TestBuyEnergy(t *testing.T) {
	if apiSecret == "" {
		t.Skip("no api secret")
	}
	cfg := &ClientConf{
		ApiKey:    apiKey,
		ApiSecret: apiSecret,
		ApiUrl:    apiUrl,
	}
	cli, err := NewClient(log.DefaultLogger, cfg)
	assert.NoError(t, err)
	reply, err := cli.BuyEnergy(t.Context(), &BuyEnergyReq{
		Address: toAddress,
		Count:   minEnergyCount,
		Period:  defaultTronRentPeriod,
	})
	assert.NoError(t, err)
	assert.NotNil(t, reply)
	t.Logf("reply: %+v", reply)
}
