package data

import (
	"byd_wallet/internal/biz/base"
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// TestModel 测试用的模型
type TestModel struct {
	ID         uint `gorm:"primarykey"`
	Name       string
	SortOrder  int
	IsAll      bool
	ChainIndex int
}

type SortTestSuite struct {
	suite.Suite
	db   *gorm.DB
	data *Data
	repo base.SortRepo
}

func (suite *SortTestSuite) SetupSuite() {
	// 使用内存SQLite数据库进行测试
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// 自动迁移测试表
	err = db.AutoMigrate(&TestModel{})
	suite.Require().NoError(err)

	suite.db = db
	suite.data = &Data{db: db}
	suite.repo = NewCommonSortRepo(suite.data)
}

func (suite *SortTestSuite) SetupTest() {
	// 每个测试前清空数据
	suite.db.Exec("DELETE FROM test_models")
}

func (suite *SortTestSuite) TearDownSuite() {
	sqlDB, _ := suite.db.DB()
	sqlDB.Close()
}

// 创建测试数据
func (suite *SortTestSuite) createTestData() {
	testData := []TestModel{
		{ID: 1, Name: "Item1", SortOrder: 1},
		{ID: 2, Name: "Item2", SortOrder: 2},
		{ID: 3, Name: "Item3", SortOrder: 3},
		{ID: 4, Name: "Item4", SortOrder: 4},
		{ID: 5, Name: "Item5", SortOrder: 5},
	}

	for _, item := range testData {
		suite.db.Create(&item)
	}
}

// 创建测试数据2
func (suite *SortTestSuite) createTestDataWithGap() {
	testData := []TestModel{
		{ID: 1, Name: "Item1", SortOrder: 1},
		{ID: 2, Name: "Item2", SortOrder: 2},
		{ID: 3, Name: "Item3", SortOrder: 3},
		{ID: 4, Name: "Item4", SortOrder: 4},
		{ID: 5, Name: "Item5", SortOrder: 8},
	}

	testData2 := []TestModel{
		{ID: 6, Name: "Item1", SortOrder: 1, IsAll: true, ChainIndex: 1},
		{ID: 7, Name: "Item2", SortOrder: 2, IsAll: true, ChainIndex: 1},
		{ID: 8, Name: "Item3", SortOrder: 3, IsAll: true, ChainIndex: 1},
		{ID: 9, Name: "Item4", SortOrder: 4, IsAll: true, ChainIndex: 1},
		{ID: 10, Name: "Item5", SortOrder: 8, IsAll: true, ChainIndex: 1},
	}

	testData3 := []TestModel{
		{ID: 11, Name: "Item1", SortOrder: 1, IsAll: true},
		{ID: 12, Name: "Item2", SortOrder: 2, IsAll: true},
		{ID: 13, Name: "Item3", SortOrder: 3, IsAll: true},
		{ID: 14, Name: "Item4", SortOrder: 4, IsAll: true},
		{ID: 15, Name: "Item5", SortOrder: 8, IsAll: true},
	}

	suite.db.Create(&testData)
	suite.db.Create(&testData2)
	suite.db.Create(&testData3)
}

// TestSortTypeUp 测试向上排序
func (suite *SortTestSuite) TestSortTypeUp() {
	suite.createTestData()

	ctx := context.Background()

	// 将ID为3的项目向上移动（从位置3移动到位置4）
	sortInput := base.SortInput{
		ID:       3,
		SortType: base.SortTypeUp,
		Model:    &TestModel{},
	}

	err := suite.repo.Sort(ctx, &sortInput)
	suite.NoError(err)

	// 验证排序结果
	var items []TestModel
	suite.db.Order("sort_order ASC").Find(&items)

	// ID为3的项目应该移动到位置4，ID为4的项目应该移动到位置3
	suite.Equal(uint(1), items[0].ID)
	suite.Equal(1, items[0].SortOrder)
	suite.Equal(uint(2), items[1].ID)
	suite.Equal(2, items[1].SortOrder)
	suite.Equal(uint(4), items[2].ID) // 原来ID为4的项目现在在位置3
	suite.Equal(3, items[2].SortOrder)
	suite.Equal(uint(3), items[3].ID) // 原来ID为3的项目现在在位置4
	suite.Equal(4, items[3].SortOrder)
	suite.Equal(uint(5), items[4].ID)
	suite.Equal(5, items[4].SortOrder)
}

// TestSortTypeDown 测试向下排序
func (suite *SortTestSuite) TestSortTypeDown() {
	suite.createTestData()

	ctx := context.Background()

	// 将ID为3的项目向下移动（从位置3移动到位置2）
	sortInput := base.SortInput{
		ID:       3,
		SortType: base.SortTypeDown,
		Model:    &TestModel{},
	}

	err := suite.repo.Sort(ctx, &sortInput)
	suite.NoError(err)

	// 验证排序结果
	var items []TestModel
	suite.db.Order("sort_order ASC").Find(&items)

	// ID为3的项目应该移动到位置2，ID为2的项目应该移动到位置3
	suite.Equal(uint(1), items[0].ID)
	suite.Equal(1, items[0].SortOrder)
	suite.Equal(uint(3), items[1].ID) // 原来ID为3的项目现在在位置2
	suite.Equal(2, items[1].SortOrder)
	suite.Equal(uint(2), items[2].ID) // 原来ID为2的项目现在在位置3
	suite.Equal(3, items[2].SortOrder)
	suite.Equal(uint(4), items[3].ID)
	suite.Equal(4, items[3].SortOrder)
	suite.Equal(uint(5), items[4].ID)
	suite.Equal(5, items[4].SortOrder)
}

// TestSortTypeDown 测试向下排序
func (suite *SortTestSuite) TestSortTypeDownWithGap() {
	suite.createTestDataWithGap()

	ctx := context.Background()

	// 将ID为3的项目向下移动（从位置3移动到位置2）
	sortInput := base.SortInput{
		ID:       10,
		SortType: base.SortTypeDown,
		Model:    &TestModel{},
		Where: []base.SortWhere{
			{Column: "is_all", ColumnValue: true},
			{Column: "chain_index", ColumnValue: 1},
		},
	}

	err := suite.repo.Sort(ctx, &sortInput)
	suite.NoError(err)

	// 验证排序结果
	var items []TestModel
	suite.db.Where("is_all=? and chain_index=?", true, 1).Order("sort_order ASC").Find(&items)

	// ID为3的项目应该移动到位置2，ID为2的项目应该移动到位置3
	suite.Equal(uint(6), items[0].ID)
	suite.Equal(1, items[0].SortOrder)
	suite.Equal(uint(7), items[1].ID) // 原来ID为3的项目现在在位置2
	suite.Equal(2, items[1].SortOrder)
	suite.Equal(uint(8), items[2].ID) // 原来ID为2的项目现在在位置3
	suite.Equal(3, items[2].SortOrder)
	suite.Equal(uint(10), items[3].ID)
	suite.Equal(4, items[3].SortOrder)
	suite.Equal(uint(9), items[4].ID)
	suite.Equal(8, items[4].SortOrder)
}

func (suite *SortTestSuite) TestSortWithGap() {
	suite.createTestDataWithGap()

	ctx := context.Background()
	where := []base.SortWhere{
		{Column: "is_all", ColumnValue: true},
		{Column: "chain_index", ColumnValue: 1},
	}
	sortInputs := []base.SortInput{
		{
			ID:       6,
			SortType: base.SortTypeTop,
			Model:    &TestModel{},
			Where:    where,
		},
		{
			ID:       9,
			SortType: base.SortTypeTop,
			Model:    &TestModel{},
			Where:    where,
		},
		{
			ID:       7,
			SortType: base.SortTypeTop,
			Model:    &TestModel{},
			Where:    where,
		},
		{
			ID:       10,
			SortType: base.SortTypeTop,
			Model:    &TestModel{},
			Where:    where,
		},
		{
			ID:       6,
			SortType: base.SortTypeDown,
			Model:    &TestModel{},
			Where:    where,
		},
		{
			ID:       6,
			SortType: base.SortTypeDown,
			Model:    &TestModel{},
			Where:    where,
		},
	}
	for _, input := range sortInputs {
		err := suite.repo.Sort(ctx, &input)
		suite.NoError(err)
	}

	// 验证排序结果
	var items []TestModel
	suite.db.Where("is_all=? and chain_index=?", true, 1).Order("sort_order DESC").Find(&items)
	for _, item := range items {
		fmt.Println(item.ID, item.SortOrder)
	}
}

// TestSortTypeTop 测试置顶
func (suite *SortTestSuite) TestSortTypeTop() {
	suite.createTestData()

	ctx := context.Background()

	// 将ID为2的项目置顶（移动到最大排序值）
	sortInput := base.SortInput{
		ID:       2,
		SortType: base.SortTypeTop,
		Model:    &TestModel{},
	}

	err := suite.repo.Sort(ctx, &sortInput)
	suite.NoError(err)

	// 验证排序结果
	var item TestModel
	suite.db.Where("id = ?", 2).First(&item)

	// ID为2的项目应该有最大的sort_order值（5）
	suite.Equal(6, item.SortOrder)
}

func (suite *SortTestSuite) TestSortTypeTopAtBoundary() {
	suite.createTestData()

	ctx := context.Background()

	// 将ID为2的项目置顶（移动到最大排序值）
	sortInput := base.SortInput{
		ID:       5,
		SortType: base.SortTypeTop,
		Model:    &TestModel{},
	}

	err := suite.repo.Sort(ctx, &sortInput)
	suite.NoError(err)

	// 验证排序结果
	var item TestModel
	suite.db.Where("id = ?", 5).First(&item)

	// ID为2的项目应该有最大的sort_order值（5）
	suite.Equal(5, item.SortOrder)
}

// TestSortUpAtBoundary 测试在边界位置向上排序
func (suite *SortTestSuite) TestSortUpAtBoundary() {
	suite.createTestData()

	ctx := context.Background()

	// 尝试将最后一个项目（ID为5）向上移动
	sortInput := base.SortInput{
		ID:       5,
		SortType: base.SortTypeUp,
		Model:    &TestModel{},
	}

	err := suite.repo.Sort(ctx, &sortInput)
	suite.NoError(err) // 应该成功，但不会有任何变化

	// 验证排序没有变化
	var items []TestModel
	suite.db.Order("sort_order ASC").Find(&items)

	for i, item := range items {
		suite.Equal(uint(i+1), item.ID)
		suite.Equal(i+1, item.SortOrder)
	}
}

// TestSortDownAtBoundary 测试在边界位置向下排序
func (suite *SortTestSuite) TestSortDownAtBoundary() {
	suite.createTestData()

	ctx := context.Background()

	// 尝试将第一个项目（ID为1）向下移动
	sortInput := base.SortInput{
		ID:       1,
		SortType: base.SortTypeDown,
		Model:    &TestModel{},
	}

	err := suite.repo.Sort(ctx, &sortInput)
	suite.NoError(err) // 应该成功，但不会有任何变化

	// 验证排序没有变化
	var items []TestModel
	suite.db.Order("sort_order ASC").Find(&items)

	for i, item := range items {
		suite.Equal(uint(i+1), item.ID)
		suite.Equal(i+1, item.SortOrder)
	}
}

// TestSortWithNonExistentID 测试不存在的ID
func (suite *SortTestSuite) TestSortWithNonExistentID() {
	suite.createTestData()

	ctx := context.Background()

	// 尝试对不存在的ID进行排序
	sortInput := base.SortInput{
		ID:       999,
		SortType: base.SortTypeUp,
		Model:    &TestModel{},
	}

	err := suite.repo.Sort(ctx, &sortInput)
	suite.Error(err) // 应该返回错误
}

// TestSortWithInvalidSortType 测试无效的排序类型
func (suite *SortTestSuite) TestSortWithInvalidSortType() {
	suite.createTestData()

	ctx := context.Background()

	// 使用无效的排序类型
	sortInput := base.SortInput{
		ID:       1,
		SortType: base.SortType(999), // 无效的排序类型
		Model:    &TestModel{},
	}

	err := suite.repo.Sort(ctx, &sortInput)
	suite.Error(err)
	suite.Contains(err.Error(), "unknown sort type")
}

// TestSortInTransaction 测试在事务中进行排序
func (suite *SortTestSuite) TestSortInTransaction() {
	suite.createTestData()

	ctx := context.Background()

	// 在事务中执行排序
	err := suite.data.ExecTx(ctx, func(ctx context.Context) error {
		sortInput := base.SortInput{
			ID:       3,
			SortType: base.SortTypeUp,
			Model:    &TestModel{},
		}
		return suite.repo.Sort(ctx, &sortInput)
	})

	suite.NoError(err)

	// 验证排序结果
	var items []TestModel
	suite.db.Order("sort_order ASC").Find(&items)

	// 验证ID为3和4的项目交换了位置
	suite.Equal(uint(4), items[2].ID)
	suite.Equal(3, items[2].SortOrder)
	suite.Equal(uint(3), items[3].ID)
	suite.Equal(4, items[3].SortOrder)
}

func TestSortSuite(t *testing.T) {
	suite.Run(t, new(SortTestSuite))
}

// 单独的单元测试函数
func TestNewCommonSortRepo(t *testing.T) {
	data := &Data{}
	repo := NewCommonSortRepo(data)

	assert.NotNil(t, repo)
	assert.IsType(t, &commonSortRepo{}, repo)
}
