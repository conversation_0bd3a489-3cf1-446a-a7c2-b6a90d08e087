package data

import (
	"byd_wallet/model"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"testing"
)

func TestGormMigrate(t *testing.T) {
	db, err := gorm.Open(sqlite.Open("file::memory:?cache=shared"), &gorm.Config{})
	assert.NoError(t, err)
	err = db.Table("tx1").AutoMigrate(&model.Transaction{})
	assert.NoError(t, err)
	tables, err := db.Migrator().GetTables()
	assert.NoError(t, err)
	assert.Len(t, tables, 1)
	assert.Equal(t, "tx1", tables[0])
}
