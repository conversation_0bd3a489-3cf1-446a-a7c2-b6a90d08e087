package data

import (
	"byd_wallet/internal/biz"
	"byd_wallet/internal/biz/base"
	"byd_wallet/model"
	"context"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestSwapAdminRepo_ListHotToken(t *testing.T) {
}

func TestSwapAdminRepo_ListToken(t *testing.T) {
	db := newTestSwapDB(t)
	migrateSwapTables(t, db)
	chain1 := &model.BlockchainNetwork{
		Name:       "chain1",
		ChainName:  "chain1",
		ChainIndex: 1,
		ChainID:    "1",
	}
	chain2 := &model.BlockchainNetwork{
		Name:       "chain2",
		ChainName:  "chain2",
		ChainIndex: 2,
		ChainID:    "2",
	}
	chain3 := &model.BlockchainNetwork{
		Name:       "chain3",
		ChainName:  "chain3",
		ChainIndex: 3,
		ChainID:    "3",
	}
	chains := []*model.BlockchainNetwork{chain1, chain2, chain3}
	db.Create(chains)
	repo := &swapAdminRepo{
		Data: &Data{
			db: db,
		},
	}
	channel := &model.SwapChannel{
		Name:   "test-channel",
		Enable: true,
	}
	channel2 := &model.SwapChannel{
		Name:   "test-channel-2",
		Enable: true,
	}
	db.Create([]*model.SwapChannel{channel, channel2})
	ta1 := &model.TokenAsset{
		ChainIndex: 1,
		Address:    "test-address-1",
		Symbol:     "test-symbol-1",
		Name:       "test-name-1",
	}
	ta2 := &model.TokenAsset{
		ChainIndex: 2,
		Address:    "test-address-2",
		Symbol:     "test-symbol-2",
		Name:       "test-name-2",
	}
	ta3 := &model.TokenAsset{
		ChainIndex: 3,
		Address:    "test-address-3",
		Symbol:     "test-symbol-3",
		Name:       "test-name-3",
	}
	db.Create([]*model.TokenAsset{ta1, ta2, ta3})
	token1 := &model.SwappableToken{
		BlockchainNetworkID: chain2.ID,
		TokenAssetID:        ta2.ID,
		ChainIndex:          ta2.ChainIndex,
		Address:             ta2.Address,
		Enable:              true,
		Display:             true,
		Channels: []*model.SwappableTokenChannel{
			{SwapChannelID: channel.ID},
			{SwapChannelID: channel2.ID},
		},
	}
	token2 := &model.SwappableToken{
		BlockchainNetworkID: chain1.ID,
		TokenAssetID:        ta1.ID,
		ChainIndex:          ta1.ChainIndex,
		Address:             ta1.Address,
		Enable:              true,
		Display:             true,
		Channels: []*model.SwappableTokenChannel{
			{SwapChannelID: channel.ID},
		},
	}
	token3 := &model.SwappableToken{
		BlockchainNetworkID: chain3.ID,
		TokenAssetID:        ta3.ID,
		ChainIndex:          ta3.ChainIndex,
		Address:             ta3.Address,
		Enable:              false,
		Display:             false,
		Channels: []*model.SwappableTokenChannel{
			{SwapChannelID: channel.ID},
		},
	}
	db.Create([]*model.SwappableToken{token1, token2, token3})
	ctx := context.Background()
	tokens, count, err := repo.ListToken(ctx, biz.AdminSwapTokenFilter{
		Pagination: base.Pagination{Page: 1, PageSize: 10},
		Symbol:     "",
		Address:    "",
		ChannelID:  0,
		ChainIndex: -1,
	})
	assert.NoError(t, err)
	assert.Equal(t, int(count), len(tokens))
	for _, token := range tokens {
		assert.NotNil(t, token.TokenAsset)
		assert.True(t, len(token.Channels) > 0)
	}

	tokens, count, err = repo.ListToken(ctx, biz.AdminSwapTokenFilter{
		Pagination: base.Pagination{Page: 1, PageSize: 10},
		//Symbol:     "test-symbol-2",
		//Address:    "test-address-2",
		ChannelID:  channel2.ID,
		ChainIndex: -1,
	})
	assert.NoError(t, err)
	assert.Len(t, tokens, 1)
	assert.Len(t, tokens[0].Channels, 2)
	assert.Equal(t, tokens[0].TokenAssetID, token1.TokenAssetID)
	assert.Equal(t, tokens[0].BlockchainNetwork.ID, chain2.ID)

	db.Create([]*model.SwappableHotToken{
		{
			SwappableTokenID: token1.ID,
			ChainIndex:       token1.ChainIndex,
			SortOrder:        1,
			IsAll:            false,
		},
		{
			SwappableTokenID: token2.ID,
			ChainIndex:       token2.ChainIndex,
			SortOrder:        2,
			IsAll:            false,
		},
		{
			SwappableTokenID: token1.ID,
			ChainIndex:       token1.ChainIndex,
			SortOrder:        1,
			IsAll:            true,
		},
		{
			SwappableTokenID: token2.ID,
			ChainIndex:       token2.ChainIndex,
			SortOrder:        2,
			IsAll:            true,
		},
	})

	hotTokens, count, err := repo.ListHotToken(ctx, biz.AdminHotTokenFilter{
		Pagination: base.Pagination{Page: 1, PageSize: 10},
		ChainIndex: -1,
		Symbol:     "",
		Address:    "",
	})
	assert.NoError(t, err)
	assert.Equal(t, int(count), len(hotTokens))
	assert.Equal(t, int(count), 2)
	assert.Equal(t, 2, hotTokens[0].SortOrder)

	hotTokens, count, err = repo.ListHotToken(ctx, biz.AdminHotTokenFilter{
		Pagination: base.Pagination{Page: 1, PageSize: 10},
		//ChainIndex: 1,
		ChainIndex: -1,
		Symbol:     "test-symbol-2",
		Address:    "test-address-2",
	})
	assert.NoError(t, err)
	assert.Len(t, hotTokens, 1)
}
