package data

import (
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"
	"fmt"
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

type TokenAssetRepoTestSuite struct {
	suite.Suite
	data *Data
	repo *tokenAssetRepo
	ctx  context.Context
}

func TestTokenAssetRepoTestSuite(t *testing.T) {
	suite.Run(t, new(TokenAssetRepoTestSuite))
}

func (s *TokenAssetRepoTestSuite) SetupSuite() {
	dsn := "host=127.0.0.1 user=root password=root dbname=wallet_test port=5432 sslmode=disable"
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	s.NoError(err)
	s.NoError(db.AutoMigrate(
		model.TokenAssetStub,
		model.TokenAssetStarStub,
	))
	rd := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
	})

	s.data = NewData(db, rd)
	s.repo = NewTokenAssetRepo(log.DefaultLogger, s.data, nil, nil).(*tokenAssetRepo)
	s.ctx = s.T().Context()
}

func (s *TokenAssetRepoTestSuite) SetupTest() {
	for _, tb := range []string{
		model.TokenAssetStub.TableName(),
		model.TokenAssetStarStub.TableName(),
	} {
		s.data.db.Exec(fmt.Sprintf("delete from %s", tb))
	}
	s.data.rd.FlushAll(context.Background())
}

func (s *TokenAssetRepoTestSuite) TestExistsByChainIndexAndAddress() {
	ta := &model.TokenAsset{
		ChainIndex: 1,
		Address:    "0x123",
	}
	s.NoError(s.repo.data.db.Create(ta).Error)

	exists, err := s.repo.ExistsByChainIndexAndAddress(s.ctx, 1, "0x123")
	s.NoError(err)
	s.True(exists)

	// delete db record
	s.NoError(s.data.db.Exec("delete from token_assets").Error)
	exists, err = s.repo.ExistsByChainIndexAndAddress(s.ctx, 1, "0x123")
	s.NoError(err)
	s.True(exists)

	exists, err = s.repo.ExistsByChainIndexAndAddress(s.ctx, 999, "fake address")
	s.NoError(err)
	s.False(exists)
}

func (s *TokenAssetRepoTestSuite) TestListViewByFilter() {
	tas := []*model.TokenAsset{
		{
			Model: gorm.Model{
				ID: 1,
			},
			Address:     "0x1",
			ChainIndex:  1,
			CoinID:      "coin_id_1",
			Decimals:    18,
			IsDisplay:   true,
			LogoUrl:     "logo_url_1",
			Name:        "name_11",
			Symbol:      "ETH",
			TokenType:   "token_type_1",
			TotalSupply: "total_supply_1",
		},
		{
			Model: gorm.Model{
				ID: 2,
			},
			Address:     "0x2",
			ChainIndex:  1,
			CoinID:      "coin_id_1",
			Decimals:    18,
			IsDisplay:   true,
			LogoUrl:     "logo_url_1",
			Name:        "name_12",
			Symbol:      "ETH",
			TokenType:   "token_type_1",
			TotalSupply: "total_supply_1",
		},
		{
			Model: gorm.Model{
				ID: 3,
			},
			Address:     "0x14",
			ChainIndex:  4,
			CoinID:      "coin_id_14",
			Decimals:    8,
			IsDisplay:   true,
			LogoUrl:     "logo_url_14",
			Name:        "name_214",
			Symbol:      "TRX",
			TokenType:   "token_type_14",
			TotalSupply: "total_supply_14",
		},
		{
			Model: gorm.Model{
				ID: 4,
			},
			Address:     "0x1",
			ChainIndex:  2,
			CoinID:      "coin_id_1",
			Decimals:    8,
			IsDisplay:   true,
			LogoUrl:     "logo_url_1",
			Name:        "name_21",
			Symbol:      "BTC",
			TokenType:   "token_type_1",
			TotalSupply: "total_supply_1",
		},
	}
	err := s.data.DB(s.ctx).CreateInBatches(tas, 200).Error
	s.NoError(err)

	tass := []*model.TokenAssetStar{
		{
			TokenAssetID: 1,
			SortOrder:    -2,
		},
		{
			TokenAssetID: 2,
			SortOrder:    -1,
		},
		{
			TokenAssetID: 4,
			SortOrder:    -3,
		},
	}
	err = s.data.DB(s.ctx).CreateInBatches(tass, 200).Error
	s.NoError(err)

	s.Run("list view by all chain", func() {
		list, totalCount, err := s.repo.ListViewByFilter(s.ctx, &biz.TokenAssetViewFilter{
			Page:         1,
			PageSize:     10,
			ChainIndexes: []int64{},
			Search:       "",
		})
		s.NoError(err)
		s.Equal(int64(4), totalCount)
		s.Len(list, 4)

		// check sort order
		s.Equal(uint(2), list[0].ID)
		s.Equal(uint(3), list[len(list)-1].ID)
	})

	s.Run("list view by chain", func() {
		list, totalCount, err := s.repo.ListViewByFilter(s.ctx, &biz.TokenAssetViewFilter{
			Page:         1,
			PageSize:     1,
			ChainIndexes: []int64{1},
			Search:       "",
		})
		s.NoError(err)
		s.Equal(int64(2), totalCount)
		s.Len(list, 1)
		s.Equal(uint(2), list[0].ID)
	})

	s.Run("list view by search", func() {
		list, totalCount, err := s.repo.ListViewByFilter(s.ctx, &biz.TokenAssetViewFilter{
			Page:         1,
			PageSize:     1,
			ChainIndexes: []int64{},
			Search:       "EtH",
		})
		s.NoError(err)
		s.Equal(int64(2), totalCount)
		s.Len(list, 1)

		list, totalCount, err = s.repo.ListViewByFilter(s.ctx, &biz.TokenAssetViewFilter{
			Page:         1,
			PageSize:     10,
			ChainIndexes: []int64{},
			Search:       "0x",
		})
		s.NoError(err)
		s.Equal(int64(4), totalCount)
		s.Len(list, 4)
		s.Equal(uint(2), list[0].ID)
		s.Equal(uint(3), list[len(list)-1].ID)

		list, totalCount, err = s.repo.ListViewByFilter(s.ctx, &biz.TokenAssetViewFilter{
			Page:         1,
			PageSize:     10,
			ChainIndexes: []int64{},
			Search:       "name_",
		})
		s.NoError(err)
		s.Equal(int64(4), totalCount)
		s.Len(list, 4)
		s.Equal(uint(2), list[0].ID)
		s.Equal(uint(3), list[len(list)-1].ID)
	})
}
