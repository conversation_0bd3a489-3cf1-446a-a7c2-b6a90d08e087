package data

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz/gaspool/paymaster/evm"
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
)

// createTestEvmPaymasterRepo 创建测试用的EVM paymaster repo
// 注意：此测试需要本地Redis实例运行在localhost:6379
func createTestEvmPaymasterRepo(t *testing.T) (evm.Repo, func()) {
	// 创建Redis客户端连接到本地Redis
	rdb := redis.NewClient(&redis.Options{
		Addr:     "***********:6379",
		Password: "JJYVBLy6Lh31B83y",
		DB:       15, // 使用测试专用数据库
	})

	// 测试连接
	ctx := context.Background()
	if err := rdb.Ping(ctx).Err(); err != nil {
		t.Skipf("跳过测试：无法连接到Redis: %v", err)
		return nil, nil
	}

	// 创建Data实例
	data := &Data{
		rd: rdb,
	}

	// 创建repo（使用测试链索引999）
	testChainIndex := int64(999)
	repo := NewEvmPaymasterRepo(data, testChainIndex)

	// 返回清理函数
	cleanup := func() {
		// 清理测试数据
		rdb.FlushDB(ctx)
		rdb.Close()
	}

	return repo, cleanup
}

// TestEvmPaymasterRepo_CreateAndRetrieve 测试创建和获取等待确认记录
func TestEvmPaymasterRepo_CreateAndRetrieve(t *testing.T) {
	repo, cleanup := createTestEvmPaymasterRepo(t)
	defer cleanup()

	ctx := context.Background()

	// 测试数据
	record1 := &evm.EvmGasTransferWaitConfirmRecord{
		TxID:              123,
		GasTransferTxHash: "0x1234567890abcdef",
		TimeUnix:          time.Now().Unix(),
	}

	record2 := &evm.EvmGasTransferWaitConfirmRecord{
		TxID:              456,
		GasTransferTxHash: "0xfedcba0987654321",
		TimeUnix:          time.Now().Unix(),
	}

	// 测试创建记录
	err := repo.CreateEvmGasTransferWaitConfirmRecord(ctx, record1)
	assert.NoError(t, err)

	err = repo.CreateEvmGasTransferWaitConfirmRecord(ctx, record2)
	assert.NoError(t, err)

	// 测试获取所有记录
	records, err := repo.AllEvmGasTransferWaitConfirmRecord(ctx)
	assert.NoError(t, err)
	assert.Len(t, records, 2)

	// 验证记录内容
	recordMap := make(map[uint]*evm.EvmGasTransferWaitConfirmRecord)
	for _, record := range records {
		recordMap[record.TxID] = record
	}

	assert.Contains(t, recordMap, uint(123))
	assert.Contains(t, recordMap, uint(456))
	assert.Equal(t, "0x1234567890abcdef", recordMap[123].GasTransferTxHash)
	assert.Equal(t, "0xfedcba0987654321", recordMap[456].GasTransferTxHash)
}

// TestEvmPaymasterRepo_Delete 测试删除等待确认记录
func TestEvmPaymasterRepo_Delete(t *testing.T) {
	repo, cleanup := createTestEvmPaymasterRepo(t)
	defer cleanup()

	ctx := context.Background()

	// 创建测试记录
	record := &evm.EvmGasTransferWaitConfirmRecord{
		TxID:              789,
		GasTransferTxHash: "0xabcdef1234567890",
		TimeUnix:          time.Now().Unix(),
	}

	// 创建记录
	err := repo.CreateEvmGasTransferWaitConfirmRecord(ctx, record)
	assert.NoError(t, err)

	// 验证记录存在
	records, err := repo.AllEvmGasTransferWaitConfirmRecord(ctx)
	assert.NoError(t, err)
	assert.Len(t, records, 1)
	assert.Equal(t, uint(789), records[0].TxID)

	// 删除记录
	err = repo.DeleteEvmGasTransferWaitConfirmRecord(ctx, 789)
	assert.NoError(t, err)

	// 验证记录已删除
	records, err = repo.AllEvmGasTransferWaitConfirmRecord(ctx)
	assert.NoError(t, err)
	assert.Len(t, records, 0)
}

// TestEvmPaymasterRepo_EmptyResult 测试空结果情况
func TestEvmPaymasterRepo_EmptyResult(t *testing.T) {
	repo, cleanup := createTestEvmPaymasterRepo(t)
	defer cleanup()

	ctx := context.Background()

	// 测试获取空记录列表
	records, err := repo.AllEvmGasTransferWaitConfirmRecord(ctx)
	assert.NoError(t, err)
	assert.Len(t, records, 0)

	// 测试删除不存在的记录（应该不报错）
	err = repo.DeleteEvmGasTransferWaitConfirmRecord(ctx, 999)
	assert.NoError(t, err)
}

// TestEvmPaymasterRepo_ErrorHandling 测试错误处理
func TestEvmPaymasterRepo_ErrorHandling(t *testing.T) {
	repo, cleanup := createTestEvmPaymasterRepo(t)
	if repo == nil {
		t.Skip("跳过测试：无法创建repo")
		return
	}
	defer cleanup()

	ctx := context.Background()

	// 测试创建nil记录
	err := repo.CreateEvmGasTransferWaitConfirmRecord(ctx, nil)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "记录不能为空")
}

// TestEvmPaymasterRepo_BasicStructure 测试基本结构和接口实现
func TestEvmPaymasterRepo_BasicStructure(t *testing.T) {
	// 创建Data实例（不需要真实的Redis连接）
	data := &Data{
		rd: nil, // 这里为nil，只测试结构
	}

	// 测试不同链索引的repo
	testCases := []struct {
		chainIndex      int64
		expectedHashKey string
	}{
		{1, "evm_gas_transfer_wait_confirm_records:1"},         // Ethereum
		{137, "evm_gas_transfer_wait_confirm_records:137"},     // Polygon
		{42161, "evm_gas_transfer_wait_confirm_records:42161"}, // Arbitrum
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("ChainIndex_%d", tc.chainIndex), func(t *testing.T) {
			// 创建repo
			repo := NewEvmPaymasterRepo(data, tc.chainIndex)

			// 验证repo实现了正确的接口
			assert.NotNil(t, repo)

			// 验证repo是正确的类型
			evmRepo, ok := repo.(*evmPaymasterRepo)
			assert.True(t, ok, "repo应该是*evmPaymasterRepo类型")
			assert.NotNil(t, evmRepo)
			assert.Equal(t, tc.chainIndex, evmRepo.chainIndex)

			// 验证动态生成的哈希键
			actualHashKey := evmRepo.getEvmGasTransferHashKey()
			assert.Equal(t, tc.expectedHashKey, actualHashKey)
		})
	}
}

// TestEvmGasTransferWaitConfirmRecord_Structure 测试记录结构
func TestEvmGasTransferWaitConfirmRecord_Structure(t *testing.T) {
	// 测试记录结构的基本功能
	record := &evm.EvmGasTransferWaitConfirmRecord{
		TxID:              12345,
		GasTransferTxHash: "0xabcdef1234567890",
		TimeUnix:          time.Now().Unix(),
	}

	// 验证字段设置正确
	assert.Equal(t, uint(12345), record.TxID)
	assert.Equal(t, "0xabcdef1234567890", record.GasTransferTxHash)
	assert.True(t, record.TimeUnix > 0)

	// 验证时间戳合理性（应该是最近的时间）
	now := time.Now().Unix()
	assert.True(t, record.TimeUnix <= now)
	assert.True(t, record.TimeUnix > now-60) // 应该在最近1分钟内
}

// TestEvmPaymasterRepoFactory 测试repo工厂函数
func TestEvmPaymasterRepoFactory(t *testing.T) {
	// 创建Data实例
	data := &Data{
		rd: nil, // 这里为nil，只测试结构
	}

	// 创建repo工厂
	factory := NewEvmPaymasterRepoFactory(data)
	assert.NotNil(t, factory)

	// 测试为不同链创建repo
	chainIndexes := []int64{1, 137, 42161, 8453, 10} // Ethereum, Polygon, Arbitrum, Base, Optimism

	repos := make(map[int64]evm.Repo)
	for _, chainIndex := range chainIndexes {
		repo := factory(chainIndex)
		assert.NotNil(t, repo)
		repos[chainIndex] = repo

		// 验证repo类型和链索引
		evmRepo, ok := repo.(*evmPaymasterRepo)
		assert.True(t, ok)
		assert.Equal(t, chainIndex, evmRepo.chainIndex)

		// 验证每个链的哈希键都不同
		expectedHashKey := fmt.Sprintf("evm_gas_transfer_wait_confirm_records:%d", chainIndex)
		actualHashKey := evmRepo.getEvmGasTransferHashKey()
		assert.Equal(t, expectedHashKey, actualHashKey)
	}

	// 验证不同链的repo实例是独立的
	for i, chainIndex1 := range chainIndexes {
		for j, chainIndex2 := range chainIndexes {
			if i != j {
				repo1 := repos[chainIndex1].(*evmPaymasterRepo)
				repo2 := repos[chainIndex2].(*evmPaymasterRepo)

				// 不同链的repo应该有不同的链索引
				assert.NotEqual(t, repo1.chainIndex, repo2.chainIndex)

				// 不同链的repo应该有不同的哈希键
				assert.NotEqual(t, repo1.getEvmGasTransferHashKey(), repo2.getEvmGasTransferHashKey())
			}
		}
	}
}

// TestEvmPaymasterRepo_ChainIsolation 测试不同链之间的数据隔离
func TestEvmPaymasterRepo_ChainIsolation(t *testing.T) {
	// 创建Redis客户端连接到本地Redis
	rdb := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
		DB:   15, // 使用测试专用数据库
	})

	// 测试连接
	ctx := context.Background()
	if err := rdb.Ping(ctx).Err(); err != nil {
		t.Skipf("跳过测试：无法连接到Redis: %v", err)
		return
	}

	// 清理测试数据
	defer func() {
		rdb.FlushDB(ctx)
		rdb.Close()
	}()

	// 创建Data实例
	data := &Data{
		rd: rdb,
	}

	// 创建不同链的repo实例
	ethRepo := NewEvmPaymasterRepo(data, constant.EthChainIndex)  // Ethereum (1)
	polyRepo := NewEvmPaymasterRepo(data, constant.PolChainIndex) // Polygon (137)
	arbRepo := NewEvmPaymasterRepo(data, constant.ArbChainIndex)  // Arbitrum (42161)

	// 为每个链创建不同的测试记录
	ethRecord := &evm.EvmGasTransferWaitConfirmRecord{
		TxID:              100,
		GasTransferTxHash: "0xeth_hash_100",
		TimeUnix:          time.Now().Unix(),
	}

	polyRecord := &evm.EvmGasTransferWaitConfirmRecord{
		TxID:              200,
		GasTransferTxHash: "0xpoly_hash_200",
		TimeUnix:          time.Now().Unix(),
	}

	arbRecord := &evm.EvmGasTransferWaitConfirmRecord{
		TxID:              300,
		GasTransferTxHash: "0xarb_hash_300",
		TimeUnix:          time.Now().Unix(),
	}

	// 在不同链的repo中创建记录
	err := ethRepo.CreateEvmGasTransferWaitConfirmRecord(ctx, ethRecord)
	assert.NoError(t, err)

	err = polyRepo.CreateEvmGasTransferWaitConfirmRecord(ctx, polyRecord)
	assert.NoError(t, err)

	err = arbRepo.CreateEvmGasTransferWaitConfirmRecord(ctx, arbRecord)
	assert.NoError(t, err)

	// 验证每个链只能看到自己的记录
	ethRecords, err := ethRepo.AllEvmGasTransferWaitConfirmRecord(ctx)
	assert.NoError(t, err)
	assert.Len(t, ethRecords, 1)
	assert.Equal(t, uint(100), ethRecords[0].TxID)
	assert.Equal(t, "0xeth_hash_100", ethRecords[0].GasTransferTxHash)

	polyRecords, err := polyRepo.AllEvmGasTransferWaitConfirmRecord(ctx)
	assert.NoError(t, err)
	assert.Len(t, polyRecords, 1)
	assert.Equal(t, uint(200), polyRecords[0].TxID)
	assert.Equal(t, "0xpoly_hash_200", polyRecords[0].GasTransferTxHash)

	arbRecords, err := arbRepo.AllEvmGasTransferWaitConfirmRecord(ctx)
	assert.NoError(t, err)
	assert.Len(t, arbRecords, 1)
	assert.Equal(t, uint(300), arbRecords[0].TxID)
	assert.Equal(t, "0xarb_hash_300", arbRecords[0].GasTransferTxHash)

	// 验证删除操作也是隔离的
	err = ethRepo.DeleteEvmGasTransferWaitConfirmRecord(ctx, 100)
	assert.NoError(t, err)

	// Ethereum链的记录应该被删除
	ethRecords, err = ethRepo.AllEvmGasTransferWaitConfirmRecord(ctx)
	assert.NoError(t, err)
	assert.Len(t, ethRecords, 0)

	// 其他链的记录应该不受影响
	polyRecords, err = polyRepo.AllEvmGasTransferWaitConfirmRecord(ctx)
	assert.NoError(t, err)
	assert.Len(t, polyRecords, 1)

	arbRecords, err = arbRepo.AllEvmGasTransferWaitConfirmRecord(ctx)
	assert.NoError(t, err)
	assert.Len(t, arbRecords, 1)
}

// TestEvmPaymasterRepo_UpdateRecord 测试更新记录（覆盖相同TxID）
func TestEvmPaymasterRepo_UpdateRecord(t *testing.T) {
	repo, cleanup := createTestEvmPaymasterRepo(t)
	defer cleanup()

	ctx := context.Background()

	// 创建初始记录
	record1 := &evm.EvmGasTransferWaitConfirmRecord{
		TxID:              100,
		GasTransferTxHash: "0x1111111111111111",
		TimeUnix:          time.Now().Unix(),
	}

	err := repo.CreateEvmGasTransferWaitConfirmRecord(ctx, record1)
	assert.NoError(t, err)

	// 创建相同TxID的新记录（应该覆盖）
	record2 := &evm.EvmGasTransferWaitConfirmRecord{
		TxID:              100,
		GasTransferTxHash: "0x2222222222222222",
		TimeUnix:          time.Now().Unix() + 100,
	}

	err = repo.CreateEvmGasTransferWaitConfirmRecord(ctx, record2)
	assert.NoError(t, err)

	// 验证记录被覆盖
	records, err := repo.AllEvmGasTransferWaitConfirmRecord(ctx)
	assert.NoError(t, err)
	assert.Len(t, records, 1)
	assert.Equal(t, uint(100), records[0].TxID)
	assert.Equal(t, "0x2222222222222222", records[0].GasTransferTxHash)
}

// TestEvmPaymasterRepo_MultipleOperations 测试多种操作组合
func TestEvmPaymasterRepo_MultipleOperations(t *testing.T) {
	repo, cleanup := createTestEvmPaymasterRepo(t)
	defer cleanup()

	ctx := context.Background()

	// 创建多个记录
	for i := 1; i <= 5; i++ {
		record := &evm.EvmGasTransferWaitConfirmRecord{
			TxID:              uint(i),
			GasTransferTxHash: fmt.Sprintf("0x%016d", i),
			TimeUnix:          time.Now().Unix() + int64(i),
		}
		err := repo.CreateEvmGasTransferWaitConfirmRecord(ctx, record)
		assert.NoError(t, err)
	}

	// 验证所有记录都存在
	records, err := repo.AllEvmGasTransferWaitConfirmRecord(ctx)
	assert.NoError(t, err)
	assert.Len(t, records, 5)

	// 删除部分记录
	err = repo.DeleteEvmGasTransferWaitConfirmRecord(ctx, 2)
	assert.NoError(t, err)
	err = repo.DeleteEvmGasTransferWaitConfirmRecord(ctx, 4)
	assert.NoError(t, err)

	// 验证剩余记录
	records, err = repo.AllEvmGasTransferWaitConfirmRecord(ctx)
	assert.NoError(t, err)
	assert.Len(t, records, 3)

	// 验证剩余的是正确的记录
	txIDs := make([]uint, 0, 3)
	for _, record := range records {
		txIDs = append(txIDs, record.TxID)
	}
	assert.Contains(t, txIDs, uint(1))
	assert.Contains(t, txIDs, uint(3))
	assert.Contains(t, txIDs, uint(5))
	assert.NotContains(t, txIDs, uint(2))
	assert.NotContains(t, txIDs, uint(4))
}
