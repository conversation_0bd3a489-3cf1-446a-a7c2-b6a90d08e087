package data

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz/syncer/chain/solana"
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
)

func TestFindViewByChainIndexAndAddress_Solana(t *testing.T) {
	ctx := t.Context()
	s := assert.New(t)

	scli, cf, err := solana.NewSmartNodeSelectionClient([]string{"https://api.mainnet-beta.solana.com"})
	s.NoError(err)
	defer cf()
	repo := NewTokenContractRepo(log.DefaultLogger, nil, nil, scli)

	tc, err := repo.FindViewByChainIndexAndAddress(ctx, constant.SolChainIndex, "8bqjz8DeSuim1sEAsQatjJN4zseyxSPdhHQcuuhL8PCK") // Synthetic ETH (xETH)
	s.NoError(err)
	s.EqualValues(9, tc.Decimals)
	s.Equal("xETH", tc.Symbol)
	s.Equal("Synthetic ETH", tc.Name)

	t.Logf("token contract: %+v", tc)
}
