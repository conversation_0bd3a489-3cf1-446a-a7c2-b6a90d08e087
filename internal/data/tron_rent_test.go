package data

import (
	"byd_wallet/model"
	"context"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gorm.io/datatypes"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"os"
	"path/filepath"
	"testing"
)

func newTestTronRentRepo(t *testing.T) *TronRentRepo {
	db, err := gorm.Open(sqlite.Open(filepath.Join(os.TempDir(), uuid.New().String())), &gorm.Config{})
	assert.NoError(t, err)

	err = db.AutoMigrate(
		&model.TronRentRecord{},
	)
	assert.NoError(t, err)
	dataInstance := &Data{db: db}
	return &TronRentRepo{Data: dataInstance} // Pass nil for okx.Client and maps as they are not used in these tests
}

func TestTronRentRepo(t *testing.T) {
	repo := newTestTronRentRepo(t)
	ctx := context.Background()
	err := repo.CreateTronRentRecord(ctx, &model.TronRentRecord{
		TxHash:              "aaaaa",
		UploadHashReplyData: datatypes.JSON(`["123"]`),
	})
	assert.NoError(t, err)
	record, err := repo.GetTronRentRecordByDelegateHash(ctx, "123")
	assert.NoError(t, err)
	assert.NotNil(t, record)
	assert.Equal(t, record.TxHash, "aaaaa")
}
