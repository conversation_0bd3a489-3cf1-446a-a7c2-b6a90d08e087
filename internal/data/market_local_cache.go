package data

import (
	"byd_wallet/model"
	"context"
	"encoding/json"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/redis/go-redis/v9"
)

type MarketLocalCache struct {
	cache     map[string]*model.CoinMarketData
	expire    time.Duration
	expiredAt time.Time
	mtx       *sync.RWMutex

	log           *log.Helper
	rd            redis.UniversalClient
	redisCacheKey string
}

func newMarketLocalCache(logger log.Logger, rd redis.UniversalClient, redisCacheKey string, expire time.Duration) *MarketLocalCache {
	return &MarketLocalCache{
		cache:     make(map[string]*model.CoinMarketData),
		expiredAt: time.Now().Add(-time.Second),
		expire:    expire,
		mtx:       &sync.RWMutex{},

		log:           log.NewHelper(logger),
		rd:            rd,
		redisCacheKey: redisCacheKey,
	}
}

func (c *MarketLocalCache) SafeGet(ctx context.Context, coinID string) *model.CoinMarketData {
	c.mtx.RLock()
	cached := time.Now().Before(c.expiredAt)
	v := c.cache[coinID]
	c.mtx.RUnlock()
	if cached {
		return v
	}
	c.mtx.Lock()
	defer c.mtx.Unlock()

	cached = time.Now().Before(c.expiredAt)
	v = c.cache[coinID]
	if cached {
		return v
	}

	// load cache from redis
	kvs, err := c.rd.HGetAll(ctx, c.redisCacheKey).Result()
	if err != nil {
		c.log.Errorf("get coin market data cache error(%s): %v", c.redisCacheKey, err)
		return nil
	}
	var r *model.CoinMarketData
	for _, v := range kvs {
		cmd := &model.CoinMarketData{}
		if err := json.Unmarshal([]byte(v), &cmd); err != nil {
			c.log.Errorf("unmarshal coin market data cache error(%s): %v", c.redisCacheKey, err)
			continue
		}
		c.cache[cmd.CoinID] = cmd
		if cmd.CoinID == coinID {
			r = cmd
		}
	}
	c.expiredAt = time.Now().Add(c.expire)
	return r
}
