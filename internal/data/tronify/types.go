package tronify

import "fmt"

type BaseResp struct {
	ResCode int    `json:"resCode"`
	ResMsg  string `json:"resMsg"`
}

func (b BaseResp) Error() string {
	if b.ResMsg == "" {
		b.ResMsg = "unknown error"
	}
	return fmt.Sprintf("%d#%s", b.<PERSON>sCode, b.ResMsg)
}

func (b BaseResp) IsSuccess() bool {
	return b.ResCode == 100
}

type TronRentRecord struct {
	OrderId       string `json:"orderId"`
	PlatformAddr  string `json:"platformAddr"`
	FromAddress   string `json:"fromAddress"`
	PledgeAddress string `json:"pledgeAddress"`
	PledgeDay     int    `json:"pledgeDay"`
	Source        string `json:"source"`
	OrderType     string `json:"orderType"`
	OrderPrice    int    `json:"orderPrice"`
	PledgeNum     int    `json:"pledgeNum"`
	PledgeTrxNum  string `json:"pledgeTrxNum"`
	Transaction   any    `json:"transaction"`
}

type AddTronRentRecordResp struct {
	BaseResp
	Data *TronRentRecord `json:"data"`
}

type UploadHashRespData struct {
	TxIds []string `json:"tx_ids"`
}

type UploadHashResp struct {
	BaseResp
	Data *UploadHashRespData `json:"data"`
}
