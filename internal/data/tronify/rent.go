package tronify

import (
	"byd_wallet/internal/biz/rent"
	"byd_wallet/model"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/shopspring/decimal"
)

/*
接口文档：https://docs-tron.tronify.io/tron-api/create-purchase-order
*/

type TronRentRequester struct {
	cli     *http.Client
	baseURL string
	log     *log.Helper
}

func NewTronRentClient(logger log.Logger) *TronRentRequester {
	return &TronRentRequester{cli: http.DefaultClient, baseURL: "https://open.tronify.io", log: log.<PERSON><PERSON>per(logger)}
}

func (t TronRentRequester) AddTronRentRecord(ctx context.Context, record *model.TronRentRecord) error {
	return t.addTronRentRecord(ctx, record, "/api/tronRent/addTronRentRecord")
}

func (t TronRentRequester) QueryPreorderInfo(ctx context.Context, record *model.TronRentRecord) error {
	return t.addTronRentRecord(ctx, record, "/api/tronRent/queryPreorderInfo")
}

func (t TronRentRequester) addTronRentRecord(ctx context.Context, record *model.TronRentRecord, path string) error {
	if record.PledgeMinute > 0 && record.PledgeHour > 0 {
		return fmt.Errorf("tron rent pledge duration config error")
	}
	req := map[string]any{
		"fromAddress":   record.FromAddress,
		"pledgeAddress": record.PledgeAddress,
		"pledgeNum":     fmt.Sprintf("%d", record.PledgeNum),
		"tradeType":     record.TradeType,
		"sourceFlag":    record.Source,
		"orderType":     record.OrderType,
	}
	if record.PledgeHour > 0 {
		req["pledgeHour"] = fmt.Sprintf("%d", record.PledgeHour)
	}
	if record.PledgeMinute > 0 {
		req["pledgeMinute"] = fmt.Sprintf("%d", record.PledgeMinute)
	}
	var resp AddTronRentRecordResp
	if err := t.postJSON(ctx, path, req, &resp); err != nil {
		return err
	}
	if resp.Data != nil {
		record.ChannelOrderId = resp.Data.OrderId
		record.PlatformAddr = resp.Data.PlatformAddr
		record.PledgeTrxNum = resp.Data.PledgeTrxNum
		record.ChannelPrice = decimal.NewFromInt(int64(resp.Data.OrderPrice))
		record.OrderPrice = record.ChannelPrice
		txByte, err := json.Marshal(resp.Data.Transaction)
		if err != nil {
			return err
		}
		record.Transaction = txByte
	}
	if !resp.IsSuccess() {
		return resp
	}
	return nil
}

func (t TronRentRequester) UploadHash(ctx context.Context, record *rent.UploadHashReq) ([]string, error) {
	var signedData any
	if err := json.Unmarshal([]byte(record.SignedData), &signedData); err != nil {
		return nil, err
	}
	req := map[string]any{
		"orderId":    record.OrderId,
		"fromHash":   record.FromHash,
		"signedData": signedData,
	}
	var resp UploadHashResp
	if err := t.postJSON(ctx, "/api/tronRent/uploadHash", req, &resp); err != nil {
		return nil, err
	}
	if !resp.IsSuccess() {
		return nil, resp
	}
	if resp.Data != nil {
		return resp.Data.TxIds, nil
	}
	record.Status = model.TronRentStatusSuccess
	return nil, nil
}

func (t TronRentRequester) postJSON(ctx context.Context, path string, params map[string]any, resp any) error {
	reqByte, err := json.Marshal(params)
	if err != nil {
		return fmt.Errorf("marshal req param err: %v", err)
	}
	t.log.WithContext(ctx).Debugf("波场能量租赁请求: path: %s, params: %s", path, string(reqByte))
	body := bytes.NewBuffer(reqByte)
	req, err := http.NewRequestWithContext(ctx, "POST", t.baseURL+path, body)
	if err != nil {
		return fmt.Errorf("NewRequestWithContext: %v", err)
	}
	req.Header.Set("Content-Type", "application/json")
	response, err := t.cli.Do(req)
	if err != nil {
		return fmt.Errorf("http do post err: %v", err)
	}
	respB, err := io.ReadAll(response.Body)
	if err != nil {
		return fmt.Errorf("read resp body err: %v", err)
	}
	t.log.WithContext(ctx).Debugf("波场能量租赁相应: path: %s, params: %s", path, string(respB))
	if err := json.Unmarshal(respB, resp); err != nil {
		return fmt.Errorf("unmarshal resp body err: %v", err)
	}
	return nil
}
