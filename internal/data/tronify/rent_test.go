package tronify

import (
	"byd_wallet/model"
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
	"os"
	"testing"
)

func TestTronRentClient_AddTronRentRecord(t *testing.T) {
	t.Skip()
	cli := NewTronRentClient(log.NewStdLogger(os.Stdout))
	err := cli.AddTronRentRecord(context.Background(), &model.TronRentRecord{
		FromAddress:   "TWeoEXnt48JpZV43dWP1EgU9jKs7ZNqLW2",
		PledgeAddress: "TWeoEXnt48JpZV43dWP1EgU9jKs7ZNqLW2",
		PledgeHour:    3,
		TradeType:     "fastTrade",
		Source:        "BossWallet",
		OrderType:     "ENERGY",
		PledgeNum:     68303,
	})
	assert.NoError(t, err)
}

func TestTronRentClient_QueryPreorderInfo(t *testing.T) {
	t.Skip()
	cli := NewTronRentClient(log.NewStdLogger(os.Stdout))
	for i := 0; i < 200; i++ {
		err := cli.QueryPreorderInfo(context.Background(), &model.TronRentRecord{
			FromAddress:   "TWeoEXnt48JpZV43dWP1EgU9jKs7ZNqLW2",
			PledgeAddress: "TWeoEXnt48JpZV43dWP1EgU9jKs7ZNqLW2",
			PledgeHour:    3,
			TradeType:     "fastTrade",
			Source:        "BossWallet",
			OrderType:     "ENERGY",
			PledgeNum:     68303,
		})
		assert.NoError(t, err)
	}

}
