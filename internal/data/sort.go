package data

import (
	"byd_wallet/internal/biz/base"
	"context"
	"errors"
	"fmt"
	"gorm.io/gorm"
)

func NewCommonSortRepo(data *Data) base.SortRepo {
	return &commonSortRepo{data}
}

type commonSortRepo struct {
	*Data
}

type BaseSort struct {
	ID        uint
	SortOrder int
}

// Sort dapp通用排序方法
// 不支持并发
// 需要在事务中进行
func (d *commonSortRepo) Sort(ctx context.Context, sort *base.SortInput) error {
	// 获取当前排序数据
	var curSort BaseSort
	if err := d.DB(ctx).Model(sort.Model).Where("id = ?", sort.ID).First(&curSort).Error; err != nil {
		return err
	}

	// 根据方向查找需要交换的排序数据
	var targetSort BaseSort
	var err error

	db := d.DB(ctx).Model(sort.Model)
	for _, where := range sort.Where {
		db = db.Where(fmt.Sprintf("%s = ?", where.Column), where.ColumnValue)
	}
	switch sort.SortType {
	case base.SortTypeUp:
		// 向上移动：找到sort_order比当前大的最小值
		err = db.Where("sort_order > ?", curSort.SortOrder).
			Order("sort_order ASC").First(&targetSort).Error
	case base.SortTypeDown:
		// 向下移动：找到sort_order比当前小的最大值
		err = db.Where("sort_order < ?", curSort.SortOrder).
			Order("sort_order DESC").First(&targetSort).Error
	case base.SortTypeTop:
		// 置顶：更新当前排序数据到最大值+1
		var maxSort BaseSort
		if err = db.Order("sort_order DESC").First(&maxSort).Error; err != nil {
			return err
		}
		if sort.ID == maxSort.ID {
			return nil
		}
		return d.DB(ctx).Model(sort.Model).Where("id = ?", sort.ID).Update("sort_order", maxSort.SortOrder+1).Error
	default:
		return fmt.Errorf("unknown sort type: %d", sort.SortType)
	}

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 已经在边界位置，无法移动
			return nil
		}
		return err
	}

	// 交换sort_order值
	currentSortOrder := curSort.SortOrder
	targetSortOrder := targetSort.SortOrder

	// 更新当前导航项的sort_order
	if err := d.DB(ctx).Model(sort.Model).
		Where("id = ?", curSort.ID).
		Update("sort_order", targetSortOrder).Error; err != nil {
		return err
	}

	// 更新目标导航项的sort_order
	if err := d.DB(ctx).Model(sort.Model).
		Where("id = ?", targetSort.ID).
		Update("sort_order", currentSortOrder).Error; err != nil {
		return err
	}

	return nil
}
