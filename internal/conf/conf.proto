syntax = "proto3";
package kratos.api;

option go_package = "github.com/go-kratos/kratos-layout/internal/conf;conf";

import "google/protobuf/duration.proto";

message Bootstrap {
  Server server = 1;
  Data data = 2;
}

message Server {
  message HTTP {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  message GRPC {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  message MQ {
    string mq_type = 1;
    repeated string addrs = 2;
    string token_topic = 3;
    string hold_new_token_topic = 4;
  }
  message Syncer {
    int64 chain_index = 1;
  }
  HTTP http = 1;
  GRPC grpc = 2;
  MQ mq = 3;
  Syncer syncer = 4;
}

message Data {
  message Database {
    string driver = 1;
    string db_user = 2;
    string db_password = 3;
    string db_host = 4;
    int64 db_port = 5;
    string db_name = 6;
    bool dry_run = 7;
    int64 max_idle_conns = 8;
    int64 max_open_conns = 9;
    string conn_max_life_time = 10;
  }
  message Redis {
    string network = 1;
    repeated string addrs = 2;
    string username = 3;
    string password = 4;
    int32 db = 5;
    google.protobuf.Duration dial_timeout = 6;
    google.protobuf.Duration read_timeout = 7;
    google.protobuf.Duration write_timeout = 8;
    bool is_cluster_mode = 9;
  }
  message Kafka {
    repeated string brokers = 1;
    string token_topic = 2;
    string hold_new_token_topic = 3;
  }
  message Jwt {
    // ed25519 ex. https://github.com/golang-jwt/jwt/blob/main/test/ed25519-private.pem
    // generate command: openssl genpkey -algorithm ed25519
    string priv_key = 1;
    // at least 1 hour
    string expires = 2;
  }
  Database database = 1;
  Redis redis = 2;
  Kafka kafka = 3;
  Jwt adminjwt = 4;
}