package solana

import (
	"github.com/gagliardetto/solana-go"
	"github.com/shopspring/decimal"
)

type SolTransaction struct {
	Txn         string
	Slot        uint64
	Payer       string
	Signers     []string
	FromAddress string
	ToAddress   string
	Method      string
	Mint        string // 为空表示是SOL
	Amount      decimal.Decimal
	Decimals    int
	Status      string `json:"status" default:"Success"`
	Fee         decimal.Decimal
}

type TransferInstruction struct {
	InstructionType uint8
	Amount          uint64
}

type Instruction uint32
type CreateAccountInstruction struct {
	Instruction Instruction
	Lamports    uint64
	Space       uint64
	Owner       solana.PublicKey
}

type CreateAccountWithSeedInstruction struct {
	Instruction Instruction
	Base        solana.PublicKey
	SeedLen     uint64
	Seed        string
	Lamports    uint64
	Space       uint64
	ProgramID   solana.PublicKey
}

type TokenChange struct {
	Owner      string
	Account    string
	DiffAmount decimal.Decimal
	Mint       string
	Amount     string
	Decimals   uint8
}

type TokenTransfer struct {
	Mint        string
	FromOwner   string
	FromAccount string
	ToOwner     string
	ToAccount   string
	Amount      string // 带精度
}

type SOLBalanceChange struct {
	Account string          // 账户地址（Base58编码）
	Amount  decimal.Decimal // 变化的金额（正数表示收入，负数表示支出）
}

type WithdrawNonceAccountInstruction struct {
	Instruction Instruction
	Lamports    uint64
}

type ApproveInstruction struct {
	Instruction Instruction
	Amount      uint64
}

type ApproveCheckedInstruction struct {
	Instruction Instruction
	Amount      uint64
	Decimals    uint8
}
