package solana

import (
	"context"
	"fmt"
	"github.com/gagliardetto/solana-go"
	"github.com/gagliardetto/solana-go/rpc"
	"time"
)

func (t *TransactionsFetcher) GetTokenAccountsByOwner(addr, programId string) ([]*rpc.TokenAccount, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	cli := t.Select()
	programIdKey := solana.MustPublicKeyFromBase58(programId)
	addrress := solana.MustPublicKeyFromBase58(addr)
	//tokenAccounts, err := cli.GetTokenAccountsByOwnerByProgram(ctx, addr, programId)
	tokenAccounts, err := cli.GetTokenAccountsByOwner(ctx, addrress, &rpc.GetTokenAccountsConfig{
		ProgramId: &programIdKey,
	}, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get account info for %s: %w", addr, err)
	}

	return tokenAccounts.Value, nil
}
