package solana

import (
	"byd_wallet/common/constant"
	"byd_wallet/model"
	"context"
	"github.com/gagliardetto/solana-go"
	"strings"
	"sync"

	"github.com/gagliardetto/solana-go/rpc"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/shopspring/decimal"
)

type TransactionsFetcher struct {
	*SmartNodeSelectionClient

	log        *log.Helper
	chainIndex int64
	ctx        context.Context
}

func NewTransactionsFetcher(logger *log.Helper, chainIndex int64, rpcList []string) (*TransactionsFetcher, func(), error) {
	cli, cf, err := NewSmartNodeSelectionClient(rpcList)
	if err != nil {
		return nil, nil, err
	}
	return &TransactionsFetcher{
		log:        logger,
		chainIndex: chainIndex,
		ctx:        context.Background(),

		SmartNodeSelectionClient: cli,
	}, cf, nil
}

var solChangePool = sync.Pool{
	New: func() interface{} {
		return make(map[string]int64, 10)
	},
}

var solTokenChangePool = sync.Pool{
	New: func() interface{} {
		return make(map[string]TokenChange, 10)
	},
}

func (t *TransactionsFetcher) FetchTransactionsByBlockNumber(ctx context.Context, slot int64) ([]*model.Transaction, error) {
	cli := t.Select()
	reward := false
	block, err := cli.GetBlockWithOpts(ctx, uint64(slot), &rpc.GetBlockOpts{
		MaxSupportedTransactionVersion: rpc.NewTransactionVersion(0), // 👈 关键在这里
		Rewards:                        &reward,
	})
	if err != nil {
		if strings.Contains(err.Error(), "-32007") || strings.Contains(err.Error(), "-32009") {
			return nil, nil
		}
		return nil, err // 其他错误照常返回
	}

	var solTransactions []*model.Transaction
	blockTime := block.BlockTime

	for _, tx := range block.Transactions {
		if tx.Meta == nil {
			continue
		}

		var signature string
		tl, err := tx.GetTransaction()
		if err != nil {
			t.log.Errorf("failed to get transaction: %v", err)
			continue
		}
		if len(tl.Signatures) > 0 {
			signature = tl.Signatures[0].String()
		}
		accounts := tl.Message.AccountKeys
		loadedWritableKeys := tx.Meta.LoadedAddresses.Writable
		accounts = append(accounts, loadedWritableKeys...)
		loadedReadonlyKeys := tx.Meta.LoadedAddresses.ReadOnly
		accounts = append(accounts, loadedReadonlyKeys...)
		// 获取 map from pool
		solChange := solChangePool.Get().(map[string]int64)
		solTokenChange := solTokenChangePool.Get().(map[string]TokenChange)

		// 清空 map（重用）
		for k := range solChange {
			delete(solChange, k)
		}
		for k := range solTokenChange {
			delete(solTokenChange, k)
		}

		for i := range tx.Meta.PostBalances {
			if i < len(accounts) && i < len(tx.Meta.PreBalances) {
				solChange[accounts[i].String()] = int64(tx.Meta.PostBalances[i] - tx.Meta.PreBalances[i])
			}
		}
		changesByMint := make(map[string][]TokenChange)
		// 构建 preTokenMap: key=accountIndex, value=preTokenBalance
		preTokenMap := make(map[int]rpc.TokenBalance)
		for _, pre := range tx.Meta.PreTokenBalances {
			preTokenMap[int(pre.AccountIndex)] = pre
		}
		for _, post := range tx.Meta.PostTokenBalances {
			if int(post.AccountIndex) < len(accounts) {
				accountIdx := int(post.AccountIndex)
				acct := accounts[post.AccountIndex]
				if accountIdx >= len(accounts) {
					continue
				}

				solTokenChange[acct.String()] = TokenChange{
					Amount:   post.UiTokenAmount.Amount,
					Decimals: post.UiTokenAmount.Decimals,
					Account:  acct.String(),
					Owner:    post.Owner.String(),
					Mint:     post.Mint.String(),
				}

				account := accounts[accountIdx].String()
				mint := post.Mint.String()
				owner := post.Owner.String()
				postAmount, _ := decimal.NewFromString(post.UiTokenAmount.Amount)
				preAmount := decimal.Zero
				if pre, ok := preTokenMap[accountIdx]; ok {
					preAmount, _ = decimal.NewFromString(pre.UiTokenAmount.Amount)
				}
				diff := postAmount.Sub(preAmount)
				if !diff.IsZero() {
					changesByMint[mint] = append(changesByMint[mint], TokenChange{
						Owner: owner, Account: account, DiffAmount: diff, Decimals: post.UiTokenAmount.Decimals,
					})
				}
			}
		}
		fee := decimal.NewFromUint64(tx.Meta.Fee).String()
		status := constant.TransactionStatusSuccess
		if tx.Meta.Err != nil {
			status = constant.TransactionStatusFail
			from := ""
			if len(accounts) > 0 {
				from = accounts[0].String()
			}
			solTransactions = append(solTransactions, t.createFailedTransaction(signature, status, from, blockTime.Time().Unix(), slot, fee))

			// put maps back to pool
			solChangePool.Put(solChange)
			solTokenChangePool.Put(solTokenChange)
			continue
		}
		instructions := append(tl.Message.Instructions, flattenInnerInstructions(tx.Meta.InnerInstructions)...)

		for _, inst := range instructions {
			if solTx := t.createTransactionFromInstruction(inst, accounts, solChange, solTokenChange, signature, status, fee, blockTime.Time().Unix(), slot); solTx != nil {
				solTransactions = append(solTransactions, solTx)
			}
		}
		solTransfer := t.ConvertSOLChangesToTransfers(solChange)
		solList := t.processTransaction(signature, slot, blockTime.Time().Unix(), status, fee, solTransfer)
		solTransactions = append(solTransactions, solList...)
		// put maps back to pool
		solChangePool.Put(solChange)
		solTokenChangePool.Put(solTokenChange)
		//把余额变化的加入到solTransactions
		tokenTransfers := ChangeTokenToTransaction(changesByMint)
		list := t.processTransaction(signature, slot, blockTime.Time().Unix(), status, fee, tokenTransfers)
		solTransactions = append(solTransactions, list...)

	}

	return solTransactions, nil
}

// ConvertSOLChangesToTransfers 将SOL余额变化转换为转账记录
func (t *TransactionsFetcher) ConvertSOLChangesToTransfers(solChanges map[string]int64) []TokenTransfer {
	var transfers []TokenTransfer
	var senders, receivers []SOLBalanceChange

	// 分离发送方和接收方
	for account, change := range solChanges {
		amount := decimal.NewFromInt(int64(change))
		if amount.IsNegative() {
			senders = append(senders, SOLBalanceChange{
				Account: account,
				Amount:  amount.Abs(),
			})
		} else if amount.IsPositive() {
			receivers = append(receivers, SOLBalanceChange{
				Account: account,
				Amount:  amount,
			})
		}
	}
	// 简单情况：一对一转账
	if len(senders) == 1 && len(receivers) == 1 {
		transfers = append(transfers, TokenTransfer{
			FromOwner: senders[0].Account,
			ToOwner:   receivers[0].Account,
			Amount:    receivers[0].Amount.String(),
		})
		return transfers
	}

	// 复杂情况：多对多转账
	//for _, sender := range senders {
	//	transfers = append(transfers, TokenTransfer{
	//		FromOwner: sender.Account,
	//		ToOwner:   "", // 留空表示未知接收方
	//		Amount:    sender.Amount.String(),
	//	})
	//}
	for k, receiver := range receivers {
		fromOwner := ""
		if len(senders) == 1 {
			fromOwner = senders[0].Account
		} else {
			if k < len(senders) {
				fromOwner = senders[k].Account
			}
		}
		if fromOwner == "" {
			continue
		}
		transfers = append(transfers, TokenTransfer{
			FromOwner: fromOwner, // 留空表示未知发送方
			ToOwner:   receiver.Account,
			Amount:    receiver.Amount.String(),
		})
	}

	return transfers
}

// createSOLTransactions 创建SOL转账交易记录
func (t *TransactionsFetcher) createSOLTransactions(signature string, slot, blockTime int64, status, fee string, transfers []TokenTransfer) []*model.Transaction {
	var transactions []*model.Transaction

	for _, transfer := range transfers {
		// 过滤掉自转账
		if transfer.FromOwner == transfer.ToOwner {
			continue
		}

		transactions = append(transactions, &model.Transaction{
			TxHash:      signature,
			BlockNumber: slot,
			ChainIndex:  t.chainIndex,
			FromAddress: transfer.FromOwner,
			ToAddress:   transfer.ToOwner,
			Value:       transfer.Amount,
			Fee:         fee,
			Method:      constant.TransferMethod,
			Status:      status,
			Timestamp:   blockTime,
		})
	}
	return transactions
}

func (t *TransactionsFetcher) processTransaction(signature string, slot, blockTime int64, status string,
	fee string, transfers []TokenTransfer) []*model.Transaction {
	var transactions []*model.Transaction
	for _, transfer := range transfers {
		transactions = append(transactions, &model.Transaction{
			TxHash:      signature,
			BlockNumber: slot,
			ChainIndex:  t.chainIndex,
			FromAddress: transfer.FromOwner,
			ToAddress:   transfer.ToOwner,
			Value:       transfer.Amount,
			Fee:         fee,
			Method:      constant.TransferMethod,
			ProgramID:   transfer.Mint,
			Status:      status,
			Timestamp:   blockTime,
		})
	}

	return transactions
}

// ChangeTokenToTransaction 根据余额变化获取transaction
func ChangeTokenToTransaction(changesByMint map[string][]TokenChange) []TokenTransfer {
	var transfers []TokenTransfer
	for mint, changes := range changesByMint {
		var froms, tos []TokenChange
		for _, ch := range changes {
			if ch.DiffAmount.IsNegative() {
				froms = append(froms, ch)
			} else if ch.DiffAmount.IsPositive() {
				tos = append(tos, ch)
			}
		}
		// 简单场景：单from单to
		if len(froms) == 1 && len(tos) == 1 {
			transfers = append(transfers, TokenTransfer{
				Mint:        mint,
				FromOwner:   froms[0].Owner,
				FromAccount: froms[0].Account,
				ToOwner:     tos[0].Owner,
				ToAccount:   tos[0].Account,
				Amount:      tos[0].DiffAmount.String(),
			})
		} else {
			//// 多from多to场景：全量输出
			//for _, from := range froms {
			//	transfers = append(transfers, TokenTransfer{
			//		Mint:        mint,
			//		FromOwner:   from.Owner,
			//		FromAccount: from.Account,
			//		ToOwner:     "",
			//		ToAccount:   "",
			//		Amount:      from.DiffAmount.Abs().String(),
			//	})
			//}
			//for _, to := range tos {
			//	transfers = append(transfers, TokenTransfer{
			//		Mint:        mint,
			//		FromOwner:   "",
			//		FromAccount: "",
			//		ToOwner:     to.Owner,
			//		ToAccount:   to.Account,
			//		Amount:      to.DiffAmount.Abs().String(),
			//	})
			//}
		}
	}
	return transfers
}

// Flatten inner instructions into a single slice
func flattenInnerInstructions(inner []rpc.InnerInstruction) []solana.CompiledInstruction {
	var all []solana.CompiledInstruction
	for _, in := range inner {
		all = append(all, in.Instructions...)
	}
	return all
}

func (t *TransactionsFetcher) createFailedTransaction(signature, status, from string, timestamp int64, blockNumber int64, fee string) *model.Transaction {
	return &model.Transaction{
		ChainIndex:  t.chainIndex,
		TxHash:      signature,
		Status:      status,
		FromAddress: from,
		Timestamp:   timestamp,
		BlockNumber: blockNumber,
		Fee:         fee,
		Value:       "0",
	}
}

// Helper function to create transaction from instruction
func (t *TransactionsFetcher) createTransactionFromInstruction(
	inst solana.CompiledInstruction,
	accounts []solana.PublicKey,
	solChange map[string]int64,
	solTokenChange map[string]TokenChange,
	signature string,
	status string,
	fee string,
	timestamp int64,
	blockNumber int64,
) *model.Transaction {
	solTx := t.HandleInstruction(inst, accounts, solChange, solTokenChange)
	if solTx == nil {
		return nil
	}
	if solTx.Mint != "" {
		if v, ok := solTokenChange[solTx.FromAddress]; ok {
			solTx.FromAddress = v.Owner
		}
		if v, ok := solTokenChange[solTx.ToAddress]; ok {
			solTx.ToAddress = v.Owner
		}
	}
	return &model.Transaction{
		ChainIndex:   t.chainIndex,
		TxHash:       signature,
		Status:       status,
		Fee:          fee,
		FromAddress:  solTx.FromAddress,
		ToAddress:    solTx.ToAddress,
		Method:       solTx.Method,
		ProgramID:    solTx.Mint,
		Value:        solTx.Amount.String(),
		Timestamp:    timestamp,
		BlockNumber:  blockNumber,
		TokenDecimal: int64(solTx.Decimals),
	}
}
