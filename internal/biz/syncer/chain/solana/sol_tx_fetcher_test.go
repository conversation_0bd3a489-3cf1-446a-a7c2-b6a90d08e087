package solana

import (
	"byd_wallet/common/constant"
	"context"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"os"
	"testing"
)

func TestTokenAccountsByOwner(t *testing.T) {
	logger := log.With(log.NewStdLogger(os.Stdout),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"service.id", 1,
		"service.name", "test",
		"service.version", "1.1",
		"trace.id", tracing.TraceID(),
		"span.id", tracing.SpanID(),
	)
	f, _, err := NewTransactionsFetcher(log.New<PERSON>elper(logger), constant.SolChainIndex, solRpc)
	if err != nil {
		t.Error(err)
	}
	tokenAccounts, err := f.GetTokenAccountsByOwner("8XWiWfZyr1hkUrgJp8YxYdUSwVZ3zE1yVN1B4gUL1w1e", "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA")
	if err != nil {
		t.Error(err)
	}
	fmt.Println(tokenAccounts)
}

func TestBlockBySlot(t *testing.T) {
	logger := log.With(log.NewStdLogger(os.Stdout),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"service.id", 1,
		"service.name", "test",
		"service.version", "1.1",
		"trace.id", tracing.TraceID(),
		"span.id", tracing.SpanID(),
	)
	f, _, err := NewTransactionsFetcher(log.NewHelper(logger), constant.SolChainIndex, solRpc)
	if err != nil {
		t.Error(err)
	}
	transactions, err := f.FetchTransactionsByBlockNumber(context.Background(), *********)
	if err != nil {
		t.Error(err)
	}
	for _, v := range transactions {
		if v.TxHash == "39zqVkZBNZ2buppnM26cVtzGpyZSe4TLMbYvKptMt543D6ADVRiyrZLtzjE4j8tWjhNskeud8ZvyY9xKZMma27a1" {
			fmt.Printf("transactions = %+v\n", v)
		}

	}

}
