package solana

import (
	"byd_wallet/common/constant"
	"encoding/binary"
	"fmt"
	"github.com/gagliardetto/solana-go"
	"github.com/shopspring/decimal"
)

func (t *TransactionsFetcher) handleInstructionInitializeAccount(inst solana.CompiledInstruction, accounts []solana.PublicKey) (*SolTransaction, error) {
	if len(inst.Accounts) < 3 {
		return nil, fmt.Errorf("insufficient accounts for close instruction")
	}
	source := getAccountSafe(accounts, inst.Accounts, 0)
	mint := getAccountSafe(accounts, inst.Accounts, 1)
	owner := getAccountSafe(accounts, inst.Accounts, 2)
	//fmt.Println("owner = ", owner)
	//destinationAccount2 := getAccountSafe(accounts, inst.Accounts, 3)
	//fmt.Println("destinationAccount2 = ", destinationAccount2)
	return &SolTransaction{
		FromAddress: source,
		ToAddress:   owner,
		Mint:        mint,
		Method:      constant.InstructionInitializeAccountMethod,
	}, nil
}
func (t *TransactionsFetcher) handleInstructionInitializeAccount2(inst solana.CompiledInstruction, accounts []solana.PublicKey) (*SolTransaction, error) {
	// 暂时不支持
	return &SolTransaction{}, nil
	if len(inst.Accounts) < 2 {
		return nil, fmt.Errorf("insufficient accounts for close instruction")
	}
	closedAccount := getAccountSafe(accounts, inst.Accounts, 0)
	destinationAccount := getAccountSafe(accounts, inst.Accounts, 1)

	return &SolTransaction{
		FromAddress: closedAccount,
		ToAddress:   destinationAccount,
		Method:      constant.CloseAccountMethod,
	}, nil
}

func (t *TransactionsFetcher) handleInstructionInitializeAccount3(inst solana.CompiledInstruction, accounts []solana.PublicKey) (*SolTransaction, error) {
	if len(inst.Accounts) < 2 {
		return nil, fmt.Errorf("insufficient accounts for close instruction")
	}
	owner := ""
	if len(inst.Data) >= 33 {
		ownerPubkey := solana.PublicKeyFromBytes(inst.Data[1:33])
		owner = ownerPubkey.String()
	}
	source := getAccountSafe(accounts, inst.Accounts, 0)
	mint := getAccountSafe(accounts, inst.Accounts, 1)

	return &SolTransaction{
		FromAddress: source,
		ToAddress:   owner,
		Mint:        mint,
		Method:      constant.InstructionInitializeAccount3Method,
	}, nil
}

func (t *TransactionsFetcher) handleInstructionApprove(inst solana.CompiledInstruction, accounts []solana.PublicKey) (*SolTransaction, error) {
	if len(inst.Data) < 9 {
		return nil, fmt.Errorf("insufficient accounts for close instruction")
	}

	//instructionType = "approve"
	//parsedInfo = map[string]interface{}{
	//	"source":   ins.Accounts[0].PubKey.ToBase58(),
	//	"delegate": ins.Accounts[1].PubKey.ToBase58(),
	//	"amount":   a.Amount,
	//}

	if len(inst.Accounts) < 2 {
		return nil, fmt.Errorf("insufficient accounts for close instruction")
	}
	amount := binary.LittleEndian.Uint64(inst.Data[1:9])
	source := getAccountSafe(accounts, inst.Accounts, 0)
	to := getAccountSafe(accounts, inst.Accounts, 1)

	return &SolTransaction{
		FromAddress: source,
		ToAddress:   to,
		Amount:      decimal.NewFromUint64(amount),
		Method:      constant.ApproveInstructionMethod,
	}, nil
}
func (t *TransactionsFetcher) handleInstructionApproveChecked(inst solana.CompiledInstruction, accounts []solana.PublicKey) (*SolTransaction, error) {
	if len(inst.Data) < 9 {
		return nil, fmt.Errorf("insufficient accounts for close instruction")
	}

	if len(inst.Accounts) < 2 {
		return nil, fmt.Errorf("insufficient accounts for close instruction")
	}
	amount := binary.LittleEndian.Uint64(inst.Data[1:9])
	source := getAccountSafe(accounts, inst.Accounts, 0)
	to := getAccountSafe(accounts, inst.Accounts, 1)

	return &SolTransaction{
		FromAddress: source,
		ToAddress:   to,
		Amount:      decimal.NewFromUint64(amount),
		Method:      constant.ApproveInstructionMethod,
	}, nil
}
