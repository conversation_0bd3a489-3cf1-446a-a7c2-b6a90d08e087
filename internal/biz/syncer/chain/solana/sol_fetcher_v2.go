package solana

import (
	"byd_wallet/model"
	"context"
	"fmt"
	"github.com/gagliardetto/solana-go/rpc"

	"github.com/go-kratos/kratos/v2/log"
)

type BlockFetcher struct {
	log               *log.Helper
	cli               *TransactionsFetcher
	blockchainNetWork *model.BlockchainNetwork
}

func NewBlockFetcher(logger log.Logger) *BlockFetcher {
	return &BlockFetcher{
		log: log.NewHelper(logger),
	}
}

func (b *BlockFetcher) BlockchainNetWork() *model.BlockchainNetwork {
	return b.blockchainNetWork
}

func (b *BlockFetcher) SetBlockchainNetWork(blockchainNetWork *model.BlockchainNetwork) {
	b.blockchainNetWork = blockchainNetWork
}

func (b *BlockFetcher) SetEndpoints(endpoints []string) error {
	if len(endpoints) == 0 {
		return nil
	}
	var err error
	b.cli, _, err = NewTransactionsFetcher(b.log, b.blockchainNetWork.ChainIndex, endpoints)
	return err
}

func (b *BlockFetcher) Close() error {
	return nil
}

func (b *BlockFetcher) FetchTransactions(ctx context.Context, height int64) (<-chan *model.Transaction, error) {
	txChan := make(chan *model.Transaction, 2500)
	dbTxs, err := b.cli.FetchTransactionsByBlockNumber(ctx, height)
	if err != nil {
		return nil, fmt.Errorf("failed to get transactions for block %d: %w", height, err)
	}

	go func() {
		defer close(txChan)

		for _, tx := range dbTxs {
			select {
			case txChan <- tx:
			case <-ctx.Done():
				b.log.Infow(log.DefaultMessageKey, "FetchTransactions: context cancelled", "blockHeight", height)
				return
			}
		}
	}()

	return txChan, nil
}

func (b *BlockFetcher) GetLatestBlockHeight(ctx context.Context) (int64, error) {
	cli := b.cli.Select()
	lastSlotNumber, err := cli.GetSlot(ctx, rpc.CommitmentConfirmed)
	if err != nil {
		return 0, err
	}
	return int64(lastSlotNumber), nil
}

func (b *BlockFetcher) GetDebugTraceTransaction(txn string) ([]*model.Transaction, error) {
	// TODO
	return nil, nil
}
