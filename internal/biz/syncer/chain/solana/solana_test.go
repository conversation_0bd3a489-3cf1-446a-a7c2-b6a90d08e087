package solana

var solRpc = []string{"https://go.getblock.io/22fd29113b5b4842a6d59e903d5d96e1"}

// var solRpc = []string{"https://go.getblock.io/b75c633f09c140958931fa0d77e4db04", "https://go.getblock.io/22fd29113b5b4842a6d59e903d5d96e1", "https://bold-damp-ensemble.solana-mainnet.quiknode.pro/1c8794bfad096e455ce87d17b00dc942af2f88ea"}
//var solRpc = []string{"https://indulgent-quiet-aura.solana-mainnet.quiknode.pro/f93abb88eb59bb86cffcc68dc456d625610d7a5b", "https://go.getblock.io/b75c633f09c140958931fa0d77e4db04", "https://bold-damp-ensemble.solana-mainnet.quiknode.pro/1c8794bfad096e455ce87d17b00dc942af2f88ea"}

//var solRpc = []string{"https://go.getblock.io/b75c633f09c140958931fa0d77e4db04"}

//var solRpc = []string{"https://bold-damp-ensemble.solana-mainnet.quiknode.pro/1c8794bfad096e455ce87d17b00dc942af2f88ea"}

//func TestNewSolClient(t *testing.T) {
//	cli, err := NewTransactionsFetcher(501, solRpc)
//	if err != nil {
//		t.Error(err)
//	}
//	node, err := cli.SelectNode()
//	if err != nil {
//		return
//	}
//
//	newSlot, err := node.Client.GetSlot(context.Background())
//	if err != nil {
//		t.Error(err)
//	}
//	fmt.Println("newSlot = ", newSlot)
//	//newSlot = *********
//
//	list := cli.SyncBlocksConcurrent(newSlot-1, newSlot, 30)
//	fmt.Printf("list %+v\n", list)
//}
//
//func TestGetTokenAccount(t *testing.T) {
//	cli, err := NewSolClient(501, solRpc)
//	if err != nil {
//		t.Error(err)
//	}
//	node, err := cli.SelectNode()
//	if err != nil {
//		return
//	}
//
//	acc, err := node.Client.GetAccountInfo(context.Background(), "syX9YosBtt6qMw7mDfaRjcguef9a9YiuYVXJCvhDaRw")
//	if err != nil {
//		t.Error(err)
//	}
//	if acc.Data != nil {
//		tokenAcc, err := token.TokenAccountFromData(acc.Data)
//		if err != nil {
//			t.Error(err)
//		}
//		t.Logf("tokenAcc = %+v \n", tokenAcc)
//	}
//
//}

//func TestGetTokenAcc(t *testing.T) {
//	cli, err := NewSolClient(501, solRpc)
//	if err != nil {
//		t.Error(err)
//	}
//	node, err := cli.SelectNode()
//	if err != nil {
//		return
//	}
//	acc, err := node.Client.GetAccountInfo(context.Background(), "3qkzSejseLztzZ3eWyZipu23zfWu4172wxR8Do6xeY4X")
//	if err != nil {
//		t.Error(err)
//	}
//	tokenInfo, err := node.Client.GetTokenAccount(context.Background(), "3qkzSejseLztzZ3eWyZipu23zfWu4172wxR8Do6xeY4X")
//	if err != nil {
//		t.Error(err)
//	}
//	tInfo, err := token.DeserializeTokenAccount(acc.Data, acc.Owner)
//	if err != nil {
//		t.Error(err)
//	}
//	fmt.Printf("acc = %+v \n", acc)
//	fmt.Printf("tInfo = %+v \n", tInfo)
//	fmt.Printf("tokenInfo = %+v\n", tokenInfo)
//	var fromTokenAccount gaToken.Account
//	if acc.Data != nil {
//		fromDecoder := bin.NewBinDecoder(acc.Data)
//		err = fromTokenAccount.UnmarshalWithDecoder(fromDecoder)
//		if err != nil {
//			fmt.Printf("[%s] from Failed to deserialize: %v\n", "gaToken", err)
//		}
//		from := fromTokenAccount.Owner.String()
//		fmt.Printf("from = %+v \n", from)
//		fmt.Printf("fromTokenAccount = %+v \n", fromTokenAccount)
//	}
//
//}
//
//func TestGetTokenBalance(t *testing.T) {
//	cli, err := NewSolClient(501, solRpc)
//	if err != nil {
//		t.Error(err)
//	}
//
//	node, err := cli.SelectNode()
//	if err != nil {
//		return
//	}
//	//cli.Cli.GetAccountInfoWithConfig()
//	acc, err := node.Client.GetAccountInfo(context.Background(), "3qkzSejseLztzZ3eWyZipu23zfWu4172wxR8Do6xeY4X")
//	if err != nil {
//		t.Error(err)
//	}
//	tokenInfo, err := node.Client.GetTokenAccount(context.Background(), "3qkzSejseLztzZ3eWyZipu23zfWu4172wxR8Do6xeY4X")
//	if err != nil {
//		t.Error(err)
//	}
//	tInfo, err := token.DeserializeTokenAccount(acc.Data, acc.Owner)
//	if err != nil {
//		t.Error(err)
//	}
//	fmt.Printf("acc = %+v \n", acc)
//	fmt.Printf("tInfo = %+v \n", tInfo)
//	fmt.Printf("tokenInfo = %+v\n", tokenInfo)
//	var fromTokenAccount gaToken.Account
//	if acc.Data != nil {
//		fromDecoder := bin.NewBinDecoder(acc.Data)
//		err = fromTokenAccount.UnmarshalWithDecoder(fromDecoder)
//		if err != nil {
//			fmt.Printf("[%s] from Failed to deserialize: %v\n", "gaToken", err)
//		}
//		from := fromTokenAccount.Owner.String()
//		fmt.Printf("from = %+v \n", from)
//		fmt.Printf("fromTokenAccount = %+v \n", fromTokenAccount)
//	}
//
//}
