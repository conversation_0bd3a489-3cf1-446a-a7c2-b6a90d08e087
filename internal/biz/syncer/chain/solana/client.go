package solana

import (
	"errors"
	"github.com/gagliardetto/solana-go/rpc"
	"sync"
)

type SmartNodeSelectionClient struct {
	clients []*rpc.Client // RPC 客户端列表

	mu    sync.Mutex
	index int
}

func NewSmartNodeSelectionClient(rpcList []string) (*SmartNodeSelectionClient, func(), error) {
	if len(rpcList) == 0 {
		return nil, nil, errors.New("empty RPC list")
	}
	nodes := make([]*rpc.Client, 0, len(rpcList))
	for _, url := range rpcList {
		if url == "" {
			continue
		}
		nodes = append(nodes, rpc.New(url))
	}

	if len(nodes) == 0 {
		return nil, nil, errors.New("no valid RPC URLs")
	}
	cli := &SmartNodeSelectionClient{
		clients: nodes,
		mu:      sync.Mutex{},
	}
	return cli, func() {
		_ = cli.Close()
	}, nil
}

func (cli *SmartNodeSelectionClient) Select() *rpc.Client {
	// FIXME: round robin selection
	cli.mu.Lock()
	defer cli.mu.Unlock()
	item := cli.clients[cli.index]
	cli.index = (cli.index + 1) % len(cli.clients)
	return item
}

func (cli *SmartNodeSelectionClient) Close() error {
	// NOTE: all nodes is http.Client
	return nil
}
