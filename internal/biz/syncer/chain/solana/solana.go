package solana

import (
	"byd_wallet/common/constant"
	"encoding/binary"
	"errors"
	"fmt"
	"github.com/gagliardetto/solana-go"
	"github.com/gagliardetto/solana-go/programs/system"
	"github.com/gagliardetto/solana-go/programs/token"
	"github.com/gagliardetto/solana-go/rpc"
	"github.com/ghostiam/binstruct"

	"github.com/near/borsh-go"
	"github.com/shopspring/decimal"
)

const (
	maxRetries = 3 // 单请求最大重试次数
)

// 执行请求（示例）
func (t *TransactionsFetcher) GetBlock(slot uint64) (*rpc.GetBlockResult, error) {
	var lastErr error
	for retry := 0; retry < maxRetries; retry++ {
		cli := t.Select()
		block, err := cli.GetBlock(t.ctx, slot)
		if err == nil {
			return block, nil
		}

		lastErr = err
	}
	return nil, fmt.Errorf("after %d retries, last error: %v", maxRetries, lastErr)
}

func (t *TransactionsFetcher) HandleInstruction(
	inst solana.CompiledInstruction,
	accounts []solana.PublicKey,
	solChange map[string]int64,
	solTokenChange map[string]TokenChange,
) *SolTransaction {
	if int(inst.ProgramIDIndex) >= len(accounts) {
		return nil
	}
	programID := accounts[inst.ProgramIDIndex]

	var solTransaction *SolTransaction
	switch programID.String() {
	case solana.TokenProgramID.String():

		solTransaction, _ = t.ParseTokenInstruction(inst, accounts, solChange, solTokenChange)

	case solana.SystemProgramID.String():
		solTransaction, _ = t.ParseSystemInstruction(inst, accounts)
	case "routeUGWgWzqBWFcrCfv8tritsqukccJPu3q5GPP3xS":
		solTransaction, _ = t.ParseSystemInstruction(inst, accounts)
	default:
		return nil
	}

	return solTransaction
}

func (t *TransactionsFetcher) ParseTokenInstruction(inst solana.CompiledInstruction, accounts []solana.PublicKey, solChange map[string]int64, tokenChanges map[string]TokenChange) (*SolTransaction, error) {
	// 基础验证
	if len(inst.Data) == 0 {
		return nil, fmt.Errorf("empty instruction data")
	}
	if len(inst.Accounts) < 2 {
		return nil, fmt.Errorf("insufficient accounts in instruction")
	}
	// 解析指令类型
	//instructionType := token.Instruction(inst.Data[0])
	instructionType := inst.Data[0]
	// 根据指令类型处理
	switch instructionType {
	case token.Instruction_InitializeMint:
		return t.handleSplInstructionInitializeMint(inst, accounts)
	case token.Instruction_Transfer:
		solTransaction, err := t.handleSplInstructionTransfer(inst, accounts)
		if err != nil {
			return nil, err
		}
		if solTransaction != nil && solTransaction.Method == constant.TransferMethod {
			if value, ok := tokenChanges[solTransaction.Mint]; ok {
				solTransaction.Mint = value.Mint
			}
		}
		return solTransaction, nil
	case token.Instruction_MintTo:
		return t.handleMintToInstruction(inst, accounts)
	case token.Instruction_TransferChecked:
		return t.handleSplInstructionTransferChecked(inst, accounts)
	case token.Instruction_CloseAccount:
		solTransaction, err := t.handleInstructionCloseAccount(inst, accounts)
		if err != nil {
			return nil, err
		}
		if solTransaction != nil && solTransaction.Method == constant.CloseAccountMethod {
			if value, ok := solChange[solTransaction.FromAddress]; ok {
				solTransaction.Amount = decimal.NewFromInt(value).Abs()
			}
		}
		return solTransaction, nil
	case token.Instruction_InitializeAccount:
		return t.handleInstructionInitializeAccount(inst, accounts)
	case token.Instruction_InitializeAccount2:
		return t.handleInstructionInitializeAccount2(inst, accounts)
	case token.Instruction_InitializeAccount3:
		return t.handleInstructionInitializeAccount3(inst, accounts)
	case token.Instruction_Approve:
		return t.handleInstructionApprove(inst, accounts)
	case token.Instruction_ApproveChecked:
		return t.handleInstructionApproveChecked(inst, accounts)
	default:
		return nil, fmt.Errorf("unsupported instruction type: %d", instructionType)
	}

}

func (t *TransactionsFetcher) GetTransferAddresses(accounts []solana.PublicKey, accountIndexes []uint16, fromIdx, toIdx int) (from, to string, err error) {
	// 安全获取账户地址
	fromAddress := getAccountSafe(accounts, accountIndexes, fromIdx)
	toAddress := getAccountSafe(accounts, accountIndexes, toIdx)
	return fromAddress, toAddress, nil
}

func (t *TransactionsFetcher) handleSplInstructionInitializeMint(inst solana.CompiledInstruction, accounts []solana.PublicKey) (*SolTransaction, error) {
	data := inst.Data
	data = data[1:] // 跳过 discriminator

	if len(data) < 1 {
		return nil, fmt.Errorf("invalid data length")
	}
	// 解析 decimals (1 字节)
	decimals := data[0]
	data = data[1:]

	// 解析 mint authority (32 字节)
	if len(data) < 32 {
		return nil, fmt.Errorf("invalid mint authority length")
	}
	mintAuthority := solana.PublicKeyFromBytes(data[:32])
	data = data[32:]

	return &SolTransaction{
		FromAddress: mintAuthority.String(),
		Method:      constant.InitializeMintMethod,
		Mint:        getAccountSafe(accounts, inst.Accounts, 0),
		Decimals:    int(decimals),
	}, nil
}
func (t *TransactionsFetcher) handleSplInstructionTransfer(inst solana.CompiledInstruction, accounts []solana.PublicKey) (*SolTransaction, error) {
	// 解析转账金额
	var transfer TransferInstruction
	if err := borsh.Deserialize(&transfer, inst.Data); err != nil {
		return nil, fmt.Errorf("failed to deserialize transfer instruction: %w data = %v", err, inst.Data)
	}
	if len(inst.Data) < 9 {
		return nil, fmt.Errorf("invalid transfer instruction data length")
	}
	from, to, err := t.GetTransferAddresses(accounts, inst.Accounts, 2, 1) // mint authority is from (index 2), destination is to (index 1)
	if err != nil {
		return nil, err
	}

	return &SolTransaction{
		FromAddress: from,
		ToAddress:   to,
		Amount:      decimal.NewFromUint64(transfer.Amount),
		Method:      constant.TransferMethod,
		Mint:        getAccountSafe(accounts, inst.Accounts, 0),
	}, nil
}
func (t *TransactionsFetcher) handleSplInstructionTransferChecked(inst solana.CompiledInstruction, accounts []solana.PublicKey) (*SolTransaction, error) {
	// 解析转账金额
	var transfer TransferInstruction
	if err := borsh.Deserialize(&transfer, inst.Data); err != nil {
		fmt.Println("deserialize err:", err, accounts, inst.Accounts)
		return nil, fmt.Errorf("failed to deserialize transfer instruction: %w data = %v", err, inst.Data)
	}
	from, to, err := t.GetTransferAddresses(accounts, inst.Accounts, 3, 2) // mint authority is from (index 2), destination is to (index 1)
	if err != nil {
		fmt.Println("transfer addresses err:", err, accounts, inst.Accounts)
		return nil, err
	}

	return &SolTransaction{
		FromAddress: from,
		ToAddress:   to,
		Amount:      decimal.NewFromUint64(transfer.Amount),
		Mint:        getAccountSafe(accounts, inst.Accounts, 1),
		Method:      constant.TransferCheckedMethod,
		Decimals:    int(inst.Data[9]),
	}, nil
}
func (t *TransactionsFetcher) handleMintToInstruction(inst solana.CompiledInstruction, accounts []solana.PublicKey) (*SolTransaction, error) {
	// 解析转账金额
	var transfer TransferInstruction
	if err := borsh.Deserialize(&transfer, inst.Data); err != nil {
		return nil, fmt.Errorf("failed to deserialize transfer instruction: %w data = %v", err, inst.Data)
	}
	from, to, err := t.GetTransferAddresses(accounts, inst.Accounts, 2, 1) // mint authority is from (index 2), destination is to (index 1)
	if err != nil {
		return nil, err
	}

	return &SolTransaction{
		FromAddress: from,
		ToAddress:   to,
		Amount:      decimal.NewFromUint64(transfer.Amount),
		Mint:        getAccountSafe(accounts, inst.Accounts, 0),
		Method:      constant.MintToMethod,
		//Decimals:    int(inst.Data[9]),
	}, nil
}

func (t *TransactionsFetcher) handleInstructionCloseAccount(inst solana.CompiledInstruction, accounts []solana.PublicKey) (*SolTransaction, error) {
	// For close account, the destination is the account to receive lamports (usually the owner)
	// and the source is the account being closed
	if len(inst.Accounts) < 2 {
		return nil, fmt.Errorf("insufficient accounts for close instruction")
	}
	closedAccount := getAccountSafe(accounts, inst.Accounts, 0)
	destinationAccount := getAccountSafe(accounts, inst.Accounts, 1)

	return &SolTransaction{
		FromAddress: closedAccount,
		ToAddress:   destinationAccount,
		Method:      constant.CloseAccountMethod,
	}, nil
}

func (t *TransactionsFetcher) ParseSystemInstruction(inst solana.CompiledInstruction, accounts []solana.PublicKey) (*SolTransaction, error) {
	if len(inst.Data) == 0 {
		return nil, fmt.Errorf("empty instruction data")
	}
	if len(inst.Data) < 4 {
		return nil, fmt.Errorf("invalid instruction data length")
	}
	data := inst.Data
	instruct := binary.LittleEndian.Uint32(data[:4])
	//in := system.Instruction(instruct)
	switch instruct {
	case system.Instruction_CreateAccount: // System create account
		return t.handleInstructionCreateAccount(inst, accounts)
	case system.Instruction_Transfer: // System transfer
		return t.handleInstructionTransfer(inst, accounts)
	case system.Instruction_CreateAccountWithSeed:
		return t.handleInstructionCreateAccountWithSeed(inst, accounts)
	//case system.InstructionUpgradeNonceAccount:
	case system.Instruction_WithdrawNonceAccount:
		return t.handleInstructionWithdrawNonceAccount(inst, accounts)
	default:
	}
	return nil, errors.New("not implemented")
}

func (t *TransactionsFetcher) handleInstructionCreateAccountWithSeed(inst solana.CompiledInstruction, accounts []solana.PublicKey) (*SolTransaction, error) {
	var a CreateAccountWithSeedInstruction
	err := binstruct.UnmarshalLE(inst.Data, &a)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal CreateAccountWithSeed instruction: %w", err)
	}
	//parsedInfo := map[string]interface{}{
	//	"source":     getAccountSafe(accounts, inst.Accounts, 0),
	//	"newAccount": getAccountSafe(accounts, inst.Accounts, 1),
	//	"base":       a.Base,
	//	"seed":       a.Seed,
	//	"space":      a.Space,
	//	"lamports":   a.Lamports,
	//	"owner":      a.ProgramID,
	//}
	var transaction SolTransaction
	transaction.Method = constant.InstructionCreateAccountWithSeedMethod
	transaction.Amount = decimal.NewFromUint64(a.Lamports)
	transaction.FromAddress = getAccountSafe(accounts, inst.Accounts, 0)
	transaction.ToAddress = getAccountSafe(accounts, inst.Accounts, 1)
	return &transaction, nil
}
func (t *TransactionsFetcher) handleInstructionWithdrawNonceAccount(inst solana.CompiledInstruction, accounts []solana.PublicKey) (*SolTransaction, error) {
	var a WithdrawNonceAccountInstruction
	err := binstruct.UnmarshalLE(inst.Data, &a)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal CreateAccountWithSeed instruction: %w", err)
	}
	//parsedInfo = map[string]interface{}{
	//	"nonceAccount":            ins.Accounts[0].PubKey.ToBase58(),
	//	"destination":             ins.Accounts[1].PubKey.ToBase58(),
	//	"recentBlockhashesSysvar": ins.Accounts[2].PubKey.ToBase58(),
	//	"rentSysvar":              ins.Accounts[3].PubKey.ToBase58(),
	//	"nonceAuthority":          ins.Accounts[4].PubKey.ToBase58(),
	//	"lamports":                a.Lamports,
	//}
	var transaction SolTransaction
	transaction.Method = constant.InstructionWithdrawNonceAccountInstructionMethod
	transaction.Amount = decimal.NewFromUint64(a.Lamports)
	transaction.FromAddress = getAccountSafe(accounts, inst.Accounts, 0)
	transaction.ToAddress = getAccountSafe(accounts, inst.Accounts, 1)
	return &transaction, nil
}

func (t *TransactionsFetcher) handleInstructionTransfer(inst solana.CompiledInstruction, accounts []solana.PublicKey) (*SolTransaction, error) {
	data := inst.Data
	if len(data) < 9 || len(inst.Accounts) < 2 {
		return nil, fmt.Errorf("invalid instruction data length")
	}
	var transaction SolTransaction
	amount := binary.LittleEndian.Uint64(data[4:])
	//amount = binary.LittleEndian.Uint64(data[4:12])
	transaction.Amount = decimal.NewFromUint64(amount)
	transaction.FromAddress = getAccountSafe(accounts, inst.Accounts, 0)
	transaction.ToAddress = getAccountSafe(accounts, inst.Accounts, 1)
	transaction.Method = constant.TransferMethod
	return &transaction, nil
}

func (t *TransactionsFetcher) handleInstructionCreateAccount(inst solana.CompiledInstruction, accounts []solana.PublicKey) (*SolTransaction, error) {
	data := inst.Data
	if len(data) < 1+8+8+32 {
		return nil, fmt.Errorf("data too short for CreateAccount instruction")
	}
	var a CreateAccountInstruction
	err := binstruct.UnmarshalLE(inst.Data, &a)
	if err != nil {
		return nil, err
	}
	//parsedInfo := map[string]interface{}{
	//	"source":     getAccountSafe(accounts, inst.Accounts, 0),
	//	"newAccount": getAccountSafe(accounts, inst.Accounts, 1),
	//	"lamports":   a.Lamports,
	//	"space":      a.Space,
	//	"owner":      a.Owner.ToBase58(),
	//}
	//fmt.Println("parsedInfo = ", parsedInfo)

	//InstructionCreateAccountWithSeed
	var transaction SolTransaction
	transaction.Amount = decimal.NewFromUint64(a.Lamports)
	transaction.FromAddress = getAccountSafe(accounts, inst.Accounts, 0)
	transaction.ToAddress = getAccountSafe(accounts, inst.Accounts, 1)
	transaction.Method = constant.InstructionCreateAccountMethod
	return &transaction, nil
}

func getAccountSafe(accounts []solana.PublicKey, indexes []uint16, i int) string {
	if i >= len(indexes) {
		return fmt.Sprintf("invalid_inst_index_%d", i)
	}
	idx := indexes[i]

	if int(idx) >= len(accounts) {
		return fmt.Sprintf("invalid_account_index_%d", idx)
	}
	return accounts[idx].String()
}
