package bitcoin

import (
	"byd_wallet/common/constant"
	"byd_wallet/model"
)

func btcTxsToDb(txs []Tx) []*model.Transaction {
	var transactions []*model.Transaction
	for _, tx := range txs {
		transactions = append(transactions, btcTxToDb(tx)...)
	}
	return transactions
}

func btcTxToDb(tx Tx) []*model.Transaction {
	var transactions []*model.Transaction

	// Handle coinbase transactions
	isCoinbase := false
	// A common way to identify coinbase is by checking if the first input has a coinbase field set,
	// or if its Txid is null/zero and it has no scriptsig (or Addresses are empty).
	// For this implementation, we rely on Vin[0].Addresses being empty or nil as a primary indicator for non-standard inputs like coinbase.
	if len(tx.Vin) == 1 && (tx.Vin[0].Addresses == nil || len(tx.Vin[0].Addresses) == 0) {
		// Further check if it's a known coinbase pattern if type definition allows (e.g., tx.Vin[0].Coinbase != "")
		isCoinbase = true
	}
	if len(tx.Vin) == 0 { // Some representations might show coinbase with no vin entries
		isCoinbase = true
	}

	if isCoinbase {
		for _, vout := range tx.Vout {
			if len(vout.Addresses) > 0 {
				for _, toAddr := range vout.Addresses { // Iterate if multiple addresses in one vout (rare for coinbase)
					transactions = append(transactions, &model.Transaction{
						TxHash:      tx.Txid,
						BlockNumber: tx.BlockHeight,
						ChainIndex:  0, // Per test cases
						FromAddress: "coinbase",
						ToAddress:   toAddr,
						Value:       vout.Value,
						Fee:         tx.Fees,   // Typically 0 for coinbase in this context
						Status:      "success", // Assuming TransactionStatusSuccess is "success"
						Timestamp:   tx.BlockTime,
						Method:      constant.TxMethodTransfer,
					})
				}
			}
		}
		return transactions
	}

	uniqueFromAddresses := make(map[string]bool)
	for _, vin := range tx.Vin {
		for _, addr := range vin.Addresses {
			uniqueFromAddresses[addr] = true
		}
	}

	// Case: "多个input, 1个output" (multiple different senders, one logical output vout)
	// This specific case expects Value to be vin.Value for each sender.
	// The test case has len(tx.Vout) == 1.
	if len(uniqueFromAddresses) > 1 && len(tx.Vout) == 1 {
		mainVout := tx.Vout[0]
		if len(mainVout.Addresses) > 0 {
			toAddr := mainVout.Addresses[0] // Assuming one primary recipient address in the vout

			for _, vin := range tx.Vin {
				if len(vin.Addresses) == 0 {
					continue // Should not happen for non-coinbase standard vin
				}
				fromAddr := vin.Addresses[0] // Assuming one primary sender address in the vin

				// In this specific scenario (multiple inputs to one output),
				// we don't treat the mainVout as change for any particular input.
				// Each input contributes its value.
				transactions = append(transactions, &model.Transaction{
					TxHash:      tx.Txid,
					BlockNumber: tx.BlockHeight,
					ChainIndex:  0,
					FromAddress: fromAddr,
					ToAddress:   toAddr,
					Value:       vin.Value, // Key: use vin.Value for this case
					Fee:         tx.Fees,   // Fee is chain-wide
					Status:      "success",
					Timestamp:   tx.BlockTime,
					Method:      constant.TxMethodTransfer,
				})
			}
			return transactions
		}
	}

	// Default case: Covers single unique sender, or multiple senders with multiple/complex outputs.
	// Value is vout.Value for non-change outputs.
	// Aggregates transactions for the same from-to pair.
	aggregatedTransactions := make(map[string]*model.Transaction)

	for _, vin := range tx.Vin {
		if len(vin.Addresses) == 0 {
			continue
		}
		fromAddr := vin.Addresses[0] // Assuming one primary sender address

		for _, vout := range tx.Vout {
			if len(vout.Addresses) == 0 {
				continue
			}

			isChange := false
			for _, voutAddr := range vout.Addresses {
				if voutAddr == fromAddr {
					isChange = true
					break
				}
			}
			if isChange {
				continue
			}

			for _, toAddr := range vout.Addresses { // Iterate if multiple addresses in one vout
				if fromAddr == toAddr { // Should be covered by isChange, but as a safeguard
					continue
				}

				// For cases like "同个from多个input, 1个output", multiple vins from the same fromAddr
				// should result in one chain to the toAddr with vout.Value.
				// The map key ensures this consolidation.
				pairKey := fromAddr + "_" + toAddr
				if _, exists := aggregatedTransactions[pairKey]; !exists {
					aggregatedTransactions[pairKey] = &model.Transaction{
						TxHash:      tx.Txid,
						BlockNumber: tx.BlockHeight,
						ChainIndex:  0,
						FromAddress: fromAddr,
						ToAddress:   toAddr,
						Value:       vout.Value, // Key: use vout.Value for these cases
						Fee:         tx.Fees,
						Status:      "success",
						Timestamp:   tx.BlockTime,
						Method:      constant.TxMethodTransfer,
					}
				} else {
					// If a chain from fromAddr to toAddr already exists,
					// and this vout also goes to the same toAddr (from the same fromAddr context of the outer vin loop),
					// the current logic takes the first vout.Value encountered.
					// For Bitcoin, distinct vouts to the same recipient are typically distinct payments.
					// However, the test cases (e.g., "同个from多个input, 1个output") imply one consolidated tx
					// where the vout.Value is the total sent to that recipient from that sender in that tx.
					// This is handled because those test cases have only one non-change vout for the specific from-to pair.
				}
			}
		}
	}

	for _, aggTx := range aggregatedTransactions {
		transactions = append(transactions, aggTx)
	}

	return transactions
}
