package bitcoin

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
)

var testEndpoints = "https://still-soft-patina.btc.quiknode.pro/83d9eda3f3e272d94c4bf15b8e8dd51c439f607e/"

func TestQuickNodeClient_GetBlockAndTxs(t *testing.T) {
	cli := NewQuickNodeClient(testEndpoints)
	resp, err := cli.GetBlockAndTxs(context.Background(), 753204, 1)
	assert.NoError(t, err)
	assert.Equal(t, int64(753204), resp.Height)
	assert.Equal(t, 1000, len(resp.Txs))
}

func TestQuickNodeClient_GetTxsByHeight(t *testing.T) {
	cli := NewQuickNodeClient(testEndpoints)
	txs, err := cli.GetTxsByHeight(context.Background(), 753204)
	assert.NoError(t, err)
	assert.Equal(t, 2855, len(txs))
}

func TestQuickNodeClient_GetBlockCount(t *testing.T) {
	cli := NewQuickNodeClient(testEndpoints)
	height, err := cli.GetBlockCount(context.Background())
	assert.NoError(t, err)
	assert.True(t, height > 0)
}

func TestQuickNodeClient_GetUTXOs(t *testing.T) {
	cli := NewQuickNodeClient(testEndpoints)
	utxos, err := cli.GetUTXOs(context.Background(), "******************************************", true)
	assert.NoError(t, err)

	rs, _ := json.Marshal(utxos)
	t.Logf("UTXOs: %s", string(rs))
}
