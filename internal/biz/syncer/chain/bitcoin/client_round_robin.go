package bitcoin

import "sync"

func NewRoundRobinClient(endpoints []string) *RoundRobinClient {
	r := &RoundRobinClient{}
	for _, endpoint := range endpoints {
		r.clients = append(r.clients, NewQuickNodeClient(endpoint))
	}
	return r
}

type RoundRobinClient struct {
	clients []*QuickNodeClient
	index   int
	mu      sync.Mutex
}

func (r *RoundRobinClient) Last() *QuickNodeClient {
	return r.clients[len(r.clients)-1]
}

func (r *RoundRobinClient) Next() *QuickNodeClient {
	r.mu.Lock()
	defer r.mu.Unlock()
	if len(r.clients) == 0 {
		return nil
	}
	item := r.clients[r.index]
	r.index = (r.index + 1) % len(r.clients) // 索引循环递增
	return item
}
