package bitcoin

import (
	"byd_wallet/common/constant"
	"encoding/json"
	"testing"

	"byd_wallet/model"

	"github.com/stretchr/testify/assert"
)

func TestBtcTxToDb(t *testing.T) {
	tests := []struct {
		name     string
		input    json.RawMessage
		expected []*model.Transaction
	}{
		{
			name: "交易包含找零",
			input: json.RawMessage(`{
  "txid": "3feaae2d2c2e2d8d6ad7aa62c94d6a9f84fdf5accc18754d8dc63c65488a8822",
  "version": 1,
  "vin": [
    {
      "txid": "4764327b044aa506c573dd71ade4035bb34de23e87c0a19fa6600ff24c62fa82",
      "vout": 1,
      "sequence": 4294967295,
      "n": 0,
      "addresses": [
        "******************************************"
      ],
      "isAddress": true,
      "value": "11263"
    }
  ],
  "vout": [
    {
      "value": "5000",
      "n": 0,
      "hex": "00149b29785c979d9b1fbd442c5711ce27d2dc116bff",
      "addresses": [
        "******************************************"
      ],
      "isAddress": true
    },
    {
      "value": "4994",
      "n": 1,
      "hex": "0014b7dd1a03a7925f88ab3e2a4302b265e5ce1ee9cb",
      "addresses": [
        "******************************************"
      ],
      "isAddress": true
    }
  ],
  "blockHash": "00000000000000000000d5cb7b5277a730f22a76a768528ccea3cb97ed48de61",
  "blockHeight": 873432,
  "confirmations": 22339,
  "blockTime": 1733442640,
  "size": 222,
  "vsize": 141,
  "value": "9994",
  "valueIn": "11263",
  "fees": "1269",
  "hex": "0100000000010182fa624cf20f60a69fa1c0873ee24db35b03e4ad71dd73c506a54a047b3264470100000000ffffffff0288130000000000001600149b29785c979d9b1fbd442c5711ce27d2dc116bff8213000000000000160014b7dd1a03a7925f88ab3e2a4302b265e5ce1ee9cb02473044022041a3a5ff8164dd30d7a9dfa80d9a3825bdaf5951fb5c8fd28a41db2d3433d0ae02203a4e8c5907e91c1ff42770334fb04a6c78cb59c2e1750cd9902e67a09ffff58601210385b440c2ebebf86c3977f12fb0abe8e0e852b13d0cc46dae31ea2505732fc90300000000"
}
`),
			expected: []*model.Transaction{
				{
					TxHash:      "3feaae2d2c2e2d8d6ad7aa62c94d6a9f84fdf5accc18754d8dc63c65488a8822",
					BlockNumber: 873432,
					ChainIndex:  0,
					FromAddress: "******************************************",
					ToAddress:   "******************************************",
					Value:       "5000",
					Fee:         "1269",
					Status:      constant.TransactionStatusSuccess,
					Timestamp:   1733442640,
				},
			},
		},

		{
			name:  "同个from多个input, 1个output",
			input: json.RawMessage(`{"txid":"cd0ecb06b7d12b3ff7a85f64d6aa0c07fa8a8dfbf9c3400bd7b22cbb2887b75d","version":1,"vin":[{"txid":"73809b61fb64cd0b7e6211ad3808a0bc88689ca739a1a81c49a45273e710be8a","sequence":4294967295,"n":0,"addresses":["******************************************"],"isAddress":true,"value":"5000"},{"txid":"3a048029e5cd8a7336428f80be5dcb30240dfb1ad3e5a0bb85e427048050a99b","sequence":4294967295,"n":1,"addresses":["******************************************"],"isAddress":true,"value":"10000"}],"vout":[{"value":"13250","n":0,"spent":true,"hex":"0014b7dd1a03a7925f88ab3e2a4302b265e5ce1ee9cb","addresses":["******************************************"],"isAddress":true}],"blockHash":"00000000000000000000130182f2284507e7d62a89c6bdaeee74389d0ce3768a","blockHeight":872449,"confirmations":23324,"blockTime":1732870285,"size":341,"vsize":178,"value":"13250","valueIn":"15000","fees":"1750","hex":"010000000001028abe10e77352a4491ca8a139a79c6888bca00838ad11627e0bcd64fb619b80730000000000ffffffff9ba950800427e485bba0e5d31afb0d2430cb5dbe808f4236738acde52980043a0000000000ffffffff01c233000000000000160014b7dd1a03a7925f88ab3e2a4302b265e5ce1ee9cb02483045022100c2037e8ca41d534a90eb8f577c5a92dae764464398df4379dd87d4b8d031b6cd02204ad2b568a2d7e1a0d8955694bdfdb61ced929ef0521f59994c6f9a324c83740d012103187fb9a294bfd1e57bbc9692aa8ff62f589ae12c3c711d5ad3260c112ed883f602483045022100ad24683a9b3a6ca2e820e1e3cc44c14e5d13c3a3ca04cb1f0a086701fc3b1808022045eaef32098d44a732484d67e91a3f9f246a76793a8fa61a056e0971cfe503a4012103187fb9a294bfd1e57bbc9692aa8ff62f589ae12c3c711d5ad3260c112ed883f600000000"}`),
			expected: []*model.Transaction{
				{
					TxHash:      "cd0ecb06b7d12b3ff7a85f64d6aa0c07fa8a8dfbf9c3400bd7b22cbb2887b75d",
					BlockNumber: 872449,
					ChainIndex:  0,
					FromAddress: "******************************************",
					ToAddress:   "******************************************",
					Value:       "13250",
					Fee:         "1750",
					Status:      constant.TransactionStatusSuccess,
					Timestamp:   1732870285,
				},
			},
		},
		{
			name: "同个from多个input, 多个output(找零)",
			input: json.RawMessage(`{"txid":"d164ac7edde4054e82be166a67bf8954a6d63ae98dc402fab9e2fd782c5e2d77","version":2,"vin":[{"txid":"dbce81ed4bfafa8dd8648520cdc5c340465ed3f3237d69ec9c6369126212846e","vout":1,"sequence":4294967295,"n":0,"addresses":["bc1q7e7j5u8rk6we50j2y4vgxnnqq8jwc7xsssws7q"],"isAddress":true,"value":"821327"},{"txid":"587a6c44e4b126a15578c5d347dce0a1a4fbd365596f56e7d3e5073f37632a2f","vout":1,"sequence":4294967295,"n":1,"addresses":["bc1q7e7j5u8rk6we50j2y4vgxnnqq8jwc7xsssws7q"],"isAddress":true,"value":"81197233"}],"vout":[{"value":"66051491","n":0,"spent":true,"hex":"001442a59d2b9bad665e537b1f3004ba8aa8fad37dc8","addresses":["bc1qg2je62um44n9u5mmrucqfw524radxlwgttfu5r"],"isAddress":true},{"value":"15963092","n":1,"hex":"0014f67d2a70e3b69d9a3e4a2558834e6001e4ec78d0","addresses":["bc1q7e7j5u8rk6we50j2y4vgxnnqq8jwc7xsssws7q"],"isAddress":true}],"blockHash":"000000000000000000001dfb165abcf0ddccd970422aa6ebd9b327861e75037c","blockHeight":895749,"confirmations":40,"blockTime":1746675876,"size":371,"vsize":209,"value":"82014583","valueIn":"82018560","fees":"3977","hex":"020000000001026e8412621269639cec697d23f3d35e4640c3c5cd208564d88dfafa4bed81cedb0100000000ffffffff2f2a63373f07e5d3e7566f5965d3fba4a1e0dc47d3c57855a126b1e4446c7a580100000000ffffffff02a3ddef030000000016001442a59d2b9bad665e537b1f3004ba8aa8fad37dc8d493f30000000000160014f67d2a70e3b69d9a3e4a2558834e6001e4ec78d00247304402207ec30f4021416823ae95862d14893410a00cc20960813660887e3154063ffb7f02201ad288e1e2280254d2e94e7c00906c27deb667bea5a14ab6cc02e971bb4322350121029c68256762a1ad3c86eeb839eb96f7c56d65b1ebc4b479d4a9bca4fd21b6338802483045022100ecfe98d811377b7d355eb44f7f0ac472f393029315bc824f0492e91f5ce28fb40220290471afa892fc837a3bd56aad21e4aa740d268f629b7218d7072791c9904c800121029c68256762a1ad3c86eeb839eb96f7c56d65b1ebc4b479d4a9bca4fd21b6338800000000"}
`),
			expected: []*model.Transaction{
				{
					TxHash:      "d164ac7edde4054e82be166a67bf8954a6d63ae98dc402fab9e2fd782c5e2d77",
					BlockNumber: 895749,
					ChainIndex:  0,
					FromAddress: "bc1q7e7j5u8rk6we50j2y4vgxnnqq8jwc7xsssws7q",
					ToAddress:   "bc1qg2je62um44n9u5mmrucqfw524radxlwgttfu5r",
					Value:       "66051491",
					Fee:         "3977",
					Status:      constant.TransactionStatusSuccess,
					Timestamp:   1746675876,
				},
			},
		},
		{
			name: "多个input, 1个output",
			input: json.RawMessage(`{"txid":"6cd5b77dbf045551c0a1a774c6df45b51f4ad04ee704340c34e6fc52ed78e079","version":1,"vin":[{"txid":"2d1682555eb3a9f8cffa371b16600e2fe931b0b4ab47842246b107da04cd8642","vout":1,"sequence":4294967293,"n":0,"addresses":["1Fih1kXYYB7yXUThkD6R9iv967ujvr5V6a"],"isAddress":true,"value":"17656","hex":"4830450221009b642bb82178f166d0e916a8506edd85ad8308aa72d35081658814b4836aabb9022052a6846bc4302431e91ac41c9019d4fe56b6314ca76ecb8f2633e100be5de7f3012103077cc167f583faf5e79f6475048903b72126becc844c67322c1654dc0ca621fc"},{"txid":"9a4e79f6e73d91cd5659fa701887bbe359d9b815a238a2ff0e76016b761136b1","vout":1,"sequence":4294967293,"n":1,"addresses":["1Q145wDNRet4KysyDcmfNdPYEK2ZnarHXn"],"isAddress":true,"value":"290113","hex":"4830450221009d096f2977a1d2137aa43b878cd017182cc6ae48f148e7147ad01de72d000bb602204002a950ef02ad22c02070be0239646d991367ed36b70e380a46ac6ce1ac6b17012103cd1948c4fe7ceef1af2951f0144767017c8ac41de2bb77905b568d2baad54e0c"}],"vout":[{"value":"301408","n":0,"spent":true,"hex":"76a9148f84097bc3ee73e02853cd2f856b744cfd86e01f88ac","addresses":["1E5qoXXzDXCNYKzhuJxR1syfkKvLY1zJ8q"],"isAddress":true}],"blockHash":"000000000000000000001dfb165abcf0ddccd970422aa6ebd9b327861e75037c","blockHeight":895749,"confirmations":42,"blockTime":1746675876,"size":340,"vsize":340,"value":"301408","valueIn":"307769","fees":"6361","hex":"01000000024286cd04da07b146228447abb4b031e92f0e60161b37facff8a9b35e5582162d010000006b4830450221009b642bb82178f166d0e916a8506edd85ad8308aa72d35081658814b4836aabb9022052a6846bc4302431e91ac41c9019d4fe56b6314ca76ecb8f2633e100be5de7f3012103077cc167f583faf5e79f6475048903b72126becc844c67322c1654dc0ca621fcfdffffffb13611766b01760effa238a215b8d959e3bb871870fa5956cd913de7f6794e9a010000006b4830450221009d096f2977a1d2137aa43b878cd017182cc6ae48f148e7147ad01de72d000bb602204002a950ef02ad22c02070be0239646d991367ed36b70e380a46ac6ce1ac6b17012103cd1948c4fe7ceef1af2951f0144767017c8ac41de2bb77905b568d2baad54e0cfdffffff0160990400000000001976a9148f84097bc3ee73e02853cd2f856b744cfd86e01f88ac00000000"}
`),
			expected: []*model.Transaction{
				{
					TxHash:      "6cd5b77dbf045551c0a1a774c6df45b51f4ad04ee704340c34e6fc52ed78e079",
					BlockNumber: 895749,
					ChainIndex:  0,
					FromAddress: "1Fih1kXYYB7yXUThkD6R9iv967ujvr5V6a",
					ToAddress:   "1E5qoXXzDXCNYKzhuJxR1syfkKvLY1zJ8q",
					Value:       "17656",
					Fee:         "6361",
					Status:      constant.TransactionStatusSuccess,
					Timestamp:   1746675876,
				},
				{
					TxHash:      "6cd5b77dbf045551c0a1a774c6df45b51f4ad04ee704340c34e6fc52ed78e079",
					BlockNumber: 895749,
					ChainIndex:  0,
					FromAddress: "1Q145wDNRet4KysyDcmfNdPYEK2ZnarHXn",
					ToAddress:   "1E5qoXXzDXCNYKzhuJxR1syfkKvLY1zJ8q",
					Value:       "290113",
					Fee:         "6361",
					Status:      constant.TransactionStatusSuccess,
					Timestamp:   1746675876,
				},
			},
		},
		{
			name: "1个input, 多个output（包含找零）",
			input: json.RawMessage(`{"txid":"bfceea7b8610846c0d7144d8b59656512a2d05990f2827b6d907db39713bf6c0","version":1,"vin":[{"txid":"5f9fd8627f939c78fe2c9c56cfca9b7571579e123571c2f975fe1d2bbd0a0572","sequence":4294967293,"n":0,"addresses":["******************************************"],"isAddress":true,"value":"152547340"}],"vout":[{"value":"152301398","n":0,"spent":true,"hex":"00141c6977423aa4b82a0d7f8496cdf3fc2f8b4f580c","addresses":["******************************************"],"isAddress":true},{"value":"138067","n":1,"spent":true,"hex":"001479270fe4fcc5167538bfc10fd1128234d3e7d738","addresses":["bc1q0ynsle8uc5t82w9lcy8azy5zxnf704ecz3ewfn"],"isAddress":true},{"value":"105875","n":2,"hex":"a9148710f956a9da93cc748953c89eaaabb6d62ef82a87","addresses":["**********************************"],"isAddress":true}],"blockHash":"000000000000000000001dfb165abcf0ddccd970422aa6ebd9b327861e75037c","blockHeight":895749,"confirmations":45,"blockTime":1746675876,"size":254,"vsize":173,"value":"152545340","valueIn":"152547340","fees":"2000","hex":"0100000000010172050abd2b1dfe75f9c27135129e5771759bcacf569c2cfe789c937f62d89f5f0000000000fdffffff0356ef1309000000001600141c6977423aa4b82a0d7f8496cdf3fc2f8b4f580c531b02000000000016001479270fe4fcc5167538bfc10fd1128234d3e7d738939d01000000000017a9148710f956a9da93cc748953c89eaaabb6d62ef82a87024730440220478dae1b88d2223101322705f4624ccd6165b235052606bf979e37812cc7550b02206bf09a67a2d885f86158ee7f01bbd9dd5a10c9544f89ea841e10f2601f71b9dd012102084ad9ff2a070ef71f32375ff91e5f98448afc62bfb5934c3c15b4348cf11df700000000"}
`),
			expected: []*model.Transaction{
				{
					TxHash:      "bfceea7b8610846c0d7144d8b59656512a2d05990f2827b6d907db39713bf6c0",
					BlockNumber: 895749,
					ChainIndex:  0,
					FromAddress: "******************************************",
					ToAddress:   "bc1q0ynsle8uc5t82w9lcy8azy5zxnf704ecz3ewfn",
					Value:       "138067",
					Fee:         "2000",
					Status:      constant.TransactionStatusSuccess,
					Timestamp:   1746675876,
				},
				{
					TxHash:      "bfceea7b8610846c0d7144d8b59656512a2d05990f2827b6d907db39713bf6c0",
					BlockNumber: 895749,
					ChainIndex:  0,
					FromAddress: "******************************************",
					ToAddress:   "**********************************",
					Value:       "105875",
					Fee:         "2000",
					Status:      constant.TransactionStatusSuccess,
					Timestamp:   1746675876,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var tx Tx
			err := json.Unmarshal(tt.input, &tx)
			assert.NoError(t, err)
			result := btcTxToDb(tx)
			assert.Equal(t, tt.expected, result)
		})
	}
}
