package bitcoin

import (
	"byd_wallet/common/jsonrpc"
	"context"
	"fmt"
)

type QuickNodeClient struct {
	*jsonrpc.Client
}

func NewQuickNodeClient(endpoint string) *QuickNodeClient {
	return &QuickNodeClient{jsonrpc.NewClient(endpoint)}
}

func (c QuickNodeClient) GetUTXOs(ctx context.Context, address string, confirmed bool) ([]UTXO, error) {
	var utxos []UTXO
	if err := c.<PERSON>or(ctx, &utxos, "bb_getUTXOs", []any{address, map[string]interface{}{"confirmed": confirmed}}); err != nil {
		return nil, err
	}
	return utxos, nil
}

func (c QuickNodeClient) GetBlockCount(ctx context.Context) (int64, error) {
	var height int64
	if err := c.CallFor(ctx, &height, "getblockcount", nil); err != nil {
		return 0, err
	}
	return height, nil
}

// GetTxsByHeight retrieves all transactions for a given block height by paginating through available pages of transactions.
// It uses the GetBlockAndTxs method to fetch chain data and aggregates results across multiple pages if necessary.
// The method returns a slice of Tx objects representing the transactions in the block or an error if retrieval fails.
func (c QuickNodeClient) GetTxsByHeight(ctx context.Context, height int64) ([]Tx, error) {
	page := int64(1)
	result, err := c.GetBlockAndTxs(ctx, height, page)
	if err != nil {
		return nil, err
	}
	txs := result.Txs
	for page < result.TotalPages {
		page++
		resp, err := c.GetBlockAndTxs(ctx, height, page)
		if err != nil {
			return nil, err
		}
		txs = append(txs, resp.Txs...)
	}
	return txs, nil
}

// GetBlockAndTxs retrieves block information and transactions for a given block height and page number.
// It returns a structured response containing pagination details, block metadata, and chain data.
// The method calls the "bb_getBlock" API endpoint with the provided height and page parameters.
// If the API call or JSON unmarshalling fails, it returns an error.
func (c QuickNodeClient) GetBlockAndTxs(ctx context.Context, height, page int64) (*GetBlockAndTxsResult, error) {
	var result GetBlockAndTxsResult
	err := c.CallFor(ctx, &result, "bb_getBlock", []any{fmt.Sprintf("%d", height), map[string]any{
		"page": page,
	}})
	if err != nil {
		return nil, err
	}
	return &result, nil
}
