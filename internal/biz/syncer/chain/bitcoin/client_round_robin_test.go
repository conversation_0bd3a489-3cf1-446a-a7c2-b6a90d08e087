package bitcoin

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestRoundRobinClient(t *testing.T) {
	endpoints := []string{
		"https://lively-summer-vineyard.btc.quiknode.pro/a9bcad71a92e5d0ee35e34b862e196a5bae732b7",
		"https://still-soft-patina.btc.quiknode.pro/83d9eda3f3e272d94c4bf15b8e8dd51c439f607e",
	}
	cli := NewRoundRobinClient(endpoints)
	assert.Equal(t, endpoints[1], cli.Last().URL)
	c1 := cli.Next()
	c2 := cli.Next()
	c3 := cli.Next()
	assert.Equal(t, c1, c3)
	assert.NotEqual(t, c1, c2)
}
