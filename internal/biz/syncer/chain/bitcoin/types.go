package bitcoin

import (
	"github.com/shopspring/decimal"
)

// GetBlockAndTxsResult represents the response from the bb_getblock API call.
// It includes pagination details, block metadata, and chain information.
type GetBlockAndTxsResult struct {
	// Current page number in the paginated response.
	Page int64 `json:"page"`
	// Total number of pages available.
	TotalPages int64 `json:"totalPages"`
	// Number of items (transactions) on the current page.
	ItemsOnPage int64 `json:"itemsOnPage"`
	// The hash of the block.
	Hash string `json:"hash"`
	// The hash of the previous block in the blockchain.
	PreviousBlockHash string `json:"previousBlockHash"`
	// The hash of the next block in the blockchain (if available).
	NextBlockHash string `json:"nextBlockHash"`
	// The height of the block in the blockchain.
	Height int64 `json:"height"`
	// Number of confirmations for the block.
	Confirmations int64 `json:"confirmations"`
	// Size of the block in bytes.
	Size int64 `json:"size"`
	// Timestamp of the block.
	Time int64 `json:"time"`
	// Block version.
	Version int64 `json:"version"`
	// The Merkle root of the transactions in the block.
	MerkleRoot string `json:"merkleRoot"`
	// The nonce used in the block header.
	Nonce string `json:"nonce"`
	// The difficulty target as a hexadecimal string.
	Bits string `json:"bits"`
	// The difficulty of the block as a decimal number.
	Difficulty string `json:"difficulty"`
	// Total number of transactions in the block.
	TxCount int64 `json:"txCount"`
	// List of transactions included in the block.
	Txs []Tx `json:"txs"`
}

// Tx represents a Bitcoin chain included in a block.
type Tx struct {
	// The chain ID (hash).
	Txid string `json:"txid"`
	// List of inputs for the chain.
	Vin []Vin `json:"vin"`
	// List of outputs for the chain.
	Vout []Vout `json:"vout"`
	// The hash of the block containing this chain.
	BlockHash string `json:"blockHash"`
	// The height of the block containing this chain.
	BlockHeight int64 `json:"blockHeight"`
	// Number of confirmations for the chain.
	Confirmations int64 `json:"confirmations"`
	// Timestamp of the block containing this chain.
	BlockTime int64 `json:"blockTime"`
	// Total output value of the chain.
	Value string `json:"value"`
	// Total input value of the chain.
	ValueIn string `json:"valueIn"`
	// Transaction fees paid.
	Fees string `json:"fees"`
}

// Vin represents an input in a Bitcoin chain.
type Vin struct {
	// Index of the input in the chain.
	N int64 `json:"n"`
	// Indicates whether the input is associated with an address.
	IsAddress bool `json:"isAddress"`
	// Value of the input.
	Value string `json:"value"`
	// List of addresses associated with the input.
	Addresses []string `json:"addresses"`
}

// Vout represents an output in a Bitcoin chain.
type Vout struct {
	// Value of the output.
	Value string `json:"value"`
	// Index of the output in the chain.
	N int64 `json:"n"`
	// List of addresses associated with the output.
	Addresses []string `json:"addresses"`
	// Indicates whether the output is associated with an address.
	IsAddress bool `json:"isAddress"`
}

type UTXO struct {
	// The chain ID (hash) of the UTXO.
	Txid string `json:"txid"`
	// The index of the output in the chain. // 0:in, 1:out
	Vout int64 `json:"vout"`
	// The address associated with the UTXO.
	Address string `json:"address"`
	// The amount of Bitcoin in the UTXO.
	Value string `json:"value"`
	// The number of block confirmations since the output was created
	Confirmations int64 `json:"confirmations"`
	// The block height at which the chain output was created
	Height int64 `json:"height"`
	// Whether the UTXO is a Coinbase chain. Coinbase UTXOs have this field set to true,
	// however, due to performance reasons,
	// it will return only up to the minimum Coinbase confirmations limit (100).
	// After this limit, UTXOs are not detected as Coinbase.
	Coinbase bool `json:"coinbase"`
}

type UTXOs []UTXO

func (utxos UTXOs) GetBalance() decimal.Decimal {
	balance := decimal.NewFromInt(0)
	for _, u := range utxos {
		if u.Confirmations < 1 {
			continue
		}
		switch u.Vout {
		case 0:
			v, _ := decimal.NewFromString(u.Value)
			balance = balance.Add(v)
		case 1:
			v, _ := decimal.NewFromString(u.Value)
			balance = balance.Sub(v)
		}
	}
	return balance
}
