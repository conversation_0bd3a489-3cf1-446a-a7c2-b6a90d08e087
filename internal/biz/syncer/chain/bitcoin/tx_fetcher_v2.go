package bitcoin

import (
	"byd_wallet/model"
	"context"
	"fmt"

	"github.com/go-kratos/kratos/v2/log"
)

type BlockFetcher struct {
	log               *log.Helper
	cli               *RoundRobinClient
	blockchainNetWork *model.BlockchainNetwork
}

func NewBlockFetcher(logger log.Logger) *BlockFetcher {
	return &BlockFetcher{
		log: log.NewHelper(logger),
	}
}

func (b *BlockFetcher) BlockchainNetWork() *model.BlockchainNetwork {
	return b.blockchainNetWork
}

func (b *BlockFetcher) SetBlockchainNetWork(blockchainNetWork *model.BlockchainNetwork) {
	b.blockchainNetWork = blockchainNetWork
}

func (b *BlockFetcher) SetEndpoints(endpoints []string) error {
	if len(endpoints) == 0 {
		return nil
	}
	b.cli = NewRoundRobinClient(endpoints)
	return nil
}

func (b *BlockFetcher) Close() error {
	return nil
}

func (b *BlockFetcher) FetchTransactions(ctx context.Context, height int64) (<-chan *model.Transaction, error) {
	txChan := make(chan *model.Transaction)

	cli := b.cli.Next()
	txs, err := cli.GetTxsByHeight(ctx, height)
	if err != nil {
		return nil, fmt.Errorf("failed to get transactions for block %d: %w", height, err)
	}
	dbTxs := btcTxsToDb(txs)

	go func() {
		defer close(txChan)

		for _, tx := range dbTxs {
			select {
			case txChan <- tx:
			case <-ctx.Done():
				b.log.Infow(log.DefaultMessageKey, "FetchTransactions: context cancelled", "blockHeight", height)
				return
			}
		}
	}()

	return txChan, nil
}

func (b *BlockFetcher) GetLatestBlockHeight(ctx context.Context) (int64, error) {
	cli := b.cli.Last()
	return cli.GetBlockCount(ctx)
}

func (b *BlockFetcher) GetDebugTraceTransaction(txn string) ([]*model.Transaction, error) {
	// TODO
	return nil, nil
}
