package evm

// import (
// 	taskCommon "byd_wallet/common"
// 	"byd_wallet/internal/biz/syncer/chain/evm/chain"
// 	"byd_wallet/internal/conf"
// 	"byd_wallet/internal/data"
// 	"byd_wallet/model"
// 	"context"
// 	"fmt"
// 	"github.com/go-kratos/kratos/v2/config"
// 	"github.com/go-kratos/kratos/v2/config/file"
// 	"github.com/go-kratos/kratos/v2/log"
// 	"github.com/go-kratos/kratos/v2/middleware/tracing"
// 	"gorm.io/gorm/clause"
// 	"os"
// 	"testing"
// )

// func TestTransActionToDb(t *testing.T) {
// 	logger := log.With(log.NewStdLogger(os.Stdout),
// 		"ts", log.DefaultTimestamp,
// 		"caller", log.DefaultCaller,
// 		"service.id", "test",
// 		"service.name", "test",
// 		"service.version", "1.0.0",
// 		"trace.id", tracing.TraceID(),
// 		"span.id", tracing.SpanID(),
// 	)
// 	c := config.New(
// 		config.WithSource(
// 			file.NewSource("./../../../configs/bydwallettask"),
// 		),
// 	)
// 	rpcList := []string{"https://bsc.blockpi.network/v1/rpc/1e83bf7dbbaef8063753a717aaad753cbbeda1de"}
// 	defer c.Close()

// 	if err := c.Load(); err != nil {
// 		panic(err)
// 	}

// 	var bc conf.Bootstrap
// 	if err := c.Scan(&bc); err != nil {
// 		panic(err)
// 	}

// 	rd, cleanup2, err := data.NewRedisClient(bc.Data)
// 	if err != nil {
// 		cleanup2()
// 		t.Fatal(err)
// 	}
// 	chainIndex := int64(20000714)
// 	base, cf, err := NewTransactionsFetcher(logger, chainIndex, rpcList)
// 	if err != nil {

// 		t.Fatal(err)
// 	}
// 	if cf == nil {
// 		cf = func() {}
// 	}
// 	blockchain, err := chain.NewEvmChain(chainIndex, rpcList...)
// 	if err != nil {
// 		t.Fatal(err)
// 	}
// 	txList := base.processBlockRangeConcurrently(context.Background(), blockchain, 50392278, 50392278, 10, 10)
// 	//t.Log("txList = ", txList)
// 	list := TransActionToDb(txList, chainIndex)
// 	trackedKey := taskCommon.GetTrackedAddress(chainIndex)
// 	transactions := make([]*model.Transaction, 0, len(list))
// 	var addresses []string
// 	for _, tx := range list {
// 		if tx == nil {
// 			continue
// 		}
// 		if tx.FromAddress != "" {
// 			addresses = append(addresses, tx.FromAddress)
// 		}
// 		if tx.ToAddress != "" && tx.ToAddress != tx.FromAddress {
// 			addresses = append(addresses, tx.ToAddress)
// 		}
// 	}
// 	existMap := make(map[string]bool)
// 	if len(addresses) > 0 {
// 		results, _ := rd.SMIsMember(context.Background(), trackedKey, addresses).Result()
// 		for i, addr := range addresses {
// 			existMap[addr] = results[i]
// 		}
// 	}
// 	for _, tx := range list {
// 		if tx == nil {
// 			continue
// 		}
// 		if existMap[tx.FromAddress] || existMap[tx.ToAddress] {
// 			transactions = append(transactions, tx)
// 		}
// 	}
// 	fmt.Println("transactions = ", transactions)
// 	db, _, err := data.NewGormDB(bc.Data)
// 	if err != nil {
// 		t.Fatal(err)
// 	}
// 	err = db.Table("transactions_binance").Clauses(clause.OnConflict{
// 		Columns:   []clause.Column{{Name: "tx_hash"}, {Name: "from_address"}, {Name: "to_address"}, {Name: "program_id"}},
// 		DoNothing: true,
// 	}).CreateInBatches(transactions, batchSize).Error
// 	if err != nil {
// 		t.Fatal(err)
// 	}

// }

// func TestAddressToRedis(t *testing.T) {
// 	c := config.New(
// 		config.WithSource(
// 			file.NewSource("./../../../configs/bydwallettask"),
// 		),
// 	)
// 	defer c.Close()

// 	if err := c.Load(); err != nil {
// 		panic(err)
// 	}

// 	var bc conf.Bootstrap
// 	if err := c.Scan(&bc); err != nil {
// 		panic(err)
// 	}
// 	db, _, err := data.NewGormDB(bc.Data)
// 	if err != nil {
// 		t.Fatal(err)
// 	}
// 	rd, cleanup2, err := data.NewRedisClient(bc.Data)
// 	if err != nil {
// 		cleanup2()
// 		t.Fatal(err)
// 	}
// 	var adds []*model.UserAddress
// 	db.Model(model.UserAddress{}).Find(&adds)
// 	for _, add := range adds {
// 		trackedKey := taskCommon.GetTrackedAddress(add.ChainIndex)
// 		rd.SAdd(context.Background(), trackedKey, add.Address)
// 	}
// }
