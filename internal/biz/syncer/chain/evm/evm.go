package evm

import (
	"byd_wallet/common/constant"
	chain2 "byd_wallet/internal/biz/syncer/chain/evm/chain"
	"byd_wallet/model"
	"byd_wallet/utils"
	"context"
	"fmt"
	"math/big"
	"strings"
	"time"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
)

func (t *TransactionsFetcher) GetLastBlockNumber(ctx context.Context, chainIndex int64, rpcs []string) (int64, error) {
	blockchain, err := chain2.NewEvmChain(chainIndex, rpcs...)
	if err != nil {
		t.log.Errorw(log.DefaultMessageKey, "initBlockchain error", "err", err)
		return 0, err
	}

	latestBlock, err := blockchain.Client.BlockNumber(ctx)
	if err != nil {
		t.log.Errorw(log.DefaultMessageKey, "get latest block error", "err", err)
		return 0, err
	}
	return int64(latestBlock), nil
}

func (t *TransactionsFetcher) GetBlockTransactions(blockNumber int64) ([]chain2.EvmTransaction, error) {
	blockchain, err := t.GetEvmClient()
	if err != nil {
		return nil, err
	}
	block, receipts, err := t.fetchBlockWithReceipts(context.Background(), blockchain, blockNumber)
	if err != nil {
		return nil, err
	}

	if block == nil {
		return nil, nil
	}

	if len(receipts) != len(block.Transactions()) {
		return nil, err
	}
	var transactions []chain2.EvmTransaction
	for txIndex, tx := range block.Transactions() {
		if txIndex >= len(receipts) {
			t.log.Warnw(log.DefaultMessageKey, "txIndex", txIndex, "receiptsLen", len(receipts))
			continue
		}

		evmTx, err := t.extractTransactionWithReceipt(blockchain, block, tx, receipts[txIndex])
		if err != nil {
			t.log.Errorw(log.DefaultMessageKey, "extractTransaction error", "err", err, "chainIndex", blockchain.ChainIndex, "tx", tx.Hash().Hex())
			continue
		}
		transactions = append(transactions, evmTx)
	}
	return transactions, nil
}

func (t *TransactionsFetcher) fetchBlockWithReceipts(ctx context.Context, blockchain *chain2.EvmChain, blockNum int64) (*types.Block, []*types.Receipt, error) {
	var (
		block    *types.Block
		receipts []*types.Receipt
		err      error
	)

	for retry := 0; retry < 3; retry++ {
		block, err = blockchain.Client.BlockByNumber(ctx, big.NewInt(blockNum))
		if err != nil {
			// 忽略空块错误
			if utils.ShouldIgnoreBlockError(err) {
				return nil, nil, nil
			}
			time.Sleep(time.Second * time.Duration(retry+1))
			continue
		}

		receipts, err = blockchain.BlockReceipts(ctx, big.NewInt(blockNum))
		if err == nil {
			return block, receipts, nil
		}

		time.Sleep(time.Second * time.Duration(retry+1))
	}

	return nil, nil, err
}

func (t *TransactionsFetcher) handleBlockWithReceipts(
	ctx context.Context,
	blockchain *chain2.EvmChain,
	block *types.Block,
	receipts []*types.Receipt,
	txCh chan<- chain2.EvmTransaction,
) {
	for txIndex, tx := range block.Transactions() {
		if txIndex >= len(receipts) {
			t.log.Warnw(log.DefaultMessageKey, "txIndex", txIndex, "receiptsLen", len(receipts))
			continue
		}

		evmTx, err := t.extractTransactionWithReceipt(blockchain, block, tx, receipts[txIndex])
		if err != nil {
			t.log.Errorw(log.DefaultMessageKey, "extractTransaction error", "err", err, "chainIndex", blockchain.ChainIndex, "tx", tx.Hash().Hex())
			continue
		}
		txCh <- evmTx
	}
}

func (t *TransactionsFetcher) extractTransactionWithReceipt(
	blockchain *chain2.EvmChain,
	block *types.Block,
	tx *types.Transaction,
	receipt *types.Receipt,
) (chain2.EvmTransaction, error) {
	evmTx := chain2.EvmTransaction{
		Txn:         tx.Hash().Hex(),
		Value:       decimal.NewFromBigInt(tx.Value(), 0),
		GasFee:      decimal.NewFromInt(int64(tx.Gas())),
		GasLimit:    decimal.NewFromBigInt(tx.GasPrice(), 0),
		Nonce:       cast.ToInt64(tx.Nonce()),
		BlockNumber: block.Number().Int64(),
		Timestamp:   block.Time(),
		Status:      receipt.Status,
	}
	from, err := t.getTxSender(tx)
	if err != nil {
		return evmTx, err
	}
	if tx.To() != nil {
		evmTx.To = tx.To().Hex()
	} else {
		evmTx.Method = constant.TxMethodCreated
		contractAddr := crypto.CreateAddress(from, tx.Nonce())
		evmTx.To = contractAddr.Hex()
	}
	evmTx.From = from.Hex()
	evmTx.ActualGasFee = t.ActualFee(tx, receipt, block.BaseFee())
	evmTx.Logs = make([]chain2.EvmLog, 0, len(receipt.Logs))
	for _, log := range receipt.Logs {
		topics := make([]string, len(log.Topics))
		for i, topic := range log.Topics {
			topics[i] = topic.Hex()
		}

		evmTx.Logs = append(evmTx.Logs, chain2.EvmLog{
			Index:       log.Index,
			Address:     log.Address.Hex(),
			Topics:      topics,
			Data:        common.Bytes2Hex(log.Data),
			Removed:     log.Removed,
			BlockNumber: block.Number().Int64(),
			TxHash:      tx.Hash().Hex(),
		})
	}

	return evmTx, nil
}

func (t *TransactionsFetcher) getTxSender(tx *types.Transaction) (common.Address, error) {
	chainId := tx.ChainId()
	if chainId == nil || chainId.Sign() <= 0 {
		return types.Sender(types.HomesteadSigner{}, tx)
	}

	signer := types.NewLondonSigner(chainId)
	from, err := types.Sender(signer, tx)
	if err == nil {
		return from, nil
	}

	return types.Sender(types.LatestSignerForChainID(chainId), tx)
}

func (t *TransactionsFetcher) ActualFee(tx *types.Transaction, receipt *types.Receipt, baseFee *big.Int) decimal.Decimal {
	var actualFee *big.Int
	switch tx.Type() {
	case types.LegacyTxType, types.AccessListTxType:
		actualFee = new(big.Int).Mul(tx.GasPrice(), new(big.Int).SetUint64(receipt.GasUsed))
	case types.DynamicFeeTxType:
		effectiveGasPrice := new(big.Int).Add(baseFee, tx.GasTipCap())
		if effectiveGasPrice.Cmp(tx.GasFeeCap()) > 0 {
			effectiveGasPrice = tx.GasFeeCap()
		}
		actualFee = new(big.Int).Mul(effectiveGasPrice, new(big.Int).SetUint64(receipt.GasUsed))
	default:
		actualFee = new(big.Int).Mul(tx.GasPrice(), new(big.Int).SetUint64(receipt.GasUsed))

	}
	return decimal.NewFromBigInt(actualFee, 0)
}

func (t *TransactionsFetcher) GetEvmClient() (*chain2.EvmChain, error) {
	blockchain, err := chain2.NewEvmChain(t.chainIndex, t.rpcList...)
	if err != nil {
		t.log.Errorw(log.DefaultMessageKey, "initBlockchain error", "err", err)
		return nil, err
	}
	return blockchain, nil
}

// TraceConfig 是 debug_traceTransaction 的配置
type TraceConfig struct {
	Tracer string `json:"tracer"`
}

// CallTrace 表示调用跟踪结果
type CallTrace struct {
	Type   string      `json:"type,omitempty"`
	From   string      `json:"from,omitempty"`
	To     string      `json:"to,omitempty"`
	Value  string      `json:"value,omitempty"`
	Input  string      `json:"input,omitempty"`
	Output string      `json:"output,omitempty"`
	Calls  []CallTrace `json:"calls,omitempty"`
}

func (t *TransactionsFetcher) GetInternalTransactions(txn string) ([]*model.Transaction, error) {
	txHash := common.HexToHash(txn)
	blockchain, err := chain2.NewEvmChain(t.chainIndex, t.rpcList...)
	if err != nil {
		t.log.Errorw(log.DefaultMessageKey, "initBlockchain error", "err", err)
		return nil, err
	}
	var result CallTrace
	err = blockchain.Client.Client().CallContext(context.Background(), &result, "debug_traceTransaction", txHash, TraceConfig{
		Tracer: "callTracer",
	})
	if err != nil {
		fmt.Println("callTracer error", "err", err)
		return nil, err
	}
	return extractInternalTxs(&result), nil
}

func (t *TransactionsFetcher) GetTransactionByHash(txn string) (*types.Transaction, bool, error) {
	txHash := common.HexToHash(txn)
	blockchain, err := chain2.NewEvmChain(t.chainIndex, t.rpcList...)
	if err != nil {
		t.log.Errorw(log.DefaultMessageKey, "get block chain ", "err", err)
		return nil, false, err
	}
	return blockchain.Client.TransactionByHash(context.Background(), txHash)
}

func (t *TransactionsFetcher) GetTransactionReceipt(txn string) (*types.Receipt, error) {
	txHash := common.HexToHash(txn)
	blockchain, err := chain2.NewEvmChain(t.chainIndex, t.rpcList...)
	if err != nil {
		t.log.Errorw(log.DefaultMessageKey, "get block chain ", "err", err)
		return nil, err
	}
	return blockchain.Client.TransactionReceipt(context.Background(), txHash)
}

// InternalTx 表示内部交易结构
type InternalTx struct {
	Type   string `json:"type"`
	From   string `json:"from"`
	To     string `json:"to"`
	Value  string `json:"value"`
	Input  string `json:"input"`
	Output string `json:"output"`
}

// extractInternalTxs 从调用跟踪中提取内部交易
func extractInternalTxs(trace *CallTrace) []*model.Transaction {
	var internalTxs []*model.Transaction

	var traverse func(node *CallTrace)
	traverse = func(node *CallTrace) {
		for _, call := range node.Calls {
			// 忽略无转账交易
			if call.Value == "" || call.Value == "0x" || call.Value == "0x0" {
				traverse(&call)
				continue
			}

			// 将 Value 从十六进制转为 *big.Int
			bigVal := new(big.Int)
			_, ok := bigVal.SetString(strings.TrimPrefix(call.Value, "0x"), 16)
			if !ok {
				// 无法解析的 value，跳过
				traverse(&call)
				continue
			}
			if call.Type == constant.TxMethodDELEGATECALL {
				traverse(&call)
				continue
			}
			internalTxs = append(internalTxs, &model.Transaction{
				Method:      call.Type,
				FromAddress: common.HexToAddress(call.From).Hex(),
				ToAddress:   common.HexToAddress(call.To).Hex(),
				Value:       bigVal.String(),
			})

			traverse(&call)
		}
	}

	traverse(trace)
	return internalTxs
}
