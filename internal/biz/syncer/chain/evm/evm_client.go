package evm

import (
	"byd_wallet/model"
	"fmt"
	"github.com/ethereum/go-ethereum/ethclient"
	"sync"
)

func NewMultiChainClient(endpoints []*model.RPCEndpoint) (*MultiChainClient, error) {
	r := &MultiChainClient{clients: make(map[int64]*roundRobinClient)}
	for _, endpoint := range endpoints {
		cli, err := ethclient.Dial(endpoint.URL)
		if err != nil {
			return nil, err
		}
		rbCli, ok := r.clients[endpoint.ChainIndex]
		if !ok {
			rbCli = newRoundRobinClient()
		}
		rbCli.addClient(cli)
		r.clients[endpoint.ChainIndex] = rbCli
	}
	return r, nil
}

type MultiChainClient struct {
	clients map[int64]*roundRobinClient
}

func (m MultiChainClient) Close() {
	for _, client := range m.clients {
		client.Close()
	}
}

func (m MultiChainClient) Select(chainIndex int64) (*ethclient.Client, error) {
	cli, ok := m.clients[chainIndex]
	if !ok {
		return nil, fmt.Errorf("chain %d not exist", chainIndex)
	}
	return cli.Next(), nil
}

func newRoundRobinClient() *roundRobinClient {
	return &roundRobinClient{}
}

type roundRobinClient struct {
	clients  []*ethclient.Client
	index    int
	mu       sync.Mutex
	cleanups []func()
}

func (r *roundRobinClient) addClient(client *ethclient.Client) {
	r.clients = append(r.clients, client)
	r.cleanups = append(r.cleanups, client.Close)
}

func (r *roundRobinClient) Next() *ethclient.Client {
	r.mu.Lock()
	defer r.mu.Unlock()
	item := r.clients[r.index]
	r.index = (r.index + 1) % len(r.clients) // 索引循环递增
	return item
}

func (r *roundRobinClient) Close() {
	for _, cleanup := range r.cleanups {
		cleanup()
	}
}
