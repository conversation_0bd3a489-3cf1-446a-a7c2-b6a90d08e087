package evm

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz/syncer/chain/evm/chain"
	"byd_wallet/model"
	"context"
	"strings"
	"sync"

	"github.com/go-kratos/kratos/v2/log"
)

type TransactionsFetcher struct {
	log *log.Helper

	chainIndex int64
	rpcList    []string
}

func NewTransactionsFetcher(logger *log.Helper, chainIndex int64, rpcList []string) (*TransactionsFetcher, func(), error) {
	return &TransactionsFetcher{
		log:        logger,
		chainIndex: chainIndex,
		rpcList:    rpcList,
	}, nil, nil
}

func (t *TransactionsFetcher) FetchTransactionsByBlockNumber(ctx context.Context, blockNumber int64) ([]*model.Transaction, error) {
	txList, err := t.GetBlockTransactions(blockNumber)
	if err != nil {
		return nil, err
	}
	if len(txList) == 0 {
		return nil, nil
	}
	list := t.TransActionToDb(txList, t.chainIndex)
	return list, nil
}

func (t *TransactionsFetcher) TransActionToDb(txs []chain.EvmTransaction, chainIndex int64) []*model.Transaction {
	var (
		wg       sync.WaitGroup
		resultCh = make(chan *model.Transaction, len(txs)*20) // 预留充足 buffer
	)

	for _, tx := range txs {
		txCopy := tx // 拷贝防止闭包问题
		wg.Add(1)

		go func(txn chain.EvmTransaction) {
			defer wg.Done()
			// 1. 处理内部交易 暂时停止使用
			//if t.chainIndex == constant.BscChainIndex {
			//	if internalTxs, err := t.GetInternalTransactions(txn.Txn); err == nil {
			//		for i := range internalTxs {
			//			internalTx := internalTxs[i] // 避免拷贝
			//			internalTx.TxHash = txn.Txn
			//			internalTx.ChainIndex = chainIndex
			//			internalTx.Timestamp = int64(txn.Timestamp)
			//			internalTx.Fee = txn.ActualGasFee.String()
			//			internalTx.BlockNumber = txn.BlockNumber
			//			internalTx.Status = constant.TransactionStatusSuccess
			//			// 只有单个内部交易时才继承FromAddress
			//			if internalTx.FromAddress != txn.From || internalTx.ToAddress != txn.From {
			//				internalTx.FromAddress = txn.From
			//			}
			//			resultCh <- internalTx
			//		}
			//	}
			//}

			// 2. 构建基本交易结构
			base := model.Transaction{
				TxHash:      txn.Txn,
				FromAddress: txn.From,
				ToAddress:   txn.To,
				Fee:         txn.ActualGasFee.String(),
				BlockNumber: txn.BlockNumber,
				Value:       txn.Value.String(),
				Method:      txn.Method,
				ChainIndex:  chainIndex,
				Status:      t.processStatus(txn.Status),
				Timestamp:   int64(txn.Timestamp),
			}

			// 3. 合约创建
			if base.Method == constant.TxMethodCreated {
				base.ProgramID = txn.To
				resultCh <- &base
				return
			}

			// 4. 普通转账
			if txn.Logs == nil || len(txn.Logs) == 0 {
				base.Method = constant.TxMethodTransfer
				resultCh <- &base
				return
			}

			// 5. 分析 logs
			logProcessed := false
			for _, vLog := range txn.Logs {
				txCopy := base // 拷贝，防止覆盖
				txCopy.ProgramID = vLog.Address
				if updated, err := t.processLog(&txCopy, vLog); err == nil && updated != nil {
					if updated.Method == constant.TxMethodSwap {
						t.ProcessSwap(&txCopy, resultCh)
					} else {
						resultCh <- updated
					}
					logProcessed = true
				}
			}
			if !logProcessed {
				resultCh <- &base
			}
		}(txCopy)
	}

	// 等待所有处理完成并关闭 channel
	go func() {
		wg.Wait()
		close(resultCh)
	}()

	// 收集结果
	var transactions []*model.Transaction
	for tx := range resultCh {
		transactions = append(transactions, tx)
	}

	return transactions
}

func (t *TransactionsFetcher) ProcessSwap(transaction *model.Transaction, resultCh chan<- *model.Transaction) {
	if t.chainIndex == constant.BscChainIndex {
		return
	}
	// 1. 处理内部交易
	if internalTxs, err := t.GetInternalTransactions(transaction.TxHash); err == nil {
		for i := range internalTxs {
			internalTx := internalTxs[i] // 避免拷贝
			internalTx.TxHash = transaction.TxHash
			internalTx.ChainIndex = transaction.ChainIndex
			internalTx.Timestamp = int64(transaction.Timestamp)
			internalTx.Fee = transaction.Fee
			internalTx.BlockNumber = transaction.BlockNumber
			internalTx.Status = constant.TransactionStatusSuccess
			if internalTx.Method == constant.TxMethodDELEGATECALL {
				continue
			}
			// 只有单个内部交易时才继承FromAddress
			if len(internalTxs) > 0 {
				lastTx := internalTxs[len(internalTxs)-1]
				if !strings.Contains(lastTx.FromAddress, transaction.FromAddress) &&
					!strings.Contains(lastTx.ToAddress, transaction.FromAddress) {
					internalTx.FromAddress = transaction.FromAddress
				}
			}
			resultCh <- internalTx

		}
	}

}
