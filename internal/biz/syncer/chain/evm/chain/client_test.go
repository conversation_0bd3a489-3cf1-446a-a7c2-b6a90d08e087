package chain

import (
	"context"
	"encoding/json"
	"fmt"
	"math/big"
	"testing"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/stretchr/testify/assert"
)

var rpcList = []string{"https://holy-practical-rain.ethereum-sepolia.quiknode.pro/c9414005b03b2712e975d4bae5077271592ed13a"}

var baseRpcList = []string{"https://go.getblock.io/390099ff55174bc08d09f2dc8090c3cd", "https://thrumming-evocative-surf.base-mainnet.quiknode.pro/3ea71d9095c718e3c5d95b058a1e4b1c6cc29202"}

func TestNewEvmChain(t *testing.T) {
	blockChain, err := NewEvmChain(60, rpcList...)
	if err != nil {
		t.Error(err)
	}
	blockNumber, err := blockChain.Client.BlockNumber(context.Background())
	if err != nil {
		t.Error(err)
	}
	t.Log(blockNumber)
}

func TestGetTransaction(t *testing.T) {
	// 初始化以太坊客户端
	blockChain, err := NewEvmChain(60, "https://holy-practical-rain.ethereum-sepolia.quiknode.pro/c9414005b03b2712e975d4bae5077271592ed13a")
	if err != nil {
		t.Fatalf("Failed to initialize EVM blockchain: %v", err)
	}

	// 获取特定区块
	blockNumber := big.NewInt(8225926)
	block, err := blockChain.Client.BlockByNumber(context.Background(), blockNumber)
	if err != nil {
		t.Fatalf("Failed to get block %d: %v", blockNumber, err)
	}

	fmt.Printf("Analyzing block %d (%s) with %d transactions\n",
		block.Number(), block.Hash().Hex(), len(block.Transactions()))

	// 遍历区块中的所有交易
	for i, tx := range block.Transactions() {
		fmt.Printf("\n=== Transaction %d ===\n", i+1)
		fmt.Println("Hash:", tx.Hash().Hex())
		fmt.Println("Value:", tx.Value().String(), "wei")
		fmt.Println("Gas Limit:", tx.Gas())

		// 根据交易类型处理不同的费用计算
		switch tx.Type() {
		case types.LegacyTxType:
			fmt.Println("Type: Legacy")
			fmt.Println("Gas Price:", tx.GasPrice().Uint64(), "wei")
			fee := new(big.Int).Mul(tx.GasPrice(), new(big.Int).SetUint64(tx.Gas()))
			fmt.Printf("Max Fee (Gas Price × Gas Limit): %s wei\n", fee.String())

		case types.AccessListTxType:
			fmt.Println("Type: AccessList (EIP-2930)")
			fmt.Println("Gas Price:", tx.GasPrice().Uint64(), "wei")
			fee := new(big.Int).Mul(tx.GasPrice(), new(big.Int).SetUint64(tx.Gas()))
			fmt.Printf("Max Fee (Gas Price × Gas Limit): %s wei\n", fee.String())

		case types.DynamicFeeTxType:
			fmt.Println("Type: DynamicFee (EIP-1559)")
			baseFee := block.BaseFee()
			if baseFee == nil {
				t.Fatal("Base fee not available for EIP-1559 chain")
			}

			fmt.Println("Base Fee:", baseFee.String(), "wei")
			fmt.Println("Max Priority Fee (Tip):", tx.GasTipCap().String(), "wei")
			fmt.Println("Max Fee Per Gas:", tx.GasFeeCap().String(), "wei")

			// 计算有效gas价格
			effectiveGasPrice := new(big.Int).Add(baseFee, tx.GasTipCap())
			if effectiveGasPrice.Cmp(tx.GasFeeCap()) > 0 {
				effectiveGasPrice = tx.GasFeeCap()
			}

			maxFee := new(big.Int).Mul(tx.GasFeeCap(), new(big.Int).SetUint64(tx.Gas()))
			estimatedFee := new(big.Int).Mul(effectiveGasPrice, new(big.Int).SetUint64(tx.Gas()))

			fmt.Println("tx.Gas() = ", tx.Gas())
			fmt.Printf("Effective Gas Price: %s wei\n", effectiveGasPrice.String())
			fmt.Printf("Max Possible Fee: %s wei\n", maxFee.String())
			fmt.Printf("Estimated Fee (Base + Tip): %s wei\n", estimatedFee.String())
		}

		// 交易接收方
		if tx.To() != nil {
			fmt.Println("To:", tx.To().Hex())
		} else {
			fmt.Println("To: Contract Creation")
		}

		// 交易nonce
		fmt.Println("Nonce:", tx.Nonce())

		// 交易数据
		if len(tx.Data()) > 0 {
			fmt.Printf("Data: %x\n", tx.Data())
		} else {
			fmt.Println("Data: None")
		}

		// 获取发送方地址
		//var signer types.Signer
		//if chainId := tx.ChainId(); chainId != nil && chainId.Sign() > 0 {
		//	signer = types.NewEIP155Signer(chainId)
		//} else {
		//	signer = types.HomesteadSigner{}
		//}
		//
		//from, err := types.Sender(signer, tx)
		//if err != nil {
		//	t.Errorf("Failed to get sender for tx %s: %v", tx.Hash().Hex(), err)
		//	continue
		//}
		//fmt.Println("From:", from.Hex())

		// 添加交易状态检查（需要从交易收据获取）
		receipt, err := blockChain.Client.TransactionReceipt(context.Background(), tx.Hash())
		if err != nil {
			t.Logf("Could not get receipt for tx %s: %v", tx.Hash().Hex(), err)
			continue
		}

		fmt.Println("Status:", receipt.Status)
		fmt.Println("Gas Used:", receipt.GasUsed)
		fmt.Println("Cumulative Gas Used:", receipt.CumulativeGasUsed)

		// 计算实际支付费用（需要gas used）
		if receipt != nil {
			var actualFee *big.Int
			switch tx.Type() {
			case types.LegacyTxType, types.AccessListTxType:
				actualFee = new(big.Int).Mul(tx.GasPrice(), new(big.Int).SetUint64(receipt.GasUsed))
			case types.DynamicFeeTxType:
				effectiveGasPrice := new(big.Int).Add(block.BaseFee(), tx.GasTipCap())
				if effectiveGasPrice.Cmp(tx.GasFeeCap()) > 0 {
					effectiveGasPrice = tx.GasFeeCap()
				}
				actualFee = new(big.Int).Mul(effectiveGasPrice, new(big.Int).SetUint64(receipt.GasUsed))
			}
			fmt.Printf("Actual Fee Paid: %s wei\n", actualFee.String())
		}
	}
}

func parseLegacyTxFee(tx *types.Transaction) *big.Int {
	gasPrice := tx.GasPrice()
	gasUsed := new(big.Int).SetUint64(tx.Gas())
	return new(big.Int).Mul(gasPrice, gasUsed)
}
func parseEIP1559TxFee(tx *types.Transaction, baseFee *big.Int) (*big.Int, *big.Int, *big.Int) {
	// 计算最大费用 (Max Fee Per Gas)
	maxFeePerGas := tx.GasFeeCap()

	// 计算优先费用 (Priority Fee Per Gas)
	priorityFeePerGas := tx.GasTipCap()

	// 实际支付的费用 = (baseFee + priorityFee) * gasUsed
	// 但通常我们计算最大可能费用
	effectiveGasPrice := new(big.Int).Add(baseFee, priorityFeePerGas)
	if effectiveGasPrice.Cmp(maxFeePerGas) > 0 {
		effectiveGasPrice = maxFeePerGas
	}

	gasUsed := new(big.Int).SetUint64(tx.Gas())
	totalFee := new(big.Int).Mul(effectiveGasPrice, gasUsed)

	return totalFee, effectiveGasPrice, priorityFeePerGas
}
func TestTransactionReceipt(t *testing.T) {
	blockChain, err := NewEvmChain(60, []string{"https://holy-practical-rain.ethereum-sepolia.quiknode.pro/c9414005b03b2712e975d4bae5077271592ed13a"}...)
	if err != nil {
		t.Error(err)
	}
	txn := "0x25d2a19ea1115bf7be096102c4e985b5b9551fd63676c9a8002842dc44c33c05"

	receipet, err := blockChain.Client.TransactionReceipt(context.Background(), common.HexToHash(txn))
	if err != nil {
		t.Error(err)
	}

	fmt.Println("recipet.Status = ", receipet.Status)
	for _, v := range receipet.Logs {
		fmt.Println("Log Address:", v.Address.Hex())
		fmt.Printf("Topics: %v\n", v.Topics)
		fmt.Printf("Data: %x\n", v.Data)
	}
}

func TestBlockTransactionReceipt(t *testing.T) {
	blockChain, err := NewEvmChain(60, []string{"https://holy-practical-rain.ethereum-sepolia.quiknode.pro/c9414005b03b2712e975d4bae5077271592ed13a"}...)
	if err != nil {
		t.Error(err)
	}
	list, err := blockChain.BlockReceipts(context.Background(), big.NewInt(8223786))
	if err != nil {
		t.Error(err)
	}
	for _, v := range list {
		fmt.Printf("chain: %+v\n", v)
	}
}

func TestContractType(t *testing.T) {
	blockChain, err := NewEvmChain(60, rpcList...)
	if err != nil {
		t.Error(err)
	}
	contractType, tokenInfo, err := blockChain.DetectContractType(common.HexToAddress("******************************************"))
	if err != nil {
		t.Error(err)
	}
	t.Log("contractType = ", contractType)
	t.Log("tokenInfo = ", tokenInfo)
}

func TestLegacyGetBlockByNumber(t *testing.T) {
	blockChain, err := NewEvmChain(8543, baseRpcList...)
	if err != nil {
		t.Error(err)
	}
	block, err := blockChain.LegacyBlockByNumber(context.Background(), big.NewInt(30004166))
	if err != nil {
		t.Error(err)
	}
	fmt.Println("block = ", block)
}

func TestL2GetBlockByNumber(t *testing.T) {
	blockChain, err := NewEvmChain(8543, baseRpcList...)
	if err != nil {
		t.Error(err)
	}
	block, err := blockChain.L2BlockByNumber(context.Background(), big.NewInt(30004166))
	if err != nil {
		t.Error(err)
	}
	fmt.Println("block = ", block)
}
func TestParseOptimismDepositTx(t *testing.T) {
	rawTx := `{
		"type": "0x7e",
		"nonce": "0x1",
		"gas": "0xf4240",
		"to": "0x4200000000000000000000000000000000000015",
		"value": "0x0",
		"input": "0x440a5e20...",
		"sourceHash": "0xc7e33773...",
		"mint": "0x0"
	}`
	blockChain, err := NewEvmChain(8543, baseRpcList...)
	if err != nil {
		t.Error(err)
	}

	tx, err := blockChain.parseLegacyTransaction(json.RawMessage(rawTx))
	if err != nil {
		t.Fatalf("解析失败: %v", err)
	}

	if tx.To().Hex() != "0x4200000000000000000000000000000000000015" {
		t.Errorf("To地址解析错误")
	}
}

func TestGetBalance(t *testing.T) {
	s := assert.New(t)

	bc, err := NewEvmChain(60, rpcList...)
	if !s.NoError(err) {
		return
	}

	balance, err := bc.GetBalance(common.HexToAddress("0x6a7aA9b882d50Bb7bc5Da1a244719C99f12F06a3"), nil)
	if !s.NoError(err) {
		return
	}
	t.Log(balance.String())
}

func TestGetERC20Balance(t *testing.T) {
	s := assert.New(t)

	bc, err := NewEvmChain(60, rpcList...)
	if !s.NoError(err) {
		return
	}

	balance, err := bc.GetERC20Balance(common.HexToAddress("0x6a7aA9b882d50Bb7bc5Da1a244719C99f12F06a3"), common.HexToAddress("0x71d7cbc6895D1f25dFaca22d4D5A42aE58110536"), nil)
	if !s.NoError(err) {
		return
	}
	t.Log(balance.String())
}
