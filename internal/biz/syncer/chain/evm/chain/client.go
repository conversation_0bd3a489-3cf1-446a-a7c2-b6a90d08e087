package chain

import (
	"byd_wallet/common/constant"
	"byd_wallet/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math/big"
	"math/rand"
	"time"

	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/ethereum/go-ethereum/trie"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
)

var (
	ctx = context.Background()
)

type EvmChain struct {
	Client     *ethclient.Client
	ChainIndex int64
	ChainName  string
	URL        string
	erc20ABI   abi.ABI
}

func NewEvmChain(chainIndex int64, rpcs ...string) (*EvmChain, error) {
	e := &EvmChain{ChainIndex: chainIndex, erc20ABI: constant.ERC20ABI}
	if _, err := e.NewClient(rpcs...); err != nil {
		return nil, fmt.Errorf("chainindex:%d url: %s,chainName %s  error: %w", chainIndex, e.ChainName, e.URL, err)
	}
	return e, nil
}

func (e *EvmChain) NewClient(urls ...string) (*ethclient.Client, error) {
	if len(urls) == 0 {
		return nil, errors.New("rpc url is empty")
	}

	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	used := make(map[int]bool)

	for i := 0; i < len(urls); i++ {
		idx := r.Intn(len(urls))
		// 防止重复尝试同一个 URL
		for used[idx] {
			idx = r.Intn(len(urls))
		}
		used[idx] = true

		url := urls[idx]
		client, err := ethclient.Dial(url)
		if err != nil {
			fmt.Printf("failed to connect to %s: %v\n", url, err)
			continue
		}

		e.Client = client
		e.URL = url
		return client, nil
	}

	return nil, errors.New("all rpc connections failed")
}

func (e *EvmChain) Eip1559() bool {
	_, exists := constant.Eip1559Index[e.ChainIndex]
	return exists
}

func (e *EvmChain) GetBalance(addr common.Address, blockNumber *big.Int) (*big.Int, error) {
	balance, err := e.Client.BalanceAt(ctx, addr, blockNumber)
	if err != nil {
		return nil, err
	}
	return balance, nil
}

func (e *EvmChain) GetERC20Balance(addr common.Address, tokenAddr common.Address, blockNumber *big.Int) (*big.Int, error) {

	callData, err := e.erc20ABI.Pack("balanceOf", addr)
	if err != nil {
		return nil, err
	}

	msg := ethereum.CallMsg{
		To:   &tokenAddr,
		Data: callData,
	}

	result, err := e.Client.CallContract(ctx, msg, blockNumber)
	if err != nil {
		return nil, err
	}

	var balance *big.Int
	if err := e.erc20ABI.UnpackIntoInterface(&balance, "balanceOf", result); err != nil {
		return nil, err
	}
	return balance, nil
}

func (e *EvmChain) SendTransaction(key string, to common.Address, encodedData []byte) (string, error) {
	addr, privateKey, err := utils.GetAddressByPrivateKey(key)
	if err != nil {
		return "", err
	}

	tx, err := e.newTx(addr, to, encodedData)
	if err != nil {
		return "", err
	}

	chainID, err := e.Client.ChainID(ctx)
	if err != nil {
		return "", err
	}

	signedTx, err := types.SignTx(tx, types.NewLondonSigner(chainID), privateKey)
	if err != nil {
		return "", err
	}

	if err := e.Client.SendTransaction(ctx, signedTx); err != nil {
		return "", err
	}
	return signedTx.Hash().Hex(), nil
}

func (e *EvmChain) CallContract(key string, contractAddress common.Address, contractABI abi.ABI, methodName string, params ...interface{}) (interface{}, error) {
	method := contractABI.Methods[methodName]
	// 只读方法
	if method.IsConstant() {
		return e.callContractStatic(contractAddress, contractABI, methodName, params...)
	}
	// 写操作
	if e.Eip1559() {
		encodedData, err := contractABI.Pack(methodName, params...)
		if err != nil {
			return nil, err
		}
		return e.SendTransaction(key, contractAddress, encodedData)
	}
	return e.callContractTransactLegacy(key, contractAddress, contractABI, methodName, params...)
}

func (e *EvmChain) callContractStatic(contractAddress common.Address, contractABI abi.ABI, methodName string, params ...interface{}) ([]interface{}, error) {
	contractInstance := bind.NewBoundContract(contractAddress, contractABI, e.Client, e.Client, e.Client)
	var output []interface{}
	callOpts := &bind.CallOpts{Context: ctx}

	if err := contractInstance.Call(callOpts, &output, methodName, params...); err != nil {
		return nil, err
	}
	return output, nil
}

func (e *EvmChain) callContractTransactLegacy(key string, contractAddress common.Address, contractABI abi.ABI, methodName string, params ...interface{}) (string, error) {
	addr, privateKey, err := utils.GetAddressByPrivateKey(key)
	if err != nil {
		return "", err
	}

	chainID, err := e.Client.ChainID(ctx)
	if err != nil {
		return "", err
	}

	transactOpts, err := bind.NewKeyedTransactorWithChainID(privateKey, chainID)
	if err != nil {
		return "", err
	}

	gasTipCap, feeCap, err := e.getGasCaps()
	if err != nil {
		return "", err
	}

	encodedData, err := contractABI.Pack(methodName, params...)
	if err != nil {
		return "", err
	}

	transactOpts.From = addr
	transactOpts.GasLimit = e.estimateGasLimit(addr, contractAddress, encodedData, feeCap, gasTipCap)
	transactOpts.GasPrice = feeCap // legacy 模式只用 GasPrice

	contractInstance := bind.NewBoundContract(contractAddress, contractABI, e.Client, e.Client, e.Client)
	tx, err := contractInstance.Transact(transactOpts, methodName, params...)
	if err != nil {
		return "", err
	}

	return tx.Hash().Hex(), nil
}

func (e *EvmChain) newTx(from, to common.Address, encodedData []byte) (*types.Transaction, error) {
	gasTipCap, feeCap, err := e.getGasCaps()
	if err != nil {
		return nil, err
	}

	nonce, err := e.Client.PendingNonceAt(ctx, from)
	if err != nil {
		return nil, err
	}

	chainID, err := e.Client.ChainID(ctx)
	if err != nil {
		return nil, err
	}

	tx := types.NewTx(&types.DynamicFeeTx{
		ChainID:   chainID,
		Nonce:     nonce,
		GasFeeCap: feeCap,
		GasTipCap: gasTipCap,
		Gas:       e.estimateGasLimit(from, to, encodedData, feeCap, gasTipCap),
		To:        &to,
		Data:      encodedData,
	})
	return tx, nil
}

func (e *EvmChain) getGasCaps() (*big.Int, *big.Int, error) {
	gasTipCap, err := e.Client.SuggestGasTipCap(ctx)
	if err != nil {
		gasTipCap = big.NewInt(constant.DefaultGasTipCap)
	}
	baseFee := big.NewInt(constant.DefaultBaseFee)

	feeCap := new(big.Int).Add(gasTipCap, baseFee)
	return gasTipCap, feeCap, nil
}

func (e *EvmChain) estimateGasLimit(from, to common.Address, data []byte, gasFeeCap, gasTipCap *big.Int) uint64 {
	limit, err := e.Client.EstimateGas(ctx, ethereum.CallMsg{
		From:      from,
		To:        &to,
		Data:      data,
		GasFeeCap: gasFeeCap,
		GasTipCap: gasTipCap,
	})
	if err != nil || limit < constant.GasLimitConst {
		return constant.GasLimitConst
	}
	return limit
}

func (e *EvmChain) DecodeContractInputData(contractABI *abi.ABI, data []byte) (map[string]interface{}, error) {
	return decodeContractInputData(contractABI, data)
}

func decodeContractInputData(contractABI *abi.ABI, data []byte) (map[string]interface{}, error) {
	if len(data) < 4 {
		return nil, errors.New("invalid data length")
	}

	methodSig := data[:4]
	method, err := contractABI.MethodById(methodSig)
	if err != nil {
		return nil, fmt.Errorf("method not found for sig %x: %w", methodSig, err)
	}

	inputData := data[4:]
	inputsMap := make(map[string]interface{})
	if err := method.Inputs.UnpackIntoMap(inputsMap, inputData); err != nil {
		return nil, err
	}
	return inputsMap, nil
}

func (e *EvmChain) EncodeContractInputData(contractABI *abi.ABI, methodName string, params ...interface{}) ([]byte, error) {
	method, ok := contractABI.Methods[methodName]
	if !ok {
		return nil, fmt.Errorf("method %s not found in ABI", methodName)
	}

	input, err := method.Inputs.Pack(params...)
	if err != nil {
		return nil, err
	}
	return input, nil
}

func DecodeContractEventData(contractABI *abi.ABI, hash common.Hash, data []byte) (map[string]interface{}, error) {
	event, err := contractABI.EventByID(hash)
	if err != nil {
		return nil, err
	}
	inputsMap := make(map[string]interface{})
	if err := contractABI.Events[event.Name].Inputs.UnpackIntoMap(inputsMap, data); err != nil {
		return nil, err
	}
	return inputsMap, nil
}

func (e *EvmChain) BlockReceipts(ctx context.Context, blockNumber *big.Int) ([]*types.Receipt, error) {
	var receipts []*types.Receipt
	err := e.Client.Client().CallContext(ctx, &receipts, "eth_getBlockReceipts", toBlockNumArg(blockNumber))
	if err != nil {
		return nil, err
	}
	return receipts, nil
}

func (e *EvmChain) L2BlockReceipts(ctx context.Context, blockNumber *big.Int) ([]*L2Receipt, error) {
	var receipts []*L2Receipt
	err := e.Client.Client().CallContext(ctx, &receipts, "eth_getBlockReceipts", toBlockNumArg(blockNumber))
	if err != nil {
		return nil, err
	}
	return receipts, nil
}
func (e *EvmChain) BlockByNumber(ctx context.Context, blockNumber *big.Int) (*types.Block, error) {
	var block types.Block
	err := e.Client.Client().CallContext(ctx, &block, "eth_getBlockByNumber", toBlockNumArg(blockNumber), true)
	if err != nil {
		return nil, err
	}
	return &block, nil
}

func (e *EvmChain) L2BlockByNumber(ctx context.Context, blockNumber *big.Int) (*Block, error) {
	var block Block
	err := e.Client.Client().CallContext(ctx, &block, "eth_getBlockByNumber", toBlockNumArg(blockNumber), true)
	if err != nil {
		return nil, err
	}
	return &block, nil
}

// LegacyBlockByNumber 获取兼容旧版L2的区块数据
// 注意：此方法专门处理可能包含非标准交易类型的旧版网络（如未升级EIP-1559的L2）
func (e *EvmChain) LegacyBlockByNumber(ctx context.Context, blockNumber *big.Int) (*types.Block, error) {
	var raw json.RawMessage
	err := e.Client.Client().CallContext(ctx, &raw, "eth_getBlockByNumber", toBlockNumArg(blockNumber), true)
	if err != nil {
		return nil, fmt.Errorf("RPC调用失败: %w", err)
	}

	// 1. 解析区块头（所有版本通用）
	var head *types.Header
	if err := json.Unmarshal(raw, &head); err != nil {
		return nil, fmt.Errorf("区块头解析失败: %w", err)
	}

	// 2. 解析原始区块数据
	var rawBlock struct {
		Transactions []json.RawMessage   `json:"transactions"`
		Uncles       []*types.Header     `json:"uncles"`
		Withdrawals  []*types.Withdrawal `json:"withdrawals,omitempty"`
	}
	if err := json.Unmarshal(raw, &rawBlock); err != nil {
		return nil, fmt.Errorf("区块体解析失败: %w", err)
	}

	// 3. 特殊交易处理流程
	txs := make([]*types.Transaction, 0, len(rawBlock.Transactions))
	for i, rawTx := range rawBlock.Transactions {
		tx, err := e.parseLegacyTransaction(rawTx)
		if err != nil {
			fmt.Printf("警告: 区块 %s 交易 %d 解析失败 (已跳过): %v",
				blockNumber.String(), i, err)
			continue
		}
		txs = append(txs, tx)
	}

	// 4. 构造兼容旧版的区块
	body := &types.Body{
		Transactions: txs,
		Uncles:       rawBlock.Uncles,
		Withdrawals:  rawBlock.Withdrawals, // 旧版网络可能为nil
	}
	return types.NewBlock(head, body, nil, trie.NewStackTrie(nil)), nil
}

// parseLegacyTransaction 旧版交易解析器
func (e *EvmChain) parseLegacyTransaction(rawTx json.RawMessage) (*types.Transaction, error) {
	// 尝试标准解析
	if tx, err := e.parseStandardTx(rawTx); err == nil {
		return tx, nil
	}

	// 尝试LegacyTx解析
	if tx, err := e.parseLegacyTxFormat(rawTx); err == nil {
		return tx, nil
	}

	// 尝试L2特殊格式（示例为Arbitrum）
	if tx, err := e.parseArbitrumTx(rawTx); err == nil {
		return tx, nil
	}

	return nil, errors.New("无法识别的交易格式")
}

// parseStandardTx 标准交易解析
func (e *EvmChain) parseStandardTx(rawTx json.RawMessage) (*types.Transaction, error) {
	var tx types.Transaction
	if err := tx.UnmarshalJSON(rawTx); err != nil {
		return nil, fmt.Errorf("标准格式解析失败: %w", err)
	}
	return &tx, nil
}

type LegacyTx struct {
	BlockHash   common.Hash     `json:"blockHash"`
	BlockNumber *hexutil.Big    `json:"blockNumber"`
	From        *common.Address `json:"from"`
	Nonce       uint64          `json:"nonce"`
	Hash        common.Hash     `json:"hash"`
	GasPrice    *hexutil.Big    `json:"gasPrice"`
	Gas         hexutil.Uint64  `json:"gas"`
	To          *common.Address `json:"to"`
	Value       *hexutil.Big    `json:"value"`
	Data        hexutil.Bytes   `json:"input"`
	V           *hexutil.Big    `json:"v"`
	R           *hexutil.Big    `json:"r"`
	S           *hexutil.Big    `json:"s"`
}
type OptimismDepositTx struct {
	LegacyTx
	Type                  string       `json:"type"`
	SourceHash            common.Hash  `json:"sourceHash"`
	Mint                  *hexutil.Big `json:"mint"`
	DepositReceiptVersion *hexutil.Big `json:"depositReceiptVersion"`
}

// parseLegacyTxFormat 旧版交易格式解析
func (e *EvmChain) parseLegacyTxFormat(rawTx json.RawMessage) (*types.Transaction, error) {

	jsStr, err := rawTx.MarshalJSON()
	if err != nil {
		return nil, err
	}
	var raw map[string]json.RawMessage
	if err := json.Unmarshal(jsStr, &raw); err != nil {
		return nil, err
	}

	var txType string
	if err := json.Unmarshal(raw["type"], &txType); err != nil {
		txType = "0x0" // 没有 type 字段视为 legacy 交易
	}

	switch txType {
	case "0x0":
		var legacy LegacyTx
		if err := json.Unmarshal(jsStr, &legacy); err != nil {
			return nil, fmt.Errorf("legacy 解析失败: %w", err)
		}
		// 使用 legacy 数据
	case "0x7e":
		var depositTx OptimismDepositTx
		if err := json.Unmarshal(jsStr, &depositTx); err != nil {
			return nil, fmt.Errorf("depositTx 解析失败: %w", err)
		}
		// 使用 Optimism L2 depositTx 数据
	default:
		return nil, fmt.Errorf("不支持的交易类型: %s", txType)
	}

	return nil, nil
}

// parseArbitrumTx Arbitrum特殊交易解析示例
func (e *EvmChain) parseArbitrumTx(rawTx json.RawMessage) (*types.Transaction, error) {
	// 实际实现需根据Arbitrum的文档调整
	var arbTx struct {
		ChainId hexutil.Big `json:"chainId"`
		// 其他Arbitrum特有字段...
	}
	if err := json.Unmarshal(rawTx, &arbTx); err != nil {
		return nil, err
	}
	// 转换为标准交易格式...
	return nil, errors.New("Arbitrum交易解析未实现")
}

func toBlockNumArg(number *big.Int) string {
	if number == nil {
		return "latest"
	}
	return hexutil.EncodeBig(number)
}

func (e *EvmChain) DetectContractType(addr common.Address) (constant.ContractType, *TokenInfo, error) {
	// ERC165 check
	if e.supportsInterface(addr, constant.ERC165ID) {
		if e.supportsInterface(addr, constant.ERC721ID) {
			return constant.TypeERC721, nil, nil
		}
		if e.supportsInterface(addr, constant.ERC1155ID) {
			return constant.TypeERC1155, nil, nil
		}
	}
	var (
		name        string
		symbol      string
		decimals    *big.Int
		totalSupply *big.Int
	)
	// 如果 `name` 无法成功读取，则认为不是 ERC20
	if err := e.CallERC20Function(e.erc20ABI, addr, "name", &name); err != nil || name == "" {
		return constant.TypeUnknown, nil, nil
	}
	_ = e.CallERC20Function(e.erc20ABI, addr, "symbol", &symbol)
	_ = e.CallERC20Function(e.erc20ABI, addr, "decimals", &decimals)
	_ = e.CallERC20Function(e.erc20ABI, addr, "totalSupply", &totalSupply)

	if symbol == "" || decimals == nil || decimals.Sign() <= 0 {
		return constant.TypeUnknown, nil, nil
	}
	return constant.TypeERC20, &TokenInfo{
		Name:        name,
		Symbol:      symbol,
		Decimals:    decimals,
		TotalSupply: totalSupply,
	}, nil
}

func (e *EvmChain) CallERC20Function(contractABI abi.ABI, address common.Address, method string, out interface{}) error {
	input, err := contractABI.Pack(method)
	if err != nil {
		return err
	}
	msg := ethereum.CallMsg{
		To:   &address,
		Data: input,
	}
	output, err := e.Client.CallContract(context.Background(), msg, nil)
	if err != nil {
		return err
	}
	return contractABI.UnpackIntoInterface(out, method, output)
}

func (e *EvmChain) supportsInterface(addr common.Address, interfaceID [4]byte) bool {
	// function supportsInterface(bytes4 interfaceID) => 0x01ffc9a7 + padded param
	data := append(common.FromHex("0x01ffc9a7"), padInterfaceID(interfaceID)...)

	msg := ethereum.CallMsg{
		To:   &addr,
		Data: data,
	}
	res, err := e.Client.CallContract(context.Background(), msg, nil)
	if err != nil || len(res) < 32 {
		return false
	}
	return res[31] == 1 // last byte true = 0x01
}

func padInterfaceID(id [4]byte) []byte {
	padded := make([]byte, 32)
	copy(padded[28:], id[:])
	return padded
}
