package chain

import (
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/common/hexutil"
)

type L2Receipt struct {
	Type              string       `json:"type"`
	Status            hexutil.Big  `json:"status"`
	CumulativeGasUsed string       `json:"cumulativeGasUsed"`
	EffectiveGasPrice *hexutil.Big `json:"effectiveGasPrice"`
	GasUsed           hexutil.Big  `json:"gasUsed"`
	ContractAddress   string       `json:"contractAddress"`
	Logs              []*Log       `json:"logs"` // 可自定义结构
	TransactionHash   string       `json:"transactionHash"`
	TransactionIndex  string       `json:"transactionIndex"`
	BlockHash         string       `json:"blockHash"`
	BlockNumber       string       `json:"blockNumber"`
	From              string       `json:"from"`
	To                string       `json:"to"`

	// L2 扩展字段
	L1GasPrice          *hexutil.Big `json:"l1GasPrice"`
	L1GasUsed           *hexutil.Big `json:"l1GasUsed"`
	L1Fee               *hexutil.Big `json:"l1Fee"`
	L1BaseFeeScalar     *hexutil.Big `json:"l1BaseFeeScalar"`
	L1BlobBaseFee       *hexutil.Big `json:"l1BlobBaseFee"`
	L1BlobBaseFeeScalar *hexutil.Big `json:"l1BlobBaseFeeScalar"`
}

type Log struct {
	Address     common.Address `json:"address"`
	Topics      []common.Hash  `json:"topics"`
	Data        string         `json:"data"`
	BlockNumber hexutil.Uint64 `json:"blockNumber"`
	TxHash      common.Hash    `json:"transactionHash"`
	TxIndex     hexutil.Uint   `json:"transactionIndex"`
	BlockHash   common.Hash    `json:"blockHash"`
	Index       hexutil.Uint   `json:"logIndex"`
	Removed     bool           `json:"removed"`
}
