package chain

import (
	"github.com/ethereum/go-ethereum/common"
	"github.com/shopspring/decimal"
	"log"
	"math/big"
	"strings"
)

type EvmTransaction struct {
	Txn          string          `json:"txn"`
	From         string          `json:"from"`
	To           string          `json:"to"`
	Value        decimal.Decimal `json:"value"`
	GasFee       decimal.Decimal `json:"gas_fee"`
	GasUsed      decimal.Decimal `json:"gas_used"`
	GasPrice     decimal.Decimal `json:"gas_price"`
	ActualGasFee decimal.Decimal `json:"actual_gas_fee"`
	GasLimit     decimal.Decimal `json:"gas_limit"`
	Nonce        int64           `json:"nonce"`
	BlockNumber  int64           `json:"block_number"`
	Timestamp    uint64          `json:"timestamp"`
	Status       uint64          `json:"status"` // 0:失败, 1:成功
	Method       string          `json:"method,omitempty"`
	Logs         []EvmLog        `json:"logs,omitempty"`
}

type EvmLog struct {
	Index       uint     `json:"index"`
	Address     string   `json:"address"`
	Topics      []string `json:"topics"`
	Data        string   `json:"data"`
	Removed     bool     `json:"removed"`
	BlockNumber int64    `json:"block_number"`
	TxHash      string   `json:"tx_hash"`
}

type TokenInfo struct {
	Name        string
	Symbol      string
	Decimals    *big.Int
	TotalSupply *big.Int
}

type Block struct {
	Number       string        `json:"number"`
	Timestamp    string        `json:"timestamp"`
	Hash         string        `json:"hash"`
	Transactions []Transaction `json:"transactions"` // 👈 修改这里
}

func (b Block) BlockNumberToInt() *big.Int {
	return hexToBig(b.Number)
}

func (b Block) TimestampToInt() *big.Int {
	return hexToBig(b.Timestamp)
}

func (b Block) HashToInt() common.Hash {
	return normalizeHash(b.Hash)
}

// 交易结构体（你可以根据需要扩展字段）
type Transaction struct {
	Hash                 string `json:"hash"`
	BlockNumber          string `json:"blockNumber"`
	ChainId              string `json:"chainId"`
	From                 string `json:"from"`
	To                   string `json:"to"`
	Value                string `json:"value"`
	Gas                  string `json:"gas"`
	GasPrice             string `json:"gasPrice"`
	MaxFeePerGas         string `json:"maxFeePerGas"`
	MaxPriorityFeePerGas string `json:"maxPriorityFeePerGas"`
	YParity              string `json:"yParity"`
	Nonce                string `json:"nonce"`
	TransactionIndex     string `json:"transactionIndex"`
	Type                 string `json:"type"`
	Input                string `json:"input"`
	S                    string `json:"s"`
	R                    string `json:"r"`
	V                    string `json:"v"`
}

func hexToBig(hexStr string) *big.Int {
	if len(hexStr) >= 2 && hexStr[:2] == "0x" {
		hexStr = hexStr[2:]
	}
	n := new(big.Int)
	n.SetString(hexStr, 16)
	return n
}

func (t Transaction) NonceToInt() *big.Int {
	return hexToBig(t.Nonce)
}
func (t Transaction) ChainIdToInt() *big.Int {
	return hexToBig(t.ChainId)
}

func (t Transaction) GasToInt() *big.Int {
	return hexToBig(t.Gas)
}

func (t Transaction) GasPriceToInt() *big.Int {
	return hexToBig(t.GasPrice)
}

func (t Transaction) ValueToInt() *big.Int {
	return hexToBig(t.Value)
}

func (t Transaction) TypeToInt() *big.Int {
	return hexToBig(t.Type)
}

func (t Transaction) MaxFeePerGasToInt() *big.Int {
	return hexToBig(t.MaxFeePerGas)
}

func (t Transaction) MaxPriorityFeePerGasToInt() *big.Int {
	return hexToBig(t.MaxPriorityFeePerGas)
}

func (t Transaction) TransactionIndexToInt() *big.Int {
	return hexToBig(t.TransactionIndex)
}

func (t Transaction) BlockNumberToInt() *big.Int {
	return hexToBig(t.BlockNumber)
}

// 标准化地址（转换为校验和格式，如果格式错误则 panic）
func (t Transaction) FromAddress() common.Address {
	return normalizeAddress(t.From)
}

func (t Transaction) ToAddress() common.Address {
	return normalizeAddress(t.To)
}

func normalizeAddress(addr string) common.Address {
	if !common.IsHexAddress(addr) {
		log.Fatalf("Invalid Ethereum address: %s", addr)
	}
	return common.HexToAddress(strings.ToLower(addr)) // 可改为 EIP-55 校验格式：common.HexToAddress(addr).Hex()
}

// 返回标准的 hash（common.Hash 类型）
func (t Transaction) HashBytes() common.Hash {
	return normalizeHash(t.Hash)
}

func normalizeHash(h string) common.Hash {
	if !common.IsHexAddress(h) && !strings.HasPrefix(h, "0x") {
		log.Fatalf("Invalid hash format: %s", h)
	}
	return common.HexToHash(h)
}
