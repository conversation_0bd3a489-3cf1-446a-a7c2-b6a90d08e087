package evm_l2

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz/syncer/chain/evm"
	"byd_wallet/model"
	"byd_wallet/utils"
	"context"
	"errors"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"strings"
)

type BlockFetcher struct {
	log               *log.Helper
	cli               *evm.L2TransactionsFetcher
	blockchainNetWork *model.BlockchainNetwork
}

func NewBlockFetcher(logger log.Logger) *BlockFetcher {
	return &BlockFetcher{
		log: log.NewHelper(logger),
	}
}

func (b *BlockFetcher) BlockchainNetWork() *model.BlockchainNetwork {
	return b.blockchainNetWork
}

func (b *BlockFetcher) SetBlockchainNetWork(blockchainNetWork *model.BlockchainNetwork) {
	b.blockchainNetWork = blockchainNetWork
}

func (b *BlockFetcher) SetEndpoints(endpoints []string) error {
	if len(endpoints) == 0 {
		return nil
	}
	var err error
	b.cli, _, err = evm.NewL2TransactionsFetcher(b.log, b.blockchainNetWork.ChainIndex, endpoints)
	return err
}

func (b *BlockFetcher) Close() error {
	return nil
}

func (b *BlockFetcher) FetchTransactions(ctx context.Context, height int64) (<-chan *model.Transaction, error) {
	txChan := make(chan *model.Transaction, 600)
	dbTxs, err := b.cli.FetchTransactionsByBlockNumber(height)
	if err != nil {
		// 忽略空块
		if strings.Contains("no transactions", err.Error()) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get transactions for block %d: %w", height, err)
	}

	go func() {
		defer close(txChan)
		for _, tx := range dbTxs {
			select {
			case txChan <- tx:
			case <-ctx.Done():
				b.log.Infow(log.DefaultMessageKey, "FetchTransactions: context cancelled", "blockHeight", height)
				return
			}
		}
	}()

	return txChan, nil
}

func (b *BlockFetcher) GetLatestBlockHeight(ctx context.Context) (int64, error) {
	return b.cli.GetLastBlockNumber(ctx, b.blockchainNetWork.ChainIndex, b.cli.RpcList)
}

func (b *BlockFetcher) GetDebugTraceTransaction(txn string) ([]*model.Transaction, error) {
	transaction, pending, err := b.cli.GetTransactionByHash(txn)
	if err != nil {
		return nil, err
	}
	if pending {
		return nil, errors.New("pending transaction")
	}
	receipt, err := b.cli.GetTransactionReceipt(txn)
	if err != nil {
		return nil, err
	}

	transactions, err := b.cli.GetInternalTransactions(txn)
	if err != nil {
		return nil, err
	}

	var result []*model.Transaction
	for _, tx := range transactions {
		result = append(result, &model.Transaction{
			TxHash:      txn,
			BlockNumber: receipt.BlockNumber.Int64(),
			ChainIndex:  b.blockchainNetWork.ChainIndex,
			FromAddress: tx.FromAddress,
			ToAddress:   tx.ToAddress,
			Value:       tx.Value,
			Fee:         evm.CalculateGasFee(receipt).String(),
			Method:      tx.Method,
			ProgramID:   "",
			Status:      constant.TransactionStatusSuccess,
			Timestamp:   transaction.Time().Unix(),
		})
	}
	if len(result) == 1 {
		from, _ := utils.GetTxSender(transaction)
		if result[0].FromAddress != from.Hex() || result[0].ToAddress != from.Hex() {
			result[0].FromAddress = from.Hex()
		}
	}
	return result, nil
}
