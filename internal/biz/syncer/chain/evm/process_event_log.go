package evm

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz/syncer/chain/evm/chain"
	"byd_wallet/model"
	"byd_wallet/utils"
	"errors"
	"fmt"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"math/big"
	"strings"
)

func (t *TransactionsFetcher) processLog(transaction *model.Transaction, vLog chain.EvmLog) (*model.Transaction, error) {
	if len(vLog.Topics) == 0 {
		return nil, errors.New("invalid log topic")
	}
	switch vLog.Topics[0] {
	case constant.TransferSigHash:
		return t.processTransferLog(transaction, vLog)
	case constant.ApprovalSigHash:
		return t.processApproveLog(transaction, vLog)
	case constant.LogTransferSigHash:
		return t.processLogTransferLog(transaction, vLog)
	case constant.MessageInSigHash:
		return t.processMessageInLog(transaction, vLog)
	case constant.DepositSigHash:
		return t.processDepositLog(transaction, vLog)
	case constant.FilledRelaySigHash:
		return t.processFilledRelayLog(transaction, vLog)
	case constant.FundsDepositedSigHash:
		return t.processFundsDepositedLog(transaction, vLog)
		// 暂时不处理，使用上报处理
	//case constant.BscUnitSwapV3SigHash, constant.SwapSigHash, constant.PancakeSwapV3Hash:
	//	return t.processSwapV3Log(transaction, vLog)
	default:
		return nil, errors.New("invalid log topic")
	}
}

func (t *TransactionsFetcher) processTransferLog(transaction *model.Transaction, vLog chain.EvmLog) (*model.Transaction, error) {
	//contractABI, err := t.processAbi(constant.ERCAll)
	//if err != nil {
	//	return nil, err
	//}
	logData := common.Hex2Bytes(strings.TrimPrefix(vLog.Data, "0x"))
	event, err := constant.ERCAllAbi.EventByID(common.HexToHash(vLog.Topics[0]))
	if err != nil {
		return nil, err
	}
	transaction.Method = event.Name
	if len(vLog.Topics) < 2 {
		return nil, fmt.Errorf("invalid log topic %+v", vLog)
	}

	var from, to string
	from = common.HexToAddress(vLog.Topics[1]).Hex()
	transaction.FromAddress = from
	switch len(vLog.Topics) {
	case 2:
		order, err := event.Inputs.UnpackValues(logData)
		if err != nil {
			return nil, err
		}

		var value = big.NewInt(0)
		switch len(order) {
		case 1:
			if v, ok := order[0].(*big.Int); ok {
				value = v
			}
		case 2:
			if addr, ok := order[0].(common.Address); ok {
				to = addr.Hex()
			}
			if v, ok := order[1].(*big.Int); ok {
				value = v
			}
		}

		transaction.Value = value.String()
	case 3:
		to = common.HexToAddress(vLog.Topics[2]).Hex()
		order, err := event.Inputs.UnpackValues(logData)
		if err != nil {
			return nil, err
		}
		value := order[0].(*big.Int)
		transaction.Value = value.String()
	case 4:
		// ERC721
		to = common.HexToAddress(vLog.Topics[2]).Hex()
		value := common.HexToHash(vLog.Topics[3]).Big()
		transaction.Value = value.String()
	}
	transaction.ToAddress = to
	return transaction, nil
}

func (t *TransactionsFetcher) processApproveLog(transaction *model.Transaction, vLog chain.EvmLog) (*model.Transaction, error) {
	if len(vLog.Topics) < 3 {
		return nil, errors.New("invalid log topic")
	}
	//contractABI, err := abi.JSON(strings.NewReader(constant.ERCAll))
	//if err != nil {
	//	return nil, err
	//}
	data := common.Hex2Bytes(strings.TrimPrefix(vLog.Data, "0x"))
	event, err := constant.ERCAllAbi.EventByID(common.HexToHash(vLog.Topics[0]))
	if err != nil {
		return nil, err
	}
	transaction.Method = event.Name
	from := common.HexToAddress(vLog.Topics[1]).Hex()
	to := common.HexToAddress(vLog.Topics[2]).Hex()
	transaction.FromAddress = from
	transaction.ToAddress = to
	switch len(vLog.Topics) {
	case 3:
		order, err := event.Inputs.UnpackValues(data)
		if err != nil {
			return nil, err
		}
		value := order[0].(*big.Int)
		transaction.Value = value.String()
	default:
		return nil, errors.New("invalid chain type")
	}
	return transaction, nil
}

func (t *TransactionsFetcher) processLogTransferLog(transaction *model.Transaction, vLog chain.EvmLog) (*model.Transaction, error) {
	if len(vLog.Topics) < 4 {
		return nil, errors.New("invalid log topic")
	}
	contractABI, err := abi.JSON(strings.NewReader(constant.MRC20ABI))
	if err != nil {
		return nil, err
	}
	data := common.Hex2Bytes(strings.TrimPrefix(vLog.Data, "0x"))
	event, err := contractABI.EventByID(common.HexToHash(vLog.Topics[0]))
	if err != nil {
		return nil, err
	}
	transaction.Method = event.Name
	transaction.ProgramID = common.HexToAddress(vLog.Topics[1]).Hex()
	if transaction.ProgramID == constant.MRC20 {
		transaction.ProgramID = ""
	}
	from := common.HexToAddress(vLog.Topics[2]).Hex()
	to := common.HexToAddress(vLog.Topics[3]).Hex()
	transaction.FromAddress = from
	transaction.ToAddress = to
	switch len(vLog.Topics) {
	case 4:
		order, err := event.Inputs.UnpackValues(data)
		if err != nil {
			return nil, err
		}
		value := order[0].(*big.Int)
		transaction.Value = value.String()
	default:
		return nil, errors.New("invalid chain type")
	}
	return transaction, nil
}

func (t *TransactionsFetcher) processMessageInLog(transaction *model.Transaction, vLog chain.EvmLog) (*model.Transaction, error) {
	if len(vLog.Topics) < 3 {
		return nil, errors.New("invalid log topic")
	}
	contractABI, err := abi.JSON(strings.NewReader(constant.MessageIn))
	if err != nil {
		return nil, err
	}
	data := common.Hex2Bytes(strings.TrimPrefix(vLog.Data, "0x"))
	event, err := contractABI.EventByID(common.HexToHash(vLog.Topics[0]))
	if err != nil {
		return nil, err
	}
	transaction.Method = event.Name

	switch len(vLog.Topics) {
	case 3:
		order, err := event.Inputs.UnpackValues(data)
		if err != nil {
			return nil, err
		}
		fmt.Println("order = ", order)
		transaction.ProgramID = order[0].(common.Address).Hex()
		if transaction.ProgramID == constant.BaseETH {
			transaction.ProgramID = ""
		}

		from := order[2].(common.Address).Hex()
		to := common.BytesToAddress(order[3].([]uint8)).Hex()
		transaction.FromAddress = from
		transaction.ToAddress = to
		value := order[1].(*big.Int)
		transaction.Value = value.String()
	default:
		return nil, errors.New("invalid chain type")
	}
	return transaction, nil
}

func (t *TransactionsFetcher) processDepositLog(transaction *model.Transaction, vLog chain.EvmLog) (*model.Transaction, error) {
	if len(vLog.Topics) < 1 {
		return nil, errors.New("invalid log topic")
	}
	contractABI, err := abi.JSON(strings.NewReader(constant.Deposit))
	if err != nil {
		return nil, err
	}
	data := common.Hex2Bytes(strings.TrimPrefix(vLog.Data, "0x"))
	event, err := contractABI.EventByID(common.HexToHash(vLog.Topics[0]))
	if err != nil {
		return nil, err
	}
	transaction.Method = event.Name

	switch len(vLog.Topics) {
	case 2:
		order, err := event.Inputs.UnpackValues(data)
		if err != nil {
			return nil, err
		}
		transaction.Value = order[0].(*big.Int).String()
		if utils.IsSupportedToken(vLog.Address) {
			transaction.ProgramID = ""
		}
		transaction.ToAddress = common.HexToAddress(vLog.Topics[1]).Hex()
	default:
		return nil, errors.New("invalid chain type")
	}
	return transaction, nil
}

func (t *TransactionsFetcher) processFilledRelayLog(transaction *model.Transaction, vLog chain.EvmLog) (*model.Transaction, error) {
	if len(vLog.Topics) < 4 {
		return nil, errors.New("invalid log topic")
	}
	contractABI, err := abi.JSON(strings.NewReader(constant.Bridge))
	if err != nil {
		return nil, err
	}
	data := common.Hex2Bytes(strings.TrimPrefix(vLog.Data, "0x"))
	event, err := contractABI.EventByID(common.HexToHash(vLog.Topics[0]))
	if err != nil {
		return nil, err
	}
	transaction.Method = event.Name

	switch len(vLog.Topics) {
	case 4:
		order, err := event.Inputs.UnpackValues(data)
		if err != nil {
			return nil, err
		}

		contractRaw := order[1].([32]byte)
		contractAddress := common.HexToAddress(common.BytesToHash(contractRaw[:]).Hex()).Hex()
		transaction.ProgramID = contractAddress
		toRaw := order[8].([32]byte)
		transaction.ToAddress = common.HexToAddress(common.BytesToHash(toRaw[:]).Hex()).Hex()
		fromRwa := order[9].([32]byte)
		transaction.FromAddress = common.HexToAddress(common.BytesToHash(fromRwa[:]).Hex()).Hex()
		transaction.Value = order[3].(*big.Int).String()
		if utils.IsSupportedToken(contractAddress) {
			transaction.ProgramID = ""
		}

	default:
		return nil, errors.New("invalid chain type")
	}
	return transaction, nil
}

func (t *TransactionsFetcher) processFundsDepositedLog(transaction *model.Transaction, vLog chain.EvmLog) (*model.Transaction, error) {
	if len(vLog.Topics) < 4 {
		return nil, errors.New("invalid log topic")
	}
	contractABI, err := abi.JSON(strings.NewReader(constant.Bridge))
	if err != nil {
		return nil, err
	}
	data := common.Hex2Bytes(strings.TrimPrefix(vLog.Data, "0x"))
	event, err := contractABI.EventByID(common.HexToHash(vLog.Topics[0]))
	if err != nil {
		return nil, err
	}
	transaction.Method = event.Name

	switch len(vLog.Topics) {
	case 4:
		order, err := event.Inputs.UnpackValues(data)
		if err != nil {
			return nil, err
		}
		transaction.FromAddress = common.HexToAddress(common.HexToHash(vLog.Topics[3]).Hex()).Hex()
		contractRaw := order[0].([32]byte)
		contractAddress := common.HexToAddress(common.BytesToHash(contractRaw[:]).Hex()).Hex()
		if !utils.IsSupportedToken(contractAddress) {
			return nil, nil
		}
		transaction.ProgramID = contractAddress
		toRaw := order[7].([32]byte)
		transaction.ToAddress = common.HexToAddress(common.BytesToHash(toRaw[:]).Hex()).Hex()
		transaction.Value = order[2].(*big.Int).String()
		transaction.ProgramID = ""

	default:
		return nil, errors.New("invalid chain type")
	}
	return transaction, nil
}

func (t *TransactionsFetcher) processSwapV3Log(transaction *model.Transaction, vLog chain.EvmLog) (*model.Transaction, error) {
	transaction.Method = constant.TxMethodSwap
	return transaction, nil
}

func (t *TransactionsFetcher) processStatus(status uint64) string {
	if status == 1 {
		return constant.TransactionStatusSuccess
	}
	if status == 0 {
		return constant.TransactionStatusFail
	}
	return constant.TransactionStatusPending
}

func (t *TransactionsFetcher) processAbi(abiStr string) (*abi.ABI, error) {
	if abiStr == "" {
		abiStr = constant.ERCAll
	}
	parsedABI, err := abi.JSON(strings.NewReader(abiStr))
	if err != nil {
		return nil, err
	}
	return &parsedABI, nil
}
