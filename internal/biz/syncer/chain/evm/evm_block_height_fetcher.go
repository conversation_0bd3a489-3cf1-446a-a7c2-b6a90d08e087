package evm

import (
	"byd_wallet/model"
	"context"
)

type BlockHeightFetcher struct {
	cli *MultiChainClient
}

func NewBlockHeightFetcher(endpoints []*model.RPCEndpoint) (*BlockHeightFetcher, error) {
	cli, err := NewMultiChainClient(endpoints)
	if err != nil {
		return nil, err
	}
	return &BlockHeightFetcher{cli: cli}, nil
}

func (b *BlockHeightFetcher) GetLatestBlockHeight(ctx context.Context, chainIndex int64) (int64, error) {
	cli, err := b.cli.Select(chainIndex)
	if err != nil {
		return 0, err
	}
	num, err := cli.BlockNumber(ctx)
	if err != nil {
		return 0, err
	}
	return int64(num), nil
}

func (b *BlockHeightFetcher) Close() {
	b.cli.Close()
}
