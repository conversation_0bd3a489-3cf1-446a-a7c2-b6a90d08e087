package evm

import (
	chain2 "byd_wallet/internal/biz/syncer/chain/evm/chain"
	"byd_wallet/model"
	"context"
	"errors"
	"github.com/go-kratos/kratos/v2/log"
)

type L2TransactionsFetcher struct {
	log *log.Helper

	*TransactionsFetcher

	chainIndex int64
	RpcList    []string
}

func NewL2TransactionsFetcher(
	logger *log.Helper,
	chainIndex int64,
	rpcList []string,
) (*L2TransactionsFetcher, func(), error) {
	base, c, err := NewTransactionsFetcher(logger, chainIndex, rpcList)
	if err != nil {
		return nil, nil, err
	}
	if c == nil {
		c = func() {}
	}
	return &L2TransactionsFetcher{
			log:                 logger,
			TransactionsFetcher: base,
			chainIndex:          chainIndex,
			RpcList:             rpcList,
		}, func() {
			c()
		}, nil
}

//func (t *L2TransactionsFetcher) FetchTransactions(ctx context.Context, startBlock int64) ([]*model.Transaction, int64, error) {
//	txList, endBlock, err := t.SyncLegacyEVMTransactionsInBatches(ctx, startBlock, t.chainIndex, t.rpcList, batchSize, maxConcurrency)
//	if err != nil {
//		return nil, 0, err
//	}
//	if len(txList) == 0 {
//		return nil, 0, nil
//	}
//	list := TransActionToDb(txList, t.chainIndex)
//	return list, endBlock, nil
//}

func (t *L2TransactionsFetcher) FetchTransactionsByBlockNumber(blockNumber int64) ([]*model.Transaction, error) {
	txList, err := t.GetBlockTransactions(blockNumber)
	if err != nil {
		return nil, err
	}
	if len(txList) == 0 {
		return nil, nil
	}
	list := t.TransActionToDb(txList, t.chainIndex)
	return list, nil
}

func (t *L2TransactionsFetcher) GetBlockTransactions(blockNumber int64) ([]chain2.EvmTransaction, error) {
	blockchain, err := t.GetEvmClient()
	if err != nil {
		return nil, err
	}
	block, receipts, err := t.fetchL2BlockWithReceipts(context.Background(), blockchain, blockNumber)
	if err != nil {
		return nil, err
	}

	if len(receipts) != len(block.Transactions) {
		return nil, errors.New("block receipts count not match")
	}

	var txList []chain2.EvmTransaction

	for txIndex, tx := range block.Transactions {
		if txIndex >= len(receipts) {
			t.log.Warnw(log.DefaultMessageKey, "tx index out of receipts range",
				"txIndex", txIndex, "receiptsLen", len(receipts))
			continue
		}

		evmTx, err := t.extractL2TransactionWithReceipt(blockchain, block, &tx, receipts[txIndex])
		if err != nil {
			t.log.Errorw(log.DefaultMessageKey, "extractTransaction error", "err", err,
				"tx", tx.HashBytes().Hex())
			continue
		}
		txList = append(txList, evmTx)
	}
	return txList, nil
}
