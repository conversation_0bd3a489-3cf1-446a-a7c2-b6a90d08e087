package evm

import (
	"byd_wallet/model"
	"context"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestClient(t *testing.T) {
	endpoints := []*model.RPCEndpoint{
		{
			ChainIndex: 10042221,
			URL:        "https://arbitrum.blockpi.network/v1/rpc/5c484c58b9dd1802b6fb75c21264f116925ab894",
		},
		{
			ChainIndex: 10042221,
			URL:        "https://arbitrum.blockpi.network/v1/rpc/47af7581e2f622f35c563cd4e5b239d6910ff1e",
		},
		{
			ChainIndex: 10000070,
			URL:        "https://optimism.blockpi.network/v1/rpc/2687ae87ae8d2851c85fb6c2e61293f72f5ac048",
		},
		{
			ChainIndex: 10000070,
			URL:        "https://optimism.blockpi.network/v1/rpc/508db82376034071e5ac39b497c0a7c5909a2d93",
		},
	}
	cli, err := NewMultiChainClient(endpoints)
	assert.NoError(t, err)
	defer cli.Close()
	c, err := cli.Select(10042221)
	assert.NoError(t, err)
	height, err := c.BlockNumber(context.Background())
	assert.NoError(t, err)
	assert.True(t, height > 0)
	c, err = cli.Select(10042221)
	assert.NoError(t, err)
	height, err = c.BlockNumber(context.Background())
	assert.Error(t, err)
	c, err = cli.Select(10042221)
	assert.NoError(t, err)
	height, err = c.BlockNumber(context.Background())
	assert.NoError(t, err)
}
