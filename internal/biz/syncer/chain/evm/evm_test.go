package evm

import (
	"testing"
)

func TestProcessBlockRange(t *testing.T) {
	// txCh := make(chan chain.EvmTransaction, 10000)
	// start := int64(8267518)
	// end := int64(8267519)
	// var mu sync.Mutex
	// var result []chain.EvmTransaction
	// blockchain, err := chain.NewEvmChain(60, []string{"https://holy-practical-rain.ethereum-sepolia.quiknode.pro/c9414005b03b2712e975d4bae5077271592ed13a"}...)
	// if err != nil {
	// 	t.Error(err)
	// }
	// go func() {
	// 	for tx := range txCh {
	// 		fmt.Println("tx = ", tx)
	// 		mu.Lock()
	// 		result = append(result, tx)
	// 		mu.Unlock()
	// 	}
	// }()

	// ProcessBlockRange(context.Background(), blockchain, start, end, txCh)
	// close(txCh)
	// fmt.Println(result)

}

func TestProcessBlockRange2(t *testing.T) {
	// txCh := make(chan chain.EvmTransaction, 10000)
	// start := int64(8267518)
	// end := int64(8267519)
	// var mu sync.Mutex
	// var result []chain.EvmTransaction
	// blockchain, err := chain.NewEvmChain(60, []string{"https://holy-practical-rain.ethereum-sepolia.quiknode.pro/c9414005b03b2712e975d4bae5077271592ed13a"}...)
	// if err != nil {
	// 	t.Error(err)
	// }
	// go func() {
	// 	for tx := range txCh {
	// 		fmt.Println("tx = ", tx)
	// 		mu.Lock()
	// 		result = append(result, tx)
	// 		mu.Unlock()
	// 	}
	// }()

	// ProcessBlockRange(context.Background(), blockchain, start, end, txCh)
	// close(txCh)
	// fmt.Println(result)

}
