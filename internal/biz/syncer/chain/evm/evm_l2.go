package evm

import (
	"byd_wallet/common/constant"
	chain2 "byd_wallet/internal/biz/syncer/chain/evm/chain"
	"context"
	"fmt"
	"math/big"
	"time"

	"github.com/ethereum/go-ethereum/crypto"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	"go.uber.org/zap"
)

func (t *L2TransactionsFetcher) fetchL2BlockWithReceipts(ctx context.Context, blockchain *chain2.EvmChain, blockNum int64) (*chain2.Block, []*chain2.L2Receipt, error) {
	var (
		block    *chain2.Block
		receipts []*chain2.L2Receipt
		err      error
	)

	for retry := 0; retry < 3; retry++ {
		block, err = blockchain.L2BlockByNumber(ctx, big.NewInt(blockNum))
		if err != nil {
			fmt.Println("block err:", zap.Error(err))
			time.Sleep(time.Second * time.Duration(retry+1))
			continue
		}

		receipts, err = blockchain.L2BlockReceipts(ctx, big.NewInt(blockNum))
		if err == nil {
			return block, receipts, nil
		}
		time.Sleep(time.Second * time.Duration(retry+1))
	}

	return nil, nil, err
}

func (t *L2TransactionsFetcher) handleL2BlockWithReceipts(
	ctx context.Context,
	blockchain *chain2.EvmChain,
	block *chain2.Block,
	receipts []*chain2.L2Receipt,
	txCh chan<- chain2.EvmTransaction,
) {
	for txIndex, tx := range block.Transactions {
		if txIndex >= len(receipts) {
			t.log.Warnw(log.DefaultMessageKey, "tx index out of receipts range",
				"txIndex", txIndex, "receiptsLen", len(receipts))
			continue
		}

		evmTx, err := t.extractL2TransactionWithReceipt(blockchain, block, &tx, receipts[txIndex])
		if err != nil {
			t.log.Errorw(log.DefaultMessageKey, "extractTransaction error", "err", err,
				"tx", tx.HashBytes().Hex())
			continue
		}
		txCh <- evmTx
	}
}

func (t *L2TransactionsFetcher) extractL2TransactionWithReceipt(
	blockchain *chain2.EvmChain,
	block *chain2.Block,
	tx *chain2.Transaction,
	receipt *chain2.L2Receipt,
) (chain2.EvmTransaction, error) {
	evmTx := chain2.EvmTransaction{
		Txn:         tx.HashBytes().Hex(),
		Value:       decimal.NewFromBigInt(tx.ValueToInt(), 0),
		GasFee:      decimal.NewFromBigInt(tx.GasToInt(), 0),
		GasLimit:    decimal.NewFromBigInt(tx.GasPriceToInt(), 0),
		Nonce:       cast.ToInt64(tx.NonceToInt()),
		BlockNumber: block.BlockNumberToInt().Int64(),
		Timestamp:   block.TimestampToInt().Uint64(),
		Status:      receipt.Status.ToInt().Uint64(),
	}
	from := tx.FromAddress()
	if tx.To != "" {
		evmTx.To = tx.ToAddress().Hex()
	} else {
		evmTx.Method = constant.TxMethodCreated
		contractAddr := crypto.CreateAddress(from, tx.NonceToInt().Uint64())
		evmTx.To = contractAddr.Hex()
	}
	evmTx.From = from.Hex()
	evmTx.ActualGasFee = t.L2ActualFee(tx, receipt)

	evmTx.Logs = make([]chain2.EvmLog, 0, len(receipt.Logs))
	for _, log := range receipt.Logs {
		topics := make([]string, len(log.Topics))
		for i, topic := range log.Topics {
			topics[i] = topic.Hex()
		}

		evmTx.Logs = append(evmTx.Logs, chain2.EvmLog{
			Index:   uint(log.Index),
			Address: log.Address.Hex(),
			Topics:  topics,
			//Data:        common.Bytes2Hex(log.Data),
			Data:        log.Data,
			Removed:     log.Removed,
			BlockNumber: block.BlockNumberToInt().Int64(),
			TxHash:      tx.HashBytes().Hex(),
		})
	}

	return evmTx, nil
}

func (t *L2TransactionsFetcher) L2ActualFee(tx *chain2.Transaction, receipt *chain2.L2Receipt) decimal.Decimal {
	var (
		actualFee = big.NewInt(0)
		tmpFee    = big.NewInt(0)
	)

	// 加上 L1 fee（如有）
	if receipt.L1Fee != nil {
		actualFee.Add(actualFee, receipt.L1Fee.ToInt())
	}
	// Arbitrum：L2 fee = effectiveGasPrice * gasUsed
	if receipt.EffectiveGasPrice != nil && receipt.EffectiveGasPrice.ToInt().Int64() > 0 && receipt.GasUsed.ToInt() != nil {
		tmpFee.Mul(receipt.EffectiveGasPrice.ToInt(), receipt.GasUsed.ToInt())
		actualFee.Add(actualFee, tmpFee)
		return decimal.NewFromBigInt(actualFee, 0)
	}

	// Optimism / Base：L2 fee + L1 fee
	if tx.GasPriceToInt() != nil && receipt.GasUsed.ToInt() != nil {
		tmpFee.Mul(tx.GasPriceToInt(), receipt.GasUsed.ToInt())
		actualFee.Add(actualFee, tmpFee)
	}

	return decimal.NewFromBigInt(actualFee, 0)
}
