package tron

import (
	"encoding/hex"
	"fmt"
	"github.com/fbsobreira/gotron-sdk/pkg/proto/core"
	"google.golang.org/protobuf/proto"
	"testing"

	"github.com/ethereum/go-ethereum/common"
	"github.com/fbsobreira/gotron-sdk/pkg/address"

	"github.com/stretchr/testify/assert"
)

func TestParseRawData(t *testing.T) {
	hexData := "0a025d032208579d05b5cfcf297c40c8bf9ae482335ab507081f12b0070a31747970652e676f6f676c65617069732e636f6d2f70726f746f636f6c2e54726967676572536d617274436f6e747261637412fa060a1541b3fbcad0809dc37f3aad9fe2ea49256569197418121541aa1a4f565d6b707f1991fe2ea1caa66f2a3108af1880ade20422c40632502519000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000002e4bf123116000000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000002600000000000000000000000000000000000000000000000000000000000000bb8000000000000000000000000eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee000000000000000000000000a614f803b6fd780986a42c78ec9c7f77e6ded13c000000000000000000000000b3fbcad0809dc37f3aad9fe2ea49256569197418000000000000000000000000000000000000000000000000000000000098968000000000000000000000000000000000000000000000000000000000002bc56a000000000000000000000000e95812d8d5b5412d2b9f3a4d5a87ca15c5c51f33000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000e47ff36ab500000000000000000000000000000000000000000000000000000000002bc56a00000000000000000000000000000000000000000000000000000000000000800000000000000000000000001ae775eeaf0dcf3054d8889db097227fa7fa6a9d00000000000000000000000000000000000000000000000000000000687e24010000000000000000000000000000000000000000000000000000000000000002000000000000000000000000891cdb91d149f23b1a45d9c5ca78a88d0cb44c18000000000000000000000000a614f803b6fd780986a42c78ec9c7f77e6ded13c0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000412ec151de2efccf739c5bda41e7cd796d5131a4027d11e5df12ce830041d3efc6791e57696204eb430428eae93a9ee6677e8a0eec24abbff301f47aa232c8e9d41c0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000709bf396e4823390018094ebdc03"
	data, err := hex.DecodeString(hexData)
	assert.NoError(t, err)
	var tx core.TransactionRaw
	err = proto.Unmarshal(data, &tx)
	assert.NoError(t, err)

	var tsm core.TriggerSmartContract
	err = tx.Contract[0].Parameter.UnmarshalTo(&tsm)
	assert.NoError(t, err)
	fmt.Println(tsm.CallValue)
	fmt.Printf("%+v\n", &tx)

}

func Test_etherAddress2Tron(t *testing.T) {
	tronAddr, _ := address.Base58ToAddress("T9yD18P9AR99bSAzYRXdBboFMPN9sPzEaq")
	type args struct {
		addr common.Address
	}
	tests := []struct {
		name string
		args args
		want address.Address
	}{
		{
			name: "normal",
			args: args{
				addr: common.HexToAddress("******************************************"),
			},
			want: tronAddr,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, etherAddress2Tron(tt.args.addr), "etherAddress2Tron(%v)", tt.args.addr)
		})
	}
}

func Test_decodeTRC20TransferInputs(t *testing.T) {
	type args struct {
		data string
	}
	tests := []struct {
		name  string
		args  args
		want  string
		want1 string
	}{
		{
			name: "1",
			args: args{
				data: "a9059cbb000000000000000000000000914481f9ac0106f62aa672d56637c83cff53730000000000000000000000000000000000000000000000000000000013ca651200",
			},
			want:  "TPDK3seGB4KyjHNcwDjXAxSvQz8Y5MqHGa",
			want1: "85000000000",
		},
		{
			name: "2",
			args: args{
				data: "a9059cbb0000000000000000000000008d9d325a699244c50723c21a177dc106d65ad0f0000000000000000000000000000000000000000000000000000000006769ffc0",
			},
			want:  "TNszbKnf8pLq1hkUsTJH22yJbHX5jvqww8",
			want1: "1735000000",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			data, err := hex.DecodeString(tt.args.data)
			assert.NoError(t, err)
			got, got1, err := decodeTRC20TransferData(data)
			assert.NoError(t, err)
			assert.Equalf(t, tt.want, got, "decodeTRC20TransferData(%v)", tt.args.data)
			assert.Equalf(t, tt.want1, got1, "decodeTRC20TransferData(%v)", tt.args.data)
		})
	}
}

func Test_decodeTRC20TransferFromData(t *testing.T) {
	tests := []struct {
		name  string
		data  string
		want  string
		want1 string
		want2 string
	}{
		{
			name:  "1",
			data:  "23b872dd000000000000000000000000ba58a0e3cb4bd6c95bebc92733acae0a877e0dad000000000000000000000000686c8219f66cada3e0db325383be4ca4b9899f5e0000000000000000000000000000000000000000000000000000000000e7a0e0",
			want:  "TSxWrkgS33YcVdzRX4wvdtYzxyPc3VA769",
			want1: "TKVMGHt7brRqsV3ghhkwWY7eS4mXxV5AB5",
			want2: "15180000",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			data, err := hex.DecodeString(tt.data)
			assert.NoError(t, err)
			got, got1, got2, err := decodeTRC20TransferFromData(data)
			assert.NoError(t, err)
			assert.Equalf(t, tt.want, got, "decodeTRC20TransferFromData(%v)", tt.data)
			assert.Equalf(t, tt.want1, got1, "decodeTRC20TransferFromData(%v)", tt.data)
			assert.Equalf(t, tt.want2, got2, "decodeTRC20TransferFromData(%v)", tt.data)
		})
	}
}

func Test_decodeTRC20MethodID(t *testing.T) {
	tests := []struct {
		name string
		data string
		want string
	}{
		{
			name: "1",
			data: "23b872dd000000000000000000000000ba58a0e3cb4bd6c95bebc92733acae0a877e0dad000000000000000000000000686c8219f66cada3e0db325383be4ca4b9899f5e0000000000000000000000000000000000000000000000000000000000e7a0e0",
			want: trc20TransferFromMethodSignature,
		},
		{
			name: "2",
			data: "a9059cbb000000000000000000000000914481f9ac0106f62aa672d56637c83cff53730000000000000000000000000000000000000000000000000000000013ca651200",
			want: trc20TransferMethodSignature,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			data, err := hex.DecodeString(tt.data)
			assert.NoError(t, err)
			assert.Equalf(t, tt.want, decodeTRC20MethodID(data), "decodeTRC20MethodID(%v)", data)
		})
	}
}

func TestDecodeTRC20ApprovalData(t *testing.T) {
	tests := []struct {
		name            string
		hexData         string
		expectedSpender string
		expectedValue   string
		expectError     bool
	}{
		{
			name:            "以太坊格式地址",
			hexData:         "095ea7b300000000000000000000000099469fd5aa08cfe836395ce993c0ebd567e2c14f000000000000000000000000000000000000000000000000000000012c09b8fc",
			expectedSpender: "TPwezUWpEGmFBENNWJHwXHRG1D2NCEEt5s", // 对应的Tron地址
			expectedValue:   "5033801980",                         // 0x12c09b8fc = 5000000000
			expectError:     false,
		},
		{
			name:            "Tron格式地址（包含0x41前缀）",
			hexData:         "095ea7b300000000000000000000004199469fd5aa08cfe836395ce993c0ebd567e2c14fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff",
			expectedSpender: "TPwezUWpEGmFBENNWJHwXHRG1D2NCEEt5s",                                             // 同一个地址
			expectedValue:   "115792089237316195423570985008687907853269984665640564039457584007913129639935", // 最大uint256值
			expectError:     false,
		},
		{
			name:            "无效数据长度",
			hexData:         "095ea7b3",
			expectedSpender: "",
			expectedValue:   "",
			expectError:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			data, err := hex.DecodeString(tt.hexData)
			if err != nil {
				t.Fatalf("Failed to decode hex data: %v", err)
			}

			spender, value, err := decodeTRC20ApprovalData(data)

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			if spender != tt.expectedSpender {
				t.Errorf("Expected spender %s, got %s", tt.expectedSpender, spender)
			}

			if value != tt.expectedValue {
				t.Errorf("Expected value %s, got %s", tt.expectedValue, value)
			}
		})
	}
}

func TestParseTRC20TransferEventLog(t *testing.T) {
	tests := []struct {
		name          string
		topics        [][]byte
		data          []byte
		expectedFrom  string
		expectedTo    string
		expectedValue string
		expectError   bool
	}{
		{
			name: "valid Transfer event",
			topics: [][]byte{
				// Transfer event signature: keccak256("Transfer(address,address,uint256)")
				hexToBytes("ddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef"),
				// from address (padded to 32 bytes)
				hexToBytes("000000000000000000000000ba58a0e3cb4bd6c95bebc92733acae0a877e0dad"),
				// to address (padded to 32 bytes)
				hexToBytes("000000000000000000000000686c8219f66cada3e0db325383be4ca4b9899f5e"),
			},
			data:          hexToBytes("0000000000000000000000000000000000000000000000000000000000e7a0e0"), // 15180000
			expectedFrom:  "TSxWrkgS33YcVdzRX4wvdtYzxyPc3VA769",
			expectedTo:    "TKVMGHt7brRqsV3ghhkwWY7eS4mXxV5AB5",
			expectedValue: "15180000",
			expectError:   false,
		},
		{
			name: "insufficient topics",
			topics: [][]byte{
				hexToBytes("ddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef"),
			},
			data:        hexToBytes("0000000000000000000000000000000000000000000000000000000000e7a0e0"),
			expectError: true,
		},
		{
			name: "wrong event signature",
			topics: [][]byte{
				hexToBytes("0000000000000000000000000000000000000000000000000000000000000000"), // wrong signature
				hexToBytes("000000000000000000000000ba58a0e3cb4bd6c95bebc92733acae0a877e0dad"),
				hexToBytes("000000000000000000000000686c8219f66cada3e0db325383be4ca4b9899f5e"),
			},
			data:        hexToBytes("0000000000000000000000000000000000000000000000000000000000e7a0e0"),
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			eventLog := &core.TransactionInfo_Log{
				Topics: tt.topics,
				Data:   tt.data,
			}

			from, to, value, err := parseTRC20TransferEventLog(eventLog)

			if tt.expectError {
				assert.Error(t, err)
				return
			}

			assert.NoError(t, err)
			assert.Equal(t, tt.expectedFrom, from)
			assert.Equal(t, tt.expectedTo, to)
			assert.Equal(t, tt.expectedValue, value)
		})
	}
}

func hexToBytes(s string) []byte {
	bytes, _ := hex.DecodeString(s)
	return bytes
}
