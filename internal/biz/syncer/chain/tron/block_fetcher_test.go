package tron

import (
	"byd_wallet/common/constant"
	"byd_wallet/model"
	"context"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestBlockFetcher_FetchTransactions(t *testing.T) {
	//t.Skip()
	f := NewBlockFetcher(log.DefaultLogger)
	endpoints := []string{
		"https://cool-proportionate-wind.tron-mainnet.quiknode.pro/cea0ad09c07a84b7071806a79651deafba758175/jsonrpc",
	}
	assert.NoError(t, f.SetEndpoints(endpoints))
	f.SetBlockchainNetWork(&model.BlockchainNetwork{ChainIndex: constant.TronChainIndex})
	txs, err := f.FetchTransactions(context.Background(), 74260374)
	assert.NoError(t, err)
	userAddress := "TVox5dA6wQNA86gYfeM1pMafyzyFyj7GH5"
	for tx := range txs {
		if tx.Value == "" {
			panic("tx value is empty")
		}
		if tx.FromAddress == userAddress || tx.ToAddress == userAddress {
			fmt.Printf("%+v\n", *tx)
		}
	}
}
