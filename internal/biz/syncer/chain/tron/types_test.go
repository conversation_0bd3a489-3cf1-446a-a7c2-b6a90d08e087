package tron

import (
	"github.com/fbsobreira/gotron-sdk/pkg/proto/api"
	"github.com/fbsobreira/gotron-sdk/pkg/proto/core"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestBlock_Unix(t *testing.T) {
	testSuits := []struct {
		name string
		ts   int64
		want int64
	}{
		{
			name: "unix mill",
			ts:   1749173577000,
			want: 1749173577,
		},
		{
			name: "unix mill 2",
			ts:   1749173578001,
			want: 1749173578,
		},
	}
	for _, suit := range testSuits {
		t.Run(suit.name, func(t *testing.T) {
			b := Block{
				BlockExtention: &api.BlockExtention{
					BlockHeader: &core.BlockHeader{
						RawData: &core.BlockHeaderRaw{
							Timestamp: suit.ts,
						},
					},
				},
			}
			assert.Equal(t, suit.want, b.Unix())
		})
	}
}
