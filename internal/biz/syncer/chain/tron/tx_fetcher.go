package tron

import (
	"byd_wallet/model"
	"context"
	"github.com/shopspring/decimal"
)

type TransactionFetcher struct {
	cli *RoundRobinClient
}

func NewTransactionFetcher(cli *RoundRobinClient) *TransactionFetcher {
	return &TransactionFetcher{cli: cli}
}

func (t *TransactionFetcher) GetTransactionByHash(ctx context.Context, chainIndex int64, hash string) (*model.Transaction, error) {
	txi, err := t.cli.Last().GetTransactionInfoByID(hash)
	if err != nil {
		return nil, err
	}
	return &model.Transaction{
		TxHash:      hash,
		BlockNumber: txi.BlockNumber,
		ChainIndex:  chainIndex,
		Fee:         decimal.NewFromInt(txi.Receipt.NetFee + txi.Receipt.EnergyFee).String(),
	}, nil
}
