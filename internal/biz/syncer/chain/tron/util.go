package tron

import (
	"encoding/hex"
	"fmt"
	"github.com/fbsobreira/gotron-sdk/pkg/proto/core"
	"math/big"

	tCommon "github.com/fbsobreira/gotron-sdk/pkg/common"

	"github.com/btcsuite/btcutil/base58"
	"github.com/ethereum/go-ethereum/common"
	"github.com/fbsobreira/gotron-sdk/pkg/address"
)

func bytes2AddressString(b []byte) string {
	return address.Address(b).String()
}

func etherAddress2Tron(eAddr common.Address) address.Address {
	// 创建波场地址（21字节：0x41前缀 + 20字节以太坊地址）
	tronAddr := make([]byte, 21)
	tronAddr[0] = 0x41
	copy(tronAddr[1:], eAddr.Bytes())
	// 转换为Base58格式的波场地址字符串
	return tronAddr
}

func trc20Bytes2AddressString(b []byte) string {
	return base58.CheckEncode(b, 0x41)
}

func decodeTRC20TransferData(data []byte) (string, string, error) {
	if len(data) != 4+32*2 {
		return "", "", fmt.Errorf("invalid trc20 transfer data length")
	}

	// 解析地址参数
	addressParam := data[4:36]
	addressBytes := addressParam[12:32] // 提取后20字节地址

	// 生成Tron地址（Base58Check）
	tronAddress := base58.CheckEncode(addressBytes, 0x41) // 修改这里，使用正确的版本字节

	// 解析金额参数
	amountParam := data[36:68]
	amount := new(big.Int).SetBytes(amountParam)
	return tronAddress, amount.String(), nil
}

func decodeTRC20TransferFromData(data []byte) (string, string, string, error) {
	if len(data) < 4+32*3 {
		return "", "", "", fmt.Errorf("invalid transferFrom data length")
	}

	// 解析from地址
	fromParam := data[4:36]
	fromBytes := fromParam[12:32]
	from := trc20Bytes2AddressString(fromBytes)

	// 解析to地址
	toParam := data[36:68]
	toBytes := toParam[12:32]
	to := trc20Bytes2AddressString(toBytes)

	// 解析金额
	amountParam := data[68:100]
	amount := new(big.Int).SetBytes(amountParam)
	return from, to, amount.String(), nil
}

func decodeTRC20ApprovalData(data []byte) (string, string, error) {
	if len(data) != 4+32*2 {
		return "", "", fmt.Errorf("invalid trc20 approval data length")
	}

	// 解析spender地址参数
	spenderParam := data[4:36]
	spenderBytes := spenderParam[12:32] // 提取后20字节地址

	// 生成Tron地址（Base58Check）
	spender := base58.CheckEncode(spenderBytes, 0x41)

	// 解析金额参数
	valueParam := data[36:68]
	value := new(big.Int).SetBytes(valueParam)
	return spender, value.String(), nil
}

func decodeTRC20MethodID(data []byte) string {
	if len(data) < 4 {
		return ""
	}
	return tCommon.BytesToHexString(data[:4])
}

// parseTRC20TransferEventLog 解析 TRC20 Transfer 事件日志
func parseTRC20TransferEventLog(eventLog *core.TransactionInfo_Log) (from, to, value string, err error) {
	// 检查是否是 Transfer 事件
	if len(eventLog.Topics) < 3 {
		return "", "", "", fmt.Errorf("insufficient topics for Transfer event")
	}

	// 验证事件签名
	eventSignature := hex.EncodeToString(eventLog.Topics[0])
	expectedSignature := trc20TransferEventSignature[2:] // 去掉 "0x" 前缀
	if eventSignature != expectedSignature {
		return "", "", "", fmt.Errorf("not a Transfer event, got: %s, expected: %s", eventSignature, expectedSignature)
	}

	// 解析 from 地址 (topic[1])
	if len(eventLog.Topics[1]) != 32 {
		return "", "", "", fmt.Errorf("invalid from address length: %d", len(eventLog.Topics[1]))
	}
	fromBytes := eventLog.Topics[1][12:32] // EVM 地址是32字节，实际地址是后20字节
	from = trc20Bytes2AddressString(fromBytes)

	// 解析 to 地址 (topic[2])
	if len(eventLog.Topics[2]) != 32 {
		return "", "", "", fmt.Errorf("invalid to address length: %d", len(eventLog.Topics[2]))
	}
	toBytes := eventLog.Topics[2][12:32] // EVM 地址是32字节，实际地址是后20字节
	to = trc20Bytes2AddressString(toBytes)

	// 解析转账金额 (data 部分)
	if len(eventLog.Data) != 32 {
		return "", "", "", fmt.Errorf("invalid data length for Transfer event: %d", len(eventLog.Data))
	}
	amount := new(big.Int).SetBytes(eventLog.Data)
	value = amount.String()

	return from, to, value, nil
}
