package tron

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func Test_parseURL(t *testing.T) {
	host, key, err := parseURL("https://newest-sly-emerald.tron-mainnet.quiknode.pro/d6bb28cd0d5a81563f3afc3ae00570711ba180a3/jsonrpc")
	assert.NoError(t, err)
	assert.Equal(t, "newest-sly-emerald.tron-mainnet.quiknode.pro", host)
	assert.Equal(t, "d6bb28cd0d5a81563f3afc3ae00570711ba180a3", key)
}

func TestClient(t *testing.T) {
	endpoint := "https://cool-proportionate-wind.tron-mainnet.quiknode.pro/cea0ad09c07a84b7071806a79651deafba758175/jsonrpc"
	//endpoint := "https://newest-sly-emerald.tron-mainnet.quiknode.pro/d6bb28cd0d5a81563f3afc3ae00570711ba180a3/jsonrpc"
	cli, err := NewRoundRobinClient([]string{endpoint})
	assert.NoError(t, err)
	defer cli.Close()
	block, err := cli.Last().GetNowBlock()
	assert.NoError(t, err)
	assert.True(t, block.GetBlockHeader().GetRawData().Number > 0)
}
