package tron

import "sync"

func NewRoundRobinClient(endpoints []string) (*RoundRobinClient, error) {
	r := &RoundRobinClient{}
	for _, endpoint := range endpoints {
		cli, cf, err := NewGRPCClient(endpoint)
		if err != nil {
			return nil, err
		}
		r.clients = append(r.clients, cli)
		r.cleanups = append(r.cleanups, cf)
	}
	return r, nil
}

type RoundRobinClient struct {
	clients  []*GRPCClient
	index    int
	mu       sync.Mutex
	cleanups []func()
}

func (r *RoundRobinClient) Last() *GRPCClient {
	return r.clients[len(r.clients)-1]
}

func (r *RoundRobinClient) Next() *GRPCClient {
	r.mu.Lock()
	defer r.mu.Unlock()
	if len(r.clients) == 0 {
		return nil
	}
	item := r.clients[r.index]
	r.index = (r.index + 1) % len(r.clients) // 索引循环递增
	return item
}

func (r *RoundRobinClient) Close() {
	for _, cleanup := range r.cleanups {
		cleanup()
	}
}
