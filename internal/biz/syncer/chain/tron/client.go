package tron

import (
	"context"
	"crypto/tls"
	"errors"
	"fmt"
	"net/url"
	"strings"
	"time"

	"github.com/fbsobreira/gotron-sdk/pkg/client"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
)

func parseURL(rawURL string) (string, string, error) {
	u, err := url.Parse(rawURL)
	if err != nil {
		return "", "", err
	}
	paths := strings.Split(u.Path, "/")
	if len(paths) < 2 {
		return "", "", errors.New("token is empty")
	}
	return u.Host, paths[1], nil
}

func NewGRPCClient(url string) (*GRPCClient, func(), error) {
	endpoint, token, err := parseURL(url)
	if err != nil {
		return nil, nil, err
	}
	return newGRPCClient(endpoint, token)
}

func newGRPCClient(endpoint, token string) (*GRPCClient, func(), error) {
	opts := []grpc.DialOption{
		grpc.WithTransportCredentials(credentials.NewTLS(&tls.Config{})),
		grpc.WithPerRPCCredentials(&auth{token}),
	}

	conn := client.NewGrpcClient(endpoint + ":50051")
	conn.SetTimeout(time.Minute)
	if err := conn.Start(opts...); err != nil {
		return nil, nil, fmt.Errorf("failed to start grpc client: %w", err)
	}
	return &GRPCClient{conn}, func() {
		_ = conn.Conn.Close()
	}, nil
}

type auth struct {
	token string
}

func (a *auth) GetRequestMetadata(ctx context.Context, uri ...string) (map[string]string, error) {
	return map[string]string{
		"x-token": a.token,
	}, nil
}

func (a *auth) RequireTransportSecurity() bool {
	return false
}

type GRPCClient struct {
	*client.GrpcClient
}
