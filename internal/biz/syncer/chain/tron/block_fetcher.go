package tron

import (
	"byd_wallet/model"
	"context"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
)

func NewBlockFetcher(logger log.Logger) *BlockFetcher {
	return &BlockFetcher{
		log: log.<PERSON><PERSON>elper(logger),
	}
}

type BlockFetcher struct {
	log               *log.Helper
	cli               *RoundRobinClient
	blockchainNetWork *model.BlockchainNetwork
}

func (b *BlockFetcher) FetchTransactions(ctx context.Context, height int64) (<-chan *model.Transaction, error) {
	txChan := make(chan *model.Transaction)
	block, err := b.fetchBlock(height)
	if err != nil {
		return nil, fmt.Errorf("failed to get transactions for block %d: %w", height, err)
	}
	dbTxs, err := blockToLocalTxs(b.log, block, b.blockchainNetWork.ChainIndex)
	if err != nil {
		return nil, err
	}
	go func() {
		defer close(txChan)

		for _, tx := range dbTxs {
			select {
			case txChan <- tx:
			case <-ctx.Done():
				b.log.Infow(log.<PERSON><PERSON>ult<PERSON><PERSON><PERSON><PERSON>, "FetchTransactions: context cancelled", "blockHeight", height)
				return
			}
		}
	}()

	return txChan, nil
}

func (b *BlockFetcher) fetchBlock(height int64) (*Block, error) {
	cli := b.cli.Next()
	be, err := cli.GetBlockByNum(height)
	if err != nil {
		return nil, err
	}
	bi, err := cli.GetBlockInfoByNum(height)
	if err != nil {
		return nil, err
	}
	if len(be.Transactions) != len(bi.TransactionInfo) {
		return nil, fmt.Errorf("GetBlockByNum,GetBlockInfoByNum tx length not match, blockHeight: %d", height)
	}
	return &Block{
		BlockExtention:  be,
		TransactionInfo: bi.GetTransactionInfo(),
	}, nil
}

func (b *BlockFetcher) GetLatestBlockHeight(ctx context.Context) (int64, error) {
	cli := b.cli.Last()
	block, err := cli.GetNowBlock()
	if err != nil {
		return 0, err
	}
	return block.GetBlockHeader().GetRawData().Number, nil
}

func (b *BlockFetcher) SetEndpoints(endpoints []string) error {
	cli, err := NewRoundRobinClient(endpoints)
	if err != nil {
		return err
	}
	b.cli = cli
	return nil
}

func (b *BlockFetcher) BlockchainNetWork() *model.BlockchainNetwork {
	return b.blockchainNetWork
}

func (b *BlockFetcher) SetBlockchainNetWork(network *model.BlockchainNetwork) {
	b.blockchainNetWork = network
}

func (b *BlockFetcher) Close() error {
	b.cli.Close()
	return nil
}

func (b *BlockFetcher) GetDebugTraceTransaction(txn string) ([]*model.Transaction, error) {
	// TODO
	return nil, nil
}
