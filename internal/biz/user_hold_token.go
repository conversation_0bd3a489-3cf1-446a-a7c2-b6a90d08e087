package biz

import (
	"byd_wallet/model"
	"context"

	"github.com/go-kratos/kratos/v2/log"
)

type UserHoldTokenViewFilter struct {
	UserAddrs []struct {
		ChainIndex int64
		Address    string
	}
}

type UserHoldTokenView struct {
	WalletAddress string
	ChainIndex    int64
	Name          string
	Symbol        string
	Decimals      int64
	LogoUrl       string
	Address       string
	ChainId       string
}

type UserHoldTokenRepo interface {
	BatchSave(ctx context.Context, us []*model.UserHoldToken) error
	ListViewByFilter(ctx context.Context, filter *UserHoldTokenViewFilter) ([]*UserHoldTokenView, error)
}

type UserHoldTokenUsecase struct {
	log  *log.Helper
	repo UserHoldTokenRepo
}

func NewUserHoldTokenUsecase(repo UserHoldTokenRepo, logger log.Logger) *UserHoldTokenUsecase {
	return &UserHoldTokenUsecase{
		log:  log.NewHelper(logger),
		repo: repo,
	}
}

func (uc *UserHoldTokenUsecase) ListUserHoldTokenView(ctx context.Context, filter *UserHoldTokenViewFilter) ([]*UserHoldTokenView, error) {
	list, err := uc.repo.ListViewByFilter(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (uc *UserHoldTokenUsecase) BatchCreateUserHoldToken(ctx context.Context, us []*model.UserHoldToken) error {
	return uc.repo.BatchSave(ctx, us)
}
