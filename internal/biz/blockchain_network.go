package biz

import (
	"byd_wallet/model"
	"context"

	"github.com/go-kratos/kratos/v2/log"
)

type BlockchainNetworkFilter struct {
	ChainName string
}

type BlockchainNetworkRepo interface {
	ListByFilter(ctx context.Context, filter *BlockchainNetworkFilter) (list []*model.BlockchainNetwork, err error)
	UpdateByUpdates(ctx context.Context, id uint, updates map[string]interface{}) error
	FindByIndex(ctx context.Context, chainIndex int64) (*model.BlockchainNetwork, error)
	All(ctx context.Context) ([]*model.BlockchainNetwork, error)
	UpdateSortOrder(ctx context.Context, id uint, current, target int64) error
	ListEVM(ctx context.Context) ([]*model.BlockchainNetwork, error)
}

type BlockchainNetworkUsecase struct {
	log *log.Helper

	repo BlockchainNetworkRepo
}

func NewBlockchainNetworkUsecase(logger log.Logger,
	repo BlockchainNetworkRepo) *BlockchainNetworkUsecase {
	return &BlockchainNetworkUsecase{
		log:  log.<PERSON>elper(logger),
		repo: repo,
	}
}

func (uc *BlockchainNetworkUsecase) AllBlockchainNetwork(ctx context.Context) (
	list []*model.BlockchainNetwork, err error) {
	return uc.repo.All(ctx)
}

func (uc *BlockchainNetworkUsecase) ListBlockchainNetwork(ctx context.Context, filter *BlockchainNetworkFilter) (
	list []*model.BlockchainNetwork, err error) {
	return uc.repo.ListByFilter(ctx, filter)
}

func (uc *BlockchainNetworkUsecase) UpdateBlockchainNetworkByUpdates(ctx context.Context, id uint, updates map[string]interface{}) error {
	return uc.repo.UpdateByUpdates(ctx, id, updates)
}

func (uc *BlockchainNetworkUsecase) UpdateBlockchainNetworkSortOrder(ctx context.Context, id uint, current, target int64) error {
	return uc.repo.UpdateSortOrder(ctx, id, current, target)
}
