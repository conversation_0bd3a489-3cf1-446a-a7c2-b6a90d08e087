package base

import "context"

type SortType int

const (
	SortTypeUp   SortType = iota + 1 // 向上排序
	SortTypeDown                     // 向下排序
	SortTypeTop                      // 置顶
)

type SortInput struct {
	ID       uint // 记录ID
	SortType SortType
	Model    any // 传指定表的指针
	// 如果表有分类别排序的，需要传这两个字段
	Where []SortWhere
}

type SortWhere struct {
	Column      string
	ColumnValue any
}

type SortRepo interface {
	Sort(context.Context, *SortInput) error
}
