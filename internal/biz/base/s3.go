package base

import (
	"context"
	"io"
	"net/http"
	"time"
)

type S3Repo interface {
	Upload(ctx context.Context, key string, body io.Reader) (string, error)
	UploadFileByFileUrl(ctx context.Context, fileUrl, key string) (string, error)
	UploadImageFileByFileUrl(ctx context.Context, fileUrl, key string) (string, error)
	GeneratePresignedRequest(ctx context.Context, key string, expires time.Duration) (*PresignedHTTPRequest, error)
	GeneratePresignedRequest4ImagePng(ctx context.Context, key string, expires time.Duration) (*PresignedHTTPRequest, error)
	DeleteObject(ctx context.Context, key string) error
	ToAppAccessUrl(ctx context.Context, s3Url string) string
}

type PresignedHTTPRequest struct {
	URL          string
	Method       string
	SignedHeader http.Header
}
