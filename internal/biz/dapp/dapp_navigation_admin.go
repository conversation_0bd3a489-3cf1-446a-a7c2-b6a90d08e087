package dapp

import (
	"byd_wallet/model"
	"context"
)

// AdminDappNavigation admin首页导航栏数据
type AdminDappNavigation struct {
	ID uint
	// 是否推荐到首页导航
	Show              bool
	DappCategoryI18Ns []*model.DappCategoryI18N
	// app数量
	AppCount int
	// 热门数量
	HotCount int
	// 序号
	SortOrder int
}

func (uc AdminUsecase) ListDappNavigation(ctx context.Context) ([]*AdminDappNavigation, error) {
	return uc.repo.ListDappNavigation(ctx)
}

func (uc AdminUsecase) CreateDappNavigation(ctx context.Context, navigation *model.DappNavigation) error {
	lastOrder, err := uc.repo.LastDappNavigationSortOrder(ctx)
	if err != nil {
		return err
	}
	navigation.SortOrder = lastOrder + 1
	return uc.repo.CreateDappNavigation(ctx, navigation)
}

func (uc AdminUsecase) DeleteDappNavigation(ctx context.Context, id uint) error {
	return uc.repo.DeleteDappNavigation(ctx, id)
}

func (uc AdminUsecase) BatchUpdateDappNavigation(ctx context.Context, navs []*model.DappNavigation) error {
	return uc.tx.ExecTx(ctx, func(ctx context.Context) error {
		return uc.repo.BatchUpdateDappNavigation(ctx, navs)
	})
}

func (uc AdminUsecase) SortDappNavigation(ctx context.Context, sort *Sort) error {
	sort.Model = &model.DappNavigation{}
	return uc.tx.ExecTx(ctx, func(ctx context.Context) error {
		return uc.repo.Sort(ctx, sort)
	})
}
