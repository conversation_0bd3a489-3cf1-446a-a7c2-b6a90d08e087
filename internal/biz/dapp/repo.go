package dapp

import (
	"byd_wallet/model"
	"context"
)

type Sort struct {
	ID uint
	// ture 向上移动
	// false 向下移动
	Direction bool
	// 传指定表的指针
	Model any
}

type Repo interface {
	FilterDapps(ctx context.Context, filter DappsFilter) ([]*model.Dapp, error)
	SearchDappByKey(ctx context.Context, key string) ([]*model.Dapp, error)
	GetDappCategoryForApp(ctx context.Context, id uint, dappLimit ...int) (*model.DappCategory, error)
	ListDappIndex(ctx context.Context, dappCategoryLimit, dappTopicLimit int) ([]any, error)
	ListDappNavigation(ctx context.Context) ([]*model.DappNavigation, error)
	GetDappTopic(ctx context.Context, id uint, dappLimit ...int) (*model.DappTopic, error)
}

type AdminRepo interface {
	Sort(ctx context.Context, sort *Sort) error

	CreateDapp(context.Context, *model.Dapp) error
	DeleteDapp(ctx context.Context, id uint) error
	UpdateDapp(context.Context, *model.Dapp) error
	GetDapp(ctx context.Context, id uint) (*model.Dapp, error)
	ListDapp(ctx context.Context) ([]*model.Dapp, error)

	CreateDappBlockchainNetworks(context.Context, []*model.DappBlockchainNetwork) error
	DeleteDappBlockchainNetworks(ctx context.Context, dappID uint) error

	CreateDappCategory(context.Context, *model.DappCategory) error
	DeleteDappCategory(ctx context.Context, id uint) error
	UpdateDappCategory(context.Context, *model.DappCategory) error
	ListDappCategory(ctx context.Context) ([]*AdminDappCategory, error)
	ListCategoryDapp(ctx context.Context, categoryID uint) ([]*model.Dapp, error)

	CreateDappCategoryRel(context.Context, *model.DappCategoryRel) error
	LastDappCategoryRelSortOrder(ctx context.Context, categoryID uint) (int, error)
	DeleteDappCategoryRel(ctx context.Context, categoryID, dappID uint) error

	CreateDappTopic(context.Context, *model.DappTopic) error
	DeleteDappTopic(ctx context.Context, id uint) error
	UpdateDappTopic(context.Context, *model.DappTopic) error
	GetDappTopic(ctx context.Context, id uint) (*model.DappTopic, error)
	ListDappTopic(ctx context.Context) ([]*AdminDappTopic, error)
	CreateDappTopicRel(context.Context, *model.DappTopicRel) error
	LastDappTopicRelSortOrder(ctx context.Context, topicID uint) (int, error)
	DeleteDappTopicRel(ctx context.Context, topicID, dappID uint) error
	ListTopicDapp(ctx context.Context, topicID uint) ([]*model.Dapp, error)

	CreateDappNavigation(context.Context, *model.DappNavigation) error
	LastDappNavigationSortOrder(context.Context) (int, error)
	ListDappNavigation(ctx context.Context) ([]*AdminDappNavigation, error)
	DeleteDappNavigation(ctx context.Context, id uint) error
	BatchUpdateDappNavigation(ctx context.Context, navs []*model.DappNavigation) error

	CreateDappIndex(context.Context, *model.DappIndex) error
	DeleteDappIndex(ctx context.Context, id uint) error
	LastDappIndexSortOrder(context.Context) (int, error)
	ListDappIndex(ctx context.Context) ([]*AdminDappIndex, error)
	BatchUpdateDappIndex(ctx context.Context, indexes []*model.DappIndex) error
}

type BlockchainNetworkRepo interface {
	GetChainByIndex(chainIndex int64) (*model.BlockchainNetwork, error)
}
