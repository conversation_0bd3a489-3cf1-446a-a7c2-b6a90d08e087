package dapp

import (
	"byd_wallet/model"
	"context"
)

type AdminDappIndex struct {
	// DappIndex.ID
	ID uint
	// 分类/专题名
	Name      string
	OwnerType string
	// 序号
	SortOrder int
	Dapps     []*model.Dapp
}

func (uc AdminUsecase) CreateDappIndex(ctx context.Context, index *model.DappIndex) error {
	lastOrder, err := uc.repo.LastDappIndexSortOrder(ctx)
	if err != nil {
		return err
	}
	index.SortOrder = lastOrder + 1
	return uc.repo.CreateDappIndex(ctx, index)
}

func (uc AdminUsecase) DeleteDappIndex(ctx context.Context, id uint) error {
	return uc.repo.DeleteDappIndex(ctx, id)
}

func (uc AdminUsecase) ListDappIndex(ctx context.Context) ([]*AdminDappIndex, error) {
	return uc.repo.ListDappIndex(ctx)
}

func (uc AdminUsecase) BatchUpdateDappIndex(ctx context.Context, indexes []*model.DappIndex) error {
	return uc.tx.ExecTx(ctx, func(ctx context.Context) error {
		return uc.repo.BatchUpdateDappIndex(ctx, indexes)
	})
}

func (uc AdminUsecase) SortDappIndex(ctx context.Context, sort *Sort) error {
	sort.Model = &model.DappIndex{}
	return uc.tx.ExecTx(ctx, func(ctx context.Context) error {
		return uc.repo.Sort(ctx, sort)
	})
}
