package dapp

import (
	"byd_wallet/model"
	"context"
)

// AdminDappTopic admin专题展示数据
type AdminDappTopic struct {
	ID uint
	// 是否展示
	Show bool
	// 背景图url
	BackgroundUrl string
	// 专题多语言
	DappTopicI18Ns []*model.DappTopicI18N
	// app数量
	AppCount int
	// 创建时间 Unix时间戳
	CreatedAt int64
}

func (uc AdminUsecase) CreateDappTopic(ctx context.Context, topic *model.DappTopic) error {
	topic.BackgroundUrl = uc.s3.ToAppAccessUrl(ctx, topic.BackgroundUrl)
	return uc.repo.CreateDappTopic(ctx, topic)
}

func (uc AdminUsecase) DeleteDappTopic(ctx context.Context, id uint) error {
	return uc.repo.DeleteDappTopic(ctx, id)
}

// UpdateDappTopic 更新DAPP专题
func (uc AdminUsecase) UpdateDappTopic(ctx context.Context, topic *model.DappTopic) error {
	topic.BackgroundUrl = uc.s3.ToAppAccessUrl(ctx, topic.BackgroundUrl)
	return uc.repo.UpdateDappTopic(ctx, topic)
}

// ListDappTopic 查询DAPP专题列表
func (uc AdminUsecase) ListDappTopic(ctx context.Context) ([]*AdminDappTopic, error) {
	return uc.repo.ListDappTopic(ctx)
}

func (uc AdminUsecase) CreateDappTopicRel(ctx context.Context, rel *model.DappTopicRel) error {
	sortOrder, err := uc.repo.LastDappTopicRelSortOrder(ctx, rel.DappTopicID)
	if err != nil {
		return err
	}
	if rel.SortOrder == 0 {
		rel.SortOrder = sortOrder + 1
	}
	return uc.repo.CreateDappTopicRel(ctx, rel)
}

func (uc AdminUsecase) DeleteDappTopicRel(ctx context.Context, rel *model.DappTopicRel) error {
	return uc.repo.DeleteDappTopicRel(ctx, rel.DappTopicID, rel.DappID)
}

func (uc AdminUsecase) ListTopicDapp(ctx context.Context, topicID uint) ([]*model.Dapp, error) {
	return uc.repo.ListTopicDapp(ctx, topicID)
}
