package dapp

import (
	"byd_wallet/internal/biz/base"
	"byd_wallet/internal/biz/dbtx"
	"byd_wallet/model"
	"context"

	"github.com/go-kratos/kratos/v2/log"
)

const (
	InvalidChainIndex        = -1
	InvalidChainID    string = "-1"
)

type Usecase struct {
	repo Repo
	tx   dbtx.DBTx
	s3   base.S3Repo
	log  *log.Helper
}

func NewUsecase(repo Repo, tx dbtx.DBTx, s3 base.S3Repo, logger log.Logger) *Usecase {
	return &Usecase{repo: repo, tx: tx, s3: s3, log: log.NewHelper(logger)}
}

type DappsFilter struct {
	ChainIndex     int64
	ChainID        string
	DappCategoryID uint
}

// FilterChain 是否根据链筛选
func (d DappsFilter) FilterChain() bool {
	return d.ChainIndex != InvalidChainIndex || d.ChainID != InvalidChainID
}

func (uc Usecase) FilterDapps(ctx context.Context, filter DappsFilter) ([]*model.Dapp, error) {
	return uc.repo.FilterDapps(ctx, filter)
}

func (uc Usecase) SearchDappByKey(ctx context.Context, key string) ([]*model.Dapp, error) {
	return uc.repo.SearchDappByKey(ctx, key)
}
