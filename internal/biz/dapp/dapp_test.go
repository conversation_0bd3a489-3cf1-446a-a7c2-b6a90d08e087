package dapp

import "testing"

func TestDappsFilter_Filter<PERSON>hain(t *testing.T) {
	type fields struct {
		ChainIndex     int64
		ChainID        string
		DappCategoryID uint
	}
	tests := []struct {
		name   string
		fields fields
		want   bool
	}{
		{
			name: "filter chain",
			fields: fields{
				ChainIndex: 0,
				ChainID:    "1",
			},
			want: true,
		},
		{
			name: "filter chain",
			fields: fields{
				ChainIndex: 0,
				ChainID:    "-1",
			},
			want: true,
		},
		{
			name: "filter chain 2",
			fields: fields{
				ChainIndex: -1,
				ChainID:    "1",
			},
			want: true,
		},
		{
			name: "no filter chain",
			fields: fields{
				ChainIndex: -1,
				ChainID:    "-1",
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := DappsFilter{
				ChainIndex:     tt.fields.ChainIndex,
				ChainID:        tt.fields.ChainID,
				DappCategoryID: tt.fields.DappCategoryID,
			}
			if got := d.Filter<PERSON>hain(); got != tt.want {
				t.<PERSON>("FilterChain() = %v, want %v", got, tt.want)
			}
		})
	}
}
