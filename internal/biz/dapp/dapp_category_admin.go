package dapp

import (
	"byd_wallet/model"
	"context"
)

// AdminDappCategory admin分类展示数据
type AdminDappCategory struct {
	ID uint
	// 是否展示
	Show              bool
	DappCategoryI18Ns []*model.DappCategoryI18N
	// app数量
	AppCount int
	// 热门数量
	HotCount int
	// 是否推荐到首页导航
	Navigation bool
}

func (uc AdminUsecase) CreateDappCategory(ctx context.Context, category *model.DappCategory) error {
	return uc.repo.CreateDappCategory(ctx, category)
}

func (uc AdminUsecase) DeleteDappCategory(ctx context.Context, id uint) error {
	return uc.repo.DeleteDappCategory(ctx, id)
}

func (uc AdminUsecase) UpdateDappCategory(ctx context.Context, category *model.DappCategory) error {
	return uc.repo.UpdateDappCategory(ctx, category)
}

func (uc AdminUsecase) ListDappCategory(ctx context.Context) ([]*AdminDappCategory, error) {
	return uc.repo.ListDappCategory(ctx)
}

func (uc AdminUsecase) CreateDappCategoryRel(ctx context.Context, rel *model.DappCategoryRel) error {
	sortOrder, err := uc.repo.LastDappCategoryRelSortOrder(ctx, rel.DappCategoryID)
	if err != nil {
		return err
	}
	if rel.SortOrder == 0 {
		rel.SortOrder = sortOrder
	}
	return uc.repo.CreateDappCategoryRel(ctx, rel)
}

func (uc AdminUsecase) DeleteDappCategoryRel(ctx context.Context, rel *model.DappCategoryRel) error {
	return uc.repo.DeleteDappCategoryRel(ctx, rel.DappCategoryID, rel.DappID)
}

func (uc AdminUsecase) ListCategoryDapp(ctx context.Context, categoryID uint) ([]*model.Dapp, error) {
	return uc.repo.ListCategoryDapp(ctx, categoryID)
}
