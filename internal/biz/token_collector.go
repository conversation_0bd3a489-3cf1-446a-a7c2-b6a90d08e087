package biz

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz/base"
	"byd_wallet/model"
	"byd_wallet/utils"
	"context"
	"fmt"
)

type TokenCollector struct {
	repo   TokenAssetRepo
	tcRepo TokenContractRepo
	s3     base.S3Repo
}

func NewTokenCollector(repo TokenAssetRepo, tcRepo TokenContractRepo, s3 base.S3Repo) *TokenCollector {
	return &TokenCollector{repo: repo, tcRepo: tcRepo, s3: s3}
}

type CollectTokenInput struct {
	ChainIndex int64
	Address    string // token合约地址
	Symbol     string
	Decimals   int64
	// --- optional
	LogoUrl    string
	CoinID     string
	DeployedAt int64 // 合约部署时间戳unix
	Name       string
}

func (t TokenCollector) CollectToken(ctx context.Context, input *CollectTokenInput) (*model.TokenAsset, error) {
	chainIndex := input.ChainIndex
	address := input.Address
	exist, err := t.repo.ExistsByChainIndexAndAddress(ctx, chainIndex, address)
	if err != nil {
		return nil, err
	}
	if exist {
		return t.repo.FindByChainIndexAndAddress(ctx, chainIndex, address)
	}
	ta := &model.TokenAsset{
		Address:         address,
		ChainIndex:      chainIndex,
		ChainId:         constant.ChainIndex2ChainID[chainIndex],
		TokenDeployedAt: input.DeployedAt,
		TokenType:       constant.GetTokenType(chainIndex),
		IsDisplay:       true,
		// --- optional
		CoinID: input.CoinID,
		// --- from API
		Symbol:   model.ConvValidSymbol(input.Symbol),
		Name:     input.Name,
		Decimals: input.Decimals,
	}
	if input.LogoUrl != "" {
		s3Key := fmt.Sprintf("token/logo/%s", utils.MD5Hash(fmt.Sprintf("%d%s", chainIndex, address)))
		resLogoUrl, err := t.s3.UploadImageFileByFileUrl(ctx, input.LogoUrl, s3Key)
		if err != nil {
			return nil, err
		}
		ta.LogoUrl = resLogoUrl
	}
	return t.repo.Save(ctx, ta)
}
