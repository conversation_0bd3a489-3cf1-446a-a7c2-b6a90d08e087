package biz

import (
	"byd_wallet/model"
	"context"
	"fmt"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/shopspring/decimal"
)

type QuerySimpleKlineReq struct {
	TokenIds []*TokenID
}

type SimpleKline struct {
	ChainIndex  int64
	Address     string
	Times       []int64
	ClosePrices []string
	Currency    string
}

type QueryCoinPriceReq struct {
	TokenIds []*TokenID
}

type TokenID struct {
	ChainIndex int64
	Address    string
}

type CoinPrice struct {
	ChainIndex    int64
	Address       string
	Price         decimal.Decimal
	LastUpdatedAt int64
}

type CoinTokenRel struct {
	CoinID2TokenID map[string][]*TokenID
	UniqueIDs      []string
}

type PopularTokenFilter struct {
	Page         int64
	PageSize     int64
	ChainIndexes []int64
	Keyword      string
}

type TokenWithMarket struct {
	ChainIndex               int64
	Address                  string
	Name                     string
	Symbol                   string
	Decimals                 int64
	LogoUrl                  string
	Price                    decimal.Decimal
	CirculatingSupply        decimal.Decimal
	TradingVolume24H         decimal.Decimal
	PriceChangePercentage24H decimal.Decimal
}

type QueryTokenReq struct {
}

type MarketRepo interface {
	GetTokenIDRelCacheByTokenIDs(ctx context.Context, tokenIDs []*TokenID) (*CoinTokenRel, error)
	GetTokenIDRelCacheByChainIndex(ctx context.Context, chainIndex int64) (*CoinTokenRel, error)
	ListCoinMarketDataByCoinIDs(ctx context.Context, coinIDs []string) ([]*model.CoinMarketData, error)
	ListCurrencyUSDRate(ctx context.Context, currencies []string) (map[string]decimal.Decimal, error)
	ListSimpleKlineByTokenIDs(ctx context.Context, tokenIDs []*TokenID) ([]*SimpleKline, error)
	ListTokenByPopular(ctx context.Context, filter *PopularTokenFilter) (list []*TokenWithMarket, totalCount int64, err error)
}

type MarketUsecase struct {
	log *log.Helper

	repo MarketRepo
}

func NewMarketUsecase(logger log.Logger, repo MarketRepo) *MarketUsecase {
	return &MarketUsecase{
		log:  log.NewHelper(logger),
		repo: repo,
	}
}

func (uc *MarketUsecase) ListTokenByPopular(ctx context.Context, filter *PopularTokenFilter) (list []*TokenWithMarket, totalCount int64, err error) {
	return uc.repo.ListTokenByPopular(ctx, filter)
}

func (uc *MarketUsecase) QuerySimpleKline(ctx context.Context, req *QuerySimpleKlineReq) ([]*SimpleKline, error) {
	return uc.repo.ListSimpleKlineByTokenIDs(ctx, req.TokenIds)
}

func (uc *MarketUsecase) QueryCoinPrice(ctx context.Context, req *QueryCoinPriceReq) ([]*CoinPrice, error) {
	rel, err := uc.repo.GetTokenIDRelCacheByTokenIDs(ctx, req.TokenIds)
	if err != nil {
		return nil, fmt.Errorf("GetTokenIDRelCache: %w", err)
	}

	if len(rel.UniqueIDs) == 0 {
		return nil, nil
	}

	list, err := uc.repo.ListCoinMarketDataByCoinIDs(ctx, rel.UniqueIDs)
	if err != nil {
		return nil, fmt.Errorf("ListCoinMarketDataByCoinIDs: %w", err)
	}

	result := make([]*CoinPrice, 0, len(req.TokenIds))
	for _, v := range list {
		for _, tokenID := range rel.CoinID2TokenID[v.CoinID] {
			result = append(result, &CoinPrice{
				ChainIndex:    tokenID.ChainIndex,
				Address:       tokenID.Address,
				Price:         v.Price,
				LastUpdatedAt: v.LastUpdatedAt,
			})
		}
	}
	return result, nil
}

func (uc *MarketUsecase) QueryCurrencyUSDRate(ctx context.Context, currencies []string) (map[string]decimal.Decimal, error) {
	return uc.repo.ListCurrencyUSDRate(ctx, currencies)
}
