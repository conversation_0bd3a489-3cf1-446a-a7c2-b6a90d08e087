package biz

import (
	pb "byd_wallet/api/wallet/v1"
	"byd_wallet/model"
	"byd_wallet/utils"
	"sort"
	"sync"

	"gorm.io/gorm/clause"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

type BlockchainNetworkCache interface {
	GetChainByIndex(chainIndex int64) (*model.BlockchainNetwork, error)
	GetChainByIndexForCache(chainIndex int64) (*model.BlockchainNetwork, error)
}

type WalletUsecase struct {
	log *log.Helper
	db  *gorm.DB // FIXME: repo interface
}

func NewWalletUsecase(logger log.Logger, db *gorm.DB) *WalletUsecase {
	return &WalletUsecase{
		log: log.NewHelper(logger),
		db:  db,
	}
}

// // NOTE: tmp handle
// func (uc *WalletUsecase) QueryTxLatestToken(ctx context.Context, chains []*v1.WalletAddress4Chain) ([]*v1.TokenWithWallet, error) {
// 	if len(chains) == 0 {
// 		return []*v1.TokenWithWallet{}, nil
// 	}

// 	var tas []*v1.TokenWithWallet
// 	sb := strings.Builder{}
// 	sb.WriteString("SELECT * FROM (SELECT DISTINCT ON (chain_index, address) * FROM (")
// 	for i, v := range chains {
// 		if len(v.Addresses) == 0 {
// 			continue
// 		}
// 		if i > 0 {
// 			sb.WriteString(" UNION ALL ")
// 		}
// 		var toaddrw string
// 		if len(v.Addresses) == 1 {
// 			toaddrw = fmt.Sprintf("='%s'", v.Addresses[0])
// 		} else {
// 			wsb := strings.Builder{}
// 			wsb.WriteString(" IN (")
// 			for i, toAddr := range v.Addresses {
// 				if i > 0 {
// 					wsb.WriteRune(',')
// 				}
// 				wsb.WriteRune('\'')
// 				wsb.WriteString(toAddr)
// 				wsb.WriteRune('\'')
// 			}
// 			wsb.WriteRune(')')
// 			toaddrw = wsb.String()
// 		}
// 		methodWhere := "method='Transfer'"
// 		if v.ChainIndex == constant.SolChainIndex {
// 			methodWhere = "method IN ('Transfer','TransferChecked','MintTo','WithdrawNonceAccount','CreateAccount','CloseAccount')"
// 		}
// 		sb.WriteString(fmt.Sprintf(`SELECT chain_index, program_id AS address, to_address AS wallet_address, block_number
//     	FROM %s	WHERE %s AND to_address%s`, model.TransactionStub.TableName(v.ChainIndex), methodWhere, toaddrw))
// 	}
// 	sb.WriteString(") tx ORDER BY chain_index, address, block_number DESC) tx left join token_assets ta on tx.chain_index=ta.chain_index and tx.address=ta.address where ta.id<>0;")

// 	err := uc.db.WithContext(ctx).Raw(sb.String()).Scan(&tas).Error
// 	if err != nil {
// 		return nil, err
// 	}
// 	return tas, nil
// }

func (s *WalletUsecase) TransactionList(req *pb.TransactionsReq) ([]model.Transaction, int64, error) {
	// 1. 解析地址列表
	addresses := utils.ParseAddresses(req.Address)
	if len(addresses) == 0 {
		return nil, 0, nil
	}

	// 2. 处理programIds
	var programIds []string
	if req.ProgramId != "all" {
		programIds = utils.ParseAddresses(req.ProgramId)
	}

	// 3. 获取表名
	tableName := model.TransactionStub.TableName(req.ChainIndex)

	// 4. 构建基础查询
	q := s.db.Table(tableName).
		Where("(from_address IN ? OR to_address IN ?)", addresses, addresses)

	if len(programIds) > 0 {
		q = q.Where("program_id IN ?", programIds)
	}

	// 5. 获取总数
	var total int64
	if err := q.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 6. 执行分页查询
	offset := (req.Page - 1) * req.Limit
	var transactions []model.Transaction

	if err := q.Order("timestamp DESC").
		Offset(int(offset)).
		Limit(int(req.Limit)).
		Find(&transactions).Error; err != nil {
		return nil, 0, err
	}

	return transactions, total, nil
}

func (s *WalletUsecase) TransactionInfo(req *pb.TransactionReq) (model.Transaction, error) {
	var transaction model.Transaction
	// // 1. 验证区块链信息
	// blockchain, err := s.bcNet.GetChainByIndex(req.ChainIndex)
	// if err != nil {
	// 	return transaction, err
	// }
	// if blockchain == nil {
	// 	return transaction, errors.New("blockchain is nil")
	// }
	// 3. 获取表名
	tableName := model.TransactionStub.TableName(req.ChainIndex)
	err := s.db.Table(tableName).Where("tx_hash = ?", req.Txn).First(&transaction).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return transaction, err
	}
	return transaction, nil
}

func (s *WalletUsecase) TransactionsByAddress(req *pb.TransactionsByAddressReq) ([]model.Transaction, int64, error) {

	// 2. 解析地址列表
	fromAddresses := utils.ParseAddresses(req.FromAddress)
	toAddresses := utils.ParseAddresses(req.ToAddress)
	var programIds []string
	if req.ProgramId != nil {
		programIds = utils.ParseAddresses(*req.ProgramId)
	}
	// 3. 获取表名
	tableName := model.TransactionStub.TableName(req.ChainIndex)
	// 4. 并行查询发送和接收交易
	var (
		fromTx, toTx       []model.Transaction
		totalFrom, totalTo int64
		wg                 sync.WaitGroup
		queryErr           error
	)

	offset := (req.Page - 1) * req.Limit
	limit := req.Limit

	wg.Add(2)

	// 查询作为发送方的交易
	go func() {
		defer wg.Done()
		q := s.db.Table(tableName)
		if len(fromAddresses) > 0 {
			q = q.Where("from_address IN ?", fromAddresses)
		}
		if len(programIds) > 0 {
			q = q.Where("program_id IN ?", programIds)
		}
		if err := q.Count(&totalFrom).Error; err != nil {
			queryErr = err
			return
		}

		if err := q.Order("timestamp DESC").
			Offset(int(offset)).
			Limit(int(limit)).
			Find(&fromTx).Error; err != nil {
			queryErr = err
		}
	}()

	// 查询作为接收方的交易
	go func() {
		defer wg.Done()
		q := s.db.Table(tableName)
		if len(toAddresses) > 0 {
			q = q.Where("to_address IN ?", toAddresses)
		}
		if len(programIds) > 0 {
			q = q.Where("program_id IN ?", programIds)
		}
		if err := q.Count(&totalTo).Error; err != nil {
			queryErr = err
			return
		}
		if err := q.Order("timestamp DESC").
			Offset(int(offset)).
			Limit(int(limit)).
			Find(&toTx).Error; err != nil {
			queryErr = err
		}
	}()

	wg.Wait()
	if queryErr != nil {
		return nil, 0, queryErr
	}

	// 5. 合并结果并去重
	transactionMap := make(map[uint]model.Transaction, len(fromTx)+len(toTx))
	for _, tx := range append(fromTx, toTx...) {
		transactionMap[tx.ID] = tx
	}

	// 6. 转换为切片并按时间排序
	transactions := make([]model.Transaction, 0, len(transactionMap))
	for _, tx := range transactionMap {
		transactions = append(transactions, tx)
	}

	sort.Slice(transactions, func(i, j int) bool {
		return transactions[i].Timestamp > transactions[j].Timestamp
	})

	// 7. 计算总数（去重后的实际总数可能小于totalFrom+totalTo）
	total := totalFrom + totalTo

	// 8. 应用最终分页（因为合并后可能有更多结果）
	start := 0
	if int(offset) < len(transactions) {
		start = int(offset)
	}
	end := start + int(limit)
	if end > len(transactions) {
		end = len(transactions)
	}
	finalTransactions := transactions[start:end]

	return finalTransactions, total, nil
}

func (s *WalletUsecase) ReportInternalTxn(req *pb.ReportInternalTxnReq) error {
	return s.db.Model(&model.MissTransaction{}).Clauses(clause.OnConflict{
		DoNothing: true,
	}).Create(&model.MissTransaction{
		TxHash:     req.Txn,
		ChainIndex: req.ChainIndex,
	}).Error
}
