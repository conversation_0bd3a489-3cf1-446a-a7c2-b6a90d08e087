package biz

import (
	"byd_wallet/model"
	"context"

	"github.com/go-kratos/kratos/v2/log"
)

type TransactionFilter struct {
	Page        int64
	PageSize    int64
	ChainIndex  int64 // 必要条件,默认0 bitcoin
	FromAddress string
	ToAddress   string
	TxHash      string
}

type TransactionViewFilter struct {
	Page        int64
	PageSize    int64
	ChainIndex  int64 // 必要条件,默认0 bitcoin
	FromAddress string
	ToAddress   string
	TxHash      string
	OrderBy     string
}

type TransactionRepo interface {
	ListByFilter(ctx context.Context, filter *TransactionFilter) (list []*model.Transaction, totalCount int64, err error)
	ListViewByFilter(ctx context.Context, filter *TransactionViewFilter) (list []*model.TransactionView, totalCount int64, err error)
	// GetByHash 根据chainIndex和hash获取单个交易记录，如果记录不存在会返回 nil, nil
	GetByHash(ctx context.Context, chainIndex int64, hash string) (*model.Transaction, error)
}

type TransactionUsecase struct {
	log *log.Helper

	repo TransactionRepo
}

func NewTransactionUsecase(logger log.Logger, repo TransactionRepo) *TransactionUsecase {
	return &TransactionUsecase{
		log:  log.NewHelper(logger),
		repo: repo,
	}
}

func (uc *TransactionUsecase) ListTransactionView(ctx context.Context, filter *TransactionViewFilter) (list []*model.TransactionView, totalCount int64, err error) {
	return uc.repo.ListViewByFilter(ctx, filter)
}

func (uc *TransactionUsecase) ListTransaction(ctx context.Context, filter *TransactionFilter) (list []*model.Transaction, totalCount int64, err error) {
	return uc.repo.ListByFilter(ctx, filter)
}
