package biz

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz/rent"
	"byd_wallet/internal/biz/syncer/chain/bitcoin"
	"byd_wallet/internal/biz/syncer/chain/evm"
	"byd_wallet/internal/biz/syncer/chain/evm/evm_l2"
	"byd_wallet/internal/biz/syncer/chain/solana"
	"byd_wallet/internal/biz/syncer/chain/tron"
	"byd_wallet/internal/conf"
	"byd_wallet/model"
	"context"
	"fmt"
	"sync"

	"github.com/alitto/pond/v2"
	"github.com/go-kratos/kratos/v2/log"
)

type BlockFetcher interface {
	FetchTransactions(ctx context.Context, height int64) (<-chan *model.Transaction, error)
	GetLatestBlockHeight(ctx context.Context) (int64, error)
	SetEndpoints(endpoints []string) error
	BlockchainNetWork() *model.BlockchainNetwork
	SetBlockchainNetWork(*model.BlockchainNetwork)
	GetDebugTraceTransaction(txn string) ([]*model.Transaction, error)
	Close() error
}

func NewBlockFetchers(rpc RPCEndpointRepo,
	btc *bitcoin.BlockFetcher,
	tron *tron.BlockFetcher,
	evmL1 *evm.BlockFetcher,
	evmL2 *evm_l2.BlockFetcher,
	solana *solana.BlockFetcher,
	networkRepo BlockchainNetworkRepo,
	c *conf.Server,
) (BlockFetchers, func(), error) {
	cleanup := func() {}
	if c.Syncer == nil {
		return nil, cleanup, fmt.Errorf("invalid syncer config")
	}
	chainIndex := c.Syncer.ChainIndex
	fetchers := make(map[int64]BlockFetcher)
	switch chainIndex {
	case constant.BtcChainIndex:
		fetchers[chainIndex] = btc
	case constant.TronChainIndex:
		fetchers[chainIndex] = tron
	case constant.EthChainIndex, constant.PolChainIndex, constant.BscChainIndex:
		fetchers[chainIndex] = evmL1
	case constant.OptimismChainIndex, constant.BaseChainIndex, constant.ArbChainIndex:
		fetchers[chainIndex] = evmL2
	case constant.SolChainIndex:
		fetchers[chainIndex] = solana
		// add more blockchain...
	default:
		return nil, cleanup, fmt.Errorf("invalid syncer chain index: %d", chainIndex)
	}
	network, err := networkRepo.FindByIndex(context.Background(), chainIndex)
	if err != nil {
		return nil, cleanup, err
	}
	rpcList, err := rpc.GetRpcListByChainIndexForCache(chainIndex, "http")
	if err != nil {
		return nil, cleanup, fmt.Errorf("GetRpcListByChainIndexForCache failed: %w", err)
	}
	fetcher := fetchers[chainIndex]
	fetcher.SetBlockchainNetWork(network)
	if err := fetcher.SetEndpoints(rpcList); err != nil {
		return nil, cleanup, err
	}
	cleanup = func() {
		_ = fetcher.Close()
	}
	return fetchers, cleanup, nil
}

// BlockFetchers key: chainIndex
type BlockFetchers map[int64]BlockFetcher

func (b BlockFetchers) GetByNetwork(network *model.BlockchainNetwork) BlockFetcher {
	return b[network.ChainIndex]
}

type TxProcessor interface {
	// Skip 是否跳过这笔交易处理
	Skip(ctx context.Context, tx *model.Transaction) (bool, error)
	IsTronRentTx(ctx context.Context, tx *model.Transaction) (bool, error)
}

type TransactionBatcher interface {
	Create(ctx context.Context, tableName string, txs []*model.Transaction) error
}

type ChainSyncerRepo interface {
	ListMissingBlock(ctx context.Context, chainIndex int64) ([]*model.MissingBlock, error)
	DeleteMissingBlock(ctx context.Context, id uint) error
	CreateMissingBlock(ctx context.Context, ms *model.MissingBlock) error
	// GetSyncedBlockHeight 获取最新已同步的区块高度
	GetSyncedBlockHeight(ctx context.Context, chainIndex int64) (int64, error)
	// SaveSyncedBlockHeight 保存已同步的区块高度
	SaveSyncedBlockHeight(ctx context.Context, chainIndex, height int64) error
	// ListMissTxn 获取上报hash list 并同步
	ListMissTxn(ctx context.Context, chainIndex int64) ([]*model.MissTransaction, error)
	// DeleteMissTxn 将同步完的删除
	DeleteMissTxn(ctx context.Context, id uint) error
}

type TxSyncEventTokenDeploy struct {
	ChainIndex int64
	ChainID    string
	Address    string
	DeployAt   int64 // seconds
}

type TxSyncEventHoldNewToken struct {
	List []*struct {
		WalletAddress string
		ChainIndex    int64
		TokenAddress  string
	}
}

type ChainSyncer struct {
	log            *log.Helper
	fetcher        BlockFetcher
	txProcessor    TxProcessor
	txRepo         TransactionBatcher
	repo           ChainSyncerRepo
	approvalRepo   ApprovalRepo
	tronRepo       rent.TronRentRepo
	network        *model.BlockchainNetwork
	runningMu      sync.Mutex
	fillingMu      sync.Mutex
	missTxnMu      sync.Mutex
	eventPublisher EventPublisher
}

func NewChainSyncer(logger log.Logger,
	fetchers BlockFetchers,
	txProcessor TxProcessor,
	txRepo TransactionBatcher,
	approvalRepo ApprovalRepo,
	repo ChainSyncerRepo,
	networkRepo BlockchainNetworkRepo,
	eventPublisher EventPublisher,
	tronRepo rent.TronRentRepo,
	c *conf.Server,
) (*ChainSyncer, error) {
	network, err := networkRepo.FindByIndex(context.Background(), c.Syncer.ChainIndex)
	if err != nil {
		return nil, err
	}
	fetcher := fetchers.GetByNetwork(network)
	if fetcher == nil {
		return nil, fmt.Errorf("BlockFetcher not exist, chain index: %d, chain name: %s", network.ChainIndex, network.ChainName)
	}
	fetcher.SetBlockchainNetWork(network)
	return &ChainSyncer{
		log:            log.NewHelper(logger),
		fetcher:        fetcher,
		txProcessor:    txProcessor,
		txRepo:         txRepo,
		approvalRepo:   approvalRepo,
		repo:           repo,
		tronRepo:       tronRepo,
		network:        network,
		eventPublisher: eventPublisher,
	}, nil
}

func (cs *ChainSyncer) GetBlockchainNetwork() *model.BlockchainNetwork {
	return cs.network
}

func (cs *ChainSyncer) SyncBlockTransactions(ctx context.Context) error {
	network := cs.network
	if !cs.runningMu.TryLock() {
		//cs.log.Debugf("同步区块交易任务[%s-%d]已存在, 跳过执行", network.ChainName, network.ChainIndex)
		return nil
	}
	defer cs.runningMu.Unlock()
	height, err := cs.repo.GetSyncedBlockHeight(ctx, cs.network.ChainIndex)
	if err != nil {
		return fmt.Errorf("GetSyncedBlockHeight: %w", err)
	}
	latestHeight, err := cs.fetcher.GetLatestBlockHeight(ctx)
	if err != nil {
		return fmt.Errorf("GetLatestBlockHeight: %w", err)
	}
	if height >= latestHeight {
		cs.log.Debugf("SyncBlockTransactions [%s-%d]已同步到最新高度, height: %d, latestHeight: %d", network.ChainName, network.ChainIndex, height, latestHeight)
		return nil
	}
	// 第一次同步取最新区块高度的前10个
	if height == 0 {
		height = latestHeight - 10
	} else {
		height++
	}
	maxConcurrency := network.MaxConcurrency

	if maxConcurrency <= 0 {
		err = cs.syncSingleBlockTransactions(ctx, height)
		if err != nil {
			err = fmt.Errorf("syncSingleBlockTransactions: %w", err)
		}
		return err
	}

	endHeight := latestHeight
	if latestHeight-height > 10 {
		endHeight = height + 10
	}
	cs.syncBlockTransactionsConcurrently(ctx, height, endHeight)
	return nil
}

// syncSingleBlockTransactions 同步单个区块交易
func (cs *ChainSyncer) syncSingleBlockTransactions(ctx context.Context, height int64) error {
	network := cs.network
	if err := cs.syncBlockTransactions(ctx, height); err != nil {
		return err
	}
	return cs.repo.SaveSyncedBlockHeight(ctx, network.ChainIndex, height)
}

// syncBlockTransactionsConcurrently 并发同步区块交易
func (cs *ChainSyncer) syncBlockTransactionsConcurrently(ctx context.Context, start, end int64) {
	network := cs.network
	maxConcurrency := network.MaxConcurrency
	cs.log.Debugf("开始同步区块交易[%s-%d]，区块高度: %d-%d, 并发数: %d", network.ChainName, network.ChainIndex, start, end, maxConcurrency)
	if maxConcurrency <= 0 {
		maxConcurrency = 1
	}
	pool := pond.NewPool(maxConcurrency, pond.WithContext(ctx))
	for start <= end {
		height := start
		pool.Submit(func() {
			cs.syncBlockTxsAndSaveMissingBlock(ctx, height)
		})
		start++
	}
	pool.StopAndWait()
	if err := cs.repo.SaveSyncedBlockHeight(ctx, network.ChainIndex, end); err != nil {
		cs.log.Errorf("[%s-%d]SaveSyncedBlockHeight failed, height: %d, err: %v", network.ChainName, network.ChainIndex, end, err)
	}
}

func (cs *ChainSyncer) syncBlockTxsAndSaveMissingBlock(ctx context.Context, height int64) {
	network := cs.network
	if err := cs.syncBlockTransactions(ctx, height); err != nil {
		if cErr := cs.repo.CreateMissingBlock(ctx, &model.MissingBlock{
			ChainIndex:  network.ChainIndex,
			BlockHeight: height,
			Msg:         err.Error(),
		}); cErr != nil {
			cs.log.Errorf("[%s-%d]syncBlockTxsAndSaveMissingBlock: CreateMissingBlock err: %v", network.ChainName, network.ChainIndex, cErr)
		}
		cs.log.Errorf("[%s-%d] syncBlockTxsAndSaveMissingBlock: syncBlockTransactions err: %v", network.ChainName, network.ChainIndex, err)
	}
}

func (cs *ChainSyncer) syncBlockTransactions(ctx context.Context, height int64) error {
	network := cs.network
	cs.log.Debugf("开始同步区块交易[%s-%d]，区块高度: %d", network.ChainName, network.ChainIndex, height)
	txs, err := cs.fetcher.FetchTransactions(ctx, height)
	if err != nil {
		return fmt.Errorf("fetch transactions: %w", err)
	}
	var (
		savedTxs       []*model.Transaction
		savedApprovals []*model.Approval
		hntFilter      = make(map[string]struct{})
		hntEvent       = &TxSyncEventHoldNewToken{
			List: []*struct {
				WalletAddress string
				ChainIndex    int64
				TokenAddress  string
			}{},
		}
	)

	for tx := range txs {
		// --- all tx
		if tx.IsCreateToken() {
			event := &TxSyncEventTokenDeploy{
				ChainIndex: cs.network.ChainIndex,
				ChainID:    cs.network.ChainID,
				Address:    tx.ProgramID,
				DeployAt:   tx.Timestamp,
			}
			pubErr := cs.eventPublisher.Publish(ctx, event)
			if pubErr != nil {
				cs.log.Errorf("publish token deploy event fail: %v: %+v", pubErr, event)
			}
		}
		// --- only user's tx
		skip, err := cs.txProcessor.Skip(ctx, tx)
		if err != nil {
			cs.log.Errorf("[%s-%d]skip tx err: %v", network.ChainName, network.ChainIndex, err)
			continue
		}
		if skip {
			continue
		}
		cs.processTransactionMethod(ctx, tx)
		switch tx.Method {
		case constant.TxMethodApproval:
			savedApprovals = append(savedApprovals, &model.Approval{
				ChainIndex:     network.ChainIndex,
				OwnerAddress:   tx.FromAddress,
				SpenderAddress: tx.ToAddress,
				TokenAddress:   tx.ProgramID,
				Value:          tx.Value,
			})
		}
		savedTxs = append(savedTxs, tx)

		if tx.IsHoldNewToken() {
			key := fmt.Sprintf("%s%d%s", tx.ToAddress, tx.ChainIndex, tx.ProgramID)
			_, ok := hntFilter[key]
			if !ok {
				hntFilter[key] = struct{}{}
				hntEvent.List = append(hntEvent.List, &struct {
					WalletAddress string
					ChainIndex    int64
					TokenAddress  string
				}{
					WalletAddress: tx.ToAddress,
					ChainIndex:    tx.ChainIndex,
					TokenAddress:  tx.ProgramID,
				})
			}
		}

	}

	if len(savedTxs) == 0 {
		return nil
	}

	if len(hntEvent.List) > 0 {
		if err := cs.eventPublisher.Publish(ctx, hntEvent); err != nil {
			cs.log.Errorf("publish hold new token event error: %v: %+v", err, hntEvent)
		}
	}

	if err := cs.txRepo.Create(ctx, network.TxTableName(), savedTxs); err != nil {
		return fmt.Errorf("create transactions: %w", err)
	}

	// TODO 如果这里有性能问题，就通过mq去异步处理
	if err := cs.approvalRepo.CreateOrUpdateApprovals(ctx, savedApprovals); err != nil {
		return err
	}
	cs.log.Debugf("同步区块交易成功[%s-%d], 区块高度: %d, 交易长度: %d", network.ChainName, network.ChainIndex, height, len(savedTxs))
	return nil
}

// processTransactionMethod 特殊处理某个链的tx method
// 在这里处理而不是在具体的某条链处理 是为了减少数据量的处理
func (cs *ChainSyncer) processTransactionMethod(ctx context.Context, tx *model.Transaction) {
	switch cs.network.ChainIndex {
	case constant.TronChainIndex:
		// 处理波场租赁转账
		rent, err := cs.txProcessor.IsTronRentTx(ctx, tx)
		if err != nil {
			cs.log.Warnf("IsTronRentTx hash: %s,  err: %v", tx.TxHash, err)
			return
		}
		if rent {
			tx.Method = constant.TxMethodTronRent
			return
		}
		// 处理delegate resource 数据
		record, err := cs.tronRepo.GetTronRentRecordByDelegateHash(ctx, tx.TxHash)
		if err != nil {
			cs.log.Warnf("GetTronRentRecordByDelegateHash hash: %s,  err: %v", tx.TxHash, err)
			return
		}
		tx.Value = fmt.Sprintf("%d", record.PledgeNum)
	}
}

func (cs *ChainSyncer) FillMissingBlocks(ctx context.Context) error {
	if !cs.fillingMu.TryLock() {
		return nil
	}
	defer cs.fillingMu.Unlock()
	blocks, err := cs.repo.ListMissingBlock(ctx, cs.network.ChainIndex)
	if err != nil {
		return err
	}
	for _, block := range blocks {
		if err := cs.fillMissingBlock(ctx, block); err != nil {
			cs.log.Errorf("failed to fillMissingBlock, MissingBlock.ID: %d, err: %v", block.ID, err)
			continue
		}
	}
	return nil
}

func (cs *ChainSyncer) fillMissingBlock(ctx context.Context, ms *model.MissingBlock) error {
	// 如果漏块的高度大于已同步的最大高度，则不处理
	latestHeight, err := cs.repo.GetSyncedBlockHeight(ctx, ms.ChainIndex)
	if err != nil {
		return err
	}
	if ms.BlockHeight > latestHeight {
		return nil
	}
	if err := cs.syncBlockTransactions(ctx, ms.BlockHeight); err != nil {
		return err
	}
	return cs.repo.DeleteMissingBlock(ctx, ms.ID)
}

func (cs *ChainSyncer) FillMissTxn(ctx context.Context) error {
	if !cs.missTxnMu.TryLock() {
		return nil
	}
	defer cs.missTxnMu.Unlock()
	msTxn, err := cs.repo.ListMissTxn(ctx, cs.network.ChainIndex)
	if err != nil {
		return err
	}
	for _, txn := range msTxn {
		if err := cs.sysInterTransaction(ctx, txn); err != nil {
			cs.log.Errorf("failed to sysInterTransaction, chainIndex: %d,txn %v err: %v", cs.network.ChainIndex, txn.TxHash, err)
			continue
		}
	}
	return nil
}

func (cs *ChainSyncer) sysInterTransaction(ctx context.Context, ms *model.MissTransaction) error {
	transactions, err := cs.fetcher.GetDebugTraceTransaction(ms.TxHash)
	if err != nil {
		return fmt.Errorf("trace txn failed: %w", err)
	}

	addTransactions := make([]*model.Transaction, 0, len(transactions))
	for _, tx := range transactions {
		skip, err := cs.txProcessor.Skip(ctx, tx)
		if err != nil {
			continue
		}
		if skip {
			continue
		}
		addTransactions = append(addTransactions, tx)
	}

	if len(addTransactions) > 0 {
		if err := cs.txRepo.Create(ctx, cs.network.TxTableName(), addTransactions); err != nil {
			return fmt.Errorf("create transactions: %w", err)
		}
	}

	return cs.repo.DeleteMissTxn(ctx, ms.ID)
}
