package biz

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
)

type TokenContract struct {
	Symbol      string
	Name        string
	TotalSupply string // optional
	Decimals    int64
}

type TokenContractRepo interface {
	FindViewByChainIndexAndAddress(ctx context.Context, chainIndex int64, address string) (*TokenContract, error)
}

type TokenContractUsecase struct {
	log  *log.Helper
	repo TokenContractRepo
}

func NewTokenContractUsecase(logger log.Logger,
	repo TokenContractRepo) *TokenContractUsecase {
	return &TokenContractUsecase{
		log:  log.NewHelper(logger),
		repo: repo,
	}
}

func (uc *TokenContractUsecase) GetTokenContract(ctx context.Context, chainIndex int64, address string) (*TokenContract, error) {
	return uc.repo.FindViewByChainIndexAndAddress(ctx, chainIndex, address)
}
