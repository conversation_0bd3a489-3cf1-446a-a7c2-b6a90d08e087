package biz

import (
	v1 "byd_wallet/api/wallet/v1"
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz/base"
	"byd_wallet/internal/biz/coindata"
	"byd_wallet/internal/biz/syncer/chain/tron"
	"byd_wallet/model"
	"context"
	"errors"
	"fmt"
	"github.com/alitto/pond/v2"
	"github.com/shopspring/decimal"
	"strings"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

type SwapRepo interface {
	ListBlockchainNetwork(ctx context.Context) ([]*model.BlockchainNetwork, error)
	ListToken(ctx context.Context, chainIndex int64) ([]*model.TokenAsset, error)
	CreateSwapRecord(ctx context.Context, record *model.SwapRecord) error
	GetSwapRecord(ctx context.Context, id uint) (*model.SwapRecord, error)
	// GetSwapRecordByHash 记录不存在返回nil, nil
	GetSwapRecordByHash(ctx context.Context, hash string) (*model.SwapRecord, error)
	ExistsSwapRecord(ctx context.Context, channelID uint, hash string) (bool, error)
	PagedSwapRecordsByAddresses(ctx context.Context, addresses []string, pagination base.Pagination) ([]*model.SwapRecord, int64, error)
	UpdateSwapRecord(ctx context.Context, updated *model.SwapRecord) error
	DefaultSwapChannel(ctx context.Context) (*model.SwapChannel, error)
	ListSwappableTokens(ctx context.Context, channelID uint) ([]*model.SwappableToken, error)
	CreateSwappableToken(ctx context.Context, token *model.SwappableToken) error
	ExistsSwappableToken(ctx context.Context, chainIndex int64, address string) (bool, error)
	DisableSwappableToken(ctx context.Context, id uint) error
	EnableSwappableToken(ctx context.Context, id uint) error
	UpdateSwappableToken(ctx context.Context, id uint, updated map[string]any) error
	ListHotTokens(ctx context.Context, chainIndex int64) ([]*model.SwappableHotToken, error)
	ListAllHotTokens(ctx context.Context) ([]*model.SwappableHotToken, error)
	FilterToken(ctx context.Context, filter SwapTokenFilter) ([]*model.TokenAsset, error)
	ListPendingSwapRecord(ctx context.Context, channelID uint) ([]*model.SwapRecord, error)
	ListSwapConfig(ctx context.Context) ([]*model.SwapConfig, error)
	GetSwapChannelByName(ctx context.Context, name string) (*model.SwapChannel, error)
	CreateSwapConfig(ctx context.Context, cfg *model.SwapConfig) error
}

type TokenSwapper interface {
	MultiQuote(ctx context.Context, param *MultiQuoteInput) (*MultiQuoteOutput, error)
	Swap(ctx context.Context, input *SwapInput) (*SwapOutput, error)
	AddSwapRecord(ctx context.Context, input *AddSwapRecordInput) (*model.SwapRecord, error)
	GetSwapRecord(ctx context.Context, record *model.SwapRecord) (*model.SwapRecord, error)
	GetOriginSwapRecordByHash(ctx context.Context, hash string) (*model.SwapRecord, error)
}

type SwapTokenFetcher interface {
	FetchSwapTokens(ctx context.Context) ([]*model.TokenAsset, error)
}

type TransactionFetcher interface {
	GetTransactionByHash(ctx context.Context, chainIndex int64, hash string) (*model.Transaction, error)
}

func NewTransactionFetcher(tron *tron.TransactionFetcher) TransactionFetcher {
	return &transactionFetcher{tron: tron}
}

type transactionFetcher struct {
	tron *tron.TransactionFetcher
}

func (t *transactionFetcher) GetTransactionByHash(ctx context.Context, chainIndex int64, hash string) (*model.Transaction, error) {
	switch chainIndex {
	case constant.TronChainIndex:
		return t.tron.GetTransactionByHash(ctx, chainIndex, hash)
	default:
		return nil, fmt.Errorf("unsupported chain index: %d", chainIndex)
	}
}

type SwapUsecase struct {
	log            *log.Helper
	repo           SwapRepo
	swapper        TokenSwapper
	tokenRepo      TokenAssetRepo
	txRepo         TransactionRepo
	tokenFetcher   SwapTokenFetcher
	tokenCollector *TokenCollector
	coinDataCli    coindata.CoinDataThirdAPI
	chainRepo      BlockchainNetworkRepo
	refreshTokenMu sync.Mutex
	txFetcher      TransactionFetcher
}

func NewSwapUsecase(
	logger log.Logger,
	repo SwapRepo,
	swapper TokenSwapper,
	tokenRepo TokenAssetRepo,
	txRepo TransactionRepo,
	tokenFetcher SwapTokenFetcher,
	tokenCollector *TokenCollector,
	coinDataCli coindata.CoinDataThirdAPI,
	chainRepo BlockchainNetworkRepo,
	txFetcher TransactionFetcher,
) *SwapUsecase {
	return &SwapUsecase{
		log:            log.NewHelper(logger),
		repo:           repo,
		swapper:        swapper,
		tokenRepo:      tokenRepo,
		txRepo:         txRepo,
		tokenFetcher:   tokenFetcher,
		tokenCollector: tokenCollector,
		coinDataCli:    coinDataCli,
		chainRepo:      chainRepo,
		txFetcher:      txFetcher,
	}
}

func (uc *SwapUsecase) ListBlockchainNetwork(ctx context.Context) ([]*model.BlockchainNetwork, error) {
	return uc.repo.ListBlockchainNetwork(ctx)
}

func (uc *SwapUsecase) FilterToken(ctx context.Context, filter SwapTokenFilter) ([]*model.TokenAsset, error) {
	return uc.repo.FilterToken(ctx, filter)
}

func (uc *SwapUsecase) MultiQuote(ctx context.Context, input *MultiQuoteInput) (*MultiQuoteOutput, error) {
	from, to, err := uc.getTokenAssets(ctx, input.From, input.To)
	if err != nil {
		return nil, err
	}
	input.From.TokenAssetID = from.ID
	input.From.Decimals = decimal.NewFromInt(from.Decimals)
	input.To.TokenAssetID = to.ID
	input.To.Decimals = decimal.NewFromInt(to.Decimals)
	return uc.swapper.MultiQuote(ctx, input)
}

func (uc *SwapUsecase) getTokenAssets(ctx context.Context, from, to *SwapToken) (*model.TokenAsset, *model.TokenAsset, error) {
	fromA, err := uc.getTokenAsset(ctx, from)
	if err != nil {
		return nil, nil, err
	}
	toA, err := uc.getTokenAsset(ctx, to)
	if err != nil {
		return nil, nil, err
	}
	return fromA, toA, nil
}

func (uc *SwapUsecase) getTokenAsset(ctx context.Context, token *SwapToken) (*model.TokenAsset, error) {
	exist, err := uc.repo.ExistsSwappableToken(ctx, token.ChainIndex, token.TokenAddress)
	if err != nil {
		return nil, err
	}
	if !exist {
		return nil, v1.ErrorValidator("unsupported coin: chain %d, token_address %s", token.ChainIndex, token.TokenAddress)
	}
	return uc.tokenRepo.FindByChainIndexAndAddress(ctx, token.ChainIndex, token.TokenAddress)
}

func (uc *SwapUsecase) Swap(ctx context.Context, input *SwapInput) (*SwapOutput, error) {
	from, to, err := uc.getTokenAssets(ctx, input.From, input.To)
	if err != nil {
		return nil, err
	}
	input.From.TokenAssetID = from.ID
	input.To.TokenAssetID = to.ID
	output, err := uc.swapper.Swap(ctx, input)
	if err != nil {
		return nil, err
	}
	return output, nil
}

func (uc *SwapUsecase) AddSwapRecord(ctx context.Context, input *AddSwapRecordInput) (*model.SwapRecord, error) {
	channel, err := uc.repo.DefaultSwapChannel(ctx)
	if err != nil {
		return nil, err
	}
	exists, err := uc.repo.ExistsSwapRecord(ctx, channel.ID, input.Hash)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, errors.New("record already exists")
	}
	from, to, err := uc.getTokenAssets(ctx, input.From, input.To)
	if err != nil {
		return nil, err
	}
	input.From.TokenAssetID = from.ID
	input.To.TokenAssetID = to.ID
	input.From.Decimals = decimal.NewFromInt(from.Decimals)
	input.To.Decimals = decimal.NewFromInt(to.Decimals)

	record, err := uc.swapper.AddSwapRecord(ctx, input)
	if err != nil {
		return nil, err
	}
	record.SwapChannelID = channel.ID
	record.EstimatedTime = input.EstimatedTime
	record.GasFee = input.GasFee
	if err := uc.repo.CreateSwapRecord(ctx, record); err != nil {
		return nil, err
	}
	result, err := uc.repo.GetSwapRecord(ctx, record.ID)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (uc *SwapUsecase) GetSwapRecord(ctx context.Context, id uint) (*model.SwapRecord, error) {
	return uc.repo.GetSwapRecord(ctx, id)
}

func (uc *SwapUsecase) GetSwapRecordByHash(ctx context.Context, hash string) (*model.SwapRecord, error) {
	return uc.repo.GetSwapRecordByHash(ctx, hash)
}

func (uc *SwapUsecase) PagedSwapRecordsByAddresses(ctx context.Context, addresses []string, pagination base.Pagination) ([]*model.SwapRecord, int64, error) {
	return uc.repo.PagedSwapRecordsByAddresses(ctx, addresses, pagination)
}

func (uc *SwapUsecase) RefreshSwapRecordsByConfig(ctx context.Context, conf *model.SwapConfig) error {
	records, err := uc.repo.ListPendingSwapRecord(ctx, conf.SwapChannelID)
	if err != nil {
		return err
	}

	pool := pond.NewPool(conf.RecordMaxConcurrency, pond.WithContext(ctx))
	for _, record := range records {
		record := record
		pool.Submit(func() {
			if err := uc.RefreshSwapRecord(pool.Context(), record); err != nil {
				uc.log.Warnf("failed to refresh swap record [%s]: %v", record.Hash, err)
			}
		})
	}
	pool.StopAndWait()
	return nil
}

func (uc *SwapUsecase) RefreshSwapRecord(ctx context.Context, record *model.SwapRecord) error {
	oldStatus := record.Status
	updatedRecord, err := uc.swapper.GetSwapRecord(ctx, record)
	if err != nil {
		return err
	}
	if updatedRecord == nil {
		return nil
	}
	if oldStatus == updatedRecord.Status {
		return nil
	}

	chainIndex := record.FromTokenAsset.ChainIndex
	tx, err := uc.txRepo.GetByHash(ctx, chainIndex, record.Hash)
	if err != nil {
		return err
	}
	if tx != nil {
		updatedRecord.GasFee = tx.Fee
		updatedRecord.BlockNumber = tx.BlockNumber
	}

	var finishedAt int64
	for i, detail := range updatedRecord.Details {
		detailTx, err := uc.txRepo.GetByHash(ctx, detail.ChainIndex, detail.Hash)
		if err != nil {
			return err
		}
		if detailTx != nil {
			detail.Collected = true
			if i == len(updatedRecord.Details)-1 {
				if detailTx.Timestamp > 0 {
					finishedAt = detailTx.Timestamp
				}
			}
		}
	}
	if updatedRecord.IsFinalStatus() {
		finishedTime := time.Now()
		if finishedAt > 0 {
			finishedTime = time.Unix(finishedAt, 0)
		}
		updatedRecord.FinishedAt = &finishedTime
	}

	return uc.repo.UpdateSwapRecord(ctx, updatedRecord)
}

// RefreshSwappableTokens 刷新可兑换的token
// 取第三方的全量数据和本地数据库对比
// 本地不存在就插入
// 存在就不处理
// 本地的数据存在但是第三方没有，就删除
func (uc *SwapUsecase) RefreshSwappableTokens(ctx context.Context, dryRun bool) error {
	if !uc.refreshTokenMu.TryLock() {
		return nil
	}
	defer uc.refreshTokenMu.Unlock()
	chains, err := uc.chainRepo.All(ctx)
	if err != nil {
		return err
	}
	chainMap := make(map[int64]*model.BlockchainNetwork)
	// 获取默认兑换渠道
	channel, err := uc.repo.DefaultSwapChannel(ctx)
	if err != nil {
		return err
	}
	for _, chain := range chains {
		chainMap[chain.ChainIndex] = chain
	}
	// 获取第三方数据
	thirdPartyTokens, err := uc.tokenFetcher.FetchSwapTokens(ctx)
	if err != nil {
		return err
	}

	// 获取本地已存在的可兑换token
	localSwappableTokens, err := uc.repo.ListSwappableTokens(ctx, channel.ID)
	if err != nil {
		return err
	}

	// 创建映射表用于快速查找
	// key: chainIndex_address, value: TokenAsset
	thirdPartyMap := make(map[string]*model.TokenAsset)
	for _, token := range thirdPartyTokens {
		key := fmt.Sprintf("%d_%s", token.ChainIndex, strings.ToLower(token.Address))
		thirdPartyMap[key] = token
	}

	// 创建本地token映射表
	localMap := make(map[string]*model.SwappableToken)
	for _, swappableToken := range localSwappableTokens {
		key := fmt.Sprintf("%d_%s", swappableToken.TokenAsset.ChainIndex, strings.ToLower(swappableToken.TokenAsset.Address))
		localMap[key] = swappableToken
	}

	if dryRun {
		for key := range thirdPartyMap {
			_, exists := localMap[key]
			if !exists {
				uc.log.Infof("SwappableToken 缺失: %s", key)
			}
		}
		return nil
	}

	// 处理新增：第三方有但本地没有的token
	for key, thirdPartyToken := range thirdPartyMap {
		localToken, exists := localMap[key]
		if exists && localToken.TokenAsset.Address != thirdPartyToken.Address {
			if err := uc.repo.UpdateSwappableToken(ctx, localToken.ID, map[string]any{
				"address": thirdPartyToken.Address,
			}); err != nil {
				return err
			}
		} else if exists && !localToken.Enable {
			// 本地有数据但是不可用
			if err := uc.repo.EnableSwappableToken(ctx, localToken.ID); err != nil {
				return err
			}
		} else if !exists {
			uc.log.Debugf("SwappableToken 缺失: %s", key)
			chainIndex := thirdPartyToken.ChainIndex
			address := thirdPartyToken.Address
			tokenAssetExists, err := uc.tokenRepo.ExistsByChainIndexAndAddress(ctx, chainIndex, address)
			if err != nil {
				return err
			}
			var tokenAsset *model.TokenAsset
			if !tokenAssetExists {
				coinID, coinErr := uc.coinDataCli.GetCoinIDByTokenAddress(chainIndex, address)
				if coinErr != nil {
					uc.log.Warnf("GetCoinIDByTokenAddress (%d, %s) fail: %v", chainIndex, address, coinErr)
				}
				tokenAsset, err = uc.tokenCollector.CollectToken(ctx, &CollectTokenInput{
					ChainIndex: chainIndex,
					Address:    address,
					Symbol:     thirdPartyToken.Symbol,
					Decimals:   thirdPartyToken.Decimals,
					LogoUrl:    thirdPartyToken.LogoUrl,
					CoinID:     coinID,
					DeployedAt: 0,
					Name:       thirdPartyToken.Name,
				})
				if err != nil {
					uc.log.Warnf("CollectToken failed: %v, (chainIndex=%d, address=%s)", err, thirdPartyToken.ChainIndex, thirdPartyToken.Address)
					continue
				}
			} else {
				tokenAsset, err = uc.tokenRepo.FindByChainIndexAndAddress(ctx, chainIndex, address)
				if err != nil {
					return err
				}
				if tokenAsset.CoinID == "" {
					coinID, coinErr := uc.coinDataCli.GetCoinIDByTokenAddress(chainIndex, address)
					if coinErr != nil {
						uc.log.Warnf("GetCoinIDByTokenAddress (%d, %s) fail: %v", chainIndex, address, coinErr)
					}
					if coinID != "" {
						if err = uc.tokenRepo.UpdateByUpdates(ctx, tokenAsset.ID, map[string]interface{}{"coin_id": coinID}); err != nil {
							uc.log.Warnf("update token_assert(%d) failed: %v", tokenAsset.ID, err)
						}
					}
				}
			}

			chain, ok := chainMap[tokenAsset.ChainIndex]
			if !ok {
				return fmt.Errorf("tokenAsset chain index %d not exist", tokenAsset.ChainIndex)
			}

			// 创建SwappableToken
			swappableToken := &model.SwappableToken{
				TokenAssetID:        tokenAsset.ID,
				BlockchainNetworkID: chain.ID,
				ChainIndex:          tokenAsset.ChainIndex,
				Address:             thirdPartyToken.Address,
				Enable:              true,
				Display:             true,
				Channels: []*model.SwappableTokenChannel{
					{SwapChannelID: channel.ID},
				},
			}

			if err := uc.repo.CreateSwappableToken(ctx, swappableToken); err != nil {
				return err
			}
			//uc.log.Infof("Created SwappableToken for token %s (chainIndex=%d, address=%s)", tokenAsset.Symbol, tokenAsset.ChainIndex, tokenAsset.Address)
		}
	}

	// 处理删除：本地有但第三方没有的token
	var deletedCount int
	for key, localToken := range localMap {
		if _, exists := thirdPartyMap[key]; !exists {
			if err := uc.repo.DisableSwappableToken(ctx, localToken.ID); err != nil {
				return err
			}
			if localToken.TokenAsset != nil {
				uc.log.Infof("disable SwappableToken for token %s (chainIndex=%d, address=%s)", localToken.TokenAsset.Symbol, localToken.TokenAsset.ChainIndex, localToken.TokenAsset.Address)
			}
			deletedCount++
		}
	}

	// 校验数据
	localSwappableTokens, err = uc.repo.ListSwappableTokens(ctx, channel.ID)
	if err != nil {
		return err
	}
	uc.log.Infof("RefreshSwappableTokens completed: processed %d third-party tokens, %d local tokens, %d deletions", len(thirdPartyTokens), len(localSwappableTokens), deletedCount)
	return nil
}

func (uc *SwapUsecase) ListHotTokens(ctx context.Context) ([]*model.SwappableHotToken, error) {
	return uc.repo.ListAllHotTokens(ctx)
}

func (uc *SwapUsecase) CreateSwapRecordsByHashes(ctx context.Context, hashes []string) error {
	//		GasFee:          "",
	//		FeeRate:         "",
	//		ApprovalHash:    "",
	//		BlockNumber:     0,
	//		SwapPrice:       "",
	//		FinishedAt:      nil,
	now := time.Now()
	channel, err := uc.repo.DefaultSwapChannel(ctx)
	if err != nil {
		return err
	}
	for _, hash := range hashes {
		record, err := uc.swapper.GetOriginSwapRecordByHash(ctx, hash)
		if err != nil {
			return err
		}
		record.FromTokenAsset, err = uc.tokenRepo.FindByChainIndexAndAddress(ctx, record.FromTokenAsset.ChainIndex, record.FromTokenAsset.Address)
		if err != nil {
			return err
		}
		record.FromTokenAssetID = record.FromTokenAsset.ID
		record.FromTokenAsset = nil
		record.ToTokenAsset, err = uc.tokenRepo.FindByChainIndexAndAddress(ctx, record.ToTokenAsset.ChainIndex, record.ToTokenAsset.Address)
		if err != nil {
			return err
		}
		record.ToTokenAssetID = record.ToTokenAsset.ID
		record.ToTokenAsset = nil
		record.FinishedAt = &now
		record.SwapChannelID = channel.ID
		if err := uc.repo.CreateSwapRecord(ctx, record); err != nil {
			return err
		}
	}
	return nil
}

func (uc *SwapUsecase) CreateSwapConfig(ctx context.Context, config *CreateSwapConfigInput) error {
	channel, err := uc.repo.GetSwapChannelByName(ctx, config.SwapChannelName)
	if err != nil {
		return err
	}
	return uc.repo.CreateSwapConfig(ctx, &model.SwapConfig{
		SwapChannelID:        channel.ID,
		CronSpec:             config.CronSpec,
		RecordMaxConcurrency: config.RecordMaxConcurrency,
	})
}
