package biz

import (
	"byd_wallet/common/constant"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestSwapToken_EVMTokenAddress(t *testing.T) {
	tests := []struct {
		name    string
		address string
		want    string
	}{
		{
			name:    "1",
			address: "******************************************",
			want:    "******************************************",
		},
		{
			name:    "2",
			address: "******************************************",
			want:    "******************************************",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := SwapToken{
				TokenAddress: tt.address,
				ChainIndex:   constant.PolChainIndex,
			}
			assert.Equalf(t, tt.want, s.StandardTokenAddress(), "StandardTokenAddress()")
		})
	}
}
