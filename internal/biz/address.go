package biz

import (
	"byd_wallet/model"
	"context"

	"github.com/go-kratos/kratos/v2/log"
)

type UserAddressFilter struct {
	Page       int64
	PageSize   int64
	ChainIndex int64 // 值为-1表示查询全部
	Address    string
	OrderBy    string
}

type UserAddressRepo interface {
	FindByChainIndexAndAddress(ctx context.Context, chainIndex int64, address string) (*model.UserAddress, error)
	ListByFilter(ctx context.Context, filter *UserAddressFilter) (list []*model.UserAddress, totalCount int64, err error)
}

type UserAddressUsecase struct {
	log *log.Helper

	repo UserAddressRepo
}

func NewUserAddressUsecase(logger log.Logger, repo UserAddressRepo) *UserAddressUsecase {
	return &UserAddressUsecase{
		log:  log.NewHelper(logger),
		repo: repo,
	}
}

func (uc *UserAddressUsecase) FindUserAddressByChainIndexAndAddress(ctx context.Context, chainIndex int64, address string) (*model.UserAddress, error) {
	return uc.repo.FindByChainIndexAndAddress(ctx, chainIndex, address)
}

func (uc *UserAddressUsecase) ListUserAddress(ctx context.Context, filter *UserAddressFilter) (list []*model.UserAddress, totalCount int64, err error) {
	return uc.repo.ListByFilter(ctx, filter)
}
