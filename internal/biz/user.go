package biz

import (
	pb "byd_wallet/api/wallet/v1"
	"byd_wallet/common"
	"byd_wallet/common/constant"
	"byd_wallet/common/request"
	"byd_wallet/model"
	"byd_wallet/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"gorm.io/gorm/clause"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// BaseSignPayload 是所有签名请求的通用结构
type BaseSignPayload struct {
	ChainIndex int64  `json:"chainIndex" form:"chainIndex"` // 用户签名
	Sign       string `json:"sign" form:"sign"`             // 用户签名
	Message    string `json:"message" form:"message"`       // 用户签名的消息内容 如注册bossId:"[{\"chainIndex\":60,\"address\":\"******************************************\"}]" UpDataBossId:"{\"oldBossId\":\"ZX4DHNL3OOTWY7P98Z1663RT\",\"newBossId\":\"888888\"}"
	Address    string `json:"address" form:"address"`       // 用户签名地址（ETH）
}

// UserRegister 表示用户注册请求体
type UserRegister struct {
	BaseSignPayload // 嵌入基础签名字段
}

// UpDateWalletId 表示修改用户名请求体
type UpDateWalletId struct {
	BaseSignPayload // 嵌入基础签名字段
}

// UserAddress 表示用户绑定的链地址信息
type UserAddress struct {
	ChainIndex int64  `json:"chainIndex"` // 链索引
	Address    string `json:"address"`    // 对应链的地址
}

type UpdateBossIdPayload struct {
	OldBossId string `json:"oldBossId"`
	NewBossId string `json:"newBossId"`
}

type UserRepo interface {
	ListByFilter(ctx context.Context, filter *UserFilter) (list []*model.User, totalCount int64, err error)
	FindByUsername(ctx context.Context, username string) (*model.User, error)
}

type UserFilter struct {
	Page     int64
	PageSize int64
	ID       uint
	Username string
	OrderBy  string
}

type UserUsecase struct {
	log *log.Helper

	db *gorm.DB
	rd redis.UniversalClient

	repo      UserRepo
	publisher EventPublisher
}

func NewUserUsecase(logger log.Logger, db *gorm.DB, rd redis.UniversalClient,
	repo UserRepo,
	publisher EventPublisher,
) *UserUsecase {
	return &UserUsecase{
		log: log.NewHelper(logger),

		db:        db,
		rd:        rd,
		repo:      repo,
		publisher: publisher,
	}
}

func (s *UserUsecase) FindUserByUsername(ctx context.Context, username string) (*model.User, error) {
	return s.repo.FindByUsername(ctx, username)
}

func (s *UserUsecase) ListUser(ctx context.Context, filter *UserFilter) (list []*model.User, totalCount int64, err error) {
	return s.repo.ListByFilter(ctx, filter)
}

func (s *UserUsecase) Register(ctx context.Context, req UserRegister) (username string, userID uint, err error) {
	err = s.verifySignature(req.BaseSignPayload)

	if err != nil {
		return
	}
	// 2. 解析地址列表
	var addresses []model.UserAddress
	if err = json.Unmarshal([]byte(req.Message), &addresses); err != nil {
		return
	}

	// 3. 启动事务
	tx := s.db.WithContext(ctx).Begin()
	if err = tx.Error; err != nil {
		err = fmt.Errorf("failed to begin chain: %w", err)
		return
	}

	defer func() {
		if r := recover(); r != nil {
			s.log.Errorf("panic during Register: %v", r)
		}
		// NOTE: don't judge by err
		tx.Rollback()
	}()

	// 4. 查询地址是否已存在绑定
	var userAddress model.UserAddress
	if req.ChainIndex == constant.BtcChainIndex {
		addr, terr := utils.PubKeyToSegWitAddress(req.Address)
		if terr != nil {
			err = terr
			return
		}
		err = tx.Model(&model.UserAddress{}).
			Where("address = ?", addr).
			First(&userAddress).Error
	} else {
		err = tx.Model(&model.UserAddress{}).
			Where("address = ?", req.Address).
			First(&userAddress).Error
	}

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		err = fmt.Errorf("failed to query user: %w", err)
		return
	}

	// 5. 创建新用户
	username, err = utils.GenerateRandomString(6, 6)
	if err != nil {
		err = fmt.Errorf("failed to generate wallet ID: %w", err)
		return
	}
	var user model.User
	// 6. 如果用户已存在，使用之前的名称
	if userAddress.UserID != 0 {
		if err = tx.Model(&model.User{}).
			Where("id = ?", userAddress.UserID).
			First(&user).Error; err != nil {
			err = fmt.Errorf("failed to get existing user: %w", err)
			return
		}
		username = user.Username
	} else {
		user = model.User{Username: username}
		if err = tx.Create(&user).Error; err != nil {
			err = fmt.Errorf("failed to create wallet: %w", err)
			return
		}
	}

	// 7. 生成 user_addresses 并去重插入
	var userAddresses []model.UserAddress
	trackMap := make(map[int64][]string) // chainIndex -> []address

	for _, v := range addresses {
		userAddresses = append(userAddresses, model.UserAddress{
			UserID:     user.ID,
			Address:    v.Address,
			ChainIndex: v.ChainIndex,
		})
		trackMap[v.ChainIndex] = append(trackMap[v.ChainIndex], v.Address)
	}

	if len(userAddresses) > 0 {
		if err = tx.Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "chain_index"}, {Name: "address"}},
			DoNothing: true,
		}).Create(&userAddresses).Error; err != nil {
			err = fmt.Errorf("failed to create wallet addresses: %w", err)
			return
		}
	}

	// 8. 写入 Redis 追踪地址，按 chainIndex 批量写入
	for chainIndex, addrs := range trackMap {
		key := common.GetTrackedAddress(chainIndex)
		if len(addrs) > 0 {
			if err := s.rd.SAdd(ctx, key, addrs).Err(); err != nil {
				s.log.Warnw("failed to track address in redis", "key", key, "err", err)
			}
		}
	}

	// 9. 提交事务
	if err = tx.Commit().Error; err != nil {
		err = fmt.Errorf("failed to commit chain: %w", err)
		return
	}

	userID = user.ID

	s.initApprovalsByUserAddressesAsync(addresses)

	return
}

type UserRegisterEvent struct {
	Addresses []model.UserAddress
}

func (s *UserUsecase) initApprovalsByUserAddressesAsync(addresses []model.UserAddress) {
	if err := s.publisher.Publish(context.Background(), &UserRegisterEvent{Addresses: addresses}); err != nil {
		s.log.Errorf("failed to publish user register event: %v", err)
	}
}

func (s *UserUsecase) UpDateWalletId(req UpDateWalletId) (string, error) {
	// 验证签名
	err := s.verifySignature(req.BaseSignPayload)
	if err != nil {
		return "", fmt.Errorf("signature verification failed: %w", err)
	}

	// 解析签名消息
	var payload request.UpdateWalletIdPayload
	if err := json.Unmarshal([]byte(req.Message), &payload); err != nil {
		return "", fmt.Errorf("invalid message format: %w", err)
	}

	// 尝试将 newWalletId 加入 Redis，占位 2 天（或你的设定时间）
	exist, err := s.rd.Exists(context.Background(), payload.NewWalletId).Result()
	if err != nil {
		return "", fmt.Errorf("redis check failed: %w", err)
	}

	if exist == 1 {
		return "", pb.ErrorBossidLock("new wallet ID already exists")
	}

	// 查询用户地址信息
	var userAddress model.UserAddress
	err = s.db.
		Model(&model.UserAddress{}).
		Where("address = ?", req.Address).
		First(&userAddress).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return "", errors.New("wallet address not found")
		}
		return "", fmt.Errorf("query wallet address error: %w", err)
	}

	// 更新用户名（根据 oldBossId 和 address 绑定的 userID）
	// 更新 username
	tx := s.db.Model(&model.User{}).
		Where("username = ? AND id = ?", payload.OldWalletId, userAddress.UserID).
		Update("username", payload.NewWalletId)
	if tx.Error != nil {
		return "", fmt.Errorf("failed to update username: %w", tx.Error)
	}
	if tx.RowsAffected == 0 {
		return "", errors.New("username not updated (maybe already updated or not found)")
	}
	_, _ = s.rd.SetEx(
		context.Background(),
		payload.OldWalletId,
		"1", // 占位符
		constant.UpdateWalletIdCacheTime,
	).Result()
	return payload.NewWalletId, nil
}

func (s *UserUsecase) GetWalletIds(addresses string) (*pb.GeWalletIdsReply, error) {
	addressList := utils.ParseAddresses(addresses)

	var allUserAddresses []model.UserAddress
	err := s.db.Model(&model.UserAddress{}).
		Where("address IN ?", addressList).
		Preload("User"). // 加上这行，预加载 User 数据
		Find(&allUserAddresses).Error
	if err != nil {
		return nil, err
	}

	// 手动去重：只保留第一个 address
	addrMap := make(map[string]model.UserAddress)
	for _, ua := range allUserAddresses {
		if _, exists := addrMap[ua.Address]; !exists {
			addrMap[ua.Address] = ua
		}
	}

	var list []*pb.GetWalletId
	for _, userAddress := range addrMap {
		// 避免 User 为空导致 panic
		bossId := &pb.GetWalletId{
			WalletId: userAddress.User.Username,
			Address:  userAddress.Address,
		}
		list = append(list, bossId)
	}

	return &pb.GeWalletIdsReply{List: list}, nil
}

var chainVerifiers = map[int64]func(message, sign, address string) (bool, error){
	constant.BtcChainIndex:  utils.BtcVerifySignature,
	constant.SolChainIndex:  utils.SolVerifySignature,
	constant.TronChainIndex: utils.TronVerifySignatureV1,
}

func (s *UserUsecase) verifySignature(req BaseSignPayload) error {
	verifier, exists := chainVerifiers[req.ChainIndex]
	if !exists {
		verifier = utils.VerifySignature // 默认验证器
	}

	ok, err := verifier(req.Message, req.Sign, req.Address)
	if err != nil {
		return fmt.Errorf("signature verification error: %w", err)
	}
	if !ok {
		return errors.New("invalid signature")
	}
	return nil
}
