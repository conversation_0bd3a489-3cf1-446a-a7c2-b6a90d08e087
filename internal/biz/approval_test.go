package biz

import (
	"github.com/stretchr/testify/assert"
	"sort"
	"testing"
	"time"
)

func TestSortUserApprovedDapps(t *testing.T) {
	now := time.Date(2025, time.April, 14, 12, 0, 0, 0, time.Local)
	dapps := UserApprovedDappsByUpdatedAt{
		{UpdatedAt: now.Add(-2 * time.Hour)},
		{UpdatedAt: now},
		{UpdatedAt: now.Add(-1 * time.Hour)},
	}
	sort.Sort(dapps)
	assert.Equal(t, dapps[0].UpdatedAt, now)
	assert.Equal(t, dapps[1].UpdatedAt, now.Add(-1*time.Hour))
	assert.Equal(t, dapps[2].UpdatedAt, now.Add(-2*time.Hour))
}
