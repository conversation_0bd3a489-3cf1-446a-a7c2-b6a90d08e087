package biz

import (
	v1 "byd_wallet/api/wallet/v1"
	"byd_wallet/model"
	"context"

	"github.com/go-kratos/kratos/v2/log"
)

type TokenAssetFilter struct {
	Page       int64
	PageSize   int64
	ChainIndex int64 // 值为-1表示查询全部
	Symbol     string
	Address    string
	OrderBy    string
}

type TokenAssetViewFilter struct {
	Page         int64
	PageSize     int64
	ChainIndexes []int64
	Search       string // search range of symbol, address, name
}

type TokenAssetRepo interface {
	Save(ctx context.Context, tokenAsset *model.TokenAsset) (*model.TokenAsset, error)
	ListByFilter(ctx context.Context, filter *TokenAssetFilter) (list []*model.TokenAsset, totalCount int64, err error)
	ListViewByFilter(ctx context.Context, filter *TokenAssetViewFilter) (list []*model.TokenAsset, totalCount int64, err error)
	UpdateByUpdates(ctx context.Context, id uint, updates map[string]interface{}) error
	FindByChainIndexAndAddress(ctx context.Context, chainIndex int64, address string) (*model.TokenAsset, error)
	ListTokenBalanceByAddress(ctx context.Context, chainIndexes []int64, address string) ([]*v1.TokenBalance, error)
	ExistsByChainIndexAndAddress(ctx context.Context, chainIndex int64, address string) (bool, error)
}

type TokenAssetUsecase struct {
	log *log.Helper

	repo TokenAssetRepo
}

func NewTokenAssetUsecase(logger log.Logger,
	repo TokenAssetRepo) *TokenAssetUsecase {
	return &TokenAssetUsecase{
		log:  log.NewHelper(logger),
		repo: repo,
	}
}

func (uc *TokenAssetUsecase) ExistsTokenAssetByChainIndexAndAddress(ctx context.Context, chainIndex int64, address string) (bool, error) {
	return uc.repo.ExistsByChainIndexAndAddress(ctx, chainIndex, address)
}

func (uc *TokenAssetUsecase) CreateTokenAsset(ctx context.Context, tokenAsset *model.TokenAsset) (*model.TokenAsset, error) {
	return uc.repo.Save(ctx, tokenAsset)
}

func (uc *TokenAssetUsecase) ListTokenAssetView(ctx context.Context, filter *TokenAssetViewFilter) (
	list []*model.TokenAsset, totalCount int64, err error) {
	return uc.repo.ListViewByFilter(ctx, filter)
}

func (uc *TokenAssetUsecase) ListTokenAsset(ctx context.Context, filter *TokenAssetFilter) (
	list []*model.TokenAsset, totalCount int64, err error) {
	return uc.repo.ListByFilter(ctx, filter)
}

func (uc *TokenAssetUsecase) UpdateTokenAssetByUpdates(ctx context.Context, id uint, updates map[string]interface{}) error {
	return uc.repo.UpdateByUpdates(ctx, id, updates)
}

func (uc *TokenAssetUsecase) FindTokenAssetByChainIndexAndAddress(ctx context.Context, chainIndex int64, address string) (*model.TokenAsset, error) {
	return uc.repo.FindByChainIndexAndAddress(ctx, chainIndex, address)
}

func (uc *TokenAssetUsecase) ListTokenBalanceByAddress(ctx context.Context, chainIndexes []int64, address string) ([]*v1.TokenBalance, error) {
	return uc.repo.ListTokenBalanceByAddress(ctx, chainIndexes, address)
}
