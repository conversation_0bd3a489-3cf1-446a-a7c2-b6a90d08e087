package biz

import (
	"context"
	"encoding/json"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/shopspring/decimal"
)

type SpotPrice struct {
	TradingPair string
	Price       decimal.Decimal
	Timestamp   int64 // seconds
}

func (s *SpotPrice) MustJsonStr() string {
	if s == nil {
		return ""
	}
	bts, _ := json.Marshal(s)
	return string(bts)
}

type SpotPriceRepo interface {
	Subscribe(ctx context.Context) error
	Unsubscribe(ctx context.Context) error

	FindByTradingPair(ctx context.Context, tradingPair string) (*SpotPrice, error)
	ListByTradingPairs(ctx context.Context, tradingPairs []string) ([]*SpotPrice, error)
}

type SpotPriceManager struct {
	log *log.Helper

	repo SpotPriceRepo
}

func NewSpotPriceManager(logger log.Logger, repo SpotPriceRepo) *SpotPriceManager {
	return &SpotPriceManager{
		log:  log.<PERSON>elper(logger),
		repo: repo,
	}
}

func (mgr *SpotPriceManager) Start(ctx context.Context) error {
	return mgr.repo.Subscribe(ctx)
}

func (mgr *SpotPriceManager) Stop(ctx context.Context) error {
	return mgr.repo.Unsubscribe(ctx)
}

func (mgr *SpotPriceManager) GetSpotPrice(ctx context.Context, tradingPair string) (*SpotPrice, error) {
	return mgr.repo.FindByTradingPair(ctx, tradingPair)
}

func (mgr *SpotPriceManager) ListSpotPriceByTradingPairs(ctx context.Context, tradingPairs []string) ([]*SpotPrice, error) {
	return mgr.repo.ListByTradingPairs(ctx, tradingPairs)
}
