package biz

import (
	"byd_wallet/model"
	"byd_wallet/utils"
	"context"
	"errors"

	"github.com/go-kratos/kratos/v2/log"
)

var (
	ErrAdminLoginQueryAcc   = errors.New("username not exist, or wrong password")
	ErrAdminInvalidJwtToken = errors.New("invalid auth cert")
)

type AdminLoginParam struct {
	Username string
	Password string
}

type AdminJwt interface {
	GenerateJwtToken(adminID uint) (jwtToken string, err error)
	VerifyJwtToken(jwtToken string) (adminID uint, err error)
}

type AdminRepo interface {
	Create(ctx context.Context, admin *model.Admin) error
	// GetByUsernameAndPassword
	// admin record not found, return nil,nil
	GetByUsernameAndPassword(ctx context.Context, username, password string) (*model.Admin, error)
}

type AdminUsecase struct {
	log *log.Helper

	jwt  AdminJwt
	repo AdminRepo
}

func NewAdminUsecase(logger log.Logger, repo AdminRepo, jwt AdminJwt) *AdminUsecase {
	return &AdminUsecase{
		log:  log.NewHelper(logger),
		repo: repo,
		jwt:  jwt,
	}
}

func (uc *AdminUsecase) Register(ctx context.Context, admin *model.Admin) (err error) {
	admin.Password = utils.MD5Hash(admin.Password)
	return uc.repo.Create(ctx, admin)
}

func (uc *AdminUsecase) Login(ctx context.Context, p *AdminLoginParam) (jwtToken string, err error) {
	// TODO: login limit

	admin, err := uc.repo.GetByUsernameAndPassword(ctx, p.Username, utils.MD5Hash(p.Password))
	if err != nil {
		return
	}
	if admin == nil {
		err = ErrAdminLoginQueryAcc
		return
	}
	jwtToken, err = uc.jwt.GenerateJwtToken(admin.ID)
	return
}

func (uc *AdminUsecase) VerifyJwtToken(_ context.Context, jwtToken string) (adminID uint, err error) {
	return uc.jwt.VerifyJwtToken(jwtToken)
}
