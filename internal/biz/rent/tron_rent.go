package rent

import (
	"byd_wallet/model"
	"context"
	"encoding/json"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
)

type TronRentUseCase struct {
	repo       TronRentRepo
	reqFactory TronRentRequesterFactory
	log        *log.Helper
}

func NewTronRentUseCase(repo TronRentRepo, reqFactory TronRentRequesterFactory, logger log.Logger) *TronRentUseCase {
	return &TronRentUseCase{repo: repo, reqFactory: reqFactory, log: log.NewHelper(logger)}
}

// GetQueryPreorderInfo 用于预估租赁费用，不存数据库
func (t *TronRentUseCase) GetQueryPreorderInfo(ctx context.Context, record *model.TronRentRecord) error {
	conf, err := t.repo.GetTronRentConfig(ctx)
	if err != nil {
		return err
	}
	tronRentRequester, err := t.reqFactory.GetTronRentRequester(conf.Channel)
	if err != nil {
		return err
	}
	record.TradeType = conf.TradeType
	record.Source = conf.SourceFlag
	record.OrderType = conf.OrderType
	record.PledgeHour = conf.PledgeHour
	record.PledgeMinute = conf.PledgeMinute
	record.CustomPrice = conf.CustomPrice
	return tronRentRequester.QueryPreorderInfo(ctx, record)
}

func (t *TronRentUseCase) PrepareRent(ctx context.Context, record *model.TronRentRecord) error {
	conf, err := t.repo.GetTronRentConfig(ctx)
	if err != nil {
		return err
	}
	tronRentRequester, err := t.reqFactory.GetTronRentRequester(conf.Channel)
	if err != nil {
		return err
	}
	record.TradeType = conf.TradeType
	record.Source = conf.SourceFlag
	record.OrderType = conf.OrderType
	record.PledgeHour = conf.PledgeHour
	record.PledgeMinute = conf.PledgeMinute
	record.CustomPrice = conf.CustomPrice
	record.Channel = conf.Channel
	record.Status = model.TronRentStatusPending
	record.OrderId = uuid.NewString()
	if conf.ReceiveTrxAddr != "" {
		record.PlatformAddr = conf.ReceiveTrxAddr
	}
	if err := tronRentRequester.AddTronRentRecord(ctx, record); err != nil {
		record.Status = model.TronRentStatusFailed
		record.ErrMsg = err.Error()
		if cErr := t.repo.CreateTronRentRecord(ctx, record); cErr != nil {
			t.log.Errorf("PrepareRent: CreateTronRentRecord failed, %s", cErr.Error())
		}
		return err
	}
	return t.repo.CreateTronRentRecord(ctx, record)
}

func (t *TronRentUseCase) UploadHash(ctx context.Context, req *UploadHashReq) error {
	record, err := t.repo.GetTronRentRecordByOrderId(ctx, req.OrderId)
	if err != nil {
		return err
	}
	tronRentRequester, err := t.reqFactory.GetTronRentRequester(record.Channel)
	if err != nil {
		return err
	}
	record.TxHash = req.FromHash
	record.SignedData = req.SignedData
	req.Transaction = record.Transaction
	req.PledgeAddress = record.PledgeAddress
	req.PledgeNum = record.PledgeNum
	txIds, err := tronRentRequester.UploadHash(ctx, req)
	if err != nil {
		record.Status = model.TronRentStatusFailed
		record.ErrMsg = err.Error()
		if uErr := t.repo.UpdateTronRentRecord(ctx, record); uErr != nil {
			t.log.Errorf("UploadHash: UpdateTronRentRecord failed, %s", uErr.Error())
		}
		return err
	}
	if req.Status != "" {
		record.Status = req.Status
	}
	if req.ChannelOrderId != "" {
		record.ChannelOrderId = req.ChannelOrderId
	}
	if req.ActualChannelPrice.IsPositive() {
		record.ActualChannelPrice = req.ActualChannelPrice
	}
	if txIds != nil {
		txIdsB, _ := json.Marshal(txIds)
		record.UploadHashReplyData = txIdsB
	}
	if err := t.repo.UpdateTronRentRecord(ctx, record); err != nil {
		t.log.Errorf("UploadHash: UpdateTronRentRecord failed, %s", err.Error())
	}

	return nil
}
