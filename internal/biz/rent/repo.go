package rent

import (
	"byd_wallet/model"
	"context"

	"github.com/shopspring/decimal"
	"gorm.io/datatypes"
)

type AddTronRentRecordReq struct {
	FromAddress   string
	PledgeAddress string
	PledgeNum     string
	TradeType     string
	PledgeDay     string
	SourceFlag    string
	OrderType     string
	PledgeHour    string
	PledgeMinute  string
}

type AddTronRentRecordResp struct {
	OrderId       string
	PlatformAddr  string
	FromAddress   string
	PledgeAddress string
	PledgeDay     int
	Source        string
	OrderType     string
	OrderPrice    int
	PledgeNum     int
	PledgeTrxNum  float64
}

type UploadHashReq struct {
	OrderId    string
	FromHash   string
	SignedData string
	// Transaction from db record
	Transaction   datatypes.JSON
	PledgeAddress string
	PledgeNum     int
	// return values // NOTE: tmp
	Status             model.TronRentStatus
	ChannelOrderId     string
	ActualChannelPrice decimal.Decimal
}

type TronRentRepo interface {
	GetTronRentConfig(ctx context.Context) (*model.TronRentConfig, error)
	CreateTronRentRecord(ctx context.Context, record *model.TronRentRecord) error
	UpdateTronRentRecord(ctx context.Context, record *model.TronRentRecord) error
	GetTronRentRecordByOrderId(ctx context.Context, orderId string) (*model.TronRentRecord, error)
	GetTronRentRecordByDelegateHash(ctx context.Context, hash string) (*model.TronRentRecord, error)
}

type TronRentRequester interface {
	AddTronRentRecord(ctx context.Context, record *model.TronRentRecord) error
	QueryPreorderInfo(ctx context.Context, record *model.TronRentRecord) error
	UploadHash(ctx context.Context, record *UploadHashReq) ([]string, error)
}

type TronRentRequesterFactory interface {
	GetTronRentRequester(channel model.TronRentChannel) (TronRentRequester, error)
}
