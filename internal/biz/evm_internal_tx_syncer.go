package biz

import (
	"byd_wallet/internal/biz/syncer/chain/evm"
	"byd_wallet/model"
	"context"
	"errors"
	"fmt"
	"sync"

	"github.com/go-kratos/kratos/v2/log"
)

type BlockHeightFetcher interface {
	GetLatestBlockHeight(ctx context.Context, chainIndex int64) (int64, error)
	Close()
}

type EvmInternalTxFetcher interface {
	GetEvmInternalTx(ctx context.Context, chainIndex int64, chainID string, height int64) ([]*model.Transaction, error)
}

type EVMInternalTxSyncerRepo interface {
	// GetSyncedBlockHeight 获取最新已同步的区块高度
	GetSyncedBlockHeight(ctx context.Context, chainIndex int64) (int64, error)
	// SaveSyncedBlockHeight 保存已同步的区块高度
	SaveSyncedBlockHeight(ctx context.Context, chainIndex, height int64) error
}

func NewBlockHeightFetcher(rpcRepo RPCEndpointRepo, chainRepo BlockchainNetworkRepo) (BlockHeightFetcher, error) {
	ctx := context.Background()
	chains, err := chainRepo.ListEVM(ctx)
	if err != nil {
		return nil, err
	}
	var chainIndexes []int64
	for _, chain := range chains {
		if chain.IsEvm {
			chainIndexes = append(chainIndexes, chain.ChainIndex)
		}
	}
	if len(chainIndexes) == 0 {
		return nil, errors.New("evm chain is empty, please check blockchain_network setting")
	}
	endpoints, err := rpcRepo.ListRPCEndpointsByChainIndexes(ctx, chainIndexes, "http")
	if err != nil {
		return nil, err
	}
	return evm.NewBlockHeightFetcher(endpoints)
}

type EVMInternalTxSyncer struct {
	log       *log.Helper
	fetcher   BlockHeightFetcher
	txFetcher EvmInternalTxFetcher
	runningMu sync.Mutex
	repo      EVMInternalTxSyncerRepo
	txRepo    TransactionBatcher
}

func NewEVMInternalTxSyncer(logger log.Logger, fetcher BlockHeightFetcher, txFetcher EvmInternalTxFetcher, repo EVMInternalTxSyncerRepo, txRepo TransactionBatcher) *EVMInternalTxSyncer {
	return &EVMInternalTxSyncer{log: log.NewHelper(logger), fetcher: fetcher, txFetcher: txFetcher, repo: repo, txRepo: txRepo}
}

func (cs *EVMInternalTxSyncer) Sync(ctx context.Context, network *model.BlockchainNetwork) error {
	chainIndex := network.ChainIndex
	if !cs.runningMu.TryLock() {
		return nil
	}
	defer cs.runningMu.Unlock()
	height, err := cs.repo.GetSyncedBlockHeight(ctx, chainIndex)
	if err != nil {
		return fmt.Errorf("GetSyncedBlockHeight: %w", err)
	}
	latestHeight, err := cs.fetcher.GetLatestBlockHeight(ctx, chainIndex)
	if err != nil {
		return fmt.Errorf("GetLatestBlockHeight: %w", err)
	}
	if height >= latestHeight {
		return nil
	}
	if height == 0 {
		height = latestHeight
	} else {
		height++
	}
	if err := cs.syncBlockInternalTransactions(ctx, height, network); err != nil {
		return err
	}
	return cs.repo.SaveSyncedBlockHeight(ctx, chainIndex, height)
}

func (cs *EVMInternalTxSyncer) syncBlockInternalTransactions(ctx context.Context, height int64, network *model.BlockchainNetwork) error {
	chainIndex := network.ChainIndex
	chainID := network.ChainID
	tx, err := cs.txFetcher.GetEvmInternalTx(ctx, chainIndex, chainID, height)
	if err != nil {
		return err
	}
	if len(tx) == 0 {
		return nil
	}

	return cs.txRepo.Create(ctx, network.TxTableName(), tx)
}
