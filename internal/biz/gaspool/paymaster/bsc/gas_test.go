package bsc

import (
	"math/big"
	"testing"

	"github.com/shopspring/decimal"
)

// TestGasPriceMultiplier 测试gas价格倍数计算的修复
func TestGasPriceMultiplier(t *testing.T) {
	// 测试用例：原始gas价格为1000000000 wei (1 gwei)
	originalGasPrice := big.NewInt(1000000000)

	// 应用1.2倍安全系数
	safetyFactor := decimal.NewFromFloat(DefaultGasLimitMultiplier) // 1.2
	originalGasPriceDecimal := decimal.NewFromBigInt(originalGasPrice, 0)
	adjustedGasPrice := originalGasPriceDecimal.Mul(safetyFactor)
	result := adjustedGasPrice.BigInt()

	// 期望结果：1000000000 * 1.2 = 1200000000
	expected := big.NewInt(1200000000)

	if result.Cmp(expected) != 0 {
		t.Errorf("Gas价格倍数计算错误。期望: %s, 实际: %s", expected.String(), result.String())
	}

	t.Logf("Gas价格倍数计算正确：%s wei * 1.2 = %s wei",
		originalGasPrice.String(), result.String())
}

// TestGasPriceMultiplierPrecision 测试精度保持
func TestGasPriceMultiplierPrecision(t *testing.T) {
	// 测试用例：使用较大的数值测试精度
	originalGasPrice := big.NewInt(123456789012345)

	// 应用1.2倍安全系数
	safetyFactor := decimal.NewFromFloat(DefaultGasLimitMultiplier)
	originalGasPriceDecimal := decimal.NewFromBigInt(originalGasPrice, 0)
	adjustedGasPrice := originalGasPriceDecimal.Mul(safetyFactor)
	result := adjustedGasPrice.BigInt()

	// 手动计算期望结果：123456789012345 * 1.2 = 148148146814814
	expected := big.NewInt(148148146814814)

	if result.Cmp(expected) != 0 {
		t.Errorf("高精度Gas价格倍数计算错误。期望: %s, 实际: %s", expected.String(), result.String())
	}

	t.Logf("高精度Gas价格倍数计算正确：%s wei * 1.2 = %s wei",
		originalGasPrice.String(), result.String())
}

// TestZeroGasPriceCondition 测试零gas价格条件判断
func TestZeroGasPriceCondition(t *testing.T) {
	tests := []struct {
		name           string
		gasPrice       *big.Int
		expectedIsZero bool
		description    string
	}{
		{
			name:           "零gas价格",
			gasPrice:       big.NewInt(0),
			expectedIsZero: true,
			description:    "gas price为0时应该使用MegaFuel模式",
		},
		{
			name:           "nil gas价格",
			gasPrice:       nil,
			expectedIsZero: true,
			description:    "gas price为nil时应该使用MegaFuel模式",
		},
		{
			name:           "正常gas价格",
			gasPrice:       big.NewInt(1000000000), // 1 gwei
			expectedIsZero: false,
			description:    "gas price大于0时应该使用标准模式",
		},
		{
			name:           "高gas价格",
			gasPrice:       big.NewInt(50000000000), // 50 gwei
			expectedIsZero: false,
			description:    "高gas price时应该使用标准模式",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 模拟条件判断逻辑
			isZeroGasPrice := tt.gasPrice == nil || tt.gasPrice.Sign() == 0

			if isZeroGasPrice != tt.expectedIsZero {
				t.Errorf("零gas价格判断错误。%s - 期望: %v, 实际: %v",
					tt.description, tt.expectedIsZero, isZeroGasPrice)
			}

			if isZeroGasPrice {
				t.Logf("✓ %s: 检测到零gas价格，将使用MegaFuel服务", tt.name)
			} else {
				t.Logf("✓ %s: 检测到非零gas价格(%s wei)，将使用标准gas转账模式",
					tt.name, tt.gasPrice.String())
			}
		})
	}
}
