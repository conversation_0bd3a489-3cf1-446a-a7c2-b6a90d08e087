package bsc

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz/gaspool"
	"byd_wallet/internal/biz/gaspool/base"
	"byd_wallet/internal/biz/syncer/chain/evm"
	"byd_wallet/internal/thirdapi/megafuel"
	"byd_wallet/model"
	"byd_wallet/utils"
	"context"
	"encoding/hex"
	"fmt"
	"math/big"
	"time"

	"github.com/ethereum/go-ethereum/common"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/shopspring/decimal"
)

// paymaster.go - BSC paymaster 主文件
// 包含 paymaster 结构体定义、构造函数和核心接口实现
// 集成 NodeReal MegaFuel 服务，支持 BSC 链的 gas 费用赞助

// BSC paymaster 相关常量
const (
	// DefaultPriceExpireSeconds 默认价格过期时间（秒）
	DefaultPriceExpireSeconds = 300 // 5分钟
	// DefaultGasLimitMultiplier 默认gas limit倍数
	DefaultGasLimitMultiplier = 1.2
	// DefaultMegaFuelTimeout MegaFuel API 调用超时时间（秒）
	DefaultMegaFuelTimeout = 30
	// DefaultRetryAttempts 默认重试次数
	DefaultRetryAttempts = 3
	// DefaultRetryDelay 默认重试延迟（毫秒）
	DefaultRetryDelay = 1000
)

// Paymaster BSC paymaster 实现
// 集成 NodeReal MegaFuel 服务，支持第三方 gas 费用赞助
type Paymaster struct {
	log              *log.Helper
	tokenPriceReader base.TokenPriceReader
	hotAccountReader base.HotAccountReader
	evmCli           *evm.MultiChainClient
	stxMgr           base.GasPoolSponsorTxMgr

	// MegaFuel 客户端
	megafuelClient *megafuel.Paymaster

	// 链配置
	chainIndex int64
	chainName  string

	// 缓存字段，避免重复计算
	cachedAddress common.Address

	// 配置字段
	priceExpireSeconds int64 // 价格过期时间（秒）
	megafuelTimeout    int64 // MegaFuel API 超时时间（秒）
	retryAttempts      int   // 重试次数
	retryDelay         int   // 重试延迟（毫秒）
}

// NewPaymaster 创建新的BSC paymaster实例（使用依赖注入）
// 参数:
//   - logger: 日志记录器
//   - tokenPriceReader: 代币价格读取器
//   - hotAccountReader: 热钱包账户读取器
//   - evmCli: EVM多链客户端
//   - stxMgr: gas pool sponsor交易管理器
//   - megafuelClient: MegaFuel客户端（通过依赖注入）
func NewPaymaster(
	logger log.Logger,
	tokenPriceReader base.TokenPriceReader,
	hotAccountReader base.HotAccountReader,
	evmCli *evm.MultiChainClient,
	stxMgr base.GasPoolSponsorTxMgr,
	megafuelClient *megafuel.Paymaster,
) *Paymaster {
	chainName := constant.GetChainName(constant.BscChainIndex)
	if chainName == "" {
		chainName = "BNB Chain"
	}

	return &Paymaster{
		log:                log.NewHelper(logger),
		tokenPriceReader:   tokenPriceReader,
		hotAccountReader:   hotAccountReader,
		evmCli:             evmCli,
		stxMgr:             stxMgr,
		megafuelClient:     megafuelClient, // 通过依赖注入设置
		chainIndex:         constant.BscChainIndex,
		chainName:          chainName,
		priceExpireSeconds: DefaultPriceExpireSeconds,
		megafuelTimeout:    DefaultMegaFuelTimeout,
		retryAttempts:      DefaultRetryAttempts,
		retryDelay:         DefaultRetryDelay,
	}
}

// gas.go - BSC paymaster gas费用处理文件
// 包含 gas 费用估算和相关辅助方法

// EstimateGas 估算BSC链交易gas费用
// 参数:
//   - ctx: 上下文对象
//   - tx: 用户交易数据
//
// 返回值:
//   - *gaspool.UserTxGas: gas费用估算结果
//   - error: 错误信息
func (pm *Paymaster) EstimateGas(ctx context.Context, tx *gaspool.UserTx) (*gaspool.UserTxGas, error) {
	return pm.estimateGasTransferFee(ctx, tx)
}

// isPriceTimeExpired 检查价格是否过期
// 参数:
//   - timeUnix: 价格时间戳
//
// 返回值:
//   - bool: 是否过期
func (pm *Paymaster) isPriceTimeExpired(timeUnix int64) bool {
	return time.Now().Unix() > timeUnix+pm.priceExpireSeconds
}

// SetPriceExpireSeconds 设置价格过期时间（秒）
func (pm *Paymaster) SetPriceExpireSeconds(seconds int64) {
	if seconds > 0 {
		pm.priceExpireSeconds = seconds
		pm.log.Infof("BSC链价格过期时间已更新为: %d 秒", seconds)
	}
}

// SetMegaFuelTimeout 设置MegaFuel API超时时间（秒）
func (pm *Paymaster) SetMegaFuelTimeout(seconds int64) {
	if seconds > 0 {
		pm.megafuelTimeout = seconds
		pm.log.Infof("BSC链MegaFuel超时时间已更新为: %d 秒", seconds)
	}
}

// DecodeUserTx 解码用户交易数据
// 参数:
//   - ctx: 上下文对象
//   - rawTx: 原始交易数据
//   - txType: 交易类型
//
// 返回值:
//   - *gaspool.UserTx: 解码后的用户交易
//   - error: 错误信息
func (pm *Paymaster) DecodeUserTx(ctx context.Context, rawTxHex string, txType model.GasPoolTxType) (*gaspool.UserTx, error) {
	return pm.decodeTransferTx(ctx, rawTxHex, txType)
}

// VerifyUserTxSignature 验证用户交易签名
// 参数:
//   - ctx: 上下文对象
//   - tx: 用户交易数据
//
// 返回值:
//   - bool: 签名是否有效
//   - error: 错误信息
func (pm *Paymaster) VerifyUserTxSignature(ctx context.Context, tx *gaspool.UserTx) (bool, error) {
	pm.log.Debugf("验证BSC链用户交易签名，交易哈希: %s", tx.TxHash)

	// 获取BSC链ID
	chainID := constant.GetChainID(pm.chainIndex)
	if chainID == 0 {
		return false, fmt.Errorf("不支持的链索引: %d", pm.chainIndex)
	}

	// 解码EVM交易
	evmTx, err := utils.RlpDecodeBytes(tx.RawTxHex)
	if err != nil {
		return false, fmt.Errorf("解码交易失败: %w", err)
	}

	// 验证签名
	sender, err := utils.GetTxSender(evmTx)
	if err != nil {
		return false, fmt.Errorf("获取交易发送者失败: %w", err)
	}

	cli, err := pm.evmCli.Select(pm.chainIndex)
	if err != nil {
		return false, err
	}
	nonce, err := cli.NonceAt(ctx, sender, nil)
	if err != nil {
		return false, err
	}
	if nonce > evmTx.Nonce() {
		return false, fmt.Errorf("获取交易发送者失败: %w", err)
	}

	pm.log.Debugf("BSC链交易签名验证成功，发送者: %s", sender.Hex())
	return true, nil
}

// AuditUserTx 审计用户交易（基于gas price的条件性gas pool余额检查）
// 参数:
//   - ctx: 上下文对象
//   - tx: 用户交易数据
//   - gas: gas费用估算结果
//
// 返回值:
//   - bool: 是否通过审计
//   - error: 错误信息
//
// 处理逻辑:
//   - DEPOSIT模式：始终绕过检查（现有逻辑保持不变）
//   - 零gas price：绕过gas pool检查，使用MegaFuel服务
//   - 非零gas price：执行完整的gas pool余额检查和预扣逻辑
func (pm *Paymaster) AuditUserTx(ctx context.Context, tx *gaspool.UserTx, gas *gaspool.UserTxGas) (bool, error) {
	pm.log.Debugf("开始审计BSC链用户交易，交易哈希: %s", tx.TxHash)
	// 步骤2: 解码用户交易获取gas price进行条件判断

	evmTx, err := utils.RlpDecodeBytes(tx.RawTxHex)
	if err != nil {
		pm.log.Errorf("解码用户交易失败，交易哈希: %s, 错误: %v", tx.TxHash, err)
		return false, fmt.Errorf("解码用户交易失败: %w", err)
	}

	// 步骤3: 获取原始gas price并判断处理模式
	originalGasPrice := evmTx.GasPrice()
	isZeroGasPrice := originalGasPrice == nil || originalGasPrice.Sign() == 0

	if isZeroGasPrice {
		// 步骤1: 对于DEPOSIT模式的交易，允许绕过gas pool余额检查（用于引导启动）
		if tx.TxType.IsDepositGasPool() {
			pm.log.Debugf("DEPOSIT模式交易绕过gas pool余额检查，交易哈希: %s", tx.TxHash)
			return true, nil
		}
		// 零gas price模式：使用MegaFuel服务，绕过gas pool余额检查
		return false, nil
	}

	return true, nil
}

// decodeTransferTx 解码转账交易
// 参数:
//   - ctx: 上下文对象
//   - rawTx: 原始交易数据
//   - txType: 交易类型
//
// 返回值:
//   - *gaspool.UserTx: 解码后的用户交易
//   - error: 错误信息
func (pm *Paymaster) decodeTransferTx(ctx context.Context, rawTxHex string, txType model.GasPoolTxType) (*gaspool.UserTx, error) {
	// 解码EVM交易
	evmTx, err := utils.RlpDecodeBytes(rawTxHex)
	if err != nil {
		return nil, fmt.Errorf("解码原始交易失败: %w", err)
	}

	// 获取交易发送者地址
	sender, err := utils.GetTxSender(evmTx)
	if err != nil {
		return nil, fmt.Errorf("获取交易发送者失败: %w", err)
	}

	// 获取BSC链ID
	chainID := constant.GetChainID(pm.chainIndex)
	if chainID == 0 {
		return nil, fmt.Errorf("无效的链索引: %d", pm.chainIndex)
	}

	// 默认使用原生转账的 to 和 value
	toAddr := evmTx.To().Hex()
	value := decimal.NewFromBigInt(evmTx.Value(), 0)
	contract := ""

	// 检查是否是 ERC20 Transfer
	if data := evmTx.Data(); len(data) >= 4 { // 至少包含 methodID（4字节）
		methodID := hex.EncodeToString(data[:4])
		if methodID == constant.ERC20TransferMethodID && len(data) >= 68 { // 4(methodID) + 32(to) + 32(value)
			// 解析 to 地址（跳过前 12 字节的 0 填充）
			to := common.BytesToAddress(data[16:36]).Hex()
			// 解析转账金额
			amount := new(big.Int).SetBytes(data[36:68])
			value = decimal.NewFromBigInt(amount, 0)
			toAddr = to
			contract = evmTx.To().Hex() // 合约地址
		}
	}

	return &gaspool.UserTx{
		From:       sender.Hex(),
		To:         toAddr,
		Value:      value,
		ChainIndex: pm.chainIndex,
		RawTxHex:   rawTxHex,
		Contract:   contract,
		TxHash:     evmTx.Hash().Hex(),
		TxType:     txType,
	}, nil
}

func (pm *Paymaster) SendDepositTx(ctx context.Context, tx *gaspool.UserTx) (err error) {
	// 性能监控：记录开始时间
	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		pm.log.Infof("BSC链交易处理完成，耗时: %v", duration)
	}()

	// 验证输入参数
	if tx == nil {
		return fmt.Errorf("tx is nil")
	}

	if tx.RawTxHex == "" {
		return fmt.Errorf("invalid rawTx")
	}

	// 解码EVM交易以分析gas price和交易类型
	evmTx, err := utils.RlpDecodeBytes(tx.RawTxHex)
	if err != nil {
		return fmt.Errorf("failed to decode EVM transaction: %w", err)
	}

	// 获取交易的gas price
	gasPrice := evmTx.GasPrice()

	// 判断交易类型（是否为token转账）
	isTokenTransfer := pm.isTokenTransfer(evmTx)

	var txHash string

	// 根据gas price和交易类型选择处理方式
	if gasPrice.Sign() == 0 && isTokenTransfer {
		// gas price为0且是token转账，使用MegaFuel服务
		txHash, err = pm.sendTransactionViaMegaFuel(ctx, tx.RawTxHex)
	} else {
		txHash, err = pm.sendUserTransaction(ctx, evmTx)
	}

	if err != nil {
		return fmt.Errorf("failed to send BSC transaction: %w", err)
	}

	// 更新交易哈希
	tx.TxHash = txHash
	return nil
}

func (pm *Paymaster) SendSponsorTx(ctx context.Context, tx *model.GasPoolSponsorTx) (err error) {
	// 性能监控：记录开始时间
	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		pm.log.Infof("BSC链交易处理完成，耗时: %v", duration)
	}()

	// 验证输入参数
	if tx == nil {
		return fmt.Errorf("tx is nil")
	}

	if tx.RawTxHex == "" {
		pm.log.Errorf("交易ID %d 的原始交易数据为空", tx.ID)
		return fmt.Errorf("invalid rawTx")
	}

	pm.log.Debugf("开始处理BSC链交易，交易ID: %d", tx.ID)

	// 解码EVM交易以分析gas price和交易类型
	evmTx, err := utils.RlpDecodeBytes(tx.RawTxHex)
	if err != nil {
		pm.log.Errorf("解码EVM交易失败，交易ID: %d, 错误: %v", tx.ID, err)
		return fmt.Errorf("failed to decode EVM transaction: %w", err)
	}

	// 获取交易的gas price
	gasPrice := evmTx.GasPrice()
	pm.log.Debugf("交易gas price: %s wei，交易ID: %d", gasPrice.String(), tx.ID)

	// 判断交易类型（是否为token转账）
	isTokenTransfer := pm.isTokenTransfer(evmTx)
	pm.log.Debugf("交易类型检测 - 是否为token转账: %t，交易ID: %d", isTokenTransfer, tx.ID)

	var txHash string

	// 根据gas price和交易类型选择处理方式
	if gasPrice.Sign() == 0 && isTokenTransfer {
		// gas price为0且是token转账，使用MegaFuel服务
		pm.log.Infof("使用MegaFuel服务处理零gas价格的token转账，交易ID: %d", tx.ID)
		txHash, err = pm.sendTransactionViaMegaFuel(ctx, tx.RawTxHex)
		if err != nil {
			pm.log.Errorf("MegaFuel发送交易失败，回退到传统gas转账方式，交易ID: %d, 错误: %v", tx.ID, err)
			// 回退到传统方式
			txHash, err = pm.sendTransactionWithGasTransfer(ctx, tx, evmTx)
		}
	} else {
		// 其他情况使用传统的gas转账方式
		if gasPrice.Sign() == 0 {
			pm.log.Infof("零gas价格的非token转账，使用传统gas转账方式，交易ID: %d", tx.ID)
		} else {
			pm.log.Infof("非零gas价格交易，使用传统gas转账方式，交易ID: %d", tx.ID)
		}
		txHash, err = pm.sendTransactionWithGasTransfer(ctx, tx, evmTx)
	}

	if err != nil {
		pm.log.Errorf("BSC链交易发送失败，交易ID: %d, 错误: %v", tx.ID, err)
		return fmt.Errorf("failed to send BSC transaction: %w", err)
	}

	// 更新交易哈希
	tx.TxHash = txHash
	pm.log.Infof("BSC链交易发送成功，交易ID: %d，交易哈希: %s", tx.ID, txHash)

	return nil
}
