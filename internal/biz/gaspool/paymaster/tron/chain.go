package tron

import (
	"fmt"

	"github.com/shopspring/decimal"
)

const (
	trc20TransferMethodSignature = "0xa9059cbb"
)

var (
	decimalsTRX  = decimal.NewFromInt(1000000)
	decimalsUSDT = decimal.NewFromInt(1000000)
	decimalsUSDC = decimal.NewFromInt(1000000)
)

const (
	contractUSDT = "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"
	contractUSDC = "TEkxiTehnzSmSe2XqrBj4w32RUN966rdz8"
)

func getDecimalsByAddress(address string) (decimal.Decimal, error) {
	switch address {
	case contractUSDT:
		return decimalsUSDT, nil
	case contractUSDC:
		return decimalsUSDC, nil
	case "":
		return decimalsTRX, nil
	default:
		return decimal.Decimal{}, fmt.Errorf("decimals not found: %s", address)
	}
}
