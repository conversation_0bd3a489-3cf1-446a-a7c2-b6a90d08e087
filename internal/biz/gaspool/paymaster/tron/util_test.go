package tron

import (
	"encoding/hex"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestVerifyTxSignature(t *testing.T) {
	txHash, _ := hex.DecodeString("6f99e0e20a39a398ad468977b3bec726ed09ff45ae5f44d27589389134fdc2bf")
	signature, _ := hex.DecodeString("8e13662e12e662154971f06ebf6bbac10264ffb4429096df6b4c026eb36921d36bdd378fabab3f05caadcdcb69049b28f58f1ca28b8308f78a41896cddcd07f400")
	addr := "TMBHCWVLYQYUy688EByoduJnzeVnkwnQRY"
	ok, err := verifyTxSignature(txHash, signature, addr)
	assert.NoError(t, err)
	assert.True(t, ok)
}

func TestParseRawTxJson(t *testing.T) {
	tx, err := parseRawTxJson(`{
	"signature": ["1f52e93eda854535869a4481a58451ecb218428445889be1f8cea2b5de6873f5"],
  "txID": "1f52e93eda854535869a4481a58451ecb218428445889be1f8cea2b5de6873f5",
  "visible": false,
  "raw_data": {
    "contract": [
      {
        "type": "TransferContract",
        "parameter": {
          "value": {
            "amount": 6140540,
            "to_address": "41dad908b40deaf4f6a49d71c0b5eb957d07ecc232",
            "owner_address": "417af1e1058f1e2b3db4a2a905b9095ebe0d2928b6"
          },
          "type_url": "type.googleapis.com/protocol.TransferContract"
        }
      }
    ],
    "timestamp": 1753348998602,
    "expiration": 1753349358619,
    "ref_block_hash": "e6dfab62b14c0a7c",
    "ref_block_bytes": "a972"
  },
  "raw_data_hex": "0a02a9722208e6dfab62b14c0a7c409b8890de83335a68080112640a2d747970652e676f6f676c65617069732e636f6d2f70726f746f636f6c2e5472616e73666572436f6e747261637412330a15417af1e1058f1e2b3db4a2a905b9095ebe0d2928b6121541dad908b40deaf4f6a49d71c0b5eb957d07ecc23218fce4f60270ca8bfadd8333"
}`)
	assert.NoError(t, err)
	t.Log(tx)
}
