package evm

import (
	"context"
	"fmt"
	"math/big"
	"strings"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/go-kratos/kratos/v2/log"
)

// optimism_oracle.go - Optimism L1 data fee计算相关功能
// 集成Optimism gas price oracle合约，支持L1 data fee的精确计算

// OptimismOracleInterface Oracle接口定义，便于测试和模拟
type OptimismOracleInterface interface {
	GetL1FeeParams(ctx context.Context) (*L1FeeParams, error)
	CalculateL1DataFee(txData []byte, params *L1FeeParams) (*big.Int, error)
	GetL1FeeFromContract(ctx context.Context, txData []byte) (*big.Int, error)
	CalculateL1DataFeeWithFallback(ctx context.Context, txData []byte, params *L1FeeParams) (*big.Int, error)
}

const (
	// OptimismGasPriceOracleAddress Optimism gas price oracle合约地址
	OptimismGasPriceOracleAddress = "0x4***********000000000000000000000000000F"

	// DefaultL1BaseFee 默认L1基础费用（当oracle调用失败时使用）
	DefaultL1BaseFee = *********** // 20 gwei

	// DefaultL1BlobBaseFee 默认L1 blob基础费用
	DefaultL1BlobBaseFee = 1000000000 // 1 gwei

	// DefaultBaseFeeScalar 默认基础费用标量（Ecotone升级后）
	DefaultBaseFeeScalar = 1368 // 0.001368 * 1e6

	// DefaultBlobBaseFeeScalar 默认blob基础费用标量
	DefaultBlobBaseFeeScalar = 810949 // 0.810949 * 1e6
)

// optimismGasPriceOracleABI Optimism gas price oracle合约ABI
// 包含获取L1费用相关参数的方法
const optimismGasPriceOracleABI = `[
	{
		"inputs": [],
		"name": "l1BaseFee",
		"outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
		"stateMutability": "view",
		"type": "function"
	},
	{
		"inputs": [],
		"name": "blobBaseFee",
		"outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
		"stateMutability": "view",
		"type": "function"
	},
	{
		"inputs": [],
		"name": "baseFeeScalar",
		"outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}],
		"stateMutability": "view",
		"type": "function"
	},
	{
		"inputs": [],
		"name": "blobBaseFeeScalar",
		"outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}],
		"stateMutability": "view",
		"type": "function"
	},
	{
		"inputs": [],
		"name": "overhead",
		"outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
		"stateMutability": "view",
		"type": "function"
	},
	{
		"inputs": [],
		"name": "scalar",
		"outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
		"stateMutability": "view",
		"type": "function"
	},
	{
		"inputs": [{"internalType": "bytes", "name": "_data", "type": "bytes"}],
		"name": "getL1Fee",
		"outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
		"stateMutability": "view",
		"type": "function"
	}
]`

// OptimismOracle Optimism gas price oracle客户端
// 用于获取L1 data fee计算所需的参数
type OptimismOracle struct {
	log    *log.Helper
	client *ethclient.Client

	// 合约地址和ABI
	oracleAddress common.Address
	oracleABI     abi.ABI
}

// NewOptimismOracle 创建新的Optimism oracle实例
// 参数:
//   - logger: 日志记录器
//   - client: Optimism网络的以太坊客户端
func NewOptimismOracle(logger log.Logger, client *ethclient.Client) (*OptimismOracle, error) {
	// 解析gas price oracle ABI
	oracleABI, err := abi.JSON(strings.NewReader(optimismGasPriceOracleABI))
	if err != nil {
		return nil, fmt.Errorf("failed to parse Optimism gas price oracle ABI: %w", err)
	}

	return &OptimismOracle{
		log:           log.NewHelper(logger),
		client:        client,
		oracleAddress: common.HexToAddress(OptimismGasPriceOracleAddress),
		oracleABI:     oracleABI,
	}, nil
}

// L1FeeParams L1费用计算参数
type L1FeeParams struct {
	L1BaseFee         *big.Int // L1基础费用
	L1BlobBaseFee     *big.Int // L1 blob基础费用
	BaseFeeScalar     uint32   // 基础费用标量
	BlobBaseFeeScalar uint32   // blob基础费用标量
	Overhead          *big.Int // 固定开销（Bedrock版本使用）
	Scalar            *big.Int // 动态标量（Bedrock版本使用）
}

// GetL1FeeParams 获取L1费用计算参数
// 支持不同版本的Optimism升级（Bedrock、Ecotone、Fjord）
func (o *OptimismOracle) GetL1FeeParams(ctx context.Context) (*L1FeeParams, error) {
	params := &L1FeeParams{}

	// 获取L1基础费用
	l1BaseFee, err := o.getL1BaseFee(ctx)
	if err != nil {
		o.log.Warnf("获取L1基础费用失败，使用默认值: %v", err)
		l1BaseFee = big.NewInt(DefaultL1BaseFee)
	}
	params.L1BaseFee = l1BaseFee

	// 获取L1 blob基础费用（Ecotone升级后支持）
	l1BlobBaseFee, err := o.getL1BlobBaseFee(ctx)
	if err != nil {
		o.log.Debugf("获取L1 blob基础费用失败（可能是Ecotone升级前），使用默认值: %v", err)
		l1BlobBaseFee = big.NewInt(DefaultL1BlobBaseFee)
	}
	params.L1BlobBaseFee = l1BlobBaseFee

	// 获取费用标量（Ecotone升级后）
	baseFeeScalar, blobBaseFeeScalar, err := o.getFeeScalars(ctx)
	if err != nil {
		o.log.Warnf("获取费用标量失败，使用默认值: %v", err)
		baseFeeScalar = DefaultBaseFeeScalar
		blobBaseFeeScalar = DefaultBlobBaseFeeScalar
	}
	params.BaseFeeScalar = baseFeeScalar
	params.BlobBaseFeeScalar = blobBaseFeeScalar

	// 获取传统参数（Bedrock版本兼容）
	overhead, scalar, err := o.getLegacyParams(ctx)
	if err != nil {
		o.log.Debugf("获取传统参数失败（可能是Ecotone升级后），忽略: %v", err)
		// Ecotone升级后这些参数可能不存在，这是正常的
	}
	params.Overhead = overhead
	params.Scalar = scalar

	o.log.Debugf("获取Optimism L1费用参数成功 - L1BaseFee: %s, L1BlobBaseFee: %s, BaseFeeScalar: %d, BlobBaseFeeScalar: %d",
		params.L1BaseFee.String(), params.L1BlobBaseFee.String(), params.BaseFeeScalar, params.BlobBaseFeeScalar)

	return params, nil
}

// CalculateL1DataFee 计算L1 data fee
// 支持多种计算方式：手动计算和合约调用
// 参数:
//   - txData: 交易数据（RLP编码后的字节）
//   - params: L1费用参数
//
// 返回值:
//   - *big.Int: L1 data fee（以wei为单位）
//   - error: 错误信息
func (o *OptimismOracle) CalculateL1DataFee(txData []byte, params *L1FeeParams) (*big.Int, error) {
	if len(txData) == 0 {
		return nil, fmt.Errorf("transaction data is empty")
	}

	// 检测使用哪种费用计算方式
	if params.BaseFeeScalar > 0 || params.BlobBaseFeeScalar > 0 {
		// Ecotone/Fjord升级后的计算方式
		return o.calculateEcotoneL1Fee(txData, params)
	} else if params.Overhead != nil && params.Scalar != nil {
		// Bedrock版本的计算方式
		return o.calculateBedrockL1Fee(txData, params)
	} else {
		return nil, fmt.Errorf("invalid L1 fee parameters")
	}
}

// CalculateL1DataFeeWithFallback 计算L1 data fee，支持合约调用作为后备方案
// 优先使用手动计算，如果参数不可用或不可靠，则使用合约调用作为后备
// 参数:
//   - ctx: 上下文对象
//   - txData: 交易数据（RLP编码后的字节）
//   - params: L1费用参数（可能为nil或不完整）
//
// 返回值:
//   - *big.Int: L1 data fee（以wei为单位）
//   - error: 错误信息
func (o *OptimismOracle) CalculateL1DataFeeWithFallback(ctx context.Context, txData []byte, params *L1FeeParams) (*big.Int, error) {
	if len(txData) == 0 {
		return nil, fmt.Errorf("transaction data is empty")
	}

	// 尝试手动计算（如果参数可用且可靠）
	if o.isParamsReliable(params) {
		o.log.Debugf("使用手动计算方式计算L1 data fee")
		manualFee, err := o.CalculateL1DataFee(txData, params)
		if err == nil {
			o.log.Debugf("手动计算L1 data fee成功: %s wei", manualFee.String())
			return manualFee, nil
		}
		o.log.Warnf("手动计算L1 data fee失败，将使用合约调用作为后备: %v", err)
	} else {
		o.log.Debugf("L1费用参数不可靠或不完整，直接使用合约调用")
	}

	// 使用合约调用作为后备方案
	o.log.Debugf("使用Optimism oracle合约getL1Fee方法作为后备方案")
	contractFee, err := o.GetL1FeeFromContract(ctx, txData)
	if err != nil {
		return nil, fmt.Errorf("both manual calculation and contract call failed: %w", err)
	}

	o.log.Debugf("合约调用L1 data fee成功: %s wei", contractFee.String())
	return contractFee, nil
}

// calculateEcotoneL1Fee 计算Ecotone升级后的L1费用
func (o *OptimismOracle) calculateEcotoneL1Fee(txData []byte, params *L1FeeParams) (*big.Int, error) {
	// 计算压缩后的交易大小估算
	compressedSize := o.estimateCompressedSize(txData)

	// 计算加权gas价格
	// weighted_gas_price = 16 * base_fee_scalar * base_fee + blob_base_fee_scalar * blob_base_fee
	baseFeeComponent := new(big.Int).Mul(
		new(big.Int).SetUint64(uint64(params.BaseFeeScalar)),
		params.L1BaseFee,
	)
	baseFeeComponent.Mul(baseFeeComponent, big.NewInt(16))

	blobFeeComponent := new(big.Int).Mul(
		new(big.Int).SetUint64(uint64(params.BlobBaseFeeScalar)),
		params.L1BlobBaseFee,
	)

	weightedGasPrice := new(big.Int).Add(baseFeeComponent, blobFeeComponent)

	// l1_data_fee = compressed_size * weighted_gas_price / 1e6
	l1Fee := new(big.Int).Mul(compressedSize, weightedGasPrice)
	l1Fee.Div(l1Fee, big.NewInt(1000000)) // 除以1e6

	o.log.Debugf("Ecotone L1费用计算 - 压缩大小: %s, 加权gas价格: %s, L1费用: %s wei",
		compressedSize.String(), weightedGasPrice.String(), l1Fee.String())

	return l1Fee, nil
}

// calculateBedrockL1Fee 计算Bedrock版本的L1费用
func (o *OptimismOracle) calculateBedrockL1Fee(txData []byte, params *L1FeeParams) (*big.Int, error) {
	// 计算交易数据的gas成本
	txDataGas := o.calculateTxDataGas(txData)

	// 应用固定和动态开销
	// tx_total_gas = (tx_data_gas + fixed_overhead) * dynamic_overhead
	totalGas := new(big.Int).Add(txDataGas, params.Overhead)
	totalGas.Mul(totalGas, params.Scalar)
	totalGas.Div(totalGas, big.NewInt(1000000)) // Scalar是以1e6为单位的

	// l1_data_fee = tx_total_gas * ethereum_base_fee
	l1Fee := new(big.Int).Mul(totalGas, params.L1BaseFee)

	o.log.Debugf("Bedrock L1费用计算 - 数据gas: %s, 总gas: %s, L1费用: %s wei",
		txDataGas.String(), totalGas.String(), l1Fee.String())

	return l1Fee, nil
}

// estimateCompressedSize 估算压缩后的交易大小
// 使用与Optimism相同的算法：(zero_bytes * 4 + non_zero_bytes * 16) / 16
func (o *OptimismOracle) estimateCompressedSize(txData []byte) *big.Int {
	zeroBytes := 0
	nonZeroBytes := 0

	for _, b := range txData {
		if b == 0 {
			zeroBytes++
		} else {
			nonZeroBytes++
		}
	}

	// compressed_size = (zero_bytes * 4 + non_zero_bytes * 16) / 16
	totalGas := zeroBytes*4 + nonZeroBytes*16
	compressedSize := big.NewInt(int64(totalGas / 16))

	return compressedSize
}

// calculateTxDataGas 计算交易数据的gas成本（Bedrock版本）
func (o *OptimismOracle) calculateTxDataGas(txData []byte) *big.Int {
	zeroBytes := 0
	nonZeroBytes := 0

	for _, b := range txData {
		if b == 0 {
			zeroBytes++
		} else {
			nonZeroBytes++
		}
	}

	// tx_data_gas = zero_bytes * 4 + non_zero_bytes * 16
	totalGas := zeroBytes*4 + nonZeroBytes*16
	return big.NewInt(int64(totalGas))
}

// getL1BaseFee 获取L1基础费用
func (o *OptimismOracle) getL1BaseFee(ctx context.Context) (*big.Int, error) {
	// 调用合约的l1BaseFee方法
	data, err := o.oracleABI.Pack("l1BaseFee")
	if err != nil {
		return nil, fmt.Errorf("failed to pack l1BaseFee call: %w", err)
	}

	result, err := o.client.CallContract(ctx, ethereum.CallMsg{
		To:   &o.oracleAddress,
		Data: data,
	}, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to call l1BaseFee: %w", err)
	}

	var l1BaseFee *big.Int
	err = o.oracleABI.UnpackIntoInterface(&l1BaseFee, "l1BaseFee", result)
	if err != nil {
		return nil, fmt.Errorf("failed to unpack l1BaseFee result: %w", err)
	}

	return l1BaseFee, nil
}

// getL1BlobBaseFee 获取L1 blob基础费用
func (o *OptimismOracle) getL1BlobBaseFee(ctx context.Context) (*big.Int, error) {
	// 调用合约的blobBaseFee方法
	data, err := o.oracleABI.Pack("blobBaseFee")
	if err != nil {
		return nil, fmt.Errorf("failed to pack blobBaseFee call: %w", err)
	}

	result, err := o.client.CallContract(ctx, ethereum.CallMsg{
		To:   &o.oracleAddress,
		Data: data,
	}, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to call blobBaseFee: %w", err)
	}

	var blobBaseFee *big.Int
	err = o.oracleABI.UnpackIntoInterface(&blobBaseFee, "blobBaseFee", result)
	if err != nil {
		return nil, fmt.Errorf("failed to unpack blobBaseFee result: %w", err)
	}

	return blobBaseFee, nil
}

// getFeeScalars 获取费用标量参数
func (o *OptimismOracle) getFeeScalars(ctx context.Context) (uint32, uint32, error) {
	// 获取基础费用标量
	baseFeeScalarData, err := o.oracleABI.Pack("baseFeeScalar")
	if err != nil {
		return 0, 0, fmt.Errorf("failed to pack baseFeeScalar call: %w", err)
	}

	baseFeeScalarResult, err := o.client.CallContract(ctx, ethereum.CallMsg{
		To:   &o.oracleAddress,
		Data: baseFeeScalarData,
	}, nil)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to call baseFeeScalar: %w", err)
	}

	var baseFeeScalar uint32
	err = o.oracleABI.UnpackIntoInterface(&baseFeeScalar, "baseFeeScalar", baseFeeScalarResult)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to unpack baseFeeScalar result: %w", err)
	}

	// 获取blob基础费用标量
	blobBaseFeeScalarData, err := o.oracleABI.Pack("blobBaseFeeScalar")
	if err != nil {
		return 0, 0, fmt.Errorf("failed to pack blobBaseFeeScalar call: %w", err)
	}

	blobBaseFeeScalarResult, err := o.client.CallContract(ctx, ethereum.CallMsg{
		To:   &o.oracleAddress,
		Data: blobBaseFeeScalarData,
	}, nil)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to call blobBaseFeeScalar: %w", err)
	}

	var blobBaseFeeScalar uint32
	err = o.oracleABI.UnpackIntoInterface(&blobBaseFeeScalar, "blobBaseFeeScalar", blobBaseFeeScalarResult)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to unpack blobBaseFeeScalar result: %w", err)
	}

	return baseFeeScalar, blobBaseFeeScalar, nil
}

// getLegacyParams 获取传统参数（Bedrock版本兼容）
func (o *OptimismOracle) getLegacyParams(ctx context.Context) (*big.Int, *big.Int, error) {
	// 获取overhead参数
	overheadData, err := o.oracleABI.Pack("overhead")
	if err != nil {
		return nil, nil, fmt.Errorf("failed to pack overhead call: %w", err)
	}

	overheadResult, err := o.client.CallContract(ctx, ethereum.CallMsg{
		To:   &o.oracleAddress,
		Data: overheadData,
	}, nil)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to call overhead: %w", err)
	}

	var overhead *big.Int
	err = o.oracleABI.UnpackIntoInterface(&overhead, "overhead", overheadResult)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to unpack overhead result: %w", err)
	}

	// 获取scalar参数
	scalarData, err := o.oracleABI.Pack("scalar")
	if err != nil {
		return nil, nil, fmt.Errorf("failed to pack scalar call: %w", err)
	}

	scalarResult, err := o.client.CallContract(ctx, ethereum.CallMsg{
		To:   &o.oracleAddress,
		Data: scalarData,
	}, nil)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to call scalar: %w", err)
	}

	var scalar *big.Int
	err = o.oracleABI.UnpackIntoInterface(&scalar, "scalar", scalarResult)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to unpack scalar result: %w", err)
	}

	return overhead, scalar, nil
}

// GetL1FeeFromContract 直接调用Optimism oracle合约的getL1Fee方法获取L1 data fee
// 这是官方推荐的方法，可以作为手动计算的验证或后备方案
// 参数:
//   - ctx: 上下文对象
//   - txData: RLP编码的交易数据
//
// 返回值:
//   - *big.Int: L1 data fee（以wei为单位）
//   - error: 错误信息
func (o *OptimismOracle) GetL1FeeFromContract(ctx context.Context, txData []byte) (*big.Int, error) {
	if len(txData) == 0 {
		return nil, fmt.Errorf("transaction data is empty")
	}

	o.log.Debugf("开始调用Optimism oracle合约getL1Fee方法，交易数据长度: %d", len(txData))

	// 调用合约的getL1Fee方法
	data, err := o.oracleABI.Pack("getL1Fee", txData)
	if err != nil {
		return nil, fmt.Errorf("failed to pack getL1Fee call: %w", err)
	}

	result, err := o.client.CallContract(ctx, ethereum.CallMsg{
		To:   &o.oracleAddress,
		Data: data,
	}, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to call getL1Fee: %w", err)
	}

	var l1Fee *big.Int
	err = o.oracleABI.UnpackIntoInterface(&l1Fee, "getL1Fee", result)
	if err != nil {
		return nil, fmt.Errorf("failed to unpack getL1Fee result: %w", err)
	}

	o.log.Debugf("Optimism oracle合约getL1Fee调用成功，返回L1费用: %s wei", l1Fee.String())

	return l1Fee, nil
}

// isParamsReliable 检查L1费用参数是否可靠和完整
// 用于决定是否使用手动计算还是合约调用
// 参数:
//   - params: L1费用参数
//
// 返回值:
//   - bool: 参数是否可靠
func (o *OptimismOracle) isParamsReliable(params *L1FeeParams) bool {
	if params == nil {
		return false
	}

	// 检查Ecotone/Fjord升级后的参数
	if params.BaseFeeScalar > 0 || params.BlobBaseFeeScalar > 0 {
		// 需要基础费用和blob费用都存在
		if params.L1BaseFee != nil && params.L1BaseFee.Sign() > 0 &&
			params.L1BlobBaseFee != nil && params.L1BlobBaseFee.Sign() >= 0 {
			return true
		}
	}

	// 检查Bedrock版本的参数
	if params.Overhead != nil && params.Scalar != nil &&
		params.L1BaseFee != nil && params.L1BaseFee.Sign() > 0 {
		return true
	}

	return false
}
