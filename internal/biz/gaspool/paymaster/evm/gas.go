package evm

import (
	"byd_wallet/common/constant"
	"byd_wallet/utils"
	"context"
	"math/big"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/shopspring/decimal"
)

// gas.go - EVM paymaster gas费用计算相关功能
// 包含gas费用估算、默认费用计算等功能

// estimateGasTransferFee 估算gas转账交易的费用
// 参数:
//   - ctx: 上下文对象
//   - userTx: 用户的原始交易，用于获取发送者地址和gas价格参考
//
// 返回值:
//   - *big.Int: 估算的gas转账费用（以wei为单位）
//   - error: 错误信息
func (pm *Paymaster) estimateGasTransferFee(ctx context.Context, userTx *types.Transaction) (*big.Int, error) {
	pm.log.Debugf("开始估算%s链gas转账交易费用", pm.chainName)

	// 步骤1: 获取交易发送者地址
	senderAddr, err := utils.GetTxSender(userTx)
	if err != nil {
		pm.log.Warnf("获取交易发送者地址失败，使用默认gas转账费用: %v", err)
		return pm.getDefaultGasTransferFee(userTx.GasPrice()), nil
	}

	// 步骤2: 获取EVM客户端（关键步骤，失败时立即回退）
	client, err := pm.evmCli.Select(pm.chainIndex)
	if err != nil {
		pm.log.Warnf("无法获取%s链EVM客户端（可能在测试环境），回退到默认gas转账费用计算: %v", pm.chainName, err)
		return pm.getDefaultGasTransferFee(userTx.GasPrice()), nil
	}

	// 步骤3: 获取热钱包私钥和地址
	hotWalletPrivateKey, err := pm.hotAccountReader.GetHotAccount(ctx, pm.chainIndex)
	if err != nil {
		pm.log.Warnf("获取热钱包私钥失败，使用默认gas转账费用: %v", err)
		return pm.getDefaultGasTransferFee(userTx.GasPrice()), nil
	}

	hotWalletAddr, _, err := utils.GetAddressByPrivateKey(hotWalletPrivateKey)
	if err != nil {
		pm.log.Warnf("解析热钱包地址失败，使用默认gas转账费用: %v", err)
		return pm.getDefaultGasTransferFee(userTx.GasPrice()), nil
	}

	// 步骤4: 获取当前gas价格
	gasTipCap, err := client.SuggestGasTipCap(ctx)
	if err != nil {
		pm.log.Warnf("获取建议tip失败，使用用户交易的gas价格: %v", err)
		gasTipCap = userTx.GasPrice()
		if gasTipCap == nil || gasTipCap.Sign() <= 0 {
			pm.log.Warnf("用户交易gas价格无效，回退到默认gas转账费用计算")
			return pm.getDefaultGasTransferFee(userTx.GasPrice()), nil
		}
	}

	// 计算基础费用（简化处理）
	baseFee := big.NewInt(constant.DefaultBaseFee)
	gasFeeCap := new(big.Int).Add(gasTipCap, baseFee)

	// 步骤5: 创建模拟的gas转账交易用于估算
	// 使用较小的转账金额进行估算（1 wei）
	simulationValue := big.NewInt(1)

	// 步骤6: 使用EstimateGas进行精确估算
	estimatedGasLimit, err := client.EstimateGas(ctx, ethereum.CallMsg{
		From:      hotWalletAddr,
		To:        &senderAddr,
		GasFeeCap: gasFeeCap,
		GasTipCap: gasTipCap,
		Value:     simulationValue,
		Data:      nil,
	})
	if err != nil {
		pm.log.Warnf("动态gas估算失败，回退到默认gas转账费用计算: %v", err)
		return pm.getDefaultGasTransferFee(userTx.GasPrice()), nil
	}

	// 步骤7: 应用安全系数（1.2倍）
	safetyFactor := decimal.NewFromFloat(DefaultGasLimitMultiplier)
	safeGasLimit := decimal.NewFromInt(int64(estimatedGasLimit)).Mul(safetyFactor)
	finalGasLimit := safeGasLimit.BigInt()

	// 步骤8: 计算总gas费用
	totalGasTransferFee := new(big.Int).Mul(finalGasLimit, gasFeeCap)

	pm.log.Debugf("动态gas转账费用估算成功 - 估算GasLimit: %d, 安全GasLimit: %s, GasPrice: %s wei, 总费用: %s wei",
		estimatedGasLimit, finalGasLimit.String(), gasFeeCap.String(), totalGasTransferFee.String())

	return totalGasTransferFee, nil
}

// getDefaultGasTransferFee 获取默认的gas转账费用（作为后备方案）
// 参数:
//   - userGasPrice: 用户交易的gas价格，用作参考
//
// 返回值:
//   - *big.Int: 默认的gas转账费用（以wei为单位）
func (pm *Paymaster) getDefaultGasTransferFee(userGasPrice *big.Int) *big.Int {
	// 使用标准ETH转账的gas limit (21000) 乘以安全系数 (1.2)
	defaultGasLimit := big.NewInt(21000)
	safetyFactor := decimal.NewFromFloat(1.2)
	safeGasLimit := decimal.NewFromBigInt(defaultGasLimit, 0).Mul(safetyFactor)

	// 使用用户交易的gas价格或默认值
	gasPrice := userGasPrice
	if gasPrice == nil || gasPrice.Sign() <= 0 {
		gasPrice = big.NewInt(constant.DefaultBaseFee + constant.DefaultGasTipCap)
	}

	// 计算默认gas转账费用
	defaultFee := new(big.Int).Mul(safeGasLimit.BigInt(), gasPrice)

	pm.log.Debugf("使用默认gas转账费用 - GasLimit: %s, GasPrice: %s wei, 总费用: %s wei",
		safeGasLimit.String(), gasPrice.String(), defaultFee.String())

	return defaultFee
}

// getBaseUnit 获取链的基本单位
// 返回值:
//   - decimal.Decimal: 链的基本单位
func (pm *Paymaster) getBaseUnit() decimal.Decimal {
	switch pm.chainIndex {
	case constant.EthChainIndex, constant.ArbChainIndex, constant.OptimismChainIndex, constant.BaseChainIndex:
		return constant.BaseUnitPerETH
	case constant.PolChainIndex:
		return constant.BaseUnitPerETH // Polygon也使用18位精度
	default:
		return constant.BaseUnitPerETH // 默认使用ETH单位
	}
}

// getChainSpecificGasMultiplier 获取链特定的gas费用倍数
// 不同链可能需要不同的安全系数
func (pm *Paymaster) getChainSpecificGasMultiplier() decimal.Decimal {
	switch pm.chainIndex {
	case constant.OptimismChainIndex:
		// Optimism由于L1 data fee的存在，需要稍高的安全系数
		return decimal.NewFromFloat(1.3)
	case constant.ArbChainIndex:
		// Arbitrum通常gas费用较低，使用标准系数
		return decimal.NewFromFloat(DefaultGasLimitMultiplier)
	case constant.BaseChainIndex:
		// Base链使用标准系数
		return decimal.NewFromFloat(DefaultGasLimitMultiplier)
	case constant.EthChainIndex:
		// 以太坊主网使用标准系数
		return decimal.NewFromFloat(DefaultGasLimitMultiplier)
	case constant.PolChainIndex:
		// Polygon使用标准系数
		return decimal.NewFromFloat(DefaultGasLimitMultiplier)
	default:
		// 默认使用标准系数
		return decimal.NewFromFloat(DefaultGasLimitMultiplier)
	}
}
