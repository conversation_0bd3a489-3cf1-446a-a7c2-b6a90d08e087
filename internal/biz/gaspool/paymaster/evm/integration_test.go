package evm

import (
	"byd_wallet/common/constant"
	"fmt"
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
)

// integration_test.go - Optimism集成测试
// 验证Optimism支持不会影响现有EVM链的功能

// TestOptimismIntegrationWithExistingChains 测试Optimism集成不影响现有链
func TestOptimismIntegrationWithExistingChains(t *testing.T) {
	// 测试所有支持的EVM链
	supportedChains := []struct {
		chainIndex int64
		chainName  string
	}{
		{constant.EthChainIndex, "Ethereum"},
		{constant.PolChainIndex, "Polygon"},
		{constant.ArbChainIndex, "Arbitrum"},
		{constant.OptimismChainIndex, "Optimism"}, // 新增的Optimism链
		{constant.BaseChainIndex, "Base"},
	}

	for _, chain := range supportedChains {
		t.Run(fmt.Sprintf("测试%s链常量定义", chain.chainName), func(t *testing.T) {
			// 验证链名称
			actualName := constant.GetChainName(chain.chainIndex)
			assert.Equal(t, chain.chainName, actualName)

			// 验证链索引有效性
			assert.True(t, constant.IsValidChainIndex(chain.chainIndex))

			// 验证是EVM链
			assert.True(t, constant.IsEVMChain(chain.chainIndex))
		})
	}
}

// TestPaymasterFactoryOptimismSupport 测试paymaster factory支持Optimism
func TestPaymasterFactoryOptimismSupport(t *testing.T) {
	// 这个测试需要真实的paymaster factory，但我们可以验证常量定义

	// 验证Optimism链索引已定义
	assert.Equal(t, int64(10000070), constant.OptimismChainIndex)

	// 验证链名称映射
	chainName := constant.GetChainName(constant.OptimismChainIndex)
	assert.Equal(t, "Optimism", chainName)

	// 验证链ID映射
	chainID := constant.GetChainID(constant.OptimismChainIndex)
	assert.Equal(t, uint64(10), chainID)

	// 验证是EVM链
	assert.True(t, constant.IsEVMChain(constant.OptimismChainIndex))
}

// TestOptimismGasCalculationCompatibility 测试Optimism gas计算兼容性
func TestOptimismGasCalculationCompatibility(t *testing.T) {
	logger := log.NewStdLogger(nil)

	// 创建Optimism paymaster
	optimismPM := &Paymaster{
		log:                log.NewHelper(logger),
		chainIndex:         constant.OptimismChainIndex,
		chainName:          "Optimism",
		priceExpireSeconds: DefaultPriceExpireSeconds,
	}

	// 创建其他EVM链的paymaster进行对比
	ethPM := &Paymaster{
		log:                log.NewHelper(logger),
		chainIndex:         constant.EthChainIndex,
		chainName:          "Ethereum",
		priceExpireSeconds: DefaultPriceExpireSeconds,
	}

	// 测试基本单位一致性
	t.Run("基本单位一致性", func(t *testing.T) {
		optimismUnit := optimismPM.getBaseUnit()
		ethUnit := ethPM.getBaseUnit()

		// 都应该使用ETH单位
		assert.Equal(t, ethUnit, optimismUnit)
		assert.Equal(t, constant.BaseUnitPerETH, optimismUnit)
	})

	// 测试链特定的gas倍数
	t.Run("链特定gas倍数", func(t *testing.T) {
		optimismMultiplier := optimismPM.getChainSpecificGasMultiplier()
		ethMultiplier := ethPM.getChainSpecificGasMultiplier()

		// Optimism应该有稍高的倍数
		assert.True(t, optimismMultiplier.GreaterThan(ethMultiplier))
		assert.Equal(t, 1.3, optimismMultiplier.InexactFloat64())
		assert.Equal(t, DefaultGasLimitMultiplier, ethMultiplier.InexactFloat64())
	})
}

// TestOptimismBackwardCompatibility 测试向后兼容性
func TestOptimismBackwardCompatibility(t *testing.T) {
	// 验证添加Optimism支持不会破坏现有的常量和映射

	// 验证所有现有链的常量仍然正确
	existingChains := map[int64]string{
		constant.EthChainIndex:  "Ethereum",
		constant.BscChainIndex:  "BNB Chain",
		constant.PolChainIndex:  "Polygon",
		constant.BaseChainIndex: "Base",
		constant.ArbChainIndex:  "Arbitrum",
		constant.SolChainIndex:  "Solana",
		constant.TronChainIndex: "Tron",
	}

	for chainIndex, expectedName := range existingChains {
		t.Run(fmt.Sprintf("验证%s链常量", expectedName), func(t *testing.T) {
			actualName := constant.GetChainName(chainIndex)
			assert.Equal(t, expectedName, actualName)

			// 验证链索引有效性
			assert.True(t, constant.IsValidChainIndex(chainIndex))
		})
	}

	// 验证新增的Optimism链
	t.Run("验证Optimism链常量", func(t *testing.T) {
		actualName := constant.GetChainName(constant.OptimismChainIndex)
		assert.Equal(t, "Optimism", actualName)
		assert.True(t, constant.IsValidChainIndex(constant.OptimismChainIndex))
	})
}
