package evm

import (
	"context"
	"crypto/ecdsa"
	"fmt"
	"strings"
	"time"

	"github.com/ethereum/go-ethereum/crypto"
)

// util.go - EVM paymaster 工具函数
// 包含价格过期检查、私钥获取等通用工具函数

// isPriceTimeExpired 检查价格是否过期
// 参数:
//   - priceTimeUnix: 价格时间戳
//
// 返回值:
//   - bool: 是否过期
func (pm *Paymaster) isPriceTimeExpired(priceTimeUnix int64) bool {
	return time.Now().Unix()-priceTimeUnix > pm.priceExpireSeconds
}

// getPaymasterPrivateKey 获取paymaster私钥
// 参数:
//   - ctx: 上下文对象
//
// 返回值:
//   - *ecdsa.PrivateKey: paymaster私钥
//   - error: 错误信息
func (pm *Paymaster) getPaymasterPrivateKey(ctx context.Context) (*ecdsa.PrivateKey, error) {
	// 从热钱包读取器获取私钥
	privateKeyHex, err := pm.hotAccountReader.GetHotAccount(ctx, pm.chainIndex)
	if err != nil {
		return nil, fmt.Errorf("获取热钱包账户失败: %w", err)
	}

	// 解析私钥
	privateKey, err := crypto.HexToECDSA(strings.TrimPrefix(privateKeyHex, "0x"))
	if err != nil {
		return nil, fmt.Errorf("privateKeyHex error: %w", err)
	}

	// 缓存私钥
	pm.cachedAddress = crypto.PubkeyToAddress(privateKey.PublicKey)

	pm.log.Debugf("成功加载%s链paymaster私钥，地址: %s", pm.chainName, pm.cachedAddress.Hex())
	return privateKey, nil
}
