package evm

import (
	"byd_wallet/model"
	"context"
	"fmt"
	"time"

	"github.com/ethereum/go-ethereum/common"
)

// async.go - EVM paymaster 异步任务处理相关功能
// 包含gas转账确认处理、交易状态检查等异步任务逻辑

// handleGasTransferConfirm 处理gas转账确认和用户交易发送
// 参数:
//   - ctx: 上下文对象
//   - gasTransferWaitSecs: gas转账等待确认时间（秒）
//   - gasTransferExpiredSecs: gas转账过期时间（秒）
func (pm *Paymaster) handleGasTransferConfirm(ctx context.Context, gasTransferWaitSecs int64) {
	records, err := pm.repo.AllEvmGasTransferWaitConfirmRecord(ctx)
	if err != nil {
		pm.log.Errorf("获取%s链gas转账等待确认记录失败: %v", pm.chainName, err)
		return
	}

	nowUnix := time.Now().Unix()

	for _, record := range records {
		spent := nowUnix - record.TimeUnix

		// 检查是否还需要等待
		if spent < gasTransferWaitSecs {
			continue
		}

		// 获取sponsor交易
		stx, err := pm.stxMgr.FindGasPoolSponsorTxByID(ctx, record.TxID)
		if err != nil {
			pm.log.Errorf("获取%s链sponsor交易失败: %v", pm.chainName, err)
			continue
		}

		// 检查交易状态
		if stx.Status != model.GasPoolTxStatusWaitGas {
			pm.log.Debugf("%s链交易状态已变更，删除等待确认记录，交易ID: %d，状态: %s",
				pm.chainName, record.TxID, stx.Status)
			err := pm.repo.DeleteEvmGasTransferWaitConfirmRecord(ctx, record.TxID)
			if err != nil {
				pm.log.Errorf("删除%s链gas转账等待确认记录失败: %v", pm.chainName, err)
			}
			continue
		}

		// 检查gas转账交易是否已确认
		confirmed, err := pm.checkGasTransferConfirmed(ctx, record.GasTransferTxHash)
		if err != nil {
			pm.log.Errorf("检查%s链gas转账确认状态失败，交易哈希: %s，错误: %v",
				pm.chainName, record.GasTransferTxHash, err)
			continue
		}

		if !confirmed {
			pm.log.Debugf("%s链gas转账尚未确认，继续等待，交易哈希: %s",
				pm.chainName, record.GasTransferTxHash)
			continue
		}

		pm.log.Infof("%s链gas转账已确认，开始发送用户交易，交易ID: %d，gas转账哈希: %s",
			pm.chainName, record.TxID, record.GasTransferTxHash)

		// 发送用户交易
		err = pm.sendUserTransaction(ctx, stx)
		if err != nil {
			pm.log.Errorf("%s链发送用户交易失败，交易ID: %d，错误: %v",
				pm.chainName, record.TxID, err)
			continue
		}

		// 删除等待确认记录
		err = pm.repo.DeleteEvmGasTransferWaitConfirmRecord(ctx, record.TxID)
		if err != nil {
			pm.log.Errorf("删除%s链gas转账等待确认记录失败: %v", pm.chainName, err)
		}
	}
}

// checkGasTransferConfirmed 检查gas转账交易是否已确认
// 参数:
//   - ctx: 上下文对象
//   - txHash: gas转账交易哈希
//
// 返回值:
//   - bool: 是否已确认（true表示已确认且成功）
//   - error: 错误信息
func (pm *Paymaster) checkGasTransferConfirmed(ctx context.Context, txHash string) (bool, error) {
	// 获取EVM客户端
	client, err := pm.evmCli.Select(pm.chainIndex)
	if err != nil {
		return false, fmt.Errorf("获取%s链客户端失败: %w", pm.chainName, err)
	}

	// 获取交易收据
	receipt, err := client.TransactionReceipt(ctx, common.HexToHash(txHash))
	if err != nil {
		// 如果交易还未被打包，返回false但不报错
		pm.log.Debugf("%s链gas转账交易尚未被打包，交易哈希: %s", pm.chainName, txHash)
		return false, nil
	}

	// 检查交易状态（1表示成功，0表示失败）
	if receipt.Status == 1 {
		pm.log.Debugf("%s链gas转账交易确认成功，交易哈希: %s，区块号: %d",
			pm.chainName, txHash, receipt.BlockNumber.Uint64())
		return true, nil
	} else {
		pm.log.Errorf("%s链gas转账交易执行失败，交易哈希: %s，区块号: %d",
			pm.chainName, txHash, receipt.BlockNumber.Uint64())
		return false, fmt.Errorf("gas转账交易执行失败")
	}
}
