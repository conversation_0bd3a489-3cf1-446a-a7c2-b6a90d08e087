package solana

import (
	"byd_wallet/common/constant"
	"fmt"

	"github.com/gagliardetto/solana-go"
	"github.com/gagliardetto/solana-go/programs/system"
	"github.com/gagliardetto/solana-go/programs/token"
)

// parser.go - Solana paymaster 消息解析相关功能
// 包含交易消息解析、指令解析等功能

// TransactionInfo 交易信息结构体
type TransactionInfo struct {
	From  string `json:"from"`  // 发送方地址列表
	To    string `json:"to"`    // 接收方地址列表
	Value uint64 `json:"value"` // 转账金额列表
	Type  string `json:"type"`  // 交易类型 (SOL/SPL)
	Mint  string `json:"mint"`  // 代币合约地址(仅SPL代币)
}

// parseMessage 解析消息内容，提取交易详情
// 参数：
//   - message: Solana交易消息对象
//
// 返回：
//   - []*TransactionInfo: 解析出的交易信息列表
//   - error: 解析过程中的错误
func (pm *Paymaster) parseMessage(message *solana.Message) (txList []*TransactionInfo, err error) {
	txInfo := &TransactionInfo{}

	// 获取账户密钥列表
	accountKeys := message.AccountKeys

	// 遍历所有指令
	for _, instruction := range message.Instructions {
		// 获取程序ID
		if int(instruction.ProgramIDIndex) >= len(accountKeys) {
			continue
		}
		programID := accountKeys[instruction.ProgramIDIndex]

		// 根据程序类型解析指令
		if programID.Equals(solana.SystemProgramID) {
			// 解析系统程序指令 (SOL转账)
			err := pm.parseSystemInstruction(instruction, accountKeys, txInfo)
			if err != nil {
				continue
			}
			txList = append(txList, txInfo)
		} else if programID.Equals(solana.TokenProgramID) {
			// 解析SPL代币程序指令
			err := pm.parseTokenInstruction(instruction, accountKeys, txInfo)
			if err != nil {
				continue
			}
			txList = append(txList, txInfo)
		}
	}

	return txList, nil
}

// parseSystemInstruction 解析系统程序指令 (SOL转账)
// 参数：
//   - instruction: 编译后的指令
//   - accountKeys: 账户公钥列表
//   - txInfo: 交易信息结构体（用于填充解析结果）
//
// 返回：
//   - error: 解析过程中的错误
func (pm *Paymaster) parseSystemInstruction(
	instruction solana.CompiledInstruction,
	accountKeys []solana.PublicKey,
	txInfo *TransactionInfo,
) error {
	// 使用统一的指令数据验证
	if err := validateInstructionData(instruction, 9, 2, "system transfer"); err != nil {
		return err
	}

	// 检查指令类型
	instructionType := instruction.Data[0]
	if instructionType == uint8(system.Instruction_Transfer) {
		// 使用统一的金额解析函数
		amount, err := parseTransferAmount(instruction.Data, 4)
		if err != nil {
			return fmt.Errorf("parse system transfer amount: %w", err)
		}

		// 使用统一的地址提取函数
		addresses, err := extractAccountAddresses(accountKeys, []uint16{instruction.Accounts[0], instruction.Accounts[1]})
		if err != nil {
			return fmt.Errorf("extract system transfer addresses: %w", err)
		}

		// 设置交易信息
		txInfo.From = addresses[0]
		txInfo.To = addresses[1]
		txInfo.Value = amount
		txInfo.Type = constant.NativeTokenType

		return nil
	}

	return fmt.Errorf("invalid system instruction type: %d", instructionType)
}

// parseTokenInstruction 解析SPL代币程序指令
// 参数：
//   - instruction: 编译后的指令
//   - accountKeys: 账户公钥列表
//   - txInfo: 交易信息结构体（用于填充解析结果）
//
// 返回：
//   - error: 解析过程中的错误
func (pm *Paymaster) parseTokenInstruction(
	instruction solana.CompiledInstruction,
	accountKeys []solana.PublicKey,
	txInfo *TransactionInfo,
) error {
	// 手动解析SPL代币指令
	if len(instruction.Data) == 0 {
		return fmt.Errorf("token data is empty")
	}

	// 获取指令类型
	instructionType := instruction.Data[0]

	switch instructionType {
	case token.Instruction_Transfer: // Transfer指令
		return pm.parseTokenTransferManual(instruction, accountKeys, txInfo)
	case token.Instruction_TransferChecked: // TransferChecked指令
		return pm.parseTokenTransferCheckedManual(instruction, accountKeys, txInfo)
	default:
		return fmt.Errorf("invalid instruction type: %d", instructionType)
	}
}

// parseTokenTransferManual 手动解析SPL代币转账指令
// 参数：
//   - instruction: 编译后的指令
//   - accountKeys: 账户公钥列表
//   - txInfo: 交易信息结构体（用于填充解析结果）
//
// 返回：
//   - error: 解析过程中的错误
func (pm *Paymaster) parseTokenTransferManual(
	instruction solana.CompiledInstruction,
	accountKeys []solana.PublicKey,
	txInfo *TransactionInfo,
) error {
	// 使用统一的指令数据验证
	if err := validateInstructionData(instruction, 9, 3, "token transfer"); err != nil {
		return err
	}

	// 使用统一的金额解析函数 (Transfer指令格式: [指令类型(1字节)] + [金额(8字节)])
	amount, err := parseTransferAmount(instruction.Data, 1)
	if err != nil {
		return fmt.Errorf("parse token transfer amount: %w", err)
	}

	// 使用统一的地址提取函数
	addresses, err := extractAccountAddresses(accountKeys, []uint16{instruction.Accounts[0], instruction.Accounts[1]})
	if err != nil {
		return fmt.Errorf("extract token transfer addresses: %w", err)
	}

	// 设置交易信息
	txInfo.From = addresses[0] // 源代币账户
	txInfo.To = addresses[1]   // 目标代币账户
	txInfo.Value = amount
	txInfo.Type = constant.SPLTokenType

	// 如果有mint账户信息，也添加进去
	if len(instruction.Accounts) > 2 {
		mintIndex := instruction.Accounts[2]
		if int(mintIndex) < len(accountKeys) {
			txInfo.Mint = accountKeys[mintIndex].String()
		}
	}

	return nil
}

// parseTokenTransferCheckedManual 手动解析SPL代币转账检查指令
// 参数：
//   - instruction: 编译后的指令
//   - accountKeys: 账户公钥列表
//   - txInfo: 交易信息结构体（用于填充解析结果）
//
// 返回：
//   - error: 解析过程中的错误
func (pm *Paymaster) parseTokenTransferCheckedManual(
	instruction solana.CompiledInstruction,
	accountKeys []solana.PublicKey,
	txInfo *TransactionInfo,
) error {
	// 使用统一的指令数据验证 (TransferChecked指令格式: [指令类型(1字节)] + [金额(8字节)] + [精度(1字节)])
	if err := validateInstructionData(instruction, 10, 4, "token transfer checked"); err != nil {
		return err
	}

	// 使用统一的金额解析函数
	amount, err := parseTransferAmount(instruction.Data, 1)
	if err != nil {
		return fmt.Errorf("parse token transfer checked amount: %w", err)
	}

	// 使用统一的地址提取函数
	addresses, err := extractAccountAddresses(accountKeys, []uint16{instruction.Accounts[0], instruction.Accounts[1], instruction.Accounts[2]})
	if err != nil {
		return fmt.Errorf("extract token transfer checked addresses: %w", err)
	}

	// 设置交易信息
	txInfo.From = addresses[0] // 源代币账户
	txInfo.To = addresses[2]   // 目标代币账户
	txInfo.Value = amount
	txInfo.Type = constant.SPLTokenType
	txInfo.Mint = addresses[1] // 代币合约

	return nil
}
