# Solana Paymaster Rent Fee 实现文档

## 概述

本文档描述了在 Solana paymaster 中实现的 rent fee（租金费用）计算功能。Solana 的租金机制是其独特的特性，要求账户保持最低余额以避免被垃圾回收。

## Solana 租金机制说明

### 什么是 Solana Rent？

Solana 使用租金机制来防止状态膨胀：
- **账户租金**：所有账户都需要支付租金来保持活跃状态
- **免租金状态**：账户可以通过保持足够的 SOL 余额来达到免租金状态
- **垃圾回收**：余额不足的账户会被网络回收

### 租金计算规则

1. **基础租金**：基于账户大小和当前租金率
2. **免租金阈值**：达到此余额的账户无需支付租金
3. **账户类型**：不同类型的账户有不同的大小要求

## 实现的功能

### 1. 核心方法

#### `GetMinimumBalanceForRentExemption`
- **功能**：获取指定大小账户的免租金最小余额
- **参数**：账户大小（字节）
- **返回**：免租金所需的最小余额（lamports）

#### `CalculateRentFeeForTransaction`
- **功能**：计算整个交易的租金费用
- **参数**：Solana 交易对象
- **返回**：总租金费用（lamports）

### 2. 指令分析

#### 系统程序指令
- **CreateAccount**：解析账户大小，计算对应租金
- **CreateAccountWithSeed**：使用标准 Token 账户大小

#### SPL Token 程序指令
- **InitializeMint**：Mint 账户（82 字节）
- **InitializeAccount**：Token 账户（165 字节）
- **InitializeMultisig**：多重签名账户（355 字节）

### 3. 账户大小常量

```go
const (
    TokenAccountSize     = 165  // SPL Token 账户
    SystemAccountMinSize = 0    // 系统账户最小大小
    MultisigAccountSize  = 355  // 多重签名账户
)
```

## 费用计算流程

### 1. 交易分析
1. 解析交易中的所有指令
2. 识别创建账户的指令类型
3. 确定新账户的大小要求

### 2. 租金计算
1. 调用 Solana RPC `getMinimumBalanceForRentExemption`
2. 获取对应账户大小的免租金余额
3. 累加所有新账户的租金费用

### 3. 总费用计算
```
总费用 = 交易费用 + 租金费用
```

## 代码示例

### 基本使用
```go
// 获取 Token 账户的免租金余额
balance, err := paymaster.GetMinimumBalanceForRentExemption(ctx, 165)

// 计算交易的租金费用
rentFee, err := paymaster.CalculateRentFeeForTransaction(ctx, transaction)
```

### 在 EstimateGas 中的集成
```go
// 获取基础交易费用
fee, err := pm.GetTransactionFeeWithContext(ctx, tx.RawTxHex)

// 计算租金费用
rentFee, err := pm.CalculateRentFeeForTransaction(ctx, transaction)

// 计算总费用
totalFee := fee.Add(rentFee)
```

## 测试覆盖

### 测试用例
1. **SPL Token 账户租金**：165 字节账户
2. **Mint 账户租金**：82 字节账户
3. **多重签名账户租金**：355 字节账户
4. **零大小账户**：基础账户

### 测试结果示例
```
SPL Token 账户 (165 字节): 2,039,280 lamports
Mint 账户 (82 字节): 1,461,600 lamports
多重签名账户 (355 字节): 3,361,680 lamports
零大小账户 (0 字节): 890,880 lamports
```

## 注意事项

### 1. 网络依赖
- 租金计算依赖 Solana RPC 网络连接
- 测试环境可能因网络问题导致获取失败

### 2. 错误处理
- 租金计算失败时使用零租金费用
- 记录警告日志但不中断交易处理

### 3. 性能考虑
- RPC 调用有网络延迟
- 可考虑缓存常用账户大小的租金信息

## 未来改进

1. **缓存机制**：缓存常用账户大小的租金信息
2. **更精确解析**：支持更复杂的指令数据解析
3. **批量查询**：优化多个账户的租金查询
4. **离线计算**：基于历史数据进行离线租金估算

## 相关文件

- `fee.go`：租金计算核心逻辑
- `paymaster.go`：EstimateGas 方法集成
- `paymaster_test.go`：测试用例
- `RENT_FEE_IMPLEMENTATION.md`：本文档
