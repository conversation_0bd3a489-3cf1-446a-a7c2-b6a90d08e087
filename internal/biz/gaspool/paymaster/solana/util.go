package solana

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz/gaspool"
	"byd_wallet/model"
	"context"
	"encoding/binary"
	"fmt"
	"github.com/shopspring/decimal"
	"time"

	"github.com/gagliardetto/solana-go"
)

// util.go - Solana paymaster 工具函数
// 包含通用验证函数、解析辅助函数、错误处理函数等

// 通用验证辅助函数 - 用于减少重复的参数验证代码

// validateRawTx 验证原始交易数据的有效性
// 参数：rawTx - 待验证的原始交易数据
// 返回：error - 验证失败时的错误信息
func validateRawTx(rawTxHex string) error {
	if rawTxHex == "" {
		return fmt.Errorf("raw tx hex is empty")
	}
	return nil
}

// validateUserTx 验证用户交易数据的有效性
// 参数：tx - 待验证的用户交易数据
// 返回：error - 验证失败时的错误信息
func validateUserTx(tx *gaspool.UserTx) error {
	if tx == nil {
		return fmt.Errorf("tx is empty")
	}
	if tx.RawTxHex == "" {
		return fmt.Errorf("raw tx hex is empty")
	}
	return nil
}

// validateSponsorTx 验证赞助交易数据的有效性
// 参数：tx - 待验证的赞助交易数据
// 返回：error - 验证失败时的错误信息
func validateSponsorTx(tx *model.GasPoolSponsorTx) error {
	if tx == nil {
		return fmt.Errorf("paymaster tx is nil")
	}
	if tx.RawTxHex == "" {
		return fmt.Errorf("raw tx hex is empty")
	}
	return nil
}

// 交易解析辅助函数 - 用于减少重复的解析逻辑

// parseTransferAmount 解析转账金额
// 参数：
//   - data - 指令数据
//   - startOffset - 金额数据的起始偏移量
//
// 返回：uint64 - 解析出的转账金额
func parseTransferAmount(data []byte, startOffset int) (uint64, error) {
	if len(data) < startOffset+8 {
		return 0, fmt.Errorf("insufficient data for amount parsing")
	}
	return binary.LittleEndian.Uint64(data[startOffset : startOffset+8]), nil
}

// extractAccountAddresses 提取账户地址
// 参数：
//   - accountKeys - 账户公钥列表
//   - indices - 账户索引列表
//
// 返回：[]string - 对应的账户地址列表
func extractAccountAddresses(accountKeys []solana.PublicKey, indices []uint16) ([]string, error) {
	addresses := make([]string, len(indices))
	for i, index := range indices {
		if int(index) >= len(accountKeys) {
			return nil, fmt.Errorf("invalid account index: %d", index)
		}
		addresses[i] = accountKeys[index].String()
	}
	return addresses, nil
}

// validateInstructionData 验证指令数据的基本要求
// 参数：
//   - instruction - 编译后的指令
//   - minDataLen - 最小数据长度要求
//   - minAccountLen - 最小账户数量要求
//   - operation - 操作描述（用于错误信息）
//
// 返回：error - 验证失败时的错误信息
func validateInstructionData(instruction solana.CompiledInstruction, minDataLen, minAccountLen int, operation string) error {
	if len(instruction.Data) < minDataLen {
		return fmt.Errorf("insufficient %s instruction data length: %d, required: %d", operation, len(instruction.Data), minDataLen)
	}
	if len(instruction.Accounts) < minAccountLen {
		return fmt.Errorf("insufficient %s instruction accounts: %d, required: %d", operation, len(instruction.Accounts), minAccountLen)
	}
	return nil
}

// 错误处理辅助函数 - 用于统一错误消息格式和日志记录

// wrapError 包装错误并添加上下文信息
// 参数：
//   - operation - 操作描述
//   - err - 原始错误
//
// 返回：error - 包装后的错误
func wrapError(operation string, err error) error {
	if err == nil {
		return nil
	}
	return fmt.Errorf("%s failed: %w", operation, err)
}

// wrapErrorWithID 包装错误并添加交易ID上下文
// 参数：
//   - operation - 操作描述
//   - id - 交易ID
//   - err - 原始错误
//
// 返回：error - 包装后的错误
func wrapErrorWithID(operation string, id int64, err error) error {
	if err == nil {
		return nil
	}
	return fmt.Errorf("%s failed for transaction ID %d: %w", operation, id, err)
}

// 缓存管理辅助函数 - 用于优化密钥缓存逻辑

// initializeKeyCache 初始化密钥缓存
// 参数：
//   - pm - Paymaster实例
//   - ctx - 上下文
//
// 返回：error - 初始化失败时的错误信息
func (pm *Paymaster) initializeKeyCache(ctx context.Context) error {
	// 如果已缓存，直接返回
	if pm.cachedPrivateKey != nil && pm.cachedPublicKey != nil {
		return nil
	}

	// 获取私钥
	privateKeyStr, err := pm.hotAccountReader.GetHotAccount(ctx, constant.SolChainIndex)
	if err != nil {
		return wrapError("get hot account", err)
	}

	// 将Base58格式的私钥转换为Solana私钥对象
	solPrivateKey, err := solana.PrivateKeyFromBase58(privateKeyStr)
	if err != nil {
		return wrapError("decode private key", err)
	}

	// 缓存私钥和公钥
	pm.cachedPrivateKey = &solPrivateKey
	publicKey := solPrivateKey.PublicKey()
	pm.cachedPublicKey = &publicKey

	pm.log.Debugf("已缓存paymaster密钥对，公钥: %s", publicKey.String())
	return nil
}

// 交易处理辅助函数 - 用于优化交易准备和签名流程

// validateTransactionSignatures 验证交易签名的完整性
// 参数：
//   - tx - 待验证的交易
//   - expectedSignerCount - 期望的签名者数量
//
// 返回：error - 验证失败时的错误信息
func validateTransactionSignatures(tx *solana.Transaction, expectedSignerCount int) error {
	if len(tx.Signatures) != expectedSignerCount {
		return fmt.Errorf("签名数量不匹配: 期望 %d, 实际 %d", expectedSignerCount, len(tx.Signatures))
	}

	// 验证没有零值签名
	for i, sig := range tx.Signatures {
		if sig.IsZero() {
			return fmt.Errorf("签名索引 %d 为零值", i)
		}
	}

	return nil
}

// createPaymasterSignature 创建paymaster签名
// 参数：
//   - pm - Paymaster实例
//   - msgBytes - 待签名的消息字节
//
// 返回：
//   - solana.Signature - 生成的签名
//   - error - 签名失败时的错误信息
func (pm *Paymaster) createPaymasterSignature(msgBytes []byte) (solana.Signature, error) {
	// 获取paymaster私钥
	privateKey, err := pm.getPayPrivate()
	if err != nil {
		return solana.Signature{}, wrapError("get paymaster private key", err)
	}

	// 生成签名
	signature, err := privateKey.Sign(msgBytes)
	if err != nil {
		return solana.Signature{}, wrapError("sign message", err)
	}

	return signature, nil
}

// isPriceTimeExpired 检查价格时间是否过期
// 参数：
//   - timeUnix - 价格时间戳
//
// 返回：bool - 是否过期
func (pm *Paymaster) isPriceTimeExpired(timeUnix int64) bool {
	return time.Now().Unix() > timeUnix+pm.priceExpireSeconds
}

// verifyTxSignature 验证交易签名
// 参数：
//   - rawTxHex - 原始交易十六进制字符串
//
// 返回：
//   - bool - 签名是否有效
//   - error - 验证过程中的错误
func (pm *Paymaster) verifyTxSignature(rawTxHex string) (bool, error) {
	_, err := pm.prepareTransaction(rawTxHex)
	if err != nil {
		return false, err
	}
	return true, nil
}

func (pm *Paymaster) decimals(token string) decimal.Decimal {
	if token != "" {
		return decimal.NewFromInt(6)
	}
	return decimal.NewFromInt(9)
}
