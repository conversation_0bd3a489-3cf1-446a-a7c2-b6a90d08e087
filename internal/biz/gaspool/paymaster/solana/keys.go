package solana

import (
	"context"
	"fmt"
	"time"

	"github.com/gagliardetto/solana-go"
)

// keys.go - Solana paymaster 密钥管理相关功能
// 包含私钥获取、公钥获取、签名者管理等功能

// getPayPrivate 获取paymaster的私钥（带缓存优化）
// 从热钱包账户读取器中获取Solana链的私钥，使用缓存避免重复解析
// 返回：
//   - solana.PrivateKey: paymaster的私钥
//   - error: 获取失败时的错误信息
func (pm *Paymaster) getPayPrivate() (solana.PrivateKey, error) {
	// 使用带超时的上下文，避免长时间阻塞
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 使用统一的缓存初始化函数
	if err := pm.initializeKeyCache(ctx); err != nil {
		return solana.PrivateKey{}, wrapError("initialize key cache", err)
	}

	// 此时缓存应该已经设置
	if pm.cachedPrivateKey == nil {
		return solana.PrivateKey{}, fmt.Errorf("private key cache not initialized")
	}

	return *pm.cachedPrivateKey, nil
}

// getPayPublic 获取paymaster的公钥（带缓存优化）
// 通过私钥派生出对应的公钥，使用缓存避免重复计算
// 返回：
//   - solana.PublicKey: paymaster的公钥
//   - error: 获取失败时的错误信息
func (pm *Paymaster) getPayPublic() (solana.PublicKey, error) {
	// 使用带超时的上下文，避免长时间阻塞
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 使用统一的缓存初始化函数
	if err := pm.initializeKeyCache(ctx); err != nil {
		return solana.PublicKey{}, wrapError("initialize key cache", err)
	}

	// 此时缓存应该已经设置
	if pm.cachedPublicKey == nil {
		return solana.PublicKey{}, fmt.Errorf("public key cache not initialized")
	}

	return *pm.cachedPublicKey, nil
}

// getRequiredSigners 获取需要签名的账户列表
// 从交易消息中提取所有需要签名的账户公钥
// 参数：
//   - msg: Solana交易消息对象
//
// 返回：
//   - []solana.PublicKey: 需要签名的账户公钥列表
func (pm *Paymaster) getRequiredSigners(msg solana.Message) []solana.PublicKey {
	var signers []solana.PublicKey

	// 遍历消息中的所有账户
	for i, acc := range msg.AccountKeys {
		// 检查该账户是否需要签名
		if msg.IsSigner(acc) {
			signers = append(signers, acc)
			pm.log.Debugf("发现需要签名的账户，索引: %d, 公钥: %s", i, acc.String())
		}
	}

	pm.log.Debugf("共找到 %d 个需要签名的账户", len(signers))
	return signers
}
