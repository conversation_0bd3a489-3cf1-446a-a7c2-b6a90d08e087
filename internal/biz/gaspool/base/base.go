package base

import (
	"byd_wallet/model"
	"context"

	"github.com/shopspring/decimal"
)

type TokenPriceReader interface {
	GetTokenLatestPriceUSDT(ctx context.Context, chainIndex int64, address string) (price decimal.Decimal, timeUnix int64, err error)
}

type GasPoolSponsorTxMgr interface {
	FindGasPoolSponsorTxByID(ctx context.Context, id uint) (*model.GasPoolSponsorTx, error)
	UpdateGasPoolSponsorTx(ctx context.Context, tx *model.GasPoolSponsorTx) error
}

type GasPoolMgr interface {
	DepositGasPool(ctx context.Context, userID uint, amount decimal.Decimal, chainIndex int64, txHash, tokenAddress string) (*model.GasPoolFlow, error)
	RefundGasPool(ctx context.Context, reduceFlowID uint, amount decimal.Decimal) (*model.GasPoolFlow, error)
}

type GasPoolDepositAddressMgr interface {
	FindDepositReceiverAddress(ctx context.Context, chainIndex int64) (string, error)
}
