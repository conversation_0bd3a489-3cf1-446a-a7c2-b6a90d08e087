package config

import "byd_wallet/common/constant"

// PaymasterConfig paymaster配置
// 定义哪些链支持paymaster功能以及相关配置
type PaymasterConfig struct {
	// SupportedChains 支持paymaster的链索引列表
	SupportedChains map[int64]ChainPaymasterConfig
}

// ChainPaymasterConfig 单个链的paymaster配置
type ChainPaymasterConfig struct {
	// Enabled 是否启用paymaster功能
	Enabled bool
	// RequiresAddress 是否需要返回paymaster地址
	RequiresAddress bool
	// ChainType 链类型，用于确定地址格式
	ChainType ChainType
	// Description 链描述
	Description string
}

// ChainType 链类型枚举
type ChainType string

const (
	ChainTypeEVM    ChainType = "evm"    // EVM兼容链
	ChainTypeSolana ChainType = "solana" // Solana链
	ChainTypeTron   ChainType = "tron"   // Tron链
)

// DefaultPaymasterConfig 默认paymaster配置
// 根据项目需求配置哪些链支持paymaster功能
var DefaultPaymasterConfig = &PaymasterConfig{
	SupportedChains: map[int64]ChainPaymasterConfig{
		constant.EthChainIndex: {
			Enabled:         true,
			RequiresAddress: true,
			ChainType:       ChainTypeEVM,
			Description:     "Ethereum主网",
		},
		constant.BscChainIndex: {
			Enabled:         true,
			RequiresAddress: true,
			ChainType:       ChainTypeEVM,
			Description:     "BSC链",
		},
		constant.SolChainIndex: {
			Enabled:         true,
			RequiresAddress: true,
			ChainType:       ChainTypeSolana,
			Description:     "Solana链",
		},
		constant.TronChainIndex: {
			Enabled:         true,
			RequiresAddress: true,
			ChainType:       ChainTypeTron,
			Description:     "Tron链",
		},
		constant.BaseChainIndex: {
			Enabled:         true,
			RequiresAddress: true,
			ChainType:       ChainTypeEVM,
			Description:     "Base链",
		},
		constant.ArbChainIndex: {
			Enabled:         true,
			RequiresAddress: true,
			ChainType:       ChainTypeEVM,
			Description:     "Arbitrum链",
		},
		constant.PolChainIndex: {
			Enabled:         true,
			RequiresAddress: true,
			ChainType:       ChainTypeEVM,
			Description:     "Polygon链",
		},
		constant.OptimismChainIndex: {
			Enabled:         true,
			RequiresAddress: true,
			ChainType:       ChainTypeEVM,
			Description:     "Optimism链",
		},
	},
}

// IsPaymasterSupported 检查指定链是否支持paymaster功能
func (c *PaymasterConfig) IsPaymasterSupported(chainIndex int64) bool {
	config, exists := c.SupportedChains[chainIndex]
	return exists && config.Enabled
}

// RequiresPaymasterAddress 检查指定链是否需要返回paymaster地址
func (c *PaymasterConfig) RequiresPaymasterAddress(chainIndex int64) bool {
	config, exists := c.SupportedChains[chainIndex]
	return exists && config.Enabled && config.RequiresAddress
}

// GetChainType 获取指定链的类型
func (c *PaymasterConfig) GetChainType(chainIndex int64) (ChainType, bool) {
	config, exists := c.SupportedChains[chainIndex]
	if !exists || !config.Enabled {
		return "", false
	}
	return config.ChainType, true
}

// GetChainDescription 获取指定链的描述
func (c *PaymasterConfig) GetChainDescription(chainIndex int64) string {
	config, exists := c.SupportedChains[chainIndex]
	if !exists {
		return "未知链"
	}
	return config.Description
}

// ValidateAddress 验证地址格式是否符合链类型要求
func (c *PaymasterConfig) ValidateAddress(chainIndex int64, address string) bool {
	chainType, exists := c.GetChainType(chainIndex)
	if !exists {
		return false
	}

	switch chainType {
	case ChainTypeEVM:
		// EVM地址应该以0x开头，长度为42
		return len(address) == 42 && address[:2] == "0x"
	case ChainTypeSolana:
		// Solana地址是Base58编码，长度通常为32-44字符
		return len(address) >= 32 && len(address) <= 44
	case ChainTypeTron:
		// Tron地址是Base58Check编码，通常以T开头
		return len(address) >= 25 && len(address) <= 35 && address[0] == 'T'
	default:
		return false
	}
}
