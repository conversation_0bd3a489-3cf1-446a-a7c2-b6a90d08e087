package coindata

import (
	"byd_wallet/internal/biz/base"
	"byd_wallet/internal/thirdapi/coingecko"
	"byd_wallet/model"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type InitCtrlTestConfig struct {
	APIUrl       string `json:"api_url"`
	APIKey       string `json:"api_key"`
	IsLocalDebug bool   `json:"is_local_debug"`
	Socks5Proxy  string `json:"socks_5_proxy"`

	RateAPIUrl  string `json:"rate_api_url"`
	RateAPISign string `json:"rate_api_sign"`
}

var initCtrlTestConfig InitCtrlTestConfig

func init() {
	data, err := os.ReadFile("../../../tmp/coindataconfig.json")
	if err != nil {
		fmt.Println("warn: read test config json fail:" + err.Error())
		return
	}
	err = json.Unmarshal(data, &initCtrlTestConfig)
	if err != nil {
		panic("parse test config json fail:" + err.Error())
	}

	cfg := initCtrlTestConfig
	if cfg.IsLocalDebug {
		os.Setenv("HTTPS_PROXY", cfg.Socks5Proxy)
		os.Setenv("HTTP_PROXY", cfg.Socks5Proxy)
	}
}

type S3Mock struct {
	base.S3Repo

	log *log.Helper
}

func (s *S3Mock) UploadFileByFileUrl(ctx context.Context, fileUrl, key string) (string, error) {
	s.log.Infof("UploadFileByFileUrl: %s: %s", fileUrl, key)
	return key, nil
}

type InitCtrlTestSuite struct {
	suite.Suite
	db  *gorm.DB
	rd  redis.UniversalClient
	it  *InitCtrl
	ctx context.Context

	coinInfos []*model.CoinInfo
	coinIds   []string
	tas       []*model.TokenAsset
}

func (s *InitCtrlTestSuite) SetupSuite() {
	cfg := initCtrlTestConfig
	api := coingecko.NewCoingeckoClient(cfg.APIUrl, cfg.APIKey)

	dsn := "host=127.0.0.1 user=root password=root dbname=wallet_test port=5432 sslmode=disable"
	db, _ := gorm.Open(postgres.Open(dsn), &gorm.Config{})

	rd := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
	})

	s.db = db
	s.rd = rd

	s.it = NewInitCtrl(log.DefaultLogger, api, db, &S3Mock{log: log.NewHelper(log.DefaultLogger)}, nil)

	s.ctx = context.Background()

	// init db
	s.db.AutoMigrate(
		model.CoinInfoStub,
		model.CoinLogoStub,
		model.TokenAssetStub,
	)

	// init records
	nt := time.Now()
	s.coinInfos = []*model.CoinInfo{
		{
			Model: gorm.Model{
				ID:        1,
				CreatedAt: nt,
				UpdatedAt: nt,
			},
			ChainIndex: 0,
			CoinID:     "bitcoin",
			Name:       "Bitcoin",
			Symbol:     "btc",
		},
		{
			Model: gorm.Model{
				ID:        2,
				CreatedAt: nt,
				UpdatedAt: nt,
			},
			CoinID:     "ethereum",
			Name:       "Ethereum",
			Symbol:     "eth",
			ChainIndex: 60,
		},
		{
			Model: gorm.Model{
				ID:        3,
				CreatedAt: nt,
				UpdatedAt: nt,
			},
			CoinID:     "tron",
			Name:       "TRON",
			Symbol:     "trx",
			ChainIndex: 195,
		},
		{
			Model: gorm.Model{
				ID:        4,
				CreatedAt: nt,
				UpdatedAt: nt,
			},
			CoinID:     "solana",
			Name:       "Solana",
			Symbol:     "sol",
			ChainIndex: 501,
		},
		{
			Model: gorm.Model{
				ID:        5,
				CreatedAt: nt,
				UpdatedAt: nt,
			},
			CoinID:     "binancecoin",
			Name:       "BNB",
			Symbol:     "bnb",
			ChainIndex: 60,
			Address:    "******************************************",
		},
		{
			Model: gorm.Model{
				ID:        6,
				CreatedAt: nt,
				UpdatedAt: nt,
			},
			CoinID:     "polygon-ecosystem-token",
			Name:       "POL (ex-MATIC)",
			Symbol:     "pol",
			ChainIndex: 966,
			Address:    "******************************************",
		},
		{
			Model: gorm.Model{
				ID:        7,
				CreatedAt: nt,
				UpdatedAt: nt,
			},
			CoinID:     "polygon-ecosystem-token",
			Name:       "POL (ex-MATIC)",
			Symbol:     "pol",
			ChainIndex: 60,
			Address:    "0x455e53cbb86018ac2b8092fdcd39d8444affc3f6",
		},
		{
			Model: gorm.Model{
				ID:        8,
				CreatedAt: nt,
				UpdatedAt: nt,
			},
			CoinID:     "base-protocol",
			Name:       "Base Protocol",
			Symbol:     "base",
			ChainIndex: 60,
			Address:    "0x07150e919b4de5fd6a63de1f9384828396f25fdc",
		},
		{
			Model: gorm.Model{
				ID:        9,
				CreatedAt: nt,
				UpdatedAt: nt,
			},
			CoinID:     "optimism",
			Name:       "Optimism",
			Symbol:     "op",
			ChainIndex: 10000070,
			Address:    "0x4200000000000000000000000000000000000042",
		},
		{
			Model: gorm.Model{
				ID:        10,
				CreatedAt: nt,
				UpdatedAt: nt,
			},
			CoinID:     "arbitrum",
			Name:       "Arbitrum",
			Symbol:     "arb",
			ChainIndex: 10042221,
			Address:    "0x912ce59144191c1204e64559fe8253a0e49e6548",
		},
		{
			Model: gorm.Model{
				ID:        11,
				CreatedAt: nt,
				UpdatedAt: nt,
			},
			CoinID:     "arbitrum",
			Name:       "Arbitrum",
			Symbol:     "arb",
			ChainIndex: 10042170,
			Address:    "0xf823c3cd3cebe0a1fa952ba88dc9eef8e0bf46ad",
		},
		{
			Model: gorm.Model{
				ID:        12,
				CreatedAt: nt,
				UpdatedAt: nt,
			},
			CoinID:     "arbitrum",
			Name:       "Arbitrum",
			Symbol:     "arb",
			ChainIndex: 60,
			Address:    "0xb50721bcf8d664c30412cfbc6cf7a15145234ad1",
		},
	}

	s.coinIds = model.CoinInfos(s.coinInfos).UniqueCoinIDs()
	tas := []*model.TokenAsset{}
	for _, v := range s.coinInfos {
		tas = append(tas, &model.TokenAsset{
			ChainIndex: v.ChainIndex,
			Address:    v.Address,
		})
	}
	s.tas = tas
}

func (s *InitCtrlTestSuite) SetupTest() {
	for _, tb := range []string{
		model.CoinInfoStub.TableName(),
		model.CoinLogoStub.TableName(),
		model.TokenAssetStub.TableName(),
	} {
		s.db.Exec(fmt.Sprintf("truncate table %s", tb))
	}
	s.rd.FlushAll(context.Background())
}

func (s *InitCtrlTestSuite) TestInitCoinInfos() {
	// coin info
	err := s.it.InitCoinInfos(s.ctx)
	if !s.NoError(err) {
		return
	}
	var count int64
	err = s.db.Model(model.CoinInfoStub).Count(&count).Error
	if !s.NoError(err) {
		return
	}
	if !s.Positive(count) {
		return
	}

	err = s.it.InitCoinInfos(s.ctx)
	if !s.NoError(err) {
		return
	}
}

func (s *InitCtrlTestSuite) TestInitCoinLogos() {
	err := s.db.Model(model.CoinInfoStub).CreateInBatches(s.coinInfos, 200).Error
	if !s.NoError(err) {
		return
	}

	// coin logo
	err = s.it.InitCoinLogos(s.ctx)
	if !s.NoError(err) {
		return
	}
	var count2 int64
	err = s.db.Model(model.CoinLogoStub).Count(&count2).Error
	if !s.NoError(err) {
		return
	}

	s.Equal(int64(len(s.coinIds)), count2)

	// again
	err = s.it.InitCoinLogos(s.ctx)
	if !s.NoError(err) {
		return
	}
}

func (s *InitCtrlTestSuite) TestInitTokenAssetsLogo() {
	err := s.db.Model(model.CoinInfoStub).CreateInBatches(s.coinInfos, 200).Error
	if !s.NoError(err) {
		return
	}

	tas := make([]*model.TokenAsset, 0, len(s.coinInfos))
	for _, v := range s.coinInfos {
		tas = append(tas, &model.TokenAsset{
			ChainIndex: v.ChainIndex,
			Address:    v.Address,
		})
	}

	err = s.db.Model(model.TokenAssetStub).CreateInBatches(tas, 200).Error
	if !s.NoError(err) {
		return
	}

	logos := make([]*model.CoinLogo, 0, len(s.coinIds))
	for _, ci := range s.coinIds {
		logos = append(logos, &model.CoinLogo{
			CoinID: ci,
			Image:  ci,
		})
	}

	err = s.db.Model(model.CoinLogoStub).Clauses(clause.Clause{}).CreateInBatches(logos, 200).Error
	if !s.NoError(err) {
		return
	}

	err = s.it.InitTokenAssetsLogo(s.ctx)
	if !s.NoError(err) {
		return
	}

}

func TestCoinDataInitCtrlTestSuite(t *testing.T) {
	if !initCtrlTestConfig.IsLocalDebug {
		t.Skip("only local debug")
	}
	suite.Run(t, new(InitCtrlTestSuite))
}
