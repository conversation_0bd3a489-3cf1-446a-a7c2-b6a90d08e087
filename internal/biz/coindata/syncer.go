package coindata

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/thirdapi/btc528"
	"byd_wallet/internal/thirdapi/coingecko"
	"byd_wallet/model"
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"

	"gorm.io/gorm"
)

type CoinDataThirdAPI interface {
	ListCoinID() ([]*coingecko.CoinID, error)
	ListCoinIDOrderBy(limit int64, orderBy string) (coinIDs []string, err error)
	ListCoinMarketDataIn24hByIDs(currency string, ids []string) ([]*coingecko.CoinMarketData, error)
	ListCoinOHLCInRangeHByID(currency, coinID string, from, to int64) ([]*coingecko.CoinOHLC, error)
	ListCoinLogosByIDs(ids []string) ([]*coingecko.CoinLogo, error)
	GetCoinIDByTokenAddress(chainIndex int64, address string) (coinID string, err error)
}

type CurrencyRateAPI interface {
	GetCurrencyUSDRate(currency string) (*btc528.CurrencyRate, error)
}

type TokenContract struct {
	Decimals    int64
	TotalSupply string
}

// FIXME: refactor
type Syncer struct {
	log *log.Helper

	api     CoinDataThirdAPI
	db      *gorm.DB
	rd      redis.UniversalClient
	rateApi CurrencyRateAPI

	popularCoinLimit int64
	popularFlushMtx  *sync.Mutex
}

func NewSyncer(logger log.Logger, api CoinDataThirdAPI,
	db *gorm.DB,
	rd redis.UniversalClient,
	rateApi CurrencyRateAPI) *Syncer {
	return &Syncer{
		log: log.NewHelper(logger),

		api:     api,
		db:      db,
		rd:      rd,
		rateApi: rateApi,

		popularCoinLimit: 1000, // NOTE: config???
		popularFlushMtx:  &sync.Mutex{},
	}
}

func (s *Syncer) getNoPopularCoinIDs(ctx context.Context) ([]string, error) {
	sql := `SELECT coin_id
FROM token_assets ta
WHERE ta.coin_id<>'' and NOT EXISTS (
    SELECT 1
    FROM token_asset_ranks tar
    WHERE ta.id = tar.token_asset_id
)`
	var dbCoinIDs []string
	err := s.db.WithContext(ctx).Raw(sql).Scan(&dbCoinIDs).Error
	if err != nil {
		return nil, fmt.Errorf("query token assets: %w", err)
	}

	// DB distinct // cost: 60ms ==> 1600+ms
	coinIDs := make([]string, 0, len(dbCoinIDs))
	set := make(map[string]struct{}, len(dbCoinIDs))
	for _, coinID := range dbCoinIDs {
		if _, ok := set[coinID]; !ok {
			coinIDs = append(coinIDs, coinID)
			set[coinID] = struct{}{}
		}
	}

	return coinIDs, nil
}

func (s *Syncer) getPopularCoinIDs(ctx context.Context) ([]string, error) {
	coinIDsBts, err := s.rd.Get(ctx, model.CoinIDRankCacheKey).Bytes()
	if err != nil {
		if err != redis.Nil {
			return nil, fmt.Errorf("get CoinIDRankCache: %w", err)
		}

		// not cache
		ids, err := s.api.ListCoinIDOrderBy(s.popularCoinLimit, coingecko.OrderByVolumeDesc)
		if err != nil {
			return nil, fmt.Errorf("ListCoinIDOrderBy: %w", err)
		}

		idsBts, err := json.Marshal(ids)
		if err != nil {
			return nil, fmt.Errorf("json marshal: %w: %v", err, ids)
		}
		err = s.rd.Set(ctx, model.CoinIDRankCacheKey, string(idsBts), 24*time.Hour).Err()
		if err != nil {
			return nil, fmt.Errorf("set CoinIDRankCache: %w", err)
		}
		s.asyncFlushTokenAssetRank(ctx, ids)
		return ids, nil
	}

	// cached
	var ids []string
	err = json.Unmarshal(coinIDsBts, &ids)
	if err != nil {
		return nil, fmt.Errorf("parse coin ids cache: %w: %s", err, string(coinIDsBts))
	}
	return ids, nil
}

func (s *Syncer) asyncFlushTokenAssetRank(ctx context.Context, coinIDs []string) {
	go func() {
		if !s.popularFlushMtx.TryLock() {
			return
		}
		defer s.popularFlushMtx.Unlock()

		var tas []*model.TokenAsset
		err := s.db.WithContext(ctx).
			Raw("select * from token_assets where coin_id in ?", coinIDs).
			Scan(&tas).Error
		if err != nil {
			s.log.Errorw(log.DefaultMessageKey, "query PopularTokenAsset by coin ids", "err", err)
			return
		}

		idFlag := make(map[string]int)
		for i, id := range coinIDs {
			idFlag[id] = i
		}

		taranks := make([]*model.TokenAssetRank, 0, len(tas))
		for _, ta := range tas {
			taranks = append(taranks, &model.TokenAssetRank{
				TokenAssetID: ta.ID,
				Rank:         idFlag[ta.CoinID] + 1,
				Model: gorm.Model{
					ID: ta.ID,
				},
			})
		}

		s.log.Infof("asyncFlushTokenAssetRank: count: %d", len(taranks))

		err = s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
			err := tx.Exec("delete from token_asset_ranks").Error
			if err != nil {
				return err
			}
			return tx.Model(model.TokenAssetRankStub).CreateInBatches(taranks, 200).Error
		})
		if err != nil {
			s.log.Errorw(log.DefaultMessageKey, "save TokenAssetRank", "err", err)
		}
	}()
}

func (s *Syncer) SyncCoinOHLCs(ctx context.Context) {
	coinIDs, err := s.getPopularCoinIDs(ctx)
	if err != nil {
		s.log.Errorw(log.DefaultMessageKey, "getSyncedCoinIDs", "err", err)
		return
	}
	s.log.Infow(log.DefaultMessageKey, "syncCoinOHLCs", "count", len(coinIDs))
	s.syncCoinOHLCs(ctx, coinIDs)
}

func (s *Syncer) syncCoinOHLCs(ctx context.Context, coinIDs []string) {
	currency := constant.CurrencyUSD
	for _, coinID := range coinIDs {
		nt := time.Now()
		nt = time.Date(nt.Year(), nt.Month(), nt.Day(), nt.Hour(), 0, 0, 0, time.Local)
		from := nt.Add(-23 * time.Hour).Unix()
		to := nt.Unix()

		s.log.Infow(log.DefaultMessageKey, "ListCoinOHLCInRangeHByID",
			"currency", currency,
			"from", from,
			"to", to,
			"coinID", coinID)

		ohlcs, err := s.api.ListCoinOHLCInRangeHByID(currency, coinID, from, to)
		if err != nil {
			s.log.Errorw(log.DefaultMessageKey, "api ListCoinOHLCInRangeHByID: request fail",
				"err", err,
				"currency", currency,
				"from", from,
				"to", to,
				"coinID", coinID,
			)
			continue
		}
		records := s.toModelCoinOHLCs(currency, ohlcs)
		bytes, err := json.Marshal(records)
		if err != nil {
			s.log.Errorw(log.DefaultMessageKey, "json Marshal CoinOHLCs fail",
				"err", err,
				"coinID", coinID)
			continue
		}

		if err = s.rd.HSet(ctx, model.CoinOHLCsCacheKey, coinID, string(bytes)).Err(); err != nil {
			s.log.Errorw(log.DefaultMessageKey, "save CoinOHLCs fail",
				"err", err,
				"coinID", coinID, "data", string(bytes))
		}
	}
}

func (s *Syncer) toModelCoinOHLCs(currency string, ohlcs []*coingecko.CoinOHLC) []*model.CoinOHLC {
	result := make([]*model.CoinOHLC, 0, len(ohlcs))
	for _, ohlc := range ohlcs {
		result = append(result, &model.CoinOHLC{
			CoinID:        ohlc.ID,
			Currency:      currency,
			Interval:      ohlc.Interval,
			LastUpdatedAt: ohlc.LastUpdatedAt,
			Open:          decimal.NewFromFloat(ohlc.Open),
			High:          decimal.NewFromFloat(ohlc.High),
			Low:           decimal.NewFromFloat(ohlc.Low),
			Close:         decimal.NewFromFloat(ohlc.Close),
		})
	}
	return result
}

func (s *Syncer) SyncCoinMarketDataByPopular(ctx context.Context) {
	coinIDs, err := s.getPopularCoinIDs(ctx)
	if err != nil {
		s.log.Errorw(log.DefaultMessageKey, "getPopularCoinIDs", "err", err)
		return
	}
	s.log.Infow(log.DefaultMessageKey, "SyncCoinMarketDataByPopular", "count", len(coinIDs))
	s.syncCoinMarketData(ctx, model.CoinMarketDataPopularCacheKey, coinIDs)
}

func (s *Syncer) SyncCoinMarketDataByNoPopular(ctx context.Context) {
	ids, err := s.getNoPopularCoinIDs(ctx)
	if err != nil {
		s.log.Errorw(log.DefaultMessageKey, "getNoPopularCoinIDs", "err", err)
		return
	}
	s.log.Infow(log.DefaultMessageKey, "SyncCoinMarketDataByNoPopular", "count", len(ids))

	const step = 1000
	for i := 0; i < len(ids); i += step {
		var tmpIds []string
		if i+step > len(ids) {
			tmpIds = ids[i:]
		} else {
			tmpIds = ids[i : i+step]
		}
		s.log.Infow(log.DefaultMessageKey, "SyncCoinMarketDataByNoPopular: group handle", "count", len(tmpIds))
		s.syncCoinMarketData(ctx, model.CoinMarketDataNoPopularCacheKey, tmpIds)
	}
}

func (s *Syncer) syncCoinMarketData(ctx context.Context, cacheKey string, coinIDs []string) {
	currency := constant.CurrencyUSD
	data, err := s.api.ListCoinMarketDataIn24hByIDs(currency, coinIDs)
	if err != nil {
		s.log.Errorw(log.DefaultMessageKey, "ListCoinMarketDataIn24hByIDs", "err", err)
		return
	}
	for _, v := range data {
		md := &model.CoinMarketData{
			CoinID:                   v.ID,
			Price:                    decimal.NewFromFloat(v.CurrentPrice),
			Currency:                 v.Currency,
			CirculatingSupply:        decimal.NewFromFloat(v.CirculatingSupply),
			LastUpdatedAtStr:         v.LastUpdated,
			LastUpdatedAt:            v.LastUpdatedAt,
			PriceChangePercentage24h: decimal.NewFromFloat(v.PriceChangePercentage24h),
			TradingVolume24h:         decimal.NewFromFloat(v.TotalVolume),
		}
		bytes, err := json.Marshal(md)
		if err != nil {
			s.log.Errorw(log.DefaultMessageKey, "json Marshal CoinMarketData fail",
				"err", err,
				"coinID", v.ID)
			continue
		}

		if err = s.rd.HSet(ctx, cacheKey, v.ID, string(bytes)).Err(); err != nil {
			s.log.Errorw(log.DefaultMessageKey, "save CoinMarketData fail",
				"err", err,
				"coinID", v.ID, "data", string(bytes))
		}
	}
}

func (s *Syncer) SyncCurrencyUSDRate(ctx context.Context, currencies []string) {
	s.log.Infof("SyncCurrencyUSDRate: %+v", currencies)

	for _, c := range currencies {
		rate, err := s.rateApi.GetCurrencyUSDRate(c)
		if err != nil {
			s.log.Errorw(log.DefaultMessageKey, "GetCurrencyUSDRate request fail", "err", err,
				"currency", c)
			continue
		}
		if err = s.rd.HSet(ctx, model.CurrencyRatesCacheKey, c, decimal.NewFromFloat(rate.Rate).String()).Err(); err != nil {
			s.log.Errorw(log.DefaultMessageKey, "save CurrencyRate fail", "err", err)
		}
	}
}
