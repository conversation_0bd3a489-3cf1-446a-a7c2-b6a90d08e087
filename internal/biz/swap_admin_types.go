package biz

import (
	"byd_wallet/internal/biz/base"
	"byd_wallet/model"
)

type AdminSwapTokenFilter struct {
	base.Pagination
	// 代币
	Symbol string
	// 代币合约号
	Address string
	// 兑换渠道id
	ChannelID uint
	// 链索引
	ChainIndex int64
	// 是否是原生代币
	Native bool
}

type AdminHotTokenFilter struct {
	base.Pagination
	// 链索引
	ChainIndex int64
	// 代币
	Symbol string
	// 代币合约号
	Address string
}

type AdminSwapRecordFilter struct {
	base.Pagination
	// 兑换渠道id
	ChannelID uint
	// 代币
	Symbol string
	// token合约地址
	Address string
	// 兑换状态
	Status model.SwapStatus
	// 发起钱包地址
	FromAddress string
}
