package biz

import (
	"byd_wallet/internal/biz/base"
	"byd_wallet/internal/biz/dbtx"
	"byd_wallet/model"
	"context"
	"errors"
	"github.com/go-kratos/kratos/v2/log"
	"sync"
)

type SwapAdminRepo interface {
	ListSwapChannel(ctx context.Context, pagination base.Pagination) ([]*model.SwapChannel, int64, error)
	UpdateSwapChannel(ctx context.Context, sc *model.SwapChannel) error
	ListToken(ctx context.Context, filter AdminSwapTokenFilter) ([]*model.SwappableToken, int64, error)
	UpdateToken(ctx context.Context, token *model.SwappableToken) error
	GetToken(ctx context.Context, id uint) (*model.SwappableToken, error)
	ListHotToken(ctx context.Context, filter AdminHotTokenFilter) ([]*model.SwappableHotToken, int64, error)
	GetHotToken(ctx context.Context, id uint) (*model.SwappableHotToken, error)
	CreateHotToken(context.Context, *model.SwappableHotToken) error
	DeleteHotToken(context.Context, uint) error
	LastHotTokenSortOrder(context.Context) (int, error)
	ListSwapRecord(ctx context.Context, filter AdminSwapRecordFilter) ([]*model.SwapRecord, int64, error)
	GetSwapRecord(ctx context.Context, id uint) (*model.SwapRecord, error)
}

type SwapAdminUsecase struct {
	log      *log.Helper
	repo     SwapAdminRepo
	tx       dbtx.DBTx
	sortRepo base.SortRepo
	sortLock *sync.Mutex
}

func NewSwapAdminUsecase(logger log.Logger, repo SwapAdminRepo, tx dbtx.DBTx, sortRepo base.SortRepo) *SwapAdminUsecase {
	return &SwapAdminUsecase{log: log.NewHelper(logger), repo: repo, tx: tx, sortRepo: sortRepo, sortLock: &sync.Mutex{}}
}

func (uc SwapAdminUsecase) ListSwapChannel(ctx context.Context, pagination base.Pagination) ([]*model.SwapChannel, int64, error) {
	return uc.repo.ListSwapChannel(ctx, pagination)
}

func (uc SwapAdminUsecase) UpdateSwapChannel(ctx context.Context, sc *model.SwapChannel) error {
	return uc.repo.UpdateSwapChannel(ctx, sc)
}

func (uc SwapAdminUsecase) ListToken(ctx context.Context, filter AdminSwapTokenFilter) ([]*model.SwappableToken, int64, error) {
	return uc.repo.ListToken(ctx, filter)
}

func (uc SwapAdminUsecase) UpdateToken(ctx context.Context, token *model.SwappableToken) error {
	return uc.repo.UpdateToken(ctx, token)
}

func (uc SwapAdminUsecase) ListHotToken(ctx context.Context, filter AdminHotTokenFilter) ([]*model.SwappableHotToken, int64, error) {
	return uc.repo.ListHotToken(ctx, filter)
}

func (uc SwapAdminUsecase) CreateHotToken(ctx context.Context, hot *model.SwappableHotToken) error {
	token, err := uc.repo.GetToken(ctx, hot.SwappableTokenID)
	if err != nil {
		return err
	}
	lastOrder, err := uc.repo.LastHotTokenSortOrder(ctx)
	if err != nil {
		return err
	}
	hot.SortOrder = lastOrder + 1
	hot.ChainIndex = token.ChainIndex
	return uc.repo.CreateHotToken(ctx, hot)
}

func (uc SwapAdminUsecase) DeleteHotToken(ctx context.Context, id uint) error {
	return uc.repo.DeleteHotToken(ctx, id)
}

func (uc SwapAdminUsecase) SortHotToken(ctx context.Context, sort *base.SortInput) error {
	if !uc.sortLock.TryLock() {
		return errors.New("请勿频繁操作")
	}
	defer uc.sortLock.Unlock()
	token, err := uc.repo.GetHotToken(ctx, sort.ID)
	if err != nil {
		return err
	}
	sort.Model = &model.SwappableHotToken{}
	sort.Where = []base.SortWhere{
		{
			Column:      "is_all",
			ColumnValue: token.IsAll,
		},
	}
	if !token.IsAll {
		sort.Where = append(sort.Where, base.SortWhere{
			Column:      "chain_index",
			ColumnValue: token.ChainIndex,
		})
	}
	return uc.tx.ExecTx(ctx, func(ctx context.Context) error {
		return uc.sortRepo.Sort(ctx, sort)
	})
}

func (uc SwapAdminUsecase) ListSwapRecord(ctx context.Context, filter AdminSwapRecordFilter) ([]*model.SwapRecord, int64, error) {
	return uc.repo.ListSwapRecord(ctx, filter)
}

func (uc SwapAdminUsecase) GetSwapRecord(ctx context.Context, id uint) (*model.SwapRecord, error) {
	return uc.repo.GetSwapRecord(ctx, id)
}
