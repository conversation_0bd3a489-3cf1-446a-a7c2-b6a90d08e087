package biz

import (
	"byd_wallet/internal/biz/base"
	"byd_wallet/model"
	"context"
)

// AdminAppVersionFilter app版本过滤器
type AdminAppVersionFilter struct {
	base.Pagination
	// 应用类型
	AppType string
}

// AppVersionAdminRepo app版本仓库接口
type AppVersionAdminRepo interface {
	CreateAppVersion(ctx context.Context, version *model.AppVersion) error
	UpdateAppVersion(ctx context.Context, version *model.AppVersion) error
	DeleteAppVersion(ctx context.Context, id uint) error
	GetAppVersion(ctx context.Context, id uint) (*model.AppVersion, error)
	ListAppVersion(ctx context.Context, filter AdminAppVersionFilter) ([]*model.AppVersion, int64, error)
	ListPublishedVersions(ctx context.Context, appType string) ([]string, error)
}

// AppVersionAdminUsecase app版本用例
type AppVersionAdminUsecase struct {
	repo AppVersionAdminRepo
}

// NewAppVersionUsecase 创建app版本用例
func NewAppVersionUsecase(repo AppVersionAdminRepo) *AppVersionAdminUsecase {
	return &AppVersionAdminUsecase{
		repo: repo,
	}
}

// CreateAppVersion 创建版本
func (uc *AppVersionAdminUsecase) CreateAppVersion(ctx context.Context, version *model.AppVersion) error {
	return uc.repo.CreateAppVersion(ctx, version)
}

// UpdateAppVersion 更新版本
func (uc *AppVersionAdminUsecase) UpdateAppVersion(ctx context.Context, version *model.AppVersion) error {
	// 先获取现有版本
	existing, err := uc.repo.GetAppVersion(ctx, version.ID)
	if err != nil {
		return err
	}

	// 更新多语言描述
	if len(version.AppVersionI18Ns) > 0 {
		existing.AppVersionI18Ns = version.AppVersionI18Ns
	}

	return uc.repo.UpdateAppVersion(ctx, existing)
}

// DeleteAppVersion 删除版本
func (uc *AppVersionAdminUsecase) DeleteAppVersion(ctx context.Context, id uint) error {
	return uc.repo.DeleteAppVersion(ctx, id)
}

// GetAppVersion 获取版本详情
func (uc *AppVersionAdminUsecase) GetAppVersion(ctx context.Context, id uint) (*model.AppVersion, error) {
	return uc.repo.GetAppVersion(ctx, id)
}

// ListAppVersion 版本列表
func (uc *AppVersionAdminUsecase) ListAppVersion(ctx context.Context, filter AdminAppVersionFilter) ([]*model.AppVersion, int64, error) {
	return uc.repo.ListAppVersion(ctx, filter)
}

// ListPublishedVersions 获取已发布的版本号列表
func (uc *AppVersionAdminUsecase) ListPublishedVersions(ctx context.Context, appType string) ([]string, error) {
	return uc.repo.ListPublishedVersions(ctx, appType)
}
