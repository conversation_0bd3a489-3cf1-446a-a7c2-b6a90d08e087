package biz

import (
	"byd_wallet/model"
	"context"

	"github.com/go-kratos/kratos/v2/log"
)

type TokenAssetStarViewFilter struct {
	Page       int64
	PageSize   int64
	ChainIndex int64 // 值为-1表示查询全部
	Symbol     string
	Address    string
	OrderBy    string
}

type TokenAssetStarRepo interface {
	Save(ctx context.Context, tas *model.TokenAssetStar) (*model.TokenAssetStar, error)
	ListViewByFilter(ctx context.Context, filter *TokenAssetStarViewFilter) (list []*model.TokenAssetWithStar, totalCount int64, err error)
	Delete(ctx context.Context, id uint) error
	UpdateSortOrder(ctx context.Context, id uint, current, target int64) error
}

type TokenAssetStarUsecase struct {
	log *log.Helper

	repo TokenAssetStarRepo
}

func NewTokenAssetStarUsecase(logger log.Logger,
	repo TokenAssetStarRepo) *TokenAssetStarUsecase {
	return &TokenAssetStarUsecase{
		log:  log.NewHelper(logger),
		repo: repo,
	}
}

func (uc *TokenAssetStarUsecase) CreateTokenAssetStar(ctx context.Context, tokenAssetID uint) (*model.TokenAssetStar, error) {
	return uc.repo.Save(ctx, &model.TokenAssetStar{
		TokenAssetID: tokenAssetID,
	})
}

func (uc *TokenAssetStarUsecase) ListTokenAssetStarView(ctx context.Context, filter *TokenAssetStarViewFilter) (list []*model.TokenAssetWithStar, totalCount int64, err error) {
	return uc.repo.ListViewByFilter(ctx, filter)
}

func (uc *TokenAssetStarUsecase) DeleteTokenAssetStar(ctx context.Context, id uint) error {
	return uc.repo.Delete(ctx, id)
}

func (uc *TokenAssetStarUsecase) UpdateTokenAssetStarSort(ctx context.Context, id uint, currentSort, targetSort int64) error {
	return uc.repo.UpdateSortOrder(ctx, id, currentSort, targetSort)
}
