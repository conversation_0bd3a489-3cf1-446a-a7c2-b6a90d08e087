package server

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz"
	"byd_wallet/internal/biz/coindata"
	"byd_wallet/internal/server/timer"
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

type TimerServer struct {
	*timer.TaskScheduler

	log *log.Helper

	coinDataSyncer *coindata.Syncer
	chainRepo      biz.BlockchainNetworkRepo
	txSyncer       *biz.EVMInternalTxSyncer
	swapUc         *biz.SwapUsecase
	swapRepo       biz.SwapRepo
}

func NewTimerServer(logger log.Logger,
	coinDataSyncer *coindata.Syncer,
	swapUc *biz.SwapUsecase,
	swapRepo biz.SwapRepo,
) (*TimerServer, error) {
	scheduler := timer.NewTaskScheduler(logger)
	s := &TimerServer{
		log:            log.NewHelper(logger),
		TaskScheduler:  scheduler,
		coinDataSyncer: coinDataSyncer,
		swapUc:         swapUc,
		swapRepo:       swapRepo,
	}
	if err := s.registerCoinDataTasks(); err != nil {
		return nil, err
	}
	if err := s.registerSwapTasks(); err != nil {
		return nil, err
	}
	return s, nil
}

func (s *TimerServer) registerSwapTasks() error {
	if err := s.Register("refresh_swappable_token", "@every 120m", func() {
		if err := s.swapUc.RefreshSwappableTokens(context.Background(), false); err != nil {
			s.log.Warnf("refresh_swappable_token: %v", err)
			return
		}
	}); err != nil {
		return err
	}
	configs, err := s.swapRepo.ListSwapConfig(context.Background())
	if err != nil {
		return err
	}
	for _, config := range configs {
		mtx := &sync.Mutex{}
		id := fmt.Sprintf("refresh_swap_records_%s", config.SwapChannel.Name)
		if err := s.Register(id, config.CronSpec, func() {
			if !mtx.TryLock() {
				return
			}
			defer mtx.Unlock()
			if err := s.swapUc.RefreshSwapRecordsByConfig(context.Background(), config); err != nil {
				s.log.Warnf("%s: %v", id, err)
				return
			}
		}); err != nil {
			return err
		}
	}

	return nil
}

func (s *TimerServer) registerEvmInternalTxTasks() error {
	chains, err := s.chainRepo.ListEVM(context.Background())
	if err != nil {
		return err
	}
	for _, chain := range chains {
		id := fmt.Sprintf("evm_%s_internal_tx_syncer", chain.Name)
		if err := s.Register(id, chain.CronSpec, func() {
			if err := s.txSyncer.Sync(context.Background(), chain); err != nil {
				s.log.Errorf("%s: %v", id, err)
				return
			}
		}); err != nil {
			return err
		}
	}
	return nil
}

func (s *TimerServer) registerCoinDataTasks() error {
	ctx := context.Background()
	for _, job := range []struct {
		name string
		cron string
		f    func()
	}{
		{
			name: "sync_coin_ohlc",
			cron: "@every 100m",
			f: func() {
				s.coinDataSyncer.SyncCoinOHLCs(ctx)
			},
		},
		{
			name: "sync_coin_data_market_data_by_popular",
			cron: "@every 10m",
			f: func() {
				s.coinDataSyncer.SyncCoinMarketDataByPopular(ctx)
			},
		},
		{
			name: "sync_coin_data_market_data_by_no_popular",
			cron: "@every 65m",
			f: func() {
				s.coinDataSyncer.SyncCoinMarketDataByNoPopular(ctx)
			},
		},
		{
			name: "sync_currency_rate",
			cron: "@every 10m",
			f: func() {
				s.coinDataSyncer.SyncCurrencyUSDRate(ctx, []string{
					constant.CurrencyCNY,
					constant.CurrencyJPY,
				})
			},
		},
	} {
		mtx := &sync.Mutex{}
		err := s.Register(job.name, job.cron, func() {
			if !mtx.TryLock() {
				return
			}
			defer mtx.Unlock()

			nt := time.Now()
			job.f()
			s.log.Infof("%s: %s", job.name, time.Since(nt).String())
		})
		if err != nil {
			return fmt.Errorf("register %s: %w", job.name, err)
		}
		s.log.Infof("registerCoinDataTasks: %s: %s", job.name, job.cron)
	}
	return nil
}
