package swagger

import (
	"byd_wallet/embedded"
	"embed"
	"html/template"
	"io/fs"
	"log"
	"net/http"
	"os"
	"path"
	"strings"
)

//go:embed ui/*
var swaggerUI embed.FS

// SwaggerHandler 创建一个新的Swagger UI处理器
// 支持中文注释的正确显示和现代化的Swagger UI界面
type SwaggerHandler struct {
	openAPIPath string // OpenAPI文件路径
	basePath    string // 基础路径前缀
	isAdmin     bool   // 是否为管理员API
}

// NewHandler 创建新的Swagger处理器
// openAPIPath: OpenAPI YAML文件的路径
// basePath: Swagger UI的基础路径（如 "/q/"）
func NewHandler(openAPIPath, basePath string) *SwaggerHandler {
	return &SwaggerHandler{
		openAPIPath: openAPIPath,
		basePath:    strings.TrimSuffix(basePath, "/"),
		isAdmin:     false,
	}
}

// NewAdminHandler 创建新的管理员Swagger处理器
func NewAdminHandler(basePath string) *SwaggerHandler {
	return &SwaggerHandler{
		openAPIPath: "", // 使用嵌入的admin-openapi.yaml
		basePath:    strings.TrimSuffix(basePath, "/"),
		isAdmin:     true,
	}
}

// ServeHTTP 实现http.Handler接口
func (h *SwaggerHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	// 移除基础路径前缀
	urlPath := strings.TrimPrefix(r.URL.Path, h.basePath)

	switch {
	case urlPath == "/" || urlPath == "/swagger-ui" || urlPath == "/swagger-ui/":
		// 服务主页面
		h.serveIndex(w, r)
	case urlPath == "/openapi.yaml" || urlPath == "/swagger.yaml":
		// 服务OpenAPI文件
		h.serveOpenAPI(w, r)
	case strings.HasPrefix(urlPath, "/"):
		// 服务静态文件
		h.serveStatic(w, r, strings.TrimPrefix(urlPath, "/"))
	default:
		http.NotFound(w, r)
	}
}

// serveIndex 服务Swagger UI主页面
func (h *SwaggerHandler) serveIndex(w http.ResponseWriter, r *http.Request) {
	var title string
	if h.isAdmin {
		title = "Admin API Documentation - BYD Wallet"
	} else {
		title = "API Documentation - BYD Wallet"
	}

	indexHTML := `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>{{.Title}}</title>
    <link rel="stylesheet" type="text/css" href="{{.BasePath}}/swagger-ui.css" />
    <link rel="icon" type="image/png" href="{{.BasePath}}/favicon-32x32.png" sizes="32x32" />
    <link rel="icon" type="image/png" href="{{.BasePath}}/favicon-16x16.png" sizes="16x16" />
    <style>
        html {
            box-sizing: border-box;
            overflow: -moz-scrollbars-vertical;
            overflow-y: scroll;
        }
        *, *:before, *:after {
            box-sizing: inherit;
        }
        body {
            margin:0;
            background: #fafafa;
            font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
        }
        /* 确保中文字符正确显示 */
        .swagger-ui .info .title {
            font-family: "PingFang SC", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
        }
        .swagger-ui .info .description {
            font-family: "PingFang SC", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
        }
    </style>
</head>
<body>
    <div id="swagger-ui"></div>
    <script src="{{.BasePath}}/swagger-ui-bundle.js" charset="UTF-8"></script>
    <script src="{{.BasePath}}/swagger-ui-standalone-preset.js" charset="UTF-8"></script>
    <script>
        window.onload = function() {
            const ui = SwaggerUIBundle({
                url: '{{.BasePath}}/openapi.yaml',
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout",
                // 支持中文显示的配置
                supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch'],
                docExpansion: 'list',
                jsonEditor: false,
                defaultModelRendering: 'schema',
                showRequestHeaders: false,
                showOperationIds: false,
                // 确保中文字符正确编码
                requestInterceptor: function(request) {
                    // 确保请求头包含正确的字符集
                    if (!request.headers['Accept']) {
                        request.headers['Accept'] = 'application/json;charset=utf-8';
                    }
                    return request;
                }
            });
        };
    </script>
</body>
</html>`

	tmpl, err := template.New("index").Parse(indexHTML)
	if err != nil {
		http.Error(w, "Template error", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "text/html; charset=utf-8")

	data := struct {
		BasePath string
		Title    string
	}{
		BasePath: h.basePath,
		Title:    title,
	}

	if err := tmpl.Execute(w, data); err != nil {
		http.Error(w, "Template execution error", http.StatusInternalServerError)
		return
	}
}

// serveOpenAPI 服务OpenAPI YAML文件
func (h *SwaggerHandler) serveOpenAPI(w http.ResponseWriter, r *http.Request) {
	var content []byte
	var source string

	// 设置通用的响应头
	// Set common response headers
	w.Header().Set("Content-Type", "application/yaml; charset=utf-8")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	// 策略1: 优先尝试使用嵌入的实际API文件（生产环境推荐）
	// Strategy 1: Try embedded actual API files first (recommended for production)
	if h.isAdmin {
		content = embedded.AdminOpenAPIContent
		source = "embedded admin API file"
	} else {
		content = embedded.WalletOpenAPIContent
		source = "embedded wallet API file"
	}

	// 验证嵌入内容是否有效
	// Validate embedded content
	if len(content) > 0 {
		log.Printf("[Swagger] Serving OpenAPI from %s (size: %d bytes)", source, len(content))
		w.Write(content)
		return
	}

	// 策略2: 如果嵌入内容无效，尝试外部文件（开发环境）
	// Strategy 2: Try external file if embedded content is invalid (development)
	if h.openAPIPath != "" {
		if _, err := os.Stat(h.openAPIPath); err == nil {
			log.Printf("[Swagger] Serving OpenAPI from external file: %s", h.openAPIPath)
			http.ServeFile(w, r, h.openAPIPath)
			return
		} else {
			log.Printf("[Swagger] External OpenAPI file not found: %s, error: %v", h.openAPIPath, err)
		}
	}

	// 策略3: 最后尝试使用嵌入的UI目录中的后备文件
	// Strategy 3: Fallback to embedded UI directory files
	var err error
	if h.isAdmin {
		content, err = fs.ReadFile(swaggerUI, path.Join("ui", "admin-openapi.yaml"))
		source = "embedded UI admin file"
	} else {
		content, err = fs.ReadFile(swaggerUI, path.Join("ui", "openapi.yaml"))
		source = "embedded UI wallet file"
	}

	if err != nil {
		log.Printf("[Swagger] All OpenAPI file sources failed. Admin: %v, Path: %s, Error: %v",
			h.isAdmin, h.openAPIPath, err)
		http.Error(w, "OpenAPI file not found", http.StatusNotFound)
		return
	}

	log.Printf("[Swagger] Serving OpenAPI from %s (fallback, size: %d bytes)", source, len(content))
	w.Write(content)
}

// serveStatic 服务静态文件
func (h *SwaggerHandler) serveStatic(w http.ResponseWriter, r *http.Request, filePath string) {
	// 从嵌入的文件系统中读取文件
	content, err := fs.ReadFile(swaggerUI, path.Join("ui", filePath))
	if err != nil {
		http.NotFound(w, r)
		return
	}

	// 设置正确的Content-Type
	contentType := getContentType(filePath)
	w.Header().Set("Content-Type", contentType)

	// 设置缓存头
	w.Header().Set("Cache-Control", "public, max-age=86400") // 1天缓存

	w.Write(content)
}

// getContentType 根据文件扩展名返回Content-Type
func getContentType(filePath string) string {
	ext := path.Ext(filePath)
	switch ext {
	case ".css":
		return "text/css; charset=utf-8"
	case ".js":
		return "application/javascript; charset=utf-8"
	case ".png":
		return "image/png"
	case ".svg":
		return "image/svg+xml"
	case ".ico":
		return "image/x-icon"
	case ".html":
		return "text/html; charset=utf-8"
	case ".yaml", ".yml":
		return "application/yaml; charset=utf-8"
	default:
		return "application/octet-stream"
	}
}
