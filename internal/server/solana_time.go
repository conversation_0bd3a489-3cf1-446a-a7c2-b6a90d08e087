package server

import (
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"
	"fmt"
	"sync"

	"github.com/robfig/cron/v3"

	"github.com/go-kratos/kratos/v2/log"
)

type SolTimerServer struct {
	cron      *cron.Cron
	network   *model.BlockchainNetwork
	log       *log.Helper
	syncMutex sync.Mutex
	syncer    *biz.ChainSyncer
}

func NewSolTimerServer(logger log.Logger, syncer *biz.ChainSyncer) (*SolTimerServer, error) {
	s := &SolTimerServer{
		log:     log.<PERSON><PERSON><PERSON>per(logger),
		cron:    cron.New(cron.WithSeconds()),
		network: syncer.GetBlockchainNetwork(),
		syncer:  syncer,
	}
	if _, err := s.cron.AddFunc(s.network.CronSpec, func() {
		if err := s.syncer.SyncBlockTransactions(context.Background()); err != nil {
			s.log.Errorf("sync block transactions error, %v", err)
			return
		}
	}); err != nil {
		return nil, fmt.Errorf("add chain chain syncer error, %w", err)
	}
	// 每分钟补漏块
	if _, err := s.cron.AddFunc("@every 1m", func() {
		if err := s.syncer.FillMissingBlocks(context.Background()); err != nil {
			s.log.Errorf("fill missing blocks error, %v", err)
			return
		}
	}); err != nil {
		return nil, fmt.Errorf("add chain chain syncer error, %w", err)
	}

	return s, nil
}

func (c *SolTimerServer) Start(_ context.Context) error {
	c.cron.Start()
	c.log.Infof("🚀 [%s-%d] chain syncer has started, spec: %s", c.network.ChainName, c.network.ChainIndex, c.network.CronSpec)
	return nil
}

func (c *SolTimerServer) Stop(_ context.Context) error {
	ctx := c.cron.Stop()
	<-ctx.Done()
	c.log.Info("🛑 the scheduler has gracefully closed")
	return nil
}
