package server

import (
	"byd_wallet/internal/biz/gaspool"
	"context"

	"github.com/go-kratos/kratos/v2/log"
)

// PaymasterServer 管理所有paymaster的后台任务
// 负责启动和停止各链的paymaster异步任务，包括gas转账确认等功能
type PaymasterServer struct {
	log              *log.Helper
	paymasterFactory gaspool.PaymasterFactory
	// 支持的链索引列表，用于启动对应的paymaster任务
	supportedChains []int64
	// 存储已启动的paymaster实例，用于停止时清理
	activePaymasters map[int64]gaspool.Paymaster
}

// NewPaymasterServer 创建新的PaymasterServer实例
// 参数:
//   - logger: 日志记录器
//   - paymasterFactory: paymaster工厂，用于获取各链的paymaster实例
//
// 返回值:
//   - *PaymasterServer: PaymasterServer实例
func NewPaymasterServer(
	logger log.Logger,
	paymasterFactory gaspool.PaymasterFactory,
) *PaymasterServer {
	// 定义支持的链索引列表
	// 包括所有已实现paymaster的链：Ethereum, BSC, Solana, Tron, Arbitrum, Polygon, Optimism, Base
	supportedChains := []int64{
		1,     // Ethereum
		56,    // BSC
		101,   // Solana
		195,   // Tron
		42161, // Arbitrum
		137,   // Polygon
		10,    // Optimism
		8453,  // Base
	}

	return &PaymasterServer{
		log:              log.NewHelper(logger),
		paymasterFactory: paymasterFactory,
		supportedChains:  supportedChains,
		activePaymasters: make(map[int64]gaspool.Paymaster),
	}
}

// Start 启动所有支持链的paymaster后台任务
// 遍历支持的链索引，获取对应的paymaster实例并启动其异步任务
// 参数:
//   - ctx: 上下文对象
//
// 返回值:
//   - error: 错误信息
func (s *PaymasterServer) Start(ctx context.Context) error {
	s.log.Info("🚀 启动PaymasterServer，开始初始化所有链的paymaster后台任务")

	successCount := 0
	errorCount := 0

	// 遍历所有支持的链，启动对应的paymaster任务
	for _, chainIndex := range s.supportedChains {
		// 获取指定链的paymaster实例
		paymaster, err := s.paymasterFactory.GetPaymaster(ctx, chainIndex)
		if err != nil {
			s.log.Errorf("获取链 %d 的paymaster失败: %v", chainIndex, err)
			errorCount++
			continue
		}

		// 启动paymaster的异步任务
		if err := paymaster.Start(ctx); err != nil {
			s.log.Errorf("启动链 %d 的paymaster任务失败: %v", chainIndex, err)
			errorCount++
			continue
		}

		// 记录成功启动的paymaster实例
		s.activePaymasters[chainIndex] = paymaster
		successCount++
		s.log.Infof("✅ 链 %d 的paymaster后台任务启动成功", chainIndex)
	}

	s.log.Infof("PaymasterServer启动完成 - 成功: %d, 失败: %d", successCount, errorCount)

	// 即使部分链启动失败，也不返回错误，允许其他链正常工作
	return nil
}

// Stop 停止所有已启动的paymaster后台任务
// 遍历已启动的paymaster实例，调用其Stop方法进行优雅关闭
// 参数:
//   - ctx: 上下文对象
//
// 返回值:
//   - error: 错误信息
func (s *PaymasterServer) Stop(ctx context.Context) error {
	s.log.Info("🛑 开始停止PaymasterServer，关闭所有paymaster后台任务")

	successCount := 0
	errorCount := 0

	// 遍历所有已启动的paymaster实例，进行停止操作
	for chainIndex, paymaster := range s.activePaymasters {
		if err := paymaster.Stop(ctx); err != nil {
			s.log.Errorf("停止链 %d 的paymaster任务失败: %v", chainIndex, err)
			errorCount++
		} else {
			s.log.Infof("✅ 链 %d 的paymaster后台任务已停止", chainIndex)
			successCount++
		}
	}

	// 清空已启动的paymaster记录
	s.activePaymasters = make(map[int64]gaspool.Paymaster)

	s.log.Infof("PaymasterServer停止完成 - 成功: %d, 失败: %d", successCount, errorCount)

	// 即使部分链停止失败，也不返回错误
	return nil
}

// GetActivePaymasters 获取当前活跃的paymaster实例列表
// 用于监控和调试目的
// 返回值:
//   - map[int64]gaspool.Paymaster: 链索引到paymaster实例的映射
func (s *PaymasterServer) GetActivePaymasters() map[int64]gaspool.Paymaster {
	// 返回副本，避免外部修改
	result := make(map[int64]gaspool.Paymaster)
	for chainIndex, paymaster := range s.activePaymasters {
		result[chainIndex] = paymaster
	}
	return result
}

// GetSupportedChains 获取支持的链索引列表
// 返回值:
//   - []int64: 支持的链索引列表
func (s *PaymasterServer) GetSupportedChains() []int64 {
	// 返回副本，避免外部修改
	result := make([]int64, len(s.supportedChains))
	copy(result, s.supportedChains)
	return result
}
