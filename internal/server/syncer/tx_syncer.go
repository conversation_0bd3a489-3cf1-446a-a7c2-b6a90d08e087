package syncer

import (
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"
	"fmt"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/robfig/cron/v3"
)

type ChainTransactionSyncer struct {
	log     *log.Helper
	cron    *cron.Cron
	network *model.BlockchainNetwork
	syncer  *biz.ChainSyncer
}

func NewChainTransactionSyncer(logger log.Logger, syncer *biz.ChainSyncer) (*ChainTransactionSyncer, error) {
	s := &ChainTransactionSyncer{
		log:     log.<PERSON><PERSON><PERSON><PERSON>(logger),
		cron:    cron.New(cron.WithSeconds()),
		network: syncer.GetBlockchainNetwork(),
		syncer:  syncer,
	}
	if _, err := s.cron.AddFunc(s.network.CronSpec, func() {
		if err := s.syncer.SyncBlockTransactions(context.Background()); err != nil {
			s.log.Errorf("sync block transactions error, %v", err)
			return
		}
	}); err != nil {
		return nil, fmt.Errorf("add chain chain syncer error, %w", err)
	}
	// 每分钟补漏块
	if _, err := s.cron.AddFunc("@every 1m", func() {
		if err := s.syncer.FillMissingBlocks(context.Background()); err != nil {
			s.log.Errorf("fill missing blocks, %v", err)
			return
		}
	}); err != nil {
		return nil, fmt.Errorf("add chain chain syncer error, %w", err)
	}

	if _, err := s.cron.AddFunc("@every 30s", func() {
		if err := s.syncer.FillMissTxn(context.Background()); err != nil {
			s.log.Errorf("fill missing txn error, %v", err)
			return
		}
	}); err != nil {
		return nil, fmt.Errorf("add interner txn syncer error, %w", err)
	}
	return s, nil
}

func (c *ChainTransactionSyncer) Start(_ context.Context) error {
	c.cron.Start()
	c.log.Infof("🚀 [%s-%d] chain syncer has started, spec: %s", c.network.ChainName, c.network.ChainIndex, c.network.CronSpec)
	return nil
}

func (c *ChainTransactionSyncer) Stop(_ context.Context) error {
	ctx := c.cron.Stop()
	<-ctx.Done()
	c.log.Info("🛑 the scheduler has gracefully closed")
	return nil
}
