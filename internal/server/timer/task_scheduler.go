package timer

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"github.com/go-kratos/kratos/v2/log"

	"github.com/robfig/cron/v3"
)

// TaskScheduler 是任务调度器
type TaskScheduler struct {
	log *log.Helper

	cron       *cron.Cron
	tasks      map[string]cron.EntryID
	cancelFunc context.CancelFunc
}

// NewTaskScheduler 创建任务调度器
func NewTaskScheduler(logger log.Logger) *TaskScheduler {
	return &TaskScheduler{
		cron:  cron.New(cron.WithSeconds()),
		tasks: make(map[string]cron.EntryID),

		log: log.NewHelper(logger),
	}
}

// SetCancelFunc 提供一个安全设置 cancelFunc 的方法
func (s *TaskScheduler) SetCancelFunc(cancel context.CancelFunc) {
	s.cancelFunc = cancel
}

// Register 注册一个定时任务
func (s *TaskScheduler) Register(id string, expr string, job func()) error {
	if _, exists := s.tasks[id]; exists {
		return fmt.Errorf("bydwallettask ID %s already exists", id)
	}
	entryID, err := s.cron.AddFunc(expr, job)
	if err != nil {
		return fmt.Errorf("register bydwallettask failed.: %w", err)
	}
	s.tasks[id] = entryID
	s.log.Infof("✅ register [%s] Success，Cron expression: %s\n", id, expr)
	return nil
}

// Remove 删除某个任务
func (s *TaskScheduler) Remove(id string) {
	if entryID, ok := s.tasks[id]; ok {
		s.cron.Remove(entryID)
		delete(s.tasks, id)
		s.log.Infof("🛑 bydwallettask removed [%s]\n", id)
	} else {
		s.log.Infof("⚠️ bydwallettask ID [%s] does not exist\n", id)
	}
}

// ListTasks 获取当前所有任务的列表
func (s *TaskScheduler) ListTasks() []string {
	var taskList []string
	for id := range s.tasks {
		taskList = append(taskList, id)
	}
	return taskList
}

// Start 启动任务调度器
func (s *TaskScheduler) Start(context.Context) error {
	s.cron.Start()
	s.log.Info("🚀 bydwallettask scheduler has started")
	return nil
}

// Stop 停止所有任务
func (s *TaskScheduler) Stop(context.Context) error {
	ctx := s.cron.Stop()
	<-ctx.Done()
	s.log.Info("🛑 the scheduler has gracefully closed")
	return nil
}

// RunWithSignal 启动调度器并监听退出信号
func (s *TaskScheduler) RunWithSignal(ctx context.Context) {
	go s.Start(ctx)

	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	select {
	case <-quit:
		s.log.Info("🔔 Upon receiving the exit signal, close the bydwallettask scheduler...")
		s.Stop(ctx)
		if s.cancelFunc != nil {
			s.cancelFunc()
		}
	case <-ctx.Done():
		s.log.Info("🛑 Context Cancel, close the scheduler...")
		s.Stop(ctx)
	}
}
