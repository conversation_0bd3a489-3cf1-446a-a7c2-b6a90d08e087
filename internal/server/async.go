package server

import (
	"byd_wallet/internal/biz"
	"context"
)

type AsyncServer struct {
	spotPriceMgr *biz.SpotPriceManager
}

func NewAsyncServer(
	spotPriceMgr *biz.SpotPriceManager,
) *AsyncServer {
	return &AsyncServer{
		spotPriceMgr: spotPriceMgr,
	}
}

func (s *AsyncServer) Start(ctx context.Context) error {
	return s.spotPriceMgr.Start(ctx)
}

func (s *AsyncServer) Stop(ctx context.Context) error {
	return s.spotPriceMgr.Stop(ctx)
}
