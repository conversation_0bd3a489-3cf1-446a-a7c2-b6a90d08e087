package localize

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func Test_parseLanguage(t *testing.T) {
	//  11 | 2025-06-18T06:46:50.058844Z | 2025-06-18T06:46:50.058844Z |                1 | 热门               | Hot                | zh
	// 12 | 2025-06-18T06:46:50.058844Z | 2025-06-18T06:46:50.058844Z |                1 | 人気のある         | Hot                | ja
	// 13 | 2025-06-18T06:46:50.058844Z | 2025-06-18T06:46:50.058844Z |                1 | Hot                | Hot                | en
	// 14 | 2025-06-18T06:46:50.058844Z | 2025-06-18T06:46:50.058844Z |                1 | Popular            | Hot                | es
	// 15 | 2025-06-18T06:46:50.058844Z | 2025-06-18T06:46:50.058844Z |                1 | 熱門               | Hot                | zh-HK
	tests := []struct {
		name   string
		accept string
		want   string
	}{
		{
			name:   "zh",
			accept: "zh",
			want:   "zh",
		},
		{
			name:   "invalid",
			accept: "111",
			want:   "zh",
		},
		{
			name:   "ja",
			accept: "ja",
			want:   "ja",
		},
		{
			name:   "en",
			accept: "en",
			want:   "en",
		},
		{
			name:   "es",
			accept: "es",
			want:   "es",
		},
		{
			name:   "zh-HK",
			accept: "zh-HK",
			want:   "zh-HK",
		},
		{
			name:   "zh-CN,zh;q=0.9",
			accept: "zh-CN,zh;q=0.9",
			want:   "zh",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotTag := parseLanguage(tt.accept)
			//fmt.Println(gotTag.String())
			assert.Equalf(t, tt.want, gotTag, "parseLanguage(%v)", tt.accept)
		})
	}
}
