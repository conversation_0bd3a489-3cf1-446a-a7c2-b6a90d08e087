package service

import (
	v1 "byd_wallet/api/wallet/v1"
	"byd_wallet/common/constant"
	"byd_wallet/common/dto"
	"byd_wallet/internal/biz"
	"context"
	"strings"
)

type MarketService struct {
	v1.UnimplementedMarketSrvServer

	uc *biz.MarketUsecase
}

func NewMarketService(uc *biz.MarketUsecase) *MarketService {
	return &MarketService{uc: uc}
}

func (s *MarketService) ListPopularToken(ctx context.Context, req *v1.ListPopularTokenReq) (*v1.ListPopularTokenReply, error) {
	list, totalCount, err := s.uc.ListTokenByPopular(ctx, &biz.PopularTokenFilter{
		Page:         req.Page,
		PageSize:     req.Limit,
		Keyword:      req.GetKeyword(),
		ChainIndexes: dto.ParseChainIndexes(req.ChainIndexes),
	})
	if err != nil {
		return nil, err
	}
	return &v1.ListPopularTokenReply{
		List:       dto.ToList(list, ToPbTokenWithMarket),
		TotalCount: totalCount,
	}, nil
}

func (s *MarketService) QuerySimpleKline(ctx context.Context, req *v1.QuerySimpleKlineReq) (*v1.QuerySimpleKlineReply, error) {
	list, err := s.uc.QuerySimpleKline(ctx, &biz.QuerySimpleKlineReq{
		TokenIds: dto.ToList(req.GetTokenIds(), ToBizTokenID),
	})
	if err != nil {
		return nil, err
	}
	return &v1.QuerySimpleKlineReply{List: dto.ToList(list, ToPbSimpleKline)}, nil
}

func (s *MarketService) QueryCoinPrice(ctx context.Context, req *v1.QueryCoinPriceReq) (*v1.QueryCoinPriceReply, error) {
	list, err := s.uc.QueryCoinPrice(ctx, &biz.QueryCoinPriceReq{
		TokenIds: dto.ToList(req.GetTokenIds(), ToBizTokenID),
	})
	if err != nil {
		return nil, err
	}
	return &v1.QueryCoinPriceReply{
		List: dto.ToList(list, ToPbCoinPrice),
	}, nil
}

func (s *MarketService) QueryCurrencyUSDRate(ctx context.Context, req *v1.QueryCurrencyUSDRateReq) (*v1.CurrencyUSDRate, error) {
	existUsd := false
	cs := strings.Split(req.Currencies, ",")
	for _, v := range cs {
		if strings.EqualFold(v, constant.CurrencyUSD) {
			existUsd = true
			break
		}
	}

	rates, err := s.uc.QueryCurrencyUSDRate(ctx, cs)
	if err != nil {
		return nil, err
	}
	value := map[string]string{}
	for k, v := range rates {
		value[k] = v.String()
	}
	if existUsd {
		value[constant.CurrencyUSD] = "1"
	}
	return &v1.CurrencyUSDRate{
		Value: value,
	}, nil
}
