package task

import (
	taskCommon "byd_wallet/common"
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz"
	"byd_wallet/model"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type SolHandler struct {
	log *log.Helper

	rpc biz.RPCEndpointRepo
	db  *gorm.DB
}

func NewSolHandler(logger log.Logger, rpc biz.RPCEndpointRepo, db *gorm.DB) *SolHandler {
	return &SolHandler{
		log: log.<PERSON>elper(logger),
		rpc: rpc,
		db:  db,
	}
}

func (h *SolHandler) Handle(token taskCommon.SyncToken) error {
	//处理token
	chainIndex := token.ChainIndex // 你的链索引
	var tokenAsset model.TokenAsset
	tokenAsset.Decimals = token.Decimals
	tokenAsset.Address = token.Contract
	tokenAsset.ChainId = token.ChainId
	tokenAsset.ChainIndex = chainIndex
	return h.db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "address"}, {Name: "chain_index"}}, // 指定唯一约束字段
		DoNothing: true,
	}).Create(&tokenAsset).Error
}

func (h *SolHandler) ChainType() ChainType {
	return SolanaChain
}

func (h *SolHandler) ChainIndexes() []int64 {
	return []int64{constant.SolChainIndex}
}
