package task

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz"
	"context"
	"log"

	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

type SolanaCronSyncService interface {
	Start(ctx context.Context) error
	Stop(ctx context.Context) error
}

func NewSolanaCronSyncService(db *gorm.DB, rd *redis.Client, rpc biz.RPCEndpointRepo) SolanaCronSyncService {
	rpcList, err := rpc.GetRpcListByChainIndexForCache(constant.SolChainIndex, "ws")
	if err != nil {
		log.Fatal(err)
	}
	return &solanaWebSocketService{
		wsURLs: rpcList,
		db:     db,
		rd:     rd,
	}
}
