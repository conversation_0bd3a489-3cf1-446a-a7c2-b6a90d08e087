package task

import (
	taskCommon "byd_wallet/common"
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz"
	"byd_wallet/internal/biz/syncer/chain/evm/chain"
	"byd_wallet/model"

	"github.com/ethereum/go-ethereum/common"
	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type EthHandler struct {
	log *log.Helper

	rpc biz.RPCEndpointRepo
	db  *gorm.DB
}

func NewEthHandler(logger log.Logger, rpc biz.RPCEndpointRepo, db *gorm.DB) *EthHandler {
	return &EthHandler{
		log: log.NewHelper(logger),
		rpc: rpc,
		db:  db,
	}
}

func (h *EthHandler) Handle(token taskCommon.SyncToken) error {
	//处理token
	chainIndex := token.ChainIndex // 你的链索引
	rpcList, err := h.rpc.GetRpcListByChainIndexForCache(chainIndex, "http")
	if err != nil {
		h.log.Errorw(log.DefaultMessageKey, "sync eth get rpc list failed", "err", err, "chanIndex", chainIndex)
		return err
	}
	evmCli, err := chain.NewEvmChain(token.ChainIndex, rpcList...)
	if err != nil {
		return err
	}
	contractType, tokenInfo, err := evmCli.DetectContractType(common.HexToAddress(token.Contract))
	if err != nil {
		return err
	}
	if contractType == constant.TypeUnknown {
		return nil
	}
	if tokenInfo == nil {
		return nil
	}
	var tokenAsset model.TokenAsset
	tokenAsset.Address = token.Contract
	tokenAsset.ChainIndex = chainIndex
	tokenAsset.ChainId = token.ChainId
	tokenAsset.TokenType = contractType.String()
	tokenAsset.Name = tokenInfo.Name
	tokenAsset.Symbol = tokenInfo.Symbol
	if tokenInfo.Decimals != nil {
		tokenAsset.Decimals = tokenInfo.Decimals.Int64()
	}
	if tokenInfo.TotalSupply != nil {
		tokenAsset.TotalSupply = tokenInfo.TotalSupply.String()
	}
	return h.db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "address"}, {Name: "chain_index"}}, // 指定唯一约束字段
		DoNothing: true,
	}).Create(&tokenAsset).Error
}

func (h *EthHandler) ChainType() ChainType {
	return EVMChain
}

// //p.Register(60, h)       // Ethereum
//
//	//p.Register(20000714, h) // Binance
//	//p.Register(966, h)      // Polygon
//	//p.Register(10042221, h) // Arbitrum
//	//p.Register(10000070, h) // Optimism
//	//p.Register(8453, h)     // Base
func (h *EthHandler) ChainIndexes() []int64 {
	return []int64{
		constant.EthChainIndex,      // Ethereum
		constant.BscChainIndex,      // Binance
		constant.PolChainIndex,      // Polygon
		constant.ArbChainIndex,      // Arbitrum
		constant.OptimismChainIndex, // Optimism
		constant.BaseChainIndex,     // Base
	}
}
