package task

import (
	"byd_wallet/common"

	"github.com/go-kratos/kratos/v2/log"
)

type handlerRegistry struct {
	byChainIndex map[int64]TokenHandler
	byChainType  map[ChainType][]TokenHandler
}

type TokenProcessor struct {
	log *log.Helper

	registry *handlerRegistry
}

func NewTokenProcessor(logger log.Logger, eth *EthHandler, sol *SolHandler) *TokenProcessor {
	p := &TokenProcessor{
		log: log.NewHelper(logger),
		registry: &handlerRegistry{
			byChainIndex: make(map[int64]TokenHandler),
			byChainType:  make(map[ChainType][]TokenHandler),
		},
	}
	for _, h := range []TokenHandler{eth, sol} {
		for _, chainIndex := range h.ChainIndexes() {
			p.Register(chainIndex, h)
		}
	}

	return p
}

// Register 注册链处理器
func (p *TokenProcessor) Register(chainIndex int64, handler <PERSON><PERSON><PERSON><PERSON><PERSON>) {
	p.registry.byChainIndex[chainIndex] = handler
	t := handler.ChainType()
	p.registry.byChainType[t] = append(p.registry.byChainType[t], handler)
}

// ProcessToken 根据 ChainIndex 派发
func (p *TokenProcessor) ProcessToken(token common.SyncToken) {
	handler, ok := p.registry.byChainIndex[token.ChainIndex]
	if !ok {
		p.log.Errorf("No handler registered for chain index: %d", token.ChainIndex)
		return
	}

	if err := handler.Handle(token); err != nil {
		p.log.Errorf("Handler error for chain %d: %v", token.ChainIndex, err)
	}
}

// GetHandlersByType 按链类型批量获取处理器（可用于扫描、状态同步等）
func (p *TokenProcessor) GetHandlersByType(t ChainType) []TokenHandler {
	return p.registry.byChainType[t]
}
