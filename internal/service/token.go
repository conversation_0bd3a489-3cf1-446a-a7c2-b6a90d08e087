package service

import (
	"byd_wallet/model"
	"context"
	"encoding/json"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

type TokenService struct {
	log *log.Helper

	db *gorm.DB
	rd redis.UniversalClient
}

func NewTokenService(logger log.Logger, db *gorm.DB, rd redis.UniversalClient) *TokenService {
	return &TokenService{
		log: log.<PERSON><PERSON>elper(logger),
		db:  db,
		rd:  rd,
	}
}

func (s *TokenService) GetTokenInfo(tokenAddress string) (*model.TokenAsset, error) {
	return s.GetTokenInfoByCache(tokenAddress)
}

func (s *TokenService) GetTokenInfoByCache(tokenAddress string) (*model.TokenAsset, error) {
	info, _ := s.GetTokenInfoByTokenForRedis(tokenAddress)
	if info != nil {
		return info, nil
	}
	info, err := s.GetTokenInfoByTokenForDb(tokenAddress)
	if err != nil {
		return nil, err
	}
	if info != nil && info.ID != 0 {
		// 回写 Redis
		data, _ := json.Marshal(info)
		s.rd.Set(context.Background(), tokenAddress, data, time.Hour*6)
	}
	return info, nil
}

func (s *TokenService) GetTokenInfoByTokenForRedis(tokenAddress string) (*model.TokenAsset, error) {
	str, err := s.rd.Get(context.Background(), tokenAddress).Result()
	if err != nil && err != redis.Nil {
		return nil, err
	}

	var token model.TokenAsset
	err = json.Unmarshal([]byte(str), &token)
	if err != nil {
		return nil, err
	}
	return &token, nil
}

func (s *TokenService) GetTokenInfoByTokenForDb(tokenAddress string) (*model.TokenAsset, error) {
	var token model.TokenAsset
	err := s.db.Model(&model.TokenAsset{}).Where("address = ?", tokenAddress).First(&token).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}

	return &token, nil
}
