package admin

import (
	v1 "byd_wallet/api/walletadmin/v1"
	"byd_wallet/internal/biz"
	"context"
)

type FileService struct {
	v1.UnimplementedFileSrvServer
	uc *biz.FileUsecase
}

func NewFileService(uc *biz.FileUsecase) *FileService {
	return &FileService{uc: uc}
}

func (f FileService) GeneratePresignedRequest(ctx context.Context, req *v1.GeneratePresignedRequestReq) (*v1.GeneratePresignedRequestReply, error) {
	result, err := f.uc.GeneratePresignedRequest(ctx, req.BizType)
	if err != nil {
		return nil, err
	}
	reply := &v1.GeneratePresignedRequestReply{
		Url:    result.URL,
		Method: result.Method,
	}
	for k, v := range result.SignedHeader {
		reply.Headers = append(reply.Headers, &v1.Header{
			Key:   k,
			Value: v,
		})
	}
	return reply, nil
}
