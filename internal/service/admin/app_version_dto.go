package admin

import (
	v1 "byd_wallet/api/walletadmin/v1"
	"byd_wallet/common/dto"
	"byd_wallet/model"
)

func ToAppVersionI18N(from *v1.AppVersionI18N) *model.AppVersionI18N {
	return &model.AppVersionI18N{
		Language:    from.Language,
		Description: from.Description,
	}
}

func FromAppVersionI18N(from *model.AppVersionI18N) *v1.AppVersionI18N {
	return &v1.AppVersionI18N{
		Language:    from.Language,
		Description: from.Description,
	}
}

func FromAppVersion(from *model.AppVersion) *v1.AppVersionInfo {
	return &v1.AppVersionInfo{
		Id:                   uint64(from.ID),
		Version:              from.Version,
		AppType:              from.AppType,
		DownloadUrl:          from.DownloadURL,
		ForceUpdate:          from.ForceUpdate,
		I18NDescriptions:     dto.ToList(from.AppVersionI18Ns, FromAppVersionI18N),
		CreatedAt:            from.CreatedAt.Unix(),
		UpdatedAt:            from.UpdatedAt.Unix(),
		ReminderType:         v1.ReminderType(from.ReminderType),
		MinCompatibleVersion: from.MinCompatibleVersion,
		OfficialDownloadUrl:  from.OfficialDownloadURL,
	}
}
