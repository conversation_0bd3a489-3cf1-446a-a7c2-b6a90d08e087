package admin

import (
	v1 "byd_wallet/api/walletadmin/v1"
	"byd_wallet/common/dto"
	"byd_wallet/internal/biz"
	"context"
)

type AddressService struct {
	v1.UnimplementedAddressSrvServer

	uc *biz.UserAddressUsecase
}

func NewAddressService(uc *biz.UserAddressUsecase) *AddressService {
	return &AddressService{uc: uc}
}

func (s *AddressService) ListAddress(ctx context.Context, req *v1.ListAddressReq) (*v1.ListAddressReply, error) {
	list, totalCount, err := s.uc.ListUserAddress(ctx, &biz.UserAddressFilter{
		Page:       req.Page,
		PageSize:   req.PageSize,
		ChainIndex: req.ChainIndex,
		Address:    req.GetAddress(),
		OrderBy:    "id desc",
	})
	if err != nil {
		return nil, err
	}
	return &v1.ListAddressReply{
		List:       dto.ToList(list, ToPbAddress),
		TotalCount: totalCount,
	}, nil
}
