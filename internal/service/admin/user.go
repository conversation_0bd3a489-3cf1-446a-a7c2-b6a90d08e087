package admin

import (
	v1 "byd_wallet/api/walletadmin/v1"
	"byd_wallet/common/dto"
	"byd_wallet/internal/biz"
	"context"
)

type UserService struct {
	v1.UnimplementedUserSrvServer

	uc *biz.UserUsecase
}

func NewUserService(uc *biz.UserUsecase) *UserService {
	return &UserService{uc: uc}
}

func (s *UserService) ListUser(ctx context.Context, req *v1.ListUserReq) (*v1.ListUserReply, error) {
	list, totalCount, err := s.uc.ListUser(ctx, &biz.UserFilter{
		Page:     req.Page,
		PageSize: req.PageSize,
		ID:       uint(req.GetId()),
		Username: req.GetBossId(),
		OrderBy:  "id desc",
	})
	if err != nil {
		return nil, err
	}
	return &v1.ListUserReply{
		List:       dto.ToList(list, ToPbUser),
		TotalCount: totalCount,
	}, nil
}
