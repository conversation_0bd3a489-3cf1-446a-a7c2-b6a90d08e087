package admin

import (
	v1 "byd_wallet/api/walletadmin/v1"
	"byd_wallet/internal/biz/dapp"
	"byd_wallet/model"
	"context"
)

var _ v1.AdminDappSrvServer = (*DappService)(nil)

func NewDappService(uc *dapp.AdminUsecase) *DappService {
	return &DappService{uc: uc}
}

type DappService struct {
	v1.UnimplementedAdminDappSrvServer
	uc *dapp.AdminUsecase
}

func (d *DappService) SortDappIndex(ctx context.Context, req *v1.SortReq) (*v1.SortReply, error) {
	if err := d.uc.SortDappIndex(ctx, ToSort(req)); err != nil {
		return nil, err
	}
	return &v1.SortReply{}, nil
}

func (d *DappService) SortDappNavigation(ctx context.Context, req *v1.SortReq) (*v1.SortReply, error) {
	if err := d.uc.SortDappNavigation(ctx, ToSort(req)); err != nil {
		return nil, err
	}
	return &v1.SortReply{}, nil
}

func (d *DappService) DeleteDappCategory(ctx context.Context, req *v1.DeleteDappCategoryReq) (*v1.DeleteDappCategoryReply, error) {
	if err := d.uc.DeleteDappCategory(ctx, uint(req.Id)); err != nil {
		return nil, err
	}
	return &v1.DeleteDappCategoryReply{}, nil
}

func (d *DappService) ListDappIndex(ctx context.Context, req *v1.ListDappIndexReq) (*v1.ListDappIndexReply, error) {
	list, err := d.uc.ListDappIndex(ctx)
	if err != nil {
		return nil, err
	}
	return &v1.ListDappIndexReply{
		List: FromDappIndexes(list),
	}, nil
}

func (d *DappService) DeleteDappIndex(ctx context.Context, req *v1.DeleteDappIndexReq) (*v1.DeleteDappIndexReply, error) {
	if err := d.uc.DeleteDappIndex(ctx, uint(req.Id)); err != nil {
		return nil, err
	}
	return &v1.DeleteDappIndexReply{}, nil
}

func (d *DappService) BatchUpdateDappIndex(ctx context.Context, req *v1.BatchUpdateDappIndexReq) (*v1.BatchUpdateDappIndexReply, error) {
	if err := d.uc.BatchUpdateDappIndex(ctx, UpdatedIndexToDappIndexes(req.List)); err != nil {
		return nil, err
	}
	return &v1.BatchUpdateDappIndexReply{}, nil
}

func (d *DappService) BatchUpdateDappNavigation(ctx context.Context, req *v1.BatchUpdateDappNavigationReq) (*v1.BatchUpdateDappNavigationReply, error) {
	if err := d.uc.BatchUpdateDappNavigation(ctx, ToDappNavigations(req.List)); err != nil {
		return nil, err
	}
	return &v1.BatchUpdateDappNavigationReply{}, nil
}

func (d *DappService) DeleteDappNavigation(ctx context.Context, req *v1.DeleteDappNavigationReq) (*v1.DeleteDappNavigationReply, error) {
	if err := d.uc.DeleteDappNavigation(ctx, uint(req.Id)); err != nil {
		return nil, err
	}
	return &v1.DeleteDappNavigationReply{}, nil
}

func (d *DappService) ListDappNavigation(ctx context.Context, _ *v1.ListDappNavigationReq) (*v1.ListDappNavigationReply, error) {
	list, err := d.uc.ListDappNavigation(ctx)
	if err != nil {
		return nil, err
	}
	return &v1.ListDappNavigationReply{
		List: FromDappNavigations(list),
	}, nil
}

func (d *DappService) CreateDappTopicRel(ctx context.Context, req *v1.CreateDappTopicRelReq) (*v1.CreateDappTopicRelReply, error) {
	if err := d.uc.CreateDappTopicRel(ctx, &model.DappTopicRel{
		DappTopicID: uint(req.Id),
		DappID:      uint(req.DappId),
		SortOrder:   int(req.SortOrder),
	}); err != nil {
		return nil, err
	}
	return &v1.CreateDappTopicRelReply{}, nil
}

func (d *DappService) DeleteDappTopicRel(ctx context.Context, req *v1.DeleteDappTopicRelReq) (*v1.DeleteDappTopicRelReply, error) {
	if err := d.uc.DeleteDappTopicRel(ctx, &model.DappTopicRel{
		DappTopicID: uint(req.Id),
		DappID:      uint(req.DappId),
	}); err != nil {
		return nil, err
	}
	return &v1.DeleteDappTopicRelReply{}, nil
}

func (d *DappService) ListDappTopicRel(ctx context.Context, req *v1.ListDappTopicRelReq) (*v1.ListDappTopicRelReply, error) {
	dapps, err := d.uc.ListTopicDapp(ctx, uint(req.Id))
	if err != nil {
		return nil, err
	}
	return &v1.ListDappTopicRelReply{
		List: FromDapps(dapps),
	}, nil
}

func (d *DappService) UpdateDappTopic(ctx context.Context, req *v1.UpdateDappTopicReq) (*v1.UpdateDappTopicReply, error) {
	if err := d.uc.UpdateDappTopic(ctx, &model.DappTopic{
		BaseModelNoDeleted: model.BaseModelNoDeleted{ID: uint(req.Id)},
		Show:               req.Show,
		BackgroundUrl:      req.BackgroundUrl,
		DappTopicI18Ns:     ToDappTopicI18Ns(req.I18Ns),
	}); err != nil {
		return nil, err
	}
	return &v1.UpdateDappTopicReply{}, nil
}

func (d *DappService) ListDappTopic(ctx context.Context, req *v1.ListDappTopicReq) (*v1.ListDappTopicReply, error) {
	list, err := d.uc.ListDappTopic(ctx)
	if err != nil {
		return nil, err
	}
	return &v1.ListDappTopicReply{
		List: FromDappTopics(list),
	}, nil
}

func (d *DappService) CreateDappCategoryRel(ctx context.Context, req *v1.CreateDappCategoryRelReq) (*v1.CreateDappCategoryRelReply, error) {
	if err := d.uc.CreateDappCategoryRel(ctx, &model.DappCategoryRel{
		DappCategoryID: uint(req.Id),
		DappID:         uint(req.DappId),
	}); err != nil {
		return nil, err
	}
	return &v1.CreateDappCategoryRelReply{}, nil
}

func (d *DappService) DeleteDappCategoryRel(ctx context.Context, req *v1.DeleteDappCategoryRelReq) (*v1.DeleteDappCategoryRelReply, error) {
	if err := d.uc.DeleteDappCategoryRel(ctx, &model.DappCategoryRel{
		DappCategoryID: uint(req.Id),
		DappID:         uint(req.DappId),
	}); err != nil {
		return nil, err
	}
	return &v1.DeleteDappCategoryRelReply{}, nil
}

func (d *DappService) ListDappCategoryRel(ctx context.Context, req *v1.ListDappCategoryRelReq) (*v1.ListDappCategoryRelReply, error) {
	dapps, err := d.uc.ListCategoryDapp(ctx, uint(req.Id))
	if err != nil {
		return nil, err
	}
	return &v1.ListDappCategoryRelReply{
		List: FromDapps(dapps),
	}, nil
}

func (d *DappService) CreateDappIndex(ctx context.Context, req *v1.CreateDappIndexReq) (*v1.CreateDappIndexReply, error) {
	if err := d.uc.CreateDappIndex(ctx, &model.DappIndex{
		OwnerType: req.OwnerType,
		OwnerID:   uint(req.OwnerId),
	}); err != nil {
		return nil, err
	}
	return &v1.CreateDappIndexReply{}, nil
}

func (d *DappService) CreateDappNavigation(ctx context.Context, req *v1.CreateDappNavigationReq) (*v1.CreateDappNavigationReply, error) {
	if err := d.uc.CreateDappNavigation(ctx, &model.DappNavigation{
		DappCategoryID: uint(req.DappCategoryId),
		Show:           req.Show,
	}); err != nil {
		return nil, err
	}
	return &v1.CreateDappNavigationReply{}, nil
}

func (d *DappService) CreateDappTopic(ctx context.Context, req *v1.CreateDappTopicReq) (*v1.CreateDappTopicReply, error) {
	if err := d.uc.CreateDappTopic(ctx, ToDappTopic(req.DataTopic)); err != nil {
		return nil, err
	}
	return &v1.CreateDappTopicReply{}, nil
}

func (d *DappService) DeleteDappTopic(ctx context.Context, req *v1.DeleteDappTopicReq) (*v1.DeleteDappTopicReply, error) {
	if err := d.uc.DeleteDappTopic(ctx, uint(req.Id)); err != nil {
		return nil, err
	}
	return &v1.DeleteDappTopicReply{}, nil
}

func (d *DappService) CreateDappCategory(ctx context.Context, req *v1.CreateDappCategoryReq) (*v1.CreateDappCategoryReply, error) {
	if err := d.uc.CreateDappCategory(ctx, &model.DappCategory{
		Show:              req.Show,
		DappCategoryI18Ns: ToDappCategoryI18Ns(req.I18Ns),
	}); err != nil {
		return nil, err
	}
	return &v1.CreateDappCategoryReply{}, nil
}

func (d *DappService) UpdateDappCategory(ctx context.Context, req *v1.UpdateDappCategoryReq) (*v1.UpdateDappCategoryReply, error) {
	if err := d.uc.UpdateDappCategory(ctx, &model.DappCategory{
		BaseModelNoDeleted: model.BaseModelNoDeleted{ID: uint(req.Id)},
		Show:               req.Show,
		DappCategoryI18Ns:  ToDappCategoryI18Ns(req.I18Ns),
	}); err != nil {
		return nil, err
	}
	return &v1.UpdateDappCategoryReply{}, nil
}

func (d *DappService) ListDappCategory(ctx context.Context, _ *v1.ListDappCategoryReq) (*v1.ListDappCategoryReply, error) {
	list, err := d.uc.ListDappCategory(ctx)
	if err != nil {
		return nil, err
	}
	return &v1.ListDappCategoryReply{
		List: FromDappCategories(list),
	}, nil
}

func (d *DappService) CreateDapp(ctx context.Context, req *v1.CreateDappReq) (*v1.CreateDappReply, error) {
	if err := d.uc.CreateDapp(ctx, ToDapp(req.Dapp)); err != nil {
		return nil, err
	}
	return &v1.CreateDappReply{}, nil
}

func (d *DappService) DeleteDapp(ctx context.Context, req *v1.DeleteDappReq) (*v1.DeleteDappReply, error) {
	if err := d.uc.DeleteDapp(ctx, uint(req.Id)); err != nil {
		return nil, err
	}
	return &v1.DeleteDappReply{}, nil
}

func (d *DappService) UpdateDapp(ctx context.Context, req *v1.UpdateDappReq) (*v1.UpdateDappReply, error) {
	dapp := ToDapp(req.Dapp)
	dapp.ID = uint(req.Id)
	if err := d.uc.UpdateDapp(ctx, dapp); err != nil {
		return nil, err
	}
	return &v1.UpdateDappReply{}, nil
}

func (d *DappService) ListDapp(ctx context.Context, req *v1.ListDappReq) (*v1.ListDappReply, error) {
	dapps, err := d.uc.ListDapp(ctx)
	if err != nil {
		return nil, err
	}
	return &v1.ListDappReply{
		List: FromDapps(dapps),
	}, nil
}
