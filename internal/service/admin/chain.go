package admin

import (
	v1 "byd_wallet/api/walletadmin/v1"
	"byd_wallet/common/dto"
	"byd_wallet/internal/biz"
	"byd_wallet/internal/biz/base"
	"context"

	"google.golang.org/protobuf/types/known/emptypb"
)

type ChainService struct {
	v1.UnimplementedChainSrvServer

	uc     *biz.BlockchainNetworkUsecase
	s3repo base.S3Repo
}

func NewChainService(uc *biz.BlockchainNetworkUsecase,
	s3repo base.S3Repo) *ChainService {
	return &ChainService{
		uc:     uc,
		s3repo: s3repo,
	}
}

func (s *ChainService) ListChain(ctx context.Context, req *v1.ListChainReq) (*v1.ListChainReply, error) {
	list, err := s.uc.ListBlockchainNetwork(ctx, &biz.BlockchainNetworkFilter{
		ChainName: req.GetChainName(),
	})
	if err != nil {
		return nil, err
	}
	return &v1.ListChainReply{
		List: dto.ToList(list, ToPbChain),
	}, nil
}

func (s *ChainService) UpdateChain(ctx context.Context, req *v1.UpdateChainReq) (*emptypb.Empty, error) {
	err := s.uc.UpdateBlockchainNetworkByUpdates(ctx, uint(req.GetId()), map[string]interface{}{
		"blockchain_url": s.s3repo.ToAppAccessUrl(ctx, req.BlockchainUrl),
		"token_url":      s.s3repo.ToAppAccessUrl(ctx, req.TokenUrl),
		"is_display":     req.IsDisplay,
	})
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

func (s *ChainService) UpdateChainSort(ctx context.Context, req *v1.UpdateChainSortReq) (*emptypb.Empty, error) {
	err := s.uc.UpdateBlockchainNetworkSortOrder(ctx, uint(req.GetId()), req.CurrentSort, req.TargetSort)
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}
