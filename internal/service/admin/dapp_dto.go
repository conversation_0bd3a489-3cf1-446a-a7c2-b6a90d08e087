package admin

import (
	v1 "byd_wallet/api/walletadmin/v1"
	"byd_wallet/internal/biz/dapp"
	"byd_wallet/model"

	"github.com/jinzhu/copier"
)

func ToSort(from *v1.SortReq) *dapp.Sort {
	return &dapp.Sort{
		ID:        uint(from.Id),
		Direction: from.CurrentSort < from.TargetSort,
	}
}

func UpdatedIndexToDappIndexes(froms []*v1.UpdatedDappIndex) []*model.DappIndex {
	var out []*model.DappIndex
	for _, from := range froms {
		out = append(out, UpdatedIndexToDappIndex(from))
	}
	return out
}

func UpdatedIndexToDappIndex(from *v1.UpdatedDappIndex) *model.DappIndex {
	return &model.DappIndex{
		BaseModelNoDeleted: model.BaseModelNoDeleted{ID: uint(from.Id)},
		SortOrder:          int(from.SortOrder),
	}
}

func FromDappIndexes(froms []*dapp.AdminDappIndex) []*v1.DappIndex {
	var out []*v1.DappIndex
	for _, from := range froms {
		out = append(out, FromDappIndex(from))
	}
	return out
}

func FromDappIndex(from *dapp.AdminDappIndex) *v1.DappIndex {
	return &v1.DappIndex{
		List:      FromDapps(from.Dapps),
		Id:        uint64(from.ID),
		SortOrder: int64(from.SortOrder),
		Name:      from.Name,
		OwnerType: from.OwnerType,
	}
}

func ToDappTopic(from *v1.DappTopic) *model.DappTopic {
	var out model.DappTopic
	_ = copier.Copy(&out, from)
	out.DappTopicI18Ns = ToDappTopicI18Ns(from.I18Ns)
	return &out
}

func ToDappTopicI18Ns(froms []*v1.DappTopicI18N) []*model.DappTopicI18N {
	var out []*model.DappTopicI18N
	_ = copier.Copy(&out, froms)
	return out
}

func ToDappCategory(from *v1.DappCategory) *model.DappCategory {
	var out model.DappCategory
	_ = copier.Copy(&out, from)
	out.DappCategoryI18Ns = ToDappCategoryI18Ns(from.I18Ns)
	return &out
}

func FromDappCategory(from *dapp.AdminDappCategory) *v1.DappCategory {
	return &v1.DappCategory{
		Id:         uint64(from.ID),
		Show:       from.Show,
		I18Ns:      FromDappCategoryI18Ns(from.DappCategoryI18Ns),
		AppCount:   int64(from.AppCount),
		HotCount:   int64(from.HotCount),
		Navigation: from.Navigation,
	}
}

func FromDappNavigation(from *dapp.AdminDappNavigation) *v1.DappNavigation {
	return &v1.DappNavigation{
		Id:        uint64(from.ID),
		Show:      from.Show,
		I18Ns:     FromDappCategoryI18Ns(from.DappCategoryI18Ns),
		AppCount:  int64(from.AppCount),
		HotCount:  int64(from.HotCount),
		SortOrder: int64(from.SortOrder),
	}
}

func FromDappCategories(froms []*dapp.AdminDappCategory) []*v1.DappCategory {
	var out []*v1.DappCategory
	for _, from := range froms {
		out = append(out, FromDappCategory(from))
	}
	return out
}

func FromDappNavigations(froms []*dapp.AdminDappNavigation) []*v1.DappNavigation {
	var out []*v1.DappNavigation
	for _, from := range froms {
		out = append(out, FromDappNavigation(from))
	}
	return out
}

func ToDappNavigation(from *v1.DappNavigation) *model.DappNavigation {
	return &model.DappNavigation{
		BaseModelNoDeleted: model.BaseModelNoDeleted{ID: uint(from.Id)},
		SortOrder:          int(from.SortOrder),
		Show:               from.Show,
	}
}

func ToDappNavigations(froms []*v1.DappNavigation) []*model.DappNavigation {
	var out []*model.DappNavigation
	for _, from := range froms {
		out = append(out, ToDappNavigation(from))
	}
	return out
}

func ToDappCategoryI18Ns(froms []*v1.DappCategoryI18N) []*model.DappCategoryI18N {
	var out []*model.DappCategoryI18N
	_ = copier.Copy(&out, froms)
	return out
}

func ToDapp(from *v1.Dapp) *model.Dapp {
	var out model.Dapp
	_ = copier.Copy(&out, from)
	out.DappI18Ns = ToDappI18Ns(from.I18Ns)
	for _, network := range from.Networks {
		out.DappBlockchainNetworks = append(out.DappBlockchainNetworks, &model.DappBlockchainNetwork{
			BlockchainNetworkID: uint(network.Id),
			Address:             network.Address,
		})
	}
	return &out
}

func ToDappI18Ns(froms []*v1.DappI18N) []*model.DappI18N {
	var out []*model.DappI18N
	_ = copier.Copy(&out, froms)
	return out
}

func FromDapps(froms []*model.Dapp) []*v1.Dapp {
	var out []*v1.Dapp
	for _, from := range froms {
		out = append(out, FromDapp(from))
	}
	return out
}

func FromDapp(from *model.Dapp) *v1.Dapp {
	var out v1.Dapp
	_ = copier.Copy(&out, from)
	out.Id = uint64(from.ID)
	out.CreatedAt = from.CreatedAt.Unix()
	out.I18Ns = FromDappI18Ns(from.DappI18Ns)
	var networks []*v1.BlockchainNetwork
	for _, network := range from.DappBlockchainNetworks {
		pbNetwork := FromBlockchainNetwork(network.BlockchainNetwork)
		pbNetwork.Address = network.Address
		networks = append(networks, pbNetwork)
	}
	out.Networks = networks
	return &out
}

func FromDappI18Ns(froms []*model.DappI18N) []*v1.DappI18N {
	var out []*v1.DappI18N
	_ = copier.Copy(&out, froms)
	return out
}

func FromDappCategoryI18Ns(froms []*model.DappCategoryI18N) []*v1.DappCategoryI18N {
	var out []*v1.DappCategoryI18N
	_ = copier.Copy(&out, froms)
	return out
}

func FromBlockchainNetwork(from *model.BlockchainNetwork) *v1.BlockchainNetwork {
	var out v1.BlockchainNetwork
	_ = copier.Copy(&out, from)
	out.Id = uint64(from.ID)
	return &out
}

func FromDappTopic(from *dapp.AdminDappTopic) *v1.DappTopicInfo {
	return &v1.DappTopicInfo{
		Id:            uint64(from.ID),
		Show:          from.Show,
		BackgroundUrl: from.BackgroundUrl,
		I18Ns:         FromDappTopicI18Ns(from.DappTopicI18Ns),
		AppCount:      int64(from.AppCount),
		CreatedAt:     from.CreatedAt,
	}
}

func FromDappTopics(froms []*dapp.AdminDappTopic) []*v1.DappTopicInfo {
	var out []*v1.DappTopicInfo
	for _, from := range froms {
		out = append(out, FromDappTopic(from))
	}
	return out
}

func FromDappTopicI18Ns(froms []*model.DappTopicI18N) []*v1.DappTopicI18N {
	var out []*v1.DappTopicI18N
	_ = copier.Copy(&out, froms)
	return out
}
