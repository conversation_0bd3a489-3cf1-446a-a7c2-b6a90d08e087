package admin

import (
	v1 "byd_wallet/api/walletadmin/v1"
	"byd_wallet/internal/biz"
	"context"
)

type authKey struct{}

// NewAuthContext put auth info into context
func NewAuthContext(ctx context.Context, adminID uint) context.Context {
	return context.WithValue(ctx, authKey{}, adminID)
}

// FromAuthContext extract auth info from context
func FromAuthContext(ctx context.Context) (adminID uint, ok bool) {
	adminID, ok = ctx.Value(authKey{}).(uint)
	return
}

type AdminService struct {
	v1.UnimplementedAdminSrvServer

	uc *biz.AdminUsecase
}

func NewAdminService(uc *biz.AdminUsecase) *AdminService {
	return &AdminService{uc: uc}
}

func (s *AdminService) Login(ctx context.Context, req *v1.LoginReq) (*v1.LoginReply, error) {
	jwtToken, err := s.uc.Login(ctx, &biz.AdminLoginParam{
		Username: req.Username,
		Password: req.Password,
	})
	if err != nil {
		return nil, err
	}
	return &v1.LoginReply{
		Token: jwtToken,
	}, nil
}

func (s *AdminService) VerifyJwtToken(ctx context.Context, jwtToken string) (adminID uint, err error) {
	return s.uc.VerifyJwtToken(ctx, jwtToken)
}
