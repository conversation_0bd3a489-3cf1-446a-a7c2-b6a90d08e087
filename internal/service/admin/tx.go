package admin

import (
	v1 "byd_wallet/api/walletadmin/v1"
	"byd_wallet/common/dto"
	"byd_wallet/internal/biz"
	"context"
)

type TxService struct {
	v1.UnimplementedTxSrvServer

	uc *biz.TransactionUsecase
}

func NewTxService(uc *biz.TransactionUsecase) *TxService {
	return &TxService{uc: uc}
}

func (s *TxService) ListTx(ctx context.Context, req *v1.ListTxReq) (*v1.ListTxReply, error) {
	list, totalCount, err := s.uc.ListTransactionView(ctx, &biz.TransactionViewFilter{
		Page:        req.Page,
		PageSize:    req.PageSize,
		ChainIndex:  req.ChainIndex,
		FromAddress: req.GetFromAddress(),
		ToAddress:   req.GetToAddress(),
		TxHash:      req.GetTxHash(),
		OrderBy:     "timestamp desc",
	})
	if err != nil {
		return nil, err
	}
	return &v1.ListTxReply{
		List:       dto.ToList(list, ToPbTxView),
		TotalCount: totalCount,
	}, nil
}
