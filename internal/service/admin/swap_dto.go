package admin

import (
	v1 "byd_wallet/api/walletadmin/v1"
	"byd_wallet/internal/biz/base"
	"byd_wallet/model"
	"fmt"
)

func ToSwapRecords(froms []*model.SwapRecord) []*v1.SwapRecord {
	tos := make([]*v1.SwapRecord, len(froms))
	for i, from := range froms {
		tos[i] = ToSwapRecord(from)
	}
	return tos
}

func ToSwapRecord(from *model.SwapRecord) *v1.SwapRecord {
	var finishedAt int64
	if from.FinishedAt != nil {
		finishedAt = from.FinishedAt.Unix()
	}
	return &v1.SwapRecord{
		Id:           uint64(from.ID),
		SwappedAt:    from.SwappedAt.Unix(),
		Status:       string(from.Status),
		From:         ToSwapToken(from.FromTokenAsset, from.FromAddress, from.FromTokenAmount),
		To:           ToSwapToken(from.ToTokenAsset, from.ToAddress, from.ToTokenAmount),
		GasFee:       from.GasFee,
		FeeRate:      from.FeeRate,
		Hash:         from.Hash,
		ApprovalHash: from.ApprovalHash,
		Height:       from.BlockNumber,
		Dex:          from.Dex,
		DexLogo:      from.DexLogo,
		SwapPrice:    from.SwapPrice,
		Details:      ToSwapDetails(from.Details),
		FinishedAt:   finishedAt,
	}
}

func ToSwapDetails(froms []*model.SwapDetail) []*v1.SwapDetail {
	tos := make([]*v1.SwapDetail, len(froms))
	for i, from := range froms {
		tos[i] = ToSwapDetail(from)
	}
	return tos
}

func ToSwapDetail(from *model.SwapDetail) *v1.SwapDetail {
	return &v1.SwapDetail{
		ChainIndex:  from.ChainIndex,
		ExplorerUrl: from.ExplorerURL,
		Hash:        from.Hash,
		Status:      from.Status,
		Collected:   from.Collected,
	}
}

func ToSwapToken(from *model.TokenAsset, address, amount string) *v1.SwapToken {
	return &v1.SwapToken{
		TokenAddress: from.Address,
		Address:      address,
		ChainIndex:   from.ChainIndex,
		Amount:       amount,
		TokenLogo:    from.LogoUrl,
		Symbol:       from.Symbol,
		Decimals:     fmt.Sprintf("%d", from.Decimals),
	}
}

var pbSortType2Model = map[v1.SortType]base.SortType{
	v1.SortType_UP:   base.SortTypeUp,
	v1.SortType_DOWN: base.SortTypeDown,
	v1.SortType_TOP:  base.SortTypeTop,
}

func FromSortInput(from *v1.CommonSortReq) *base.SortInput {
	return &base.SortInput{
		ID:       uint(from.Id),
		SortType: pbSortType2Model[from.SortType],
	}
}

func ToHotSwappableTokens(froms []*model.SwappableHotToken) []*v1.SwappableToken {
	tos := make([]*v1.SwappableToken, len(froms))
	for i, from := range froms {
		to := ToSwappableToken(from.SwappableToken)
		to.Id = uint64(from.ID)
		to.CreatedAt = from.CreatedAt.Unix()
		to.IsAll = from.IsAll
		tos[i] = to
	}
	return tos
}

func ToSwappableTokens(froms []*model.SwappableToken) []*v1.SwappableToken {
	tos := make([]*v1.SwappableToken, len(froms))
	for i, from := range froms {
		tos[i] = ToSwappableToken(from)
	}
	return tos
}

func ToSwappableToken(from *model.SwappableToken) *v1.SwappableToken {
	var ta model.TokenAsset
	if from.TokenAsset != nil {
		ta = *from.TokenAsset
	}
	var chain model.BlockchainNetwork
	if from.BlockchainNetwork != nil {
		chain = *from.BlockchainNetwork
	}
	var channels []string
	for _, channel := range from.Channels {
		if channel.SwapChannel == nil {
			continue
		}
		channels = append(channels, channel.SwapChannel.Name)
	}
	return &v1.SwappableToken{
		Id:         uint64(from.ID),
		Name:       ta.Name,
		Symbol:     ta.Symbol,
		ChainIndex: ta.ChainIndex,
		Decimals:   ta.Decimals,
		Address:    ta.Address,
		CreatedAt:  from.CreatedAt.Unix(),
		ChainName:  chain.ChainName,
		LogoUrl:    ta.LogoUrl,
		Display:    from.Display,
		ChainId:    ta.ChainId,
		Enable:     from.Enable,
		Channels:   channels,
	}
}

func FromPagination(page, pageSize int64) base.Pagination {
	return base.Pagination{
		Page:     int(page),
		PageSize: int(pageSize),
	}
}

func ToSwapChannel(from *model.SwapChannel) *v1.SwapChannel {
	return &v1.SwapChannel{
		Id:     uint64(from.ID),
		Name:   from.Name,
		Enable: from.Enable,
	}
}

func ToSwapChannels(froms []*model.SwapChannel) []*v1.SwapChannel {
	tos := make([]*v1.SwapChannel, len(froms))
	for i, from := range froms {
		tos[i] = ToSwapChannel(from)
	}
	return tos
}
