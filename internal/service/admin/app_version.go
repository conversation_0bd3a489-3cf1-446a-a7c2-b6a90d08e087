package admin

import (
	"byd_wallet/common/dto"
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"

	pb "byd_wallet/api/walletadmin/v1"

	"google.golang.org/protobuf/types/known/emptypb"
)

var _ pb.AppVersionServiceServer = (*AppVersionService)(nil)

// NewAppVersionService 创建app版本管理服务
func NewAppVersionService(uc *biz.AppVersionAdminUsecase) *AppVersionService {
	return &AppVersionService{
		uc: uc,
	}
}

// AppVersionService app版本管理服务
type AppVersionService struct {
	pb.UnimplementedAppVersionServiceServer
	uc *biz.AppVersionAdminUsecase
}

func (s *AppVersionService) ListPublishedVersions2(ctx context.Context, req *pb.ListPublishedVersionsReq) (*pb.ListPublishedVersionsReply, error) {
	//TODO implement me
	panic("implement me")
}

func (s *AppVersionService) ListPublishedVersions(ctx context.Context, req *pb.ListPublishedVersionsReq) (*pb.ListPublishedVersionsReply, error) {
	list, err := s.uc.ListPublishedVersions(ctx, req.AppType)
	if err != nil {
		return nil, err
	}
	return &pb.ListPublishedVersionsReply{List: list}, nil
}

// CreateAppVersion 创建版本
func (s *AppVersionService) CreateAppVersion(ctx context.Context, req *pb.CreateAppVersionReq) (*pb.CreateAppVersionReply, error) {
	err := s.uc.CreateAppVersion(ctx, &model.AppVersion{
		Version:              req.Version,
		AppType:              req.AppType,
		DownloadURL:          req.DownloadUrl,
		OfficialDownloadURL:  req.OfficialDownloadUrl,
		ForceUpdate:          req.ForceUpdate,
		ReminderType:         model.AppVersionReminderType(req.ReminderType),
		MinCompatibleVersion: req.MinCompatibleVersion,
		AppVersionI18Ns:      dto.ToList(req.I18NDescriptions, ToAppVersionI18N),
	})
	if err != nil {
		return nil, err
	}

	return &pb.CreateAppVersionReply{}, nil
}

// UpdateAppVersion 更新版本
func (s *AppVersionService) UpdateAppVersion(ctx context.Context, req *pb.UpdateAppVersionReq) (*emptypb.Empty, error) {
	version := &model.AppVersion{AppVersionI18Ns: dto.ToList(req.I18NDescriptions, ToAppVersionI18N)}
	version.ID = uint(req.Id)
	err := s.uc.UpdateAppVersion(ctx, version)
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

// DeleteAppVersion 删除版本
func (s *AppVersionService) DeleteAppVersion(ctx context.Context, req *pb.DeleteAppVersionReq) (*emptypb.Empty, error) {
	err := s.uc.DeleteAppVersion(ctx, uint(req.Id))
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

// GetAppVersion 获取版本详情
func (s *AppVersionService) GetAppVersion(ctx context.Context, req *pb.GetAppVersionReq) (*pb.AppVersionInfo, error) {
	info, err := s.uc.GetAppVersion(ctx, uint(req.Id))
	if err != nil {
		return nil, err
	}
	return FromAppVersion(info), nil
}

// ListAppVersions 版本列表
func (s *AppVersionService) ListAppVersions(ctx context.Context, req *pb.ListAppVersionsReq) (*pb.ListAppVersionsReply, error) {
	list, count, err := s.uc.ListAppVersion(ctx, biz.AdminAppVersionFilter{
		Pagination: FromPagination(req.Page, req.PageSize),
		AppType:    req.AppType,
	})
	if err != nil {
		return nil, err
	}
	return &pb.ListAppVersionsReply{
		List:       dto.ToList(list, FromAppVersion),
		TotalCount: count,
	}, nil
}
