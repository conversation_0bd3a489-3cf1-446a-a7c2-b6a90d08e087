package admin

import (
	"testing"

	"github.com/jinzhu/copier"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"

	v1 "byd_wallet/api/walletadmin/v1"
	"byd_wallet/model"
)

func TestCopier(t *testing.T) {
	src := []uint64{1, 2, 3, 4}
	var dst []uint
	err := copier.Copy(&dst, &src)
	assert.NoError(t, err)
	assert.Equal(t, dst, []uint{1, 2, 3, 4})
}

func TestToDappI18Ns(t *testing.T) {
	input := []*v1.DappI18N{
		{Name: "Dapp1", Summary: "Summary1", Language: "en"},
		{Name: "Dapp2", Summary: "Summary2", Language: "zh"},
	}
	want := []*model.DappI18N{
		{Name: "Dapp1", Summary: "Summary1", Language: "en"},
		{Name: "Dapp2", Summary: "Summary2", Language: "zh"},
	}
	got := ToDappI18Ns(input)
	assert.Equal(t, want, got)
}

func TestToDapp(t *testing.T) {
	input := &v1.Dapp{
		Id:   123,
		Logo: "logo_url",
		Link: "link_url",
		Tags: "tag1,tag2",
		Hot:  true,
		Show: false,
		I18Ns: []*v1.DappI18N{
			{Name: "Dapp1", Summary: "Summary1", Language: "en"},
			{Name: "Dapp2", Summary: "Summary2", Language: "zh"},
		},
	}
	want := &model.Dapp{
		BaseModelNoDeleted: model.BaseModelNoDeleted{
			ID: 123,
		},
		Logo: "logo_url",
		Link: "link_url",
		Tags: "tag1,tag2",
		Hot:  true,
		Show: false,
		DappI18Ns: []*model.DappI18N{
			{Name: "Dapp1", Summary: "Summary1", Language: "en"},
			{Name: "Dapp2", Summary: "Summary2", Language: "zh"},
		},
	}
	got := ToDapp(input)
	assert.Equal(t, want, got)
}

func TestFromDapps(t *testing.T) {
	input := []*model.Dapp{
		{
			BaseModelNoDeleted: model.BaseModelNoDeleted{ID: 1},
			Logo:               "logo1_url",
			Link:               "link1_url",
			Tags:               "tag1,tag2",
			Hot:                true,
			Show:               true,
			DappI18Ns: []*model.DappI18N{
				{Name: "Dapp1 EN", Summary: "Summary1 EN", Language: "en"},
				{Name: "Dapp1 ZH", Summary: "Summary1 ZH", Language: "zh"},
			},
			DappBlockchainNetworks: []*model.DappBlockchainNetwork{
				{
					BlockchainNetworkID: 101,
					BlockchainNetwork: &model.BlockchainNetwork{
						Model:     gorm.Model{ID: 101},
						Name:      "Network 1",
						ChainName: "Chain 1",
					},
				},
				{
					BlockchainNetworkID: 102,
					BlockchainNetwork: &model.BlockchainNetwork{
						Model:     gorm.Model{ID: 102},
						Name:      "Network 2",
						ChainName: "Chain 2",
					},
				},
			},
		},
		{
			BaseModelNoDeleted: model.BaseModelNoDeleted{ID: 2},
			Logo:               "logo2_url",
			Link:               "link2_url",
			Tags:               "tag3",
			Hot:                false,
			Show:               true,
			DappI18Ns: []*model.DappI18N{
				{Name: "Dapp2 EN", Summary: "Summary2 EN", Language: "en"},
			},
		},
	}

	want := []*v1.Dapp{
		{
			Id:   1,
			Logo: "logo1_url",
			Link: "link1_url",
			Tags: "tag1,tag2",
			Hot:  true,
			Show: true,
			I18Ns: []*v1.DappI18N{
				{Name: "Dapp1 EN", Summary: "Summary1 EN", Language: "en"},
				{Name: "Dapp1 ZH", Summary: "Summary1 ZH", Language: "zh"},
			},
			Networks: []*v1.BlockchainNetwork{
				{
					Id:   101,
					Name: "Network 1",
					// ChainName: "Chain 1",
				},
				{
					Id:   102,
					Name: "Network 2",
					// ChainName: "Chain 2",
				},
			},
		},
		{
			Id:   2,
			Logo: "logo2_url",
			Link: "link2_url",
			Tags: "tag3",
			Hot:  false,
			Show: true,
			I18Ns: []*v1.DappI18N{
				{Name: "Dapp2 EN", Summary: "Summary2 EN", Language: "en"},
			},
			Networks: nil, // Expecting nil or empty slice if DappBlockchainNetworks is empty or nil
		},
	}

	got := FromDapps(input)
	assert.Equal(t, want, got)

	// Test with nil input
	assert.Nil(t, FromDapps(nil))

}
