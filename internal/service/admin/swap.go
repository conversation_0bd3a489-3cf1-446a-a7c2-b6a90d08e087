package admin

import (
	v1 "byd_wallet/api/walletadmin/v1"
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"
	"gorm.io/gorm"
)

var _ v1.SwapServiceServer = (*SwapService)(nil)

func NewSwapService(uc *biz.SwapAdminUsecase) *SwapService {
	return &SwapService{uc: uc}
}

type SwapService struct {
	v1.UnimplementedSwapServiceServer
	uc *biz.SwapAdminUsecase
}

func (s *SwapService) ListSwapChannel(ctx context.Context, request *v1.ListSwapChannelRequest) (*v1.ListSwapChannelReply, error) {
	list, count, err := s.uc.ListSwapChannel(ctx, FromPagination(request.Page, request.PageSize))
	if err != nil {
		return nil, err
	}
	return &v1.ListSwapChannelReply{
		List:       ToSwapChannels(list),
		TotalCount: count,
	}, nil
}

func (s *SwapService) UpdateSwapChannel(ctx context.Context, request *v1.UpdateSwapChannelRequest) (*v1.UpdateSwapChannelReply, error) {
	if err := s.uc.UpdateSwapChannel(ctx, &model.SwapChannel{
		Model:  gorm.Model{ID: uint(request.Id)},
		Enable: request.Enable,
	}); err != nil {
		return nil, err
	}
	return &v1.UpdateSwapChannelReply{}, nil
}

func (s *SwapService) ListToken(ctx context.Context, request *v1.ListTokenRequest) (*v1.ListTokenReply, error) {
	list, count, err := s.uc.ListToken(ctx, biz.AdminSwapTokenFilter{
		Pagination: FromPagination(request.Page, request.PageSize),
		Symbol:     request.Symbol,
		Address:    request.Address,
		ChannelID:  uint(request.ChannelId),
		ChainIndex: request.ChainIndex,
		Native:     request.Native,
	})
	if err != nil {
		return nil, err
	}
	return &v1.ListTokenReply{
		List:       ToSwappableTokens(list),
		TotalCount: count,
	}, nil
}

func (s *SwapService) UpdateToken(ctx context.Context, request *v1.UpdateTokenRequest) (*v1.UpdateTokenReply, error) {
	if err := s.uc.UpdateToken(ctx, &model.SwappableToken{
		Model:   gorm.Model{ID: uint(request.Id)},
		Display: request.Display,
	}); err != nil {
		return nil, err
	}
	return &v1.UpdateTokenReply{}, nil
}

func (s *SwapService) CreateHotToken(ctx context.Context, request *v1.CreateHotTokenRequest) (*v1.CreateHotTokenReply, error) {
	if err := s.uc.CreateHotToken(ctx, &model.SwappableHotToken{
		SwappableTokenID: uint(request.SwappableTokenId),
		IsAll:            request.IsAll,
	}); err != nil {
		return nil, err
	}
	return &v1.CreateHotTokenReply{}, nil
}

func (s *SwapService) DeleteHotToken(ctx context.Context, request *v1.DeleteHotTokenRequest) (*v1.DeleteHotTokenReply, error) {
	if err := s.uc.DeleteHotToken(ctx, uint(request.Id)); err != nil {
		return nil, err
	}
	return &v1.DeleteHotTokenReply{}, nil
}

func (s *SwapService) SortHotToken(ctx context.Context, req *v1.CommonSortReq) (*v1.CommonSortReply, error) {
	if err := s.uc.SortHotToken(ctx, FromSortInput(req)); err != nil {
		return nil, err
	}
	return &v1.CommonSortReply{}, nil
}

func (s *SwapService) ListHotToken(ctx context.Context, request *v1.ListHotTokenRequest) (*v1.ListHotTokenReply, error) {
	list, count, err := s.uc.ListHotToken(ctx, biz.AdminHotTokenFilter{
		Pagination: FromPagination(request.Page, request.PageSize),
		ChainIndex: request.ChainIndex,
		Symbol:     request.Symbol,
		Address:    request.Address,
	})
	if err != nil {
		return nil, err
	}
	return &v1.ListHotTokenReply{
		List:       ToHotSwappableTokens(list),
		TotalCount: count,
	}, nil
}

func (s *SwapService) GetSwapRecord(ctx context.Context, request *v1.GetSwapRecordRequest) (*v1.SwapRecord, error) {
	data, err := s.uc.GetSwapRecord(ctx, uint(request.Id))
	if err != nil {
		return nil, err
	}
	return ToSwapRecord(data), nil
}

func (s *SwapService) ListSwapRecord(ctx context.Context, request *v1.ListSwapRecordRequest) (*v1.ListSwapRecordReply, error) {
	list, count, err := s.uc.ListSwapRecord(ctx, biz.AdminSwapRecordFilter{
		Pagination:  FromPagination(request.Page, request.PageSize),
		ChannelID:   uint(request.ChannelId),
		Symbol:      request.Symbol,
		Address:     request.Address,
		Status:      model.SwapStatus(request.Status),
		FromAddress: request.FromAddress,
	})
	if err != nil {
		return nil, err
	}
	return &v1.ListSwapRecordReply{
		List:       ToSwapRecords(list),
		TotalCount: count,
	}, nil
}
