package admin

import (
	v1 "byd_wallet/api/walletadmin/v1"
	"byd_wallet/common/dto"
	"byd_wallet/internal/biz"
	"byd_wallet/internal/biz/base"
	"context"

	"google.golang.org/protobuf/types/known/emptypb"
)

type CoinService struct {
	v1.UnimplementedCoinSrvServer

	uc     *biz.TokenAssetUsecase
	s3repo base.S3Repo
	tasUc  *biz.TokenAssetStarUsecase
}

func NewCoinService(uc *biz.TokenAssetUsecase,
	s3repo base.S3Repo,
	tasUc *biz.TokenAssetStarUsecase) *CoinService {
	return &CoinService{
		uc:     uc,
		s3repo: s3repo,
		tasUc:  tasUc,
	}
}

func (s *CoinService) CreateCoinStar(ctx context.Context, req *v1.CreateCoinStarReq) (*emptypb.Empty, error) {
	_, err := s.tasUc.CreateTokenAssetStar(ctx, uint(req.GetCoinId()))
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}
func (s *CoinService) ListCoinStar(ctx context.Context, req *v1.ListCoinStarReq) (*v1.ListCoinStarReply, error) {
	list, totalCount, err := s.tasUc.ListTokenAssetStarView(ctx, &biz.TokenAssetStarViewFilter{
		Page:       req.Page,
		PageSize:   req.PageSize,
		ChainIndex: req.ChainIndex,
		Symbol:     req.GetSymbol(),
		Address:    req.GetAddress(),
		OrderBy:    "sort_order desc",
	})
	if err != nil {
		return nil, err
	}
	return &v1.ListCoinStarReply{
		List:       dto.ToList(list, ToPbCoinStar),
		TotalCount: totalCount,
	}, nil
}
func (s *CoinService) DeleteCoinStar(ctx context.Context, req *v1.DeleteCoinStarReq) (*emptypb.Empty, error) {
	err := s.tasUc.DeleteTokenAssetStar(ctx, uint(req.Id))
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}
func (s *CoinService) UpdateCoinStarSort(ctx context.Context, req *v1.UpdateCoinStarSortReq) (*emptypb.Empty, error) {
	err := s.tasUc.UpdateTokenAssetStarSort(ctx, uint(req.Id), req.CurrentSort, req.TargetSort)
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

func (s *CoinService) ListCoin(ctx context.Context, req *v1.ListCoinReq) (*v1.ListCoinReply, error) {
	list, totalCount, err := s.uc.ListTokenAsset(ctx, &biz.TokenAssetFilter{
		Page:       req.Page,
		PageSize:   req.PageSize,
		ChainIndex: req.ChainIndex,
		Symbol:     req.GetSymbol(),
		Address:    req.GetAddress(),
		OrderBy:    "id desc",
	})
	if err != nil {
		return nil, err
	}
	return &v1.ListCoinReply{
		List:       dto.ToList(list, ToPbCoin),
		TotalCount: totalCount,
	}, nil
}

func (s *CoinService) UpdateCoin(ctx context.Context, req *v1.UpdateCoinReq) (*emptypb.Empty, error) {
	err := s.uc.UpdateTokenAssetByUpdates(ctx, uint(req.GetId()), map[string]interface{}{
		"logo_url":   s.s3repo.ToAppAccessUrl(ctx, req.LogoUrl),
		"is_display": req.IsDisplay,
	})
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}
