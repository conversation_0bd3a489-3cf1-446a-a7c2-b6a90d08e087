package service

import (
	v1 "byd_wallet/api/wallet/v1"
	"byd_wallet/internal/biz"
	"byd_wallet/internal/biz/dapp"
	"byd_wallet/model"
	"context"
)

var _ v1.DappSrvServer = (*DappService)(nil)

func NewDappService(uc *dapp.Usecase, approvalUc *biz.ApprovalUsecase) *DappService {
	return &DappService{uc: uc, approvalUc: approvalUc}
}

type DappService struct {
	v1.UnimplementedDappSrvServer
	uc         *dapp.Usecase
	approvalUc *biz.ApprovalUsecase
}

func (d DappService) ListApprovedAddresses(ctx context.Context, req *v1.ListApprovedAddressesReq) (*v1.ListApprovedAddressesReply, error) {
	list, err := d.approvalUc.ListApprovedAddresses(ctx, FromUserAddresses(req.GetAddresses()))
	if err != nil {
		return nil, err
	}
	return &v1.ListApprovedAddressesReply{
		List: ToUserAddresses(list),
	}, nil
}

func (d DappService) ListApprovalByUserAddress(ctx context.Context, req *v1.ListApprovalByUserAddressReq) (*v1.ListApprovalByUserAddressReply, error) {
	data, err := d.approvalUc.ListApprovalByUserAddress(ctx, model.UserAddress{
		ChainIndex: req.ChainIndex,
		Address:    req.Address,
	})
	if err != nil {
		return nil, err
	}
	return ToTokenDappApprovals(data), nil
}

func (d DappService) ListApprovedDappsByUserAddresses(ctx context.Context, req *v1.ListApprovalByUserAddressesReq) (*v1.ListApprovedDappsByUserAddressesReply, error) {
	list, err := d.approvalUc.ListApprovedDappsByUserAddresses(ctx, FromUserAddresses(req.Addresses))
	if err != nil {
		return nil, err
	}
	return &v1.ListApprovedDappsByUserAddressesReply{
		List: ToDappBlockchainNetworks(list),
	}, nil
}

func (d DappService) ListTokenApprovalsByDapp(ctx context.Context, req *v1.ListTokenApprovalsByDappReq) (*v1.ListTokenApprovalsByDappReply, error) {
	list, err := d.approvalUc.ListTokenApprovalsByDapp(ctx, &biz.UserApprovedDapp{
		Addresses:         req.Addresses,
		BlockchainNetwork: &model.BlockchainNetwork{ChainIndex: req.ChainIndex},
		Spender:           req.SpenderAddress,
	})
	if err != nil {
		return nil, err
	}
	return &v1.ListTokenApprovalsByDappReply{
		List: ToTokenApprovals(list),
	}, nil
}

func (d DappService) SearchDapp(ctx context.Context, req *v1.SearchDappReq) (*v1.SearchDappReply, error) {
	list, err := d.uc.SearchDappByKey(ctx, req.Key)
	if err != nil {
		return nil, err
	}
	return &v1.SearchDappReply{
		List: ToDapps(list),
	}, nil
}

func (d DappService) GetDappTopic(ctx context.Context, req *v1.GetDappTopicReq) (*v1.GetDappTopicReply, error) {
	topic, err := d.uc.GetDappTopic(ctx, uint(req.Id))
	if err != nil {
		return nil, err
	}
	return &v1.GetDappTopicReply{
		Topic: ToDappTopic(topic),
	}, nil
}

func (d DappService) GetDappCategory(ctx context.Context, req *v1.GetDappCategoryReq) (*v1.GetDappCategoryReply, error) {
	dapps, err := d.uc.FilterDapps(ctx, dapp.DappsFilter{
		ChainIndex:     req.ChainIndex,
		ChainID:        req.ChainId,
		DappCategoryID: uint(req.Id),
	})
	if err != nil {
		return nil, err
	}
	return &v1.GetDappCategoryReply{
		List: ToDapps(dapps),
	}, nil
}

func (d DappService) ListNavigation(ctx context.Context, req *v1.ListNavigationReq) (*v1.ListNavigationReply, error) {
	list, err := d.uc.ListDappNavigation(ctx)
	if err != nil {
		return nil, err
	}
	var out v1.ListNavigationReply
	for _, navigation := range list {
		if navigation.DappCategory == nil || len(navigation.DappCategory.DappCategoryI18Ns) == 0 {
			continue
		}
		out.List = append(out.List, &v1.DappNavigation{
			DappCategoryId: uint64(navigation.DappCategoryID),
			Name:           navigation.DappCategory.DappCategoryI18Ns[0].Name,
		})
	}
	return &out, nil
}

func (d DappService) ListDappIndex(ctx context.Context, req *v1.ListDappIndexReq) (*v1.ListDappIndexReply, error) {
	list, err := d.uc.ListDappIndex(ctx)
	if err != nil {
		return nil, err
	}
	var result v1.ListDappIndexReply
	for _, data := range list {
		switch dappX := data.(type) {
		case *model.DappCategory:
			category := ToDappCategory(dappX)
			if category == nil {
				continue
			}
			result.List = append(result.List, &v1.DappIndex{
				Category: category,
			})
		case *model.DappTopic:
			topic := ToDappTopic(dappX)
			if topic == nil {
				continue
			}
			result.List = append(result.List, &v1.DappIndex{
				Topic: topic,
			})
		}
	}

	return &result, nil
}
