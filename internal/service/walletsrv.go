package service

import (
	pb "byd_wallet/api/wallet/v1"
	v1 "byd_wallet/api/wallet/v1"
	"byd_wallet/common/dto"
	"byd_wallet/internal/biz"
	"context"

	"google.golang.org/protobuf/types/known/emptypb"
)

type WalletSrvService struct {
	pb.UnimplementedWalletSrvServer
	uc   *biz.WalletUsecase
	taUc *biz.TokenAssetUsecase
	bnUc *biz.BlockchainNetworkUsecase
	uht  *biz.UserHoldTokenUsecase
}

func NewWalletSrvService(uc *biz.WalletUsecase,
	taUc *biz.TokenAssetUsecase,
	bnUc *biz.BlockchainNetworkUsecase,
	uht *biz.UserHoldTokenUsecase) *WalletSrvService {
	return &WalletSrvService{
		uc:   uc,
		taUc: taUc,
		bnUc: bnUc,
		uht:  uht,
	}
}

func (s *WalletSrvService) QueryTxLatestToken(ctx context.Context, req *v1.QueryTxLatestTokenReq) (*v1.QueryTxLatestTokenReply, error) {
	list, err := s.uht.ListUserHoldTokenView(ctx, ToBizUserHoldTokenFilter(req))
	if err != nil {
		return nil, err
	}
	return &v1.QueryTxLatestTokenReply{
		List: dto.ToList(list, ToPbTokenWithWallet),
	}, nil
}

func (s *WalletSrvService) ListTokenBalance(ctx context.Context, req *v1.ListTokenBalanceReq) (*v1.ListTokenBalanceReply, error) {
	list := make([]*v1.WalletBalance, 0, len(req.Addresses))

	for _, v := range req.GetAddresses() {
		tbs, err := s.taUc.ListTokenBalanceByAddress(ctx, v.GetChainIndexes(), v.GetAddress())
		if err != nil {
			return nil, err
		}
		list = append(list, &v1.WalletBalance{
			Address:  v.GetAddress(),
			Balances: tbs,
		})
	}

	return &v1.ListTokenBalanceReply{
		List: list,
	}, nil
}

func (s *WalletSrvService) GetToken(ctx context.Context, req *pb.GetTokenReq) (*pb.Token, error) {
	ta, err := s.taUc.FindTokenAssetByChainIndexAndAddress(ctx, req.ChainIndex, req.Address)
	if err != nil {
		return nil, err
	}
	return ToPbToken(ta), nil
}

func (s *WalletSrvService) NetworkList(ctx context.Context, req *pb.NetworkListReq) (*pb.NetworkListReply, error) {
	list, err := s.bnUc.AllBlockchainNetwork(ctx)
	if err != nil {
		return nil, err
	}
	var networks []*pb.Network
	for _, v := range list {
		var network pb.Network
		network.Name = v.Name
		network.ChainIndex = v.ChainIndex
		network.Symbol = v.Symbol
		network.Decimals = v.Decimals
		network.BlockchainUrl = v.BlockchainURL
		network.TokenUrl = v.TokenURL
		network.ExplorerUrl = v.ExplorerURL
		network.Handle = v.Handle
		network.ChainType = v.ChainType
		network.GasTokenSymbol = v.GasTokenSymbol
		network.ChainId = v.ChainID
		network.SortOrder = v.SortOrder
		network.ChainName = v.ChainName
		networks = append(networks, &network)
	}

	return &pb.NetworkListReply{
		List: networks,
	}, nil
}

func (s *WalletSrvService) Transactions(ctx context.Context, req *pb.TransactionsReq) (*pb.TransactionsReply, error) {
	list, total, err := s.uc.TransactionList(req)
	if err != nil {
		return nil, err
	}
	var transactions []*pb.Transaction
	for _, v := range list {
		var transaction pb.Transaction
		transaction.Txn = v.TxHash
		transaction.ChainIndex = v.ChainIndex
		transaction.FromAddress = v.FromAddress
		transaction.ToAddress = v.ToAddress
		transaction.Value = v.Value
		transaction.ProgramId = v.ProgramID
		transaction.Fee = v.Fee
		transaction.Timestamp = v.Timestamp
		transaction.Method = v.Method
		transaction.Status = v.Status
		transaction.BlockNumber = v.BlockNumber
		transactions = append(transactions, &transaction)
	}
	return &pb.TransactionsReply{
		List:  transactions,
		Count: total,
	}, nil
}

func (s *WalletSrvService) TokenList(ctx context.Context, req *pb.TokenListReq) (*pb.TokenListReply, error) {
	list, totalCount, err := s.taUc.ListTokenAssetView(ctx, &biz.TokenAssetViewFilter{
		Page:         req.Page,
		PageSize:     req.Limit,
		ChainIndexes: dto.ParseChainIndexes(req.ChainIndexes),
		Search:       req.Query,
	})
	if err != nil {
		return nil, err
	}
	return &pb.TokenListReply{
		List:  dto.ToList(list, ToPbToken),
		Count: totalCount,
	}, nil
}

func (s *WalletSrvService) TransactionInfo(ctx context.Context, req *pb.TransactionReq) (*pb.Transaction, error) {
	info, err := s.uc.TransactionInfo(req)
	if err != nil {
		return nil, err
	}
	return &pb.Transaction{
		Txn:         info.TxHash,
		FromAddress: info.FromAddress,
		ToAddress:   info.ToAddress,
		Value:       info.Value,
		Fee:         info.Fee,
		Method:      info.Method,
		ProgramId:   info.ProgramID,
		Timestamp:   info.Timestamp,
		Status:      info.Status,
		BlockNumber: info.BlockNumber,
		ChainIndex:  info.ChainIndex,
	}, nil
}

func (s *WalletSrvService) TransactionsByAddress(ctx context.Context, req *pb.TransactionsByAddressReq) (*pb.TransactionsReply, error) {
	list, total, err := s.uc.TransactionsByAddress(req)
	if err != nil {
		return nil, err
	}
	var transactions []*pb.Transaction
	for _, v := range list {
		var transaction pb.Transaction
		transaction.Txn = v.TxHash
		transaction.ChainIndex = v.ChainIndex
		transaction.FromAddress = v.FromAddress
		transaction.ToAddress = v.ToAddress
		transaction.Value = v.Value
		transaction.ProgramId = v.ProgramID
		transaction.Fee = v.Fee
		transaction.Timestamp = v.Timestamp
		transaction.Method = v.Method
		transaction.Status = v.Status
		transaction.BlockNumber = v.BlockNumber
		transactions = append(transactions, &transaction)
	}
	return &pb.TransactionsReply{
		List:  transactions,
		Count: total,
	}, nil
}

func (s *WalletSrvService) ReportInternalTxn(ctx context.Context, req *pb.ReportInternalTxnReq) (*emptypb.Empty, error) {
	err := s.uc.ReportInternalTxn(req)
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}
