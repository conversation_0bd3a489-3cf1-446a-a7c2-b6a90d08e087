package service

import (
	v1 "byd_wallet/api/wallet/v1"
	"byd_wallet/internal/biz"
	"byd_wallet/model"
)

func ToPbGasPoolDepositToken(v *model.GasPoolDepositTokenView) *v1.GasPoolDepositToken {
	return &v1.GasPoolDepositToken{
		Name:             v.Name,
		Symbol:           v.Symbol,
		Decimals:         v.Decimals,
		LogoUrl:          v.LogoUrl,
		Address:          v.Address,
		ChainIndex:       v.ChainIndex,
		ChainId:          v.ChainId,
		MinDepositAmount: v.MinDepositAmount.String(),
		DepositAddress:   v.DepositAddress,
	}
}

func ToBizUserHoldTokenFilter(req *v1.QueryTxLatestTokenReq) *biz.UserHoldTokenViewFilter {
	was := make([]struct {
		ChainIndex int64
		Address    string
	}, 0, len(req.GetChains()))
	for _, v := range req.GetChains() {
		for _, vv := range v.GetAddresses() {
			was = append(was, struct {
				ChainIndex int64
				Address    string
			}{
				ChainIndex: v.GetChainIndex(),
				Address:    vv,
			})
		}
	}
	return &biz.UserHoldTokenViewFilter{
		UserAddrs: was,
	}
}

func ToPbTokenWithWallet(v *biz.UserHoldTokenView) *v1.TokenWithWallet {
	return &v1.TokenWithWallet{
		WalletAddress: v.WalletAddress,
		ChainIndex:    v.ChainIndex,
		Name:          v.Name,
		Symbol:        v.Symbol,
		Decimals:      v.Decimals,
		LogoUrl:       v.LogoUrl,
		Address:       v.Address,
		ChainId:       v.ChainId,
	}
}

func ToPbTokenWithMarket(v *biz.TokenWithMarket) *v1.TokenWithMarket {
	return &v1.TokenWithMarket{
		ChainIndex:                v.ChainIndex,
		Address:                   v.Address,
		Name:                      v.Name,
		Symbol:                    v.Symbol,
		Decimals:                  v.Decimals,
		LogoUrl:                   v.LogoUrl,
		CirculatingSupply:         v.CirculatingSupply.String(),
		TradingVolume_24H:         v.TradingVolume24H.String(),
		PriceChangePercentage_24H: v.PriceChangePercentage24H.String(),
		Price:                     v.Price.String(),
	}
}

func ToBizTokenID(v *v1.TokenID) *biz.TokenID {
	return &biz.TokenID{
		ChainIndex: v.GetChainIndex(),
		Address:    v.GetAddress(),
	}
}

func ToPbCoinPrice(v *biz.CoinPrice) *v1.CoinPrice {
	return &v1.CoinPrice{
		ChainIndex:    v.ChainIndex,
		Address:       v.Address,
		Price:         v.Price.String(),
		LastUpdatedAt: v.LastUpdatedAt,
	}
}

func ToPbSimpleKline(v *biz.SimpleKline) *v1.SimpleKline {
	return &v1.SimpleKline{
		ChainIndex:  v.ChainIndex,
		Address:     v.Address,
		Times:       v.Times,
		ClosePrices: v.ClosePrices,
	}
}

func ToPbToken(ta *model.TokenAsset) *v1.Token {
	if ta == nil {
		return nil
	}
	return &v1.Token{
		ChainIndex: ta.ChainIndex,
		Name:       ta.Name,
		Symbol:     ta.Symbol,
		Address:    ta.Address,
		Decimals:   ta.Decimals,
		LogoUrl:    ta.LogoUrl,
		ChainId:    ta.ChainId,
	}
}

func ToPbTokens(froms []*model.TokenAsset) []*v1.Token {
	out := make([]*v1.Token, len(froms))
	for i, from := range froms {
		out[i] = ToPbToken(from)
	}
	return out
}

func ToPbNetwork(from *model.BlockchainNetwork) *v1.Network {
	return &v1.Network{
		Name:           from.Name,
		Symbol:         from.Symbol,
		ChainIndex:     from.ChainIndex,
		ChainId:        from.ChainID,
		Decimals:       from.Decimals,
		BlockchainUrl:  from.BlockchainURL,
		TokenUrl:       from.TokenURL,
		ExplorerUrl:    from.ExplorerURL,
		GasTokenSymbol: from.GasTokenSymbol,
		ChainType:      from.ChainType,
		Handle:         from.Handle,
		SortOrder:      from.SortOrder,
		ChainName:      from.ChainName,
	}
}

func ToPbNetworks(froms []*model.BlockchainNetwork) []*v1.Network {
	out := make([]*v1.Network, len(froms))
	for i, from := range froms {
		out[i] = ToPbNetwork(from)
	}
	return out
}
