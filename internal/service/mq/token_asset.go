package mq

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"
	"fmt"
)

type TokenAssetService struct {
	uc   *biz.TokenAssetUsecase
	tcUc *biz.TokenContractUsecase
}

func NewTokenAssetService(uc *biz.TokenAssetUsecase,
	tcUc *biz.TokenContractUsecase) *TokenAssetService {
	return &TokenAssetService{
		uc:   uc,
		tcUc: tcUc,
	}
}

func (s *TokenAssetService) CreateTokenAssetByEvent(ctx context.Context, e *biz.TxSyncEventTokenDeploy) error {
	ok, err := s.uc.ExistsTokenAssetByChainIndexAndAddress(ctx, e.ChainIndex, e.Address)
	if err != nil {
		return fmt.Errorf("ExistsTokenAssetByChainIndexAndAddress: %w", err)
	}
	if ok {
		return nil
	}

	tc, err := s.tcUc.GetTokenContract(ctx, e.ChainIndex, e.Address)
	if err != nil {
		return fmt.Errorf("get token contract error: %v", err)
	}

	if tc.Name == "" || tc.Symbol == "" {
		return nil
	}

	ta := &model.TokenAsset{
		Address:         e.Address,
		ChainIndex:      e.ChainIndex,
		ChainId:         e.ChainID,
		TokenDeployedAt: e.DeployAt,
		TokenType:       constant.GetTokenType(e.ChainIndex),
		IsDisplay:       true,
		// --- optional
		LogoUrl: "",
		CoinID:  "",
		// --- from API
		Symbol:      model.ConvValidSymbol(tc.Symbol),
		Name:        tc.Name,
		TotalSupply: tc.TotalSupply, // optional
		Decimals:    tc.Decimals,
	}
	_, err = s.uc.CreateTokenAsset(ctx, ta)
	if err != nil {
		err = fmt.Errorf("create token asset error: %v", err)
	}
	return err
}
