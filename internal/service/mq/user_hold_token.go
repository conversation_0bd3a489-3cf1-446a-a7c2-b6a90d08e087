package mq

import (
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"
	"errors"
	"fmt"

	"gorm.io/gorm"
)

type UserHoldTokenService struct {
	uc   *biz.TokenAssetUsecase
	uaUc *biz.UserAddressUsecase
	uht  *biz.UserHoldTokenUsecase
}

func NewUserHoldTokenService(uc *biz.TokenAssetUsecase,
	uaUc *biz.UserAddressUsecase,
	uht *biz.UserHoldTokenUsecase) *UserHoldTokenService {
	return &UserHoldTokenService{
		uc:   uc,
		uaUc: uaUc,
		uht:  uht,
	}
}

func (s *UserHoldTokenService) CreateHoldNewTokenAssetByEvent(ctx context.Context, e *biz.TxSyncEventHoldNewToken) error {
	uaCache := map[string]*model.UserAddress{}
	taCache := map[string]*model.TokenAsset{}

	tokens := make([]*model.UserHoldToken, 0, len(e.List))

	for _, t := range e.List {
		key := fmt.Sprintf("%d%s", t.ChainIndex, t.<PERSON>)
		ua, ok := uaCache[key]
		if !ok {
			tmpUA, err := s.uaUc.FindUserAddressByChainIndexAndAddress(ctx, t.ChainIndex, t.WalletAddress)
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("get user address: %w: %d: %s", err, t.ChainIndex, t.WalletAddress)
			}
			ua = tmpUA
			uaCache[key] = ua
		}
		if ua == nil {
			// skip not exist user address
			continue
		}

		key = fmt.Sprintf("%d%s", t.ChainIndex, t.TokenAddress)
		ta, ok := taCache[key]
		if !ok {
			tmpTA, err := s.uc.FindTokenAssetByChainIndexAndAddress(ctx, t.ChainIndex, t.TokenAddress)
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("get token: %w: %d: %s", err, t.ChainIndex, t.TokenAddress)
			}
			ta = tmpTA
			taCache[key] = ta
		}
		if ta == nil {
			// skip not exist token
			continue
		}

		tokens = append(tokens, &model.UserHoldToken{
			UserAddressID: ua.ID,
			TokenAssetID:  ta.ID,
		})
	}

	return s.uht.BatchCreateUserHoldToken(ctx, tokens)
}
