package mq

import (
	"byd_wallet/internal/biz"
	"context"
)

type UserRegisterService struct {
	approvalUc *biz.ApprovalUsecase
}

func NewUserRegisterService(approvalUc *biz.ApprovalUsecase) *UserRegisterService {
	return &UserRegisterService{approvalUc: approvalUc}
}

func (u *UserRegisterService) OnUserRegisterEvent(ctx context.Context, event *biz.UserRegisterEvent) error {
	return u.approvalUc.InitApprovalsByUserAddresses(ctx, event.Addresses)
}
