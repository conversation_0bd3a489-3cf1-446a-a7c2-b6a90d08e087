package service

import (
	v1 "byd_wallet/api/wallet/v1"
	"byd_wallet/internal/biz"
	"byd_wallet/internal/biz/gaspool"
	"byd_wallet/model"
	"context"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/shopspring/decimal"
)

type UserService struct {
	v1.UnimplementedUserSrvServer
	log *log.Helper

	uc   *biz.UserUsecase
	gpUc *gaspool.Usecase
}

func NewUserService(logger log.Logger,
	uc *biz.UserUsecase,
	gpUc *gaspool.Usecase) *UserService {
	return &UserService{
		log: log.NewHelper(logger),

		uc:   uc,
		gpUc: gpUc,
	}
}

func (s *UserService) UserRegister(ctx context.Context, req *v1.UserRegisterReq) (*v1.UserRegisterReply, error) {
	walletID, userID, err := s.uc.Register(ctx, biz.UserRegister{
		BaseSignPayload: biz.BaseSignPayload{
			ChainIndex: req.Base.ChainIndex,
			Sign:       req.Base.Sign,
			Message:    req.Base.Message,
			Address:    req.Base.Address,
		},
	})
	if err != nil {
		return nil, err
	}
	_, terr := s.gpUc.CreateGasPool(ctx, &model.GasPool{
		UserID:  userID,
		Balance: decimal.Zero,
	})
	if terr != nil {
		s.log.Errorf("failed to create gas pool: %v: walletID=%s", terr, walletID)
	}
	return &v1.UserRegisterReply{
		WalletId: walletID,
	}, nil
}

func (s *UserService) UpdateWalletID(ctx context.Context, req *v1.UpdateWalletIDReq) (*v1.UpdateWalletIDReply, error) {
	if req.Base == nil {
		return nil, v1.ErrorValidator("base is nil")
	}
	walletId, err := s.uc.UpDateWalletId(biz.UpDateWalletId{
		BaseSignPayload: biz.BaseSignPayload{
			ChainIndex: req.Base.ChainIndex,
			Sign:       req.Base.Sign,
			Message:    req.Base.Message,
			Address:    req.Base.Address,
		},
	})
	if err != nil {
		return nil, err
	}
	return &v1.UpdateWalletIDReply{
		WalletId: walletId,
	}, nil
}

func (s *UserService) GetWalletIds(ctx context.Context, req *v1.GetWalletIdReq) (*v1.GeWalletIdsReply, error) {
	return s.uc.GetWalletIds(req.Addresses)
}
