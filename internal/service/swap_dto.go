package service

import (
	v1 "byd_wallet/api/wallet/v1"
	"byd_wallet/internal/biz"
	"byd_wallet/internal/biz/base"
	"byd_wallet/model"
	"fmt"
	"github.com/shopspring/decimal"
)

func FromPagination(page, pageSize int64) base.Pagination {
	return base.Pagination{
		Page:     int(page),
		PageSize: int(pageSize),
	}
}

func ToSwappableHotToken(from *model.SwappableHotToken) *v1.SwappableHotToken {
	ta := from.SwappableToken.TokenAsset
	return &v1.SwappableHotToken{
		ChainIndex: ta.ChainIndex,
		Name:       ta.Name,
		Symbol:     ta.Symbol,
		Decimals:   ta.Decimals,
		LogoUrl:    ta.LogoUrl,
		Address:    from.SwappableToken.Address,
		ChainId:    ta.ChainId,
		SortOrder:  int64(from.SortOrder),
		IsAll:      from.IsAll,
	}
}

func ToSwappableHotTokens(froms []*model.SwappableHotToken) []*v1.SwappableHotToken {
	var out []*v1.SwappableHotToken
	for _, from := range froms {
		if from.SwappableToken.TokenAsset.Symbol == "" {
			continue
		}
		out = append(out, ToSwappableHotToken(from))
	}
	return out
}

func FromSwapToken(from *v1.SwapToken) *biz.SwapToken {
	out := &biz.SwapToken{
		ChainIndex:   from.ChainIndex,
		TokenAddress: from.TokenAddress,
		Address:      from.Address,
	}
	if from.Amount != "" {
		out.Amount = decimal.RequireFromString(from.Amount)
	}
	return out
}

func ToSwapRecords(froms []*model.SwapRecord) []*v1.SwapRecord {
	out := make([]*v1.SwapRecord, len(froms))
	for i, from := range froms {
		out[i] = ToSwapRecord(from)
	}
	return out
}

func ToSwapRecord(from *model.SwapRecord) *v1.SwapRecord {
	details := make([]*v1.SwapDetail, len(from.Details))
	for i, detail := range from.Details {
		details[i] = &v1.SwapDetail{
			ChainIndex:  detail.ChainIndex,
			ExplorerUrl: detail.ExplorerURL,
			Hash:        detail.Hash,
			Status:      detail.Status,
		}
	}
	var finishedAt int64
	if from.FinishedAt != nil {
		finishedAt = from.FinishedAt.Unix()
	}
	return &v1.SwapRecord{
		Id:        uint64(from.ID),
		SwappedAt: from.SwappedAt.Unix(),
		Status:    from.Status.AppStatus(),
		From: &v1.SwapToken{
			TokenAddress: from.FromTokenAsset.Address,
			Address:      from.FromAddress,
			ChainIndex:   from.FromTokenAsset.ChainIndex,
			Amount:       from.FromTokenAmount,
			TokenLogo:    from.FromTokenAsset.LogoUrl,
			Symbol:       from.FromTokenAsset.Symbol,
			Decimals:     fmt.Sprintf("%d", from.FromTokenAsset.Decimals),
		},
		To: &v1.SwapToken{
			TokenAddress: from.ToTokenAsset.Address,
			Address:      from.ToAddress,
			ChainIndex:   from.ToTokenAsset.ChainIndex,
			Amount:       from.ToTokenAmount,
			TokenLogo:    from.ToTokenAsset.LogoUrl,
			Symbol:       from.ToTokenAsset.Symbol,
			Decimals:     fmt.Sprintf("%d", from.ToTokenAsset.Decimals),
		},
		GasFee:        from.GasFee,
		FeeRate:       from.FeeRate,
		Hash:          from.Hash,
		ApprovalHash:  from.ApprovalHash,
		Height:        from.BlockNumber,
		Dex:           from.Dex,
		DexLogo:       from.DexLogo,
		SwapPrice:     from.SwapPrice,
		Details:       details,
		FinishedAt:    finishedAt,
		EstimatedTime: from.EstimatedTime,
	}
}
