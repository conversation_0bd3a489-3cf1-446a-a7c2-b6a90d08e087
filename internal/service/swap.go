package service

import (
	v1 "byd_wallet/api/wallet/v1"
	"byd_wallet/internal/biz"
	"context"
	"github.com/shopspring/decimal"
	"strconv"
)

var _ v1.SwapServiceServer = (*SwapService)(nil)

func NewSwapService(uc *biz.SwapUsecase) *SwapService {
	return &SwapService{uc: uc}
}

type SwapService struct {
	v1.UnimplementedSwapServiceServer
	uc *biz.SwapUsecase
}

func (s *SwapService) ListHotToken(ctx context.Context, _ *v1.ListHotTokenRequest) (*v1.ListHotTokenReply, error) {
	list, err := s.uc.ListHotTokens(ctx)
	if err != nil {
		return nil, err
	}
	return &v1.ListHotTokenReply{
		List: ToSwappableHotTokens(list),
	}, nil
}

func (s *SwapService) GetSwapRecord(ctx context.Context, request *v1.GetSwapRecordRequest) (*v1.SwapRecord, error) {
	record, err := s.uc.GetSwapRecordByHash(ctx, request.Hash)
	if err != nil {
		return nil, err
	}
	if record == nil {
		return nil, nil
	}
	return ToSwapRecord(record), nil
}

func (s *SwapService) ListSwapRecord(ctx context.Context, request *v1.ListSwapRecordRequest) (*v1.ListSwapRecordReply, error) {
	list, count, err := s.uc.PagedSwapRecordsByAddresses(ctx, request.Addresses, FromPagination(request.Page, request.Limit))
	if err != nil {
		return nil, err
	}
	return &v1.ListSwapRecordReply{
		List:  ToSwapRecords(list),
		Count: count,
	}, nil
}

func (s *SwapService) ListBlockchainNetwork(ctx context.Context, _ *v1.ListBlockchainNetworkRequest) (*v1.ListBlockchainNetworkReply, error) {
	list, err := s.uc.ListBlockchainNetwork(ctx)
	if err != nil {
		return nil, err
	}
	return &v1.ListBlockchainNetworkReply{
		List: ToPbNetworks(list),
	}, nil
}

func (s *SwapService) ListToken(ctx context.Context, request *v1.ListTokenRequest) (*v1.ListTokenReply, error) {
	list, err := s.uc.FilterToken(ctx, biz.SwapTokenFilter{
		ChainIndex: request.ChainIndex,
		SearchKey:  request.SearchKey,
	})
	if err != nil {
		return nil, err
	}
	return &v1.ListTokenReply{
		List: ToPbTokens(list),
	}, nil
}

func (s *SwapService) MultiQuote(ctx context.Context, request *v1.MultiQuoteRequest) (*v1.MultiQuoteReply, error) {
	output, err := s.uc.MultiQuote(ctx, &biz.MultiQuoteInput{
		From:     FromSwapToken(request.From),
		To:       FromSwapToken(request.To),
		Slippage: decimal.RequireFromString(request.Slippage),
	})
	if err != nil {
		return nil, err
	}
	quotes := make([]*v1.QuoteInfo, len(output.Quotes))
	for i, quote := range output.Quotes {
		quotes[i] = &v1.QuoteInfo{
			ReceiveTokenAmount:    quote.ReceiveTokenAmount.String(),
			MinReceiveTokenAmount: quote.MinReceiveTokenAmount.String(),
			Slippage:              request.Slippage,
			Dex:                   quote.Dex,
			DexLogo:               quote.DexLogo,
			FeeRate:               quote.FeeRate.String(),
			EstimatedTime:         strconv.Itoa(quote.EstimatedTime),
			Path:                  quote.Path,
			MaxFromTokenAmount:    quote.MaxFromTokenAmount.String(),
			MinFromTokenAmount:    quote.MinFromTokenAmount.String(),
		}
	}
	return &v1.MultiQuoteReply{
		List: quotes,
	}, nil
}

func (s *SwapService) Swap(ctx context.Context, request *v1.SwapRequest) (*v1.SwapReply, error) {
	output, err := s.uc.Swap(ctx, &biz.SwapInput{
		From:       FromSwapToken(request.From),
		To:         FromSwapToken(request.To),
		Slippage:   decimal.RequireFromString(request.Slippage),
		RouterPath: request.Path,
		Dex:        request.Dex,
	})
	if err != nil {
		return nil, err
	}
	return &v1.SwapReply{
		From:           output.From,
		To:             output.To,
		Value:          output.Value,
		GasLimit:       output.GasLimit,
		GasPrice:       output.GasPrice,
		Data:           output.Data,
		ApproveAddress: output.ApproveAddress,
		ToType:         output.ToType,
		PlatformAddr:   output.PlatformAddr,
		SwapPrice:      output.SwapPrice,
		OrderId:        output.OrderId,
		//RawData:        output.RawData,
	}, nil
}

func (s *SwapService) AddSwapRecord(ctx context.Context, request *v1.AddSwapRecordRequest) (*v1.SwapRecord, error) {
	record, err := s.uc.AddSwapRecord(ctx, &biz.AddSwapRecordInput{
		From:          FromSwapToken(request.From),
		To:            FromSwapToken(request.To),
		Slippage:      decimal.RequireFromString(request.Slippage),
		RouterPath:    request.Path,
		Dex:           request.Dex,
		Hash:          request.Hash,
		ApprovalHash:  request.ApprovalHash,
		SwapPrice:     request.SwapPrice,
		FeeRate:       decimal.RequireFromString(request.FeeRate),
		EstimatedTime: request.EstimatedTime,
		GasFee:        request.GasFee,
		OrderId:       request.OrderId,
	})
	if err != nil {
		return nil, err
	}
	return ToSwapRecord(record), nil
}
