package service

import (
	v1 "byd_wallet/api/wallet/v1"
	"byd_wallet/internal/biz/rent"
	"byd_wallet/model"
	"context"
	"strconv"
)

type TronService struct {
	v1.UnsafeTronSrvServer
	uc *rent.TronRentUseCase
}

func (t TronService) QueryPreorderInfo(ctx context.Context, req *v1.QueryPreorderInfoReq) (*v1.QueryPreorderInfoReply, error) {
	record := &model.TronRentRecord{
		FromAddress:   req.FromAddress,
		PledgeAddress: req.<PERSON>ledge<PERSON>ddress,
		PledgeNum:     int(req.PledgeNum),
	}
	if err := t.uc.GetQueryPreorderInfo(ctx, record); err != nil {
		return nil, v1.ErrorRent(err.Error())
	}
	return &v1.QueryPreorderInfoReply{
		OrderPrice:   record.OrderPrice.String(),
		PledgeNum:    strconv.Itoa(record.PledgeNum),
		PledgeTrxNum: record.PledgeTrxNum,
	}, nil
}

func NewTronService(uc *rent.TronRentUseCase) *TronService {
	return &TronService{uc: uc}
}

func (t TronService) AddTronRentRecord(ctx context.Context, req *v1.AddTronRentRecordReq) (*v1.AddTronRentRecordReply, error) {
	record := &model.TronRentRecord{
		FromAddress:   req.FromAddress,
		PledgeAddress: req.PledgeAddress,
		PledgeNum:     int(req.PledgeNum),
	}
	if err := t.uc.PrepareRent(ctx, record); err != nil {
		return nil, v1.ErrorRent(err.Error())
	}
	return &v1.AddTronRentRecordReply{
		OrderId:      record.OrderId,
		Transaction:  string(record.Transaction),
		PledgeTrxNum: record.PledgeTrxNum,
	}, nil
}

func (t TronService) UploadHash(ctx context.Context, req *v1.UploadHashReq) (*v1.UploadHashReply, error) {
	if err := t.uc.UploadHash(ctx, &rent.UploadHashReq{
		OrderId:    req.OrderId,
		FromHash:   req.FromHash,
		SignedData: req.SignedData,
	}); err != nil {
		return nil, v1.ErrorRent(err.Error())
	}
	return &v1.UploadHashReply{}, nil
}
