package rango

import (
	"fmt"
	"testing"
)

func TestCheckStatus(t *testing.T) {
	requestId := "5d98aac8-7d65-4f5b-b94f-05bc4dd78987"
	//requestId := "9f7017fe-dc61-4519-8417-37e1af3062ed" //fail

	txId := "0xa61415ed41a23eb56531e3e422137e5cabadeb0a7def6ae9cd25455df73c8a45" //success
	//txId := "0x91a49759bcbcfdfb470d0332231f176b45c115c369e305c5c039e973d45ea276" //fail
	step := int64(1)

	result, err := CheckStatus(requestId, txId, step)
	if err != nil {
		t.Fatalf("CheckStatus failed: %v", err)
	}
	fmt.Println("CheckStatus = ", result)
}

func TestCreateTx(t *testing.T) {
	requestId := "ca93f0a2-667e-4364-8116-7666185544f6"
	userSettings := UserSettings{
		Slippage:        3,
		InfiniteApprove: false,
	}
	validations := Validations{
		Balance: true,
		Fee:     true,
		Approve: true,
	}
	step := int64(1)

	result, err := CreateTx(requestId, userSettings, validations, step)
	if err != nil {
		t.Fatalf("CreateTx failed: %v", err)
	}
	fmt.Println("CreateTx = ", result)
	if !result.Ok {
		t.Error("result.Ok is false")
	}
	if result.Transaction == nil {
		t.Error("Tx should not be nil")
	}
	t.Log("CreateTx = ", result.Transaction)
}

func TestGetExchangeMeta(t *testing.T) {
	info, err := GetExchangeMeta()
	if err != nil {
		t.Fatalf("GetExchangeMeta failed: %v", err)
	}
	fmt.Printf("Blockchains = %+v\n", info.Blockchains[0])
	fmt.Printf("Tokens = %+v\n", info.Tokens[0])
	fmt.Printf("PopularTokens = %+v\n", info.PopularTokens[0])
	fmt.Printf("Swappers = %+v\n", info.Swappers[0])
}
