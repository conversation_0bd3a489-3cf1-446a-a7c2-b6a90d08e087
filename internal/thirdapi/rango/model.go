package rango

type FromTo struct {
	Blockchain string `json:"blockchain"`
	Symbol     string `json:"symbol"`
	Address    string `json:"address"`
}

type RespBestRoute struct {
	From                              FromTo   `json:"from"`
	To                                FromTo   `json:"to"`
	RequestAmount                     string   `json:"requestAmount"`
	RequestId                         string   `json:"requestId"`
	Result                            Result   `json:"result"`
	ValidationStatus                  string   `json:"validationStatus"`
	WalletNotSupportingFromBlockchain bool     `json:"walletNotSupportingFromBlockchain"`
	MissingBlockchains                []string `json:"missingBlockchains"`
	DiagnosisMessages                 []string `json:"diagnosisMessages"`
	Error                             string   `json:"error"`
	ErrorCode                         string   `json:"errorCode"`
	TraceId                           string   `json:"traceId"`
}

type Result struct {
	RequestId                         string   `json:"requestId"`
	OutputAmount                      string   `json:"outputAmount"`
	Swaps                             []Swap   `json:"swaps"`
	ResultType                        string   `json:"resultType"`
	Scores                            []Score  `json:"scores"`
	Tags                              []Tag    `json:"tags"`
	WalletNotSupportingFromBlockchain bool     `json:"walletNotSupportingFromBlockchain"`
	MissingBlockchains                []string `json:"missingBlockchains"`
	PriceImpactUsd                    string   `json:"priceImpactUsd"`
	PriceImpactUsdPercent             string   `json:"priceImpactUsdPercent"`
}

type Swap struct {
	SwapperId                 string              `json:"swapperId"`
	SwapperLogo               string              `json:"swapperLogo"`
	SwapperType               string              `json:"swapperType"`
	From                      TokenInfo           `json:"from"`
	To                        TokenInfo           `json:"to"`
	FromAmount                string              `json:"fromAmount"`
	FromAmountPrecision       string              `json:"fromAmountPrecision"`
	FromAmountMinValue        *string             `json:"fromAmountMinValue"`
	FromAmountMaxValue        *string             `json:"fromAmountMaxValue"`
	FromAmountRestrictionType *string             `json:"fromAmountRestrictionType"`
	ToAmount                  string              `json:"toAmount"`
	Fee                       []Fee               `json:"fee"`
	EstimatedTimeInSeconds    int64               `json:"estimatedTimeInSeconds"`
	SwapChainType             string              `json:"swapChainType"`
	Routes                    []Route             `json:"routes"`
	RecommendedSlippage       RecommendedSlippage `json:"recommendedSlippage"`
	Warnings                  []string            `json:"warnings"`
	TimeStat                  TimeStat            `json:"timeStat"`
	IncludesDestinationTx     bool                `json:"includesDestinationTx"`
	InternalSwaps             []Internal          `json:"internalSwaps"`
	MaxRequiredSign           int64               `json:"maxRequiredSign"`
}

type Score struct {
	PreferenceType string `json:"preferenceType"`
	Score          int    `json:"score"`
}

type Tag struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

type TokenInfo struct {
	Symbol         string  `json:"symbol"`
	Logo           string  `json:"logo"`
	BlockchainLogo string  `json:"blockchainLogo"`
	Address        string  `json:"address"`
	Blockchain     string  `json:"blockchain"`
	Decimals       int64   `json:"decimals"`
	UsdPrice       float64 `json:"usdPrice"`
}

type Asset struct {
	Blockchain string `json:"blockchain"`
	Symbol     string `json:"symbol"`
	Address    string `json:"address"`
}
type Meta struct {
	Type     string `json:"type"`
	GasLimit string `json:"gasLimit"`
	GasPrice string `json:"gasPrice"`
}

type Fee struct {
	Asset       Asset   `json:"asset"`
	ExpenseType string  `json:"expenseType"`
	Amount      string  `json:"amount"`
	Name        string  `json:"name"`
	Meta        Meta    `json:"meta,omitempty"`
	Price       float64 `json:"price"`
}
type RecommendedSlippage struct {
	Error    bool   `json:"error"`
	Slippage string `json:"slippage"`
}
type TimeStat struct {
	Min int64 `json:"min"`
	Avg int64 `json:"avg"`
	Max int64 `json:"max"`
}

type Route struct {
	Nodes []Nodes `json:"nodes"`
}

type Nodes struct {
	Nodes          []Node `json:"nodes"`
	From           string `json:"from"`
	FromLogo       string `json:"fromLogo"`
	FromAddress    string `json:"fromAddress"`
	FromBlockchain string `json:"fromBlockchain"`
	To             string `json:"to"`
	ToLogo         string `json:"toLogo"`
	ToAddress      string `json:"toAddress"`
	ToBlockchain   string `json:"toBlockchain"`
}

type Node struct {
	InputAmount  string   `json:"inputAmount"`
	MarketId     string   `json:"marketId"`
	MarketName   string   `json:"marketName"`
	OutputAmount string   `json:"outputAmount"`
	Percent      float64  `json:"percent"`
	Pools        []string `json:"pools"`
}

type Internal struct {
	SwapperId   string    `json:"swapperId"`
	SwapperLogo string    `json:"swapperLogo"`
	SwapperType string    `json:"swapperType"`
	From        TokenInfo `json:"from"`

	To                        TokenInfo `json:"to"`
	FromAmount                string    `json:"fromAmount"`
	FromAmountPrecision       string    `json:"fromAmountPrecision"`
	FromAmountMinValue        *string   `json:"fromAmountMinValue"`
	FromAmountMaxValue        *string   `json:"fromAmountMaxValue"`
	FromAmountRestrictionType *string   `json:"fromAmountRestrictionType"`
	ToAmount                  string    `json:"toAmount"`
	Fee                       []Fee     `json:"fee"`
	EstimatedTimeInSeconds    int64     `json:"estimatedTimeInSeconds"`
	SwapChainType             string    `json:"swapChainType"`
	Routes                    []Route   `json:"routes"`
	RecommendedSlippage       string    `json:"recommendedSlippage"`
	Warnings                  string    `json:"warnings"`
	TimeStat                  string    `json:"timeStat"`
	IncludesDestinationTx     bool      `json:"includesDestinationTx"`
	InternalSwaps             string    `json:"internalSwaps"`
	MaxRequiredSign           int64     `json:"maxRequiredSign"`
}

type AllPossibleRoutes struct {
	From              FromTo   `json:"from"`
	To                FromTo   `json:"to"`
	RequestAmount     string   `json:"requestAmount"`
	RouteId           string   `json:"routeId"`
	Results           []Result `json:"results"`
	DiagnosisMessages []string `json:"diagnosisMessages"`
	Error             string   `json:"error"`
	ErrorCode         int64    `json:"errorCode"`
	TraceId           int64    `json:"traceId"`
}

type ConfirmRouteResponse struct {
	Ok        bool               `json:"ok"`
	Error     string             `json:"error"`
	ErrorCode int64              `json:"errorCode"`
	TraceId   int64              `json:"traceId"`
	Result    ConfirmRouteResult `json:"result"`
}

type ConfirmRouteResult struct {
	From struct {
		Blockchain string `json:"blockchain"`
		Symbol     string `json:"symbol"`
		Address    string `json:"address"`
	} `json:"from"`
	To struct {
		Blockchain string `json:"blockchain"`
		Symbol     string `json:"symbol"`
		Address    string `json:"address"`
	} `json:"to"`
	RequestAmount string `json:"requestAmount"`
	RequestId     string `json:"requestId"`
	Result        struct {
		OutputAmount string              `json:"outputAmount"`
		Swaps        []*ConfirmRouteSwap `json:"swaps"`
		ResultType   string              `json:"resultType"`
	} `json:"result"`
	ValidationStatus                  []ConfirmRouteValidation `json:"validationStatus"`
	WalletNotSupportingFromBlockchain bool                     `json:"walletNotSupportingFromBlockchain"`
	MissingBlockchains                []string                 `json:"missingBlockchains"`
	DiagnosisMessages                 []string                 `json:"diagnosisMessages"`
	SelectedWallets                   map[string]string        `json:"selectedWallets"`
}

type ConfirmRouteSwap struct {
	SwapperId                 string    `json:"swapperId"`
	SwapperLogo               string    `json:"swapperLogo"`
	SwapperType               string    `json:"swapperType"`
	From                      TokenInfo `json:"from"`
	To                        TokenInfo `json:"to"`
	FromAmount                string    `json:"fromAmount"`
	FromAmountPrecision       string    `json:"fromAmountPrecision"`
	FromAmountMinValue        string    `json:"fromAmountMinValue"`
	FromAmountMaxValue        string    `json:"fromAmountMaxValue"`
	FromAmountRestrictionType string    `json:"fromAmountRestrictionType"`
	ToAmount                  string    `json:"toAmount"`
	Fee                       []Fee     `json:"fee"`
	EstimatedTimeInSeconds    int64     `json:"estimatedTimeInSeconds"`
	SwapChainType             string    `json:"swapChainType"`
	Routes                    []Route   `json:"routes"`
	RecommendedSlippage       string    `json:"recommendedSlippage"`
	Warnings                  []string  `json:"warnings"`
	TimeStat                  string    `json:"timeStat"`
	IncludesDestinationTx     bool      `json:"includesDestinationTx"`
	InternalSwaps             string    `json:"internalSwaps"`
	MaxRequiredSign           int64     `json:"maxRequiredSign"`
}

type ConfirmRouteValidation struct {
	Blockchain string               `json:"blockchain"`
	Wallets    []ConfirmRouteWallet `json:"wallets"`
}

type ConfirmRouteWallet struct {
	Address        string                      `json:"address"`
	RequiredAssets []ConfirmRouteRequiredAsset `json:"requiredAssets"`
	AddressIsValid bool                        `json:"addressIsValid"`
	ValidResult    bool                        `json:"validResult"`
}

type ConfirmRouteRequiredAsset struct {
	Asset          ConfirmRouteAsset  `json:"asset"`
	RequiredAmount ConfirmRouteAmount `json:"requiredAmount"`
	CurrentAmount  ConfirmRouteAmount `json:"currentAmount"`
	Reason         string             `json:"reason"`
	Ok             bool               `json:"ok"`
}

type ConfirmRouteAsset struct {
	Blockchain string `json:"blockchain"`
	Symbol     string `json:"symbol"`
	Address    string `json:"address"`
}

type ConfirmRouteAmount struct {
	Amount   string `json:"amount"`
	Decimals int64  `json:"decimals"`
}

type ConfirmRouteRequest struct {
	SelectedWallets map[string]string `json:"selectedWallets"`
	Destination     string            `json:"destination"`
	RequestId       string            `json:"requestId"`
}

type RespCheckApproval struct {
	IsApproved             bool   `json:"isApproved"`
	TxStatus               string `json:"txStatus"`
	CurrentApprovedAmount  string `json:"currentApprovedAmount"`
	RequiredApprovedAmount string `json:"requiredApprovedAmount"`
}

type RespCreateTx struct {
	Ok          bool         `json:"ok"`
	Error       string       `json:"error,omitempty"`
	ErrorCode   int64        `json:"errorCode"`
	TraceId     int          `json:"traceId"`
	Transaction *Transaction `json:"chain"`
}

type Transaction struct {
	Type         string `json:"type"`
	BlockChain   string `json:"blockChain"`
	IsApprovalTx bool   `json:"isApprovalTx"`
	From         string `json:"from"`
	//Evm
	To                   string `json:"to"`
	Spender              string `json:"spender"`
	Data                 string `json:"data"`
	Value                string `json:"value"`
	GasLimit             string `json:"gasLimit"`
	GasPrice             string `json:"gasPrice"`
	MaxPriorityFeePerGas string `json:"maxPriorityFeePerGas"`
	MaxFeePerGas         string `json:"maxFeePerGas"`
	Nonce                string `json:"nonce"`

	//NoEvm
	Identifier        string   `json:"identifier"`
	Instructions      []string `json:"instructions"`
	RecentBlockhash   string   `json:"recentBlockhash"`
	Signatures        []string `json:"signatures"`
	SerializedMessage []int    `json:"serializedMessage"`
	TxType            string   `json:"txType"`
}

type UserSettings struct {
	Slippage        float64 `json:"slippage"`
	InfiniteApprove bool    `json:"infiniteApprove"`
}

type Validations struct {
	Balance bool `json:"balance"`
	Fee     bool `json:"fee"`
	Approve bool `json:"approve"`
}

type RespCheckStatus struct {
	Status       string         `json:"status"`
	ExtraMessage *string        `json:"extraMessage"`
	FailedType   *string        `json:"failedType"`
	Timestamp    int64          `json:"timestamp"`
	OutputAmount string         `json:"outputAmount"`
	ExplorerUrl  []ExplorerInfo `json:"explorerUrl"`
	Referrals    []Referral     `json:"referrals"`
	NewTx        *CheckStatusTx `json:"newTx"`
	DiagnosisUrl *string        `json:"diagnosisUrl"`
	Steps        interface{}    `json:"steps"` // 可按实际结构修改
	OutputToken  OutputToken    `json:"outputToken"`
	OutputType   string         `json:"outputType"`
	BridgeExtra  BridgeExtra    `json:"bridgeExtra"`
	Error        *string        `json:"error"`
	ErrorCode    *string        `json:"errorCode"`
	TraceId      *string        `json:"traceId"`
}

type ExplorerInfo struct {
	Url         string `json:"url"`
	Description string `json:"description"`
}

type Referral struct {
	Amount     string  `json:"amount"`
	BlockChain string  `json:"blockChain"`
	Symbol     string  `json:"symbol"`
	Address    *string `json:"address"`
	Decimals   int     `json:"decimals"`
	Type       string  `json:"type"`
}

type OutputToken struct {
	Blockchain        string   `json:"blockchain"`
	Symbol            string   `json:"symbol"`
	Image             string   `json:"image"`
	Address           string   `json:"address"`
	UsdPrice          float64  `json:"usdPrice"`
	Decimals          int      `json:"decimals"`
	Name              *string  `json:"name"`
	IsPopular         bool     `json:"isPopular"`
	IsSecondaryCoin   bool     `json:"isSecondaryCoin"`
	CoinSource        *string  `json:"coinSource"`
	CoinSourceUrl     *string  `json:"coinSourceUrl"`
	SupportedSwappers []string `json:"supportedSwappers"`
}

type BridgeExtra struct {
	RequireRefundAction bool   `json:"requireRefundAction"`
	SrcTx               string `json:"srcTx"`
	DestTx              string `json:"destTx"`
}

type CheckStatusTx struct {
	Data     string `json:"data"`
	To       string `json:"to"`
	Value    string `json:"value"`
	GasLimit string `json:"gasLimit"`
	GasPrice string `json:"gasPrice"`
	Nonce    string `json:"nonce"`
}

type CreateTxRequest struct {
	RequestId    string       `json:"requestId"`
	UserSettings UserSettings `json:"userSettings"`
	Validations  Validations  `json:"validations"`
	Step         int64        `json:"step"`
}

type RespReportTx struct {
	RequestId string `json:"requestId"`
	Status    string `json:"status"`
	Error     string `json:"error,omitempty"`
}

type ReportTxRequest struct {
	RequestId string `json:"requestId"`
	Step      int64  `json:"step"`
	EventType string `json:"eventType"`
	Reason    string `json:"reason"`
}

type RespGetCustomToken struct {
	Token struct {
		Blockchain        string   `json:"blockchain"`
		Symbol            string   `json:"symbol"`
		Image             string   `json:"image"`
		Address           string   `json:"address"`
		UsdPrice          string   `json:"usdPrice"`
		Decimals          int      `json:"decimals"`
		Name              string   `json:"name"`
		IsPopular         bool     `json:"isPopular"`
		IsSecondaryCoin   bool     `json:"isSecondaryCoin"`
		CoinSource        string   `json:"coinSource"`
		CoinSourceUrl     string   `json:"coinSourceUrl"`
		SupportedSwappers []string `json:"supportedSwappers"`
	} `json:"token"`
	Error     string `json:"error"`
	ErrorCode int64  `json:"errorCode"`
	TraceId   string `json:"traceId"`
}

// RespGetWalletDetails Response structure for getting wallet details
type RespGetWalletDetails struct {
	Wallets []struct {
		BlockChain  string `json:"blockChain"`
		Address     string `json:"address"`
		Failed      bool   `json:"failed"`
		ExplorerUrl string `json:"explorerUrl"`
		Balances    []struct {
			Asset struct {
				Blockchain string  `json:"blockchain"`
				Symbol     string  `json:"symbol"`
				Address    *string `json:"address"`
			} `json:"asset"`
			Amount struct {
				Amount   string `json:"amount"`
				Decimals int    `json:"decimals"`
			} `json:"amount"`
			Price *float64 `json:"price"`
		} `json:"balances"`
	} `json:"wallets"`
}

type Token struct {
	BlockChain        string   `json:"blockchain"`
	Symbol            string   `json:"symbol"`
	Image             string   `json:"image"`
	Address           string   `json:"address"`
	UsdPrice          float64  `json:"usdPrice"`
	Decimals          int64    `json:"decimals"`
	Name              string   `json:"name"`
	IsPopular         bool     `json:"isPopular"`
	IsSecondaryCoin   bool     `json:"isSecondaryCoin"`
	CoinSource        string   `json:"coinSource"`
	CoinSourceUrl     string   `json:"coinSourceUrl"`
	SupportedSwappers []string `json:"supportedSwappers"`
}
type Swapper struct {
	Id           string   `json:"id"`
	Title        string   `json:"title"`
	Logo         string   `json:"logo"`
	SwapperGroup string   `json:"swapperGroup"`
	Types        []string `json:"types"`
	Enabled      bool     `json:"enabled"`
}

type RespExchangeMeta struct {
	Tokens        []*Token   `json:"tokens"`
	PopularTokens []*Token   `json:"popularTokens"`
	Blockchains   []*Chain   `json:"blockchains"`
	Swappers      []*Swapper `json:"swappers"`
}
type ChainNativeCurrency struct {
	Name     string `json:"name"`
	Symbol   string `json:"symbol"`
	Decimals int64  `json:"decimals"`
}

type ChainInfo struct {
	InfoType          string              `json:"infoType"`
	ChainName         string              `json:"chainName"`
	NativeCurrency    ChainNativeCurrency `json:"nativeCurrency"`
	RpcUrls           []string            `json:"rpcUrls"`
	BlockExplorerUrls []string            `json:"blockExplorerUrls"`
	AddressUrl        string              `json:"addressUrl"`
	TransactionUrl    string              `json:"transactionUrl"`
	EnableGasV2       bool                `json:"enableGasV2"`
}

type ChainFeeAsset struct {
	Blockchain string `json:"blockchain"`
	Symbol     string `json:"symbol"`
	Address    string `json:"address"`
}

type Chain struct {
	Name            string           `json:"name"`
	DefaultDecimals int64            `json:"defaultDecimals"`
	AddressPatterns []string         `json:"addressPatterns"`
	FeeAssets       []*ChainFeeAsset `json:"feeAssets"`
	Logo            string           `json:"logo"`
	DisplayName     string           `json:"displayName"`
	ShortName       string           `json:"shortName"`
	Sort            int64            `json:"sort"`
	Color           string           `json:"color"`
	Enabled         bool             `json:"enabled"`
	Type            string           `json:"type"`
	ChainId         string           `json:"chainId"`
	Info            *ChainInfo       `json:"info"`
}
