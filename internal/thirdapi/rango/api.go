package rango

import (
	"byd_wallet/utils"
	"bytes"
	"encoding/json"
	"fmt"
)

const RangoUrl = "https://api.rango.exchange"
const apiKey = "c6381a79-2817-4602-83bf-6a641a409e32"

func GetExchangeMeta() (*RespExchangeMeta, error) {
	api := "/meta"
	url := fmt.Sprintf("%v%v", RangoUrl, api)
	httpClient := utils.NewHttpClient(url, "GET")
	httpClient.SetParam("apiKey", fmt.Sprintf("%v", apiKey))
	resBody, err := httpClient.Send()
	if err != nil {
		return nil, err
	}
	var respExchangeMeta RespExchangeMeta
	err = json.Unmarshal(resBody, &respExchangeMeta)
	if err != nil {
		return nil, fmt.Errorf("parse response failed: %v", err)
	}

	return &respExchangeMeta, nil
}

func CheckStatus(requestId string, txId string, step int64) (*RespCheckStatus, error) {
	reqBody := make(map[string]interface{})
	reqBody["requestId"] = requestId
	reqBody["txId"] = txId
	reqBody["step"] = step

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("marshal request failed: %v", err)
	}
	api := "/tx/check-status"
	url := fmt.Sprintf("%v%v", RangoUrl, api)
	httpClient := utils.NewHttpClient(url, "POST")
	httpClient.SetParam("apiKey", fmt.Sprintf("%v", apiKey))
	body := bytes.NewBuffer(jsonData)
	httpClient.SetBody(body)
	resBody, err := httpClient.Send()
	if err != nil {
		return nil, err
	}
	var respCheckStatus RespCheckStatus
	err = json.Unmarshal(resBody, &respCheckStatus)
	if err != nil {
		return nil, fmt.Errorf("parse response failed: %v", err)
	}

	return &respCheckStatus, nil
}

func GetBestRoute(fromBlockchain, fromSymbol, fromTokenAddress, fromAddress, toBlockchain, toSymbol, toTokenAddress, toAddress, amount, affiliatePercent string, affiliateWallets map[string]string) (*RespBestRoute, error) {
	to := map[string]interface{}{
		"blockchain": toBlockchain,
		"symbol":     toSymbol,
		"address":    toTokenAddress,
	}
	if toTokenAddress == "" {
		to = map[string]interface{}{
			"blockchain": toBlockchain,
			"symbol":     toSymbol,
		}
	}
	from := map[string]interface{}{
		"blockchain": fromBlockchain,
		"symbol":     fromSymbol,
		"address":    fromTokenAddress,
	}
	if fromTokenAddress == "" {
		from = map[string]interface{}{
			"blockchain": fromBlockchain,
			"symbol":     fromSymbol,
		}
	}
	reqBody := map[string]interface{}{
		"from":     from,
		"to":       to,
		"slippage": "1.0",
		"selectedWallets": map[string]string{
			fromBlockchain: fromAddress,
			toBlockchain:   toAddress,
		},
		"checkPrerequisites": false,
		"amount":             amount,
	}
	if affiliatePercent != "0" {
		reqBody["affiliateWallets"] = affiliateWallets
		reqBody["affiliatePercent"] = affiliatePercent
		reqBody["affiliateRef"] = "jxD5sD"
	}
	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("marshal request failed: %v", err)
	}

	api := "/routing/best"
	url := fmt.Sprintf("%v%v", RangoUrl, api)
	httpClient := utils.NewHttpClient(url, "POST")
	httpClient.SetParam("apiKey", fmt.Sprintf("%v", apiKey))
	body := bytes.NewBuffer(jsonData)
	httpClient.SetBody(body)
	resBody, err := httpClient.Send()
	if err != nil {
		return nil, err
	}
	var respBestRoute RespBestRoute
	err = json.Unmarshal(resBody, &respBestRoute)
	if err != nil {
		return nil, fmt.Errorf("parse response failed: %v", err)
	}

	return &respBestRoute, nil
}

func GetAllPossibleRoutes(fromBlockchain, fromSymbol, fromTokenAddress, toBlockchain, toSymbol, toTokenAddress, amount, slippage, affiliatePercent string, affiliateWallets map[string]string) (*AllPossibleRoutes, error) {
	from := map[string]interface{}{
		"blockchain": fromBlockchain,
		"symbol":     fromSymbol,
		"address":    fromTokenAddress,
	}
	if fromTokenAddress == "" {
		from = map[string]interface{}{
			"blockchain": fromBlockchain,
			"symbol":     fromSymbol,
		}
	}

	to := map[string]interface{}{
		"blockchain": toBlockchain,
		"symbol":     toSymbol,
		"address":    toTokenAddress,
	}
	if toTokenAddress == "" {
		to = map[string]interface{}{
			"blockchain": toBlockchain,
			"symbol":     toSymbol,
			"address":    "",
		}
	}

	requestBody := map[string]interface{}{
		"from":     from,
		"to":       to,
		"amount":   amount,
		"slippage": slippage,
	}
	if _, exist := affiliateWallets[fromBlockchain]; exist {
		requestBody["affiliateWallets"] = affiliateWallets
		requestBody["affiliatePercent"] = affiliatePercent
		requestBody["affiliateRef"] = "jxD5sD"
	}
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to serialize the request body: %v", err)
	}

	api := "/routing/bests"
	url := fmt.Sprintf("%v%v", RangoUrl, api)
	httpClient := utils.NewHttpClient(url, "POST")
	httpClient.SetParam("apiKey", fmt.Sprintf("%v", apiKey))
	body := bytes.NewBuffer(jsonData)
	httpClient.SetBody(body)
	resBody, err := httpClient.Send()
	if err != nil {
		return nil, err
	}
	var response AllPossibleRoutes
	if err := json.Unmarshal(resBody, &response); err != nil {
		return nil, fmt.Errorf("decode response fail: %v", err)
	}
	return &response, nil
}

func ConfirmRoute(selectedWallets map[string]string, destination string, requestId string) (*ConfirmRouteResponse, error) {
	reqBody := ConfirmRouteRequest{
		SelectedWallets: selectedWallets,
		Destination:     destination,
		RequestId:       requestId,
	}

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("marshal request failed: %v", err)
	}
	api := "/routing/confirm"
	url := fmt.Sprintf("%v%v", RangoUrl, api)
	httpClient := utils.NewHttpClient(url, "POST")
	httpClient.SetParam("apiKey", fmt.Sprintf("%v", apiKey))
	body := bytes.NewBuffer(jsonData)
	httpClient.SetBody(body)
	resBody, err := httpClient.Send()
	if err != nil {
		return nil, err
	}
	var respConfirmRoute ConfirmRouteResponse
	err = json.Unmarshal(resBody, &respConfirmRoute)
	if err != nil {
		return nil, fmt.Errorf("parse response failed: %v", err)
	}
	respConfirmRoute.Result.SelectedWallets = selectedWallets
	if !respConfirmRoute.Ok {
		return nil, fmt.Errorf("confirm route failed: %v", respConfirmRoute)
	}
	return &respConfirmRoute, nil
}

func CreateTx(requestId string, userSettings UserSettings, validations Validations, step int64) (*RespCreateTx, error) {
	reqBody := CreateTxRequest{
		RequestId:    requestId,
		UserSettings: userSettings,
		Validations:  validations,
		Step:         step,
	}

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("marshal request failed: %v", err)
	}

	api := "/tx/create"
	url := fmt.Sprintf("%v%v", RangoUrl, api)
	httpClient := utils.NewHttpClient(url, "POST")
	httpClient.SetParam("apiKey", fmt.Sprintf("%v", apiKey))
	body := bytes.NewBuffer(jsonData)
	httpClient.SetBody(body)
	resBody, err := httpClient.Send()
	if err != nil {
		return nil, err
	}
	fmt.Println("resBody = ", string(resBody))

	var respCreateTx RespCreateTx
	err = json.Unmarshal(resBody, &respCreateTx)
	if err != nil {
		return nil, fmt.Errorf("parse response failed: %v", err)
	}

	return &respCreateTx, nil
}

func CheckApproval(requestId string, txId string) (*RespCheckApproval, error) {
	api := fmt.Sprintf("/tx/%s/check-approval", requestId)
	url := fmt.Sprintf("%v%v", RangoUrl, api)
	httpClient := utils.NewHttpClient(url, "GET")
	httpClient.SetParam("txId", fmt.Sprintf("%v", txId))
	httpClient.SetParam("apiKey", fmt.Sprintf("%v", apiKey))
	resBody, err := httpClient.Send()
	if err != nil {
		return nil, err
	}

	var respCheckApproval RespCheckApproval
	err = json.Unmarshal(resBody, &respCheckApproval)
	if err != nil {
		return nil, fmt.Errorf("parse response failed: %v", err)
	}

	return &respCheckApproval, nil
}

func ReportTx(requestId string, step int64, eventType string, reason string) (*RespReportTx, error) {
	reqBody := ReportTxRequest{
		RequestId: requestId,
		Step:      step,
		EventType: eventType,
		Reason:    reason,
	}

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("marshal request failed: %v", err)
	}

	api := "/tx/report-tx"
	url := fmt.Sprintf("%v%v", RangoUrl, api)
	httpClient := utils.NewHttpClient(url, "POST")
	httpClient.SetParam("apiKey", fmt.Sprintf("%v", apiKey))
	body := bytes.NewBuffer(jsonData)
	httpClient.SetBody(body)
	resBody, err := httpClient.Send()
	if err != nil {
		return nil, err
	}

	var respReportTx RespReportTx
	err = json.Unmarshal(resBody, &respReportTx)
	if err != nil {
		return nil, fmt.Errorf("parse response failed: %v", err)
	}

	return &respReportTx, nil
}

// GetCustomToken token information price and so on
func GetCustomToken(blockchain, tokenAddress string) (*RespGetCustomToken, error) {
	api := "/meta/custom-token"
	url := fmt.Sprintf("%v%v", RangoUrl, api)
	httpClient := utils.NewHttpClient(url, "GET")
	httpClient.SetParam("blockchain", fmt.Sprintf("%v", blockchain))
	httpClient.SetParam("address", fmt.Sprintf("%v", tokenAddress))
	httpClient.SetParam("apiKey", fmt.Sprintf("%v", apiKey))
	resBody, err := httpClient.Send()
	if err != nil {
		return nil, err
	}

	var respGetCustomToken RespGetCustomToken
	err = json.Unmarshal(resBody, &respGetCustomToken)
	if err != nil {
		return nil, fmt.Errorf("parse response failed: %v", err)
	}

	return &respGetCustomToken, nil
}

func GetWalletDetails(address string) (*RespGetWalletDetails, error) {
	api := "/wallets/details"
	url := fmt.Sprintf("%v%v", RangoUrl, api)
	httpClient := utils.NewHttpClient(url, "GET")
	httpClient.SetParam("address", fmt.Sprintf("%v", address))
	httpClient.SetParam("apiKey", fmt.Sprintf("%v", apiKey))
	resBody, err := httpClient.Send()
	if err != nil {
		return nil, err
	}

	var respGetWalletDetails RespGetWalletDetails
	err = json.Unmarshal(resBody, &respGetWalletDetails)
	if err != nil {
		return nil, fmt.Errorf("parse response failed: %v", err)
	}

	return &respGetWalletDetails, nil
}
