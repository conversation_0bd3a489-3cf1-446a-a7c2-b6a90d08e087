package coingecko

type CoinPrice struct {
	ID            string  `json:"id"` // coin id
	Currency      string  `json:"currency"`
	Price         float64 `json:"price"`          // price in currency(usd,...)
	LastUpdatedAt int64   `json:"last_update_at"` // last updated price time in UNIX // seconds
}

type CoinID struct {
	ID        string            `json:"id"`
	Symbol    string            `json:"symbol"`
	Name      string            `json:"name"`
	Platforms map[string]string `json:"platforms"`
}

type CoinMarketData struct {
	ID                       string  `json:"id"`            // coin id
	Image                    string  `json:"image"`         // coin logo
	Currency                 string  `json:"currency"`      // custome field
	CurrentPrice             float64 `json:"current_price"` // coin current price in currency(usd,...)
	CirculatingSupply        float64 `json:"circulating_supply"`
	LastUpdated              string  `json:"last_updated"`   // datetime //ex.2025-05-13T02:38:14.949Z
	LastUpdatedAt            int64   `json:"last_update_at"` // last updated price time in UNIX // custome field // seconds
	PriceChangePercentage24h float64 `json:"price_change_percentage_24h"`
	TotalVolume              float64 `json:"total_volume"` // trading volume in 24h
}

type CoinOHLC struct {
	ID            string  `json:"id"`
	Currency      string  `json:"currency"`
	Interval      string  `json:"interval"`        // daily,hourly
	LastUpdatedAt int64   `json:"last_updated_at"` // seconds
	Open          float64 `json:"open"`
	High          float64 `json:"high"`
	Low           float64 `json:"low"`
	Close         float64 `json:"close"`
}

type CoinLogo struct {
	ID    string `json:"id"`
	Image string `json:"image"`
}
