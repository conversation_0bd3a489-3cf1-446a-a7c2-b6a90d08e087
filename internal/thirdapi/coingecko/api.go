package coingecko

import (
	"byd_wallet/common/constant"
	"byd_wallet/utils"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"
)

type CoingeckoClient struct {
	apiUrl string
	apiKey string
}

func NewCoingeckoClient(apiUrl, apiKey string) *CoingeckoClient {
	return &CoingeckoClient{
		apiUrl: apiUrl,
		apiKey: apiKey,
	}
}

func (cc *CoingeckoClient) reqUrl(router string) string {
	return cc.apiUrl + router
}

var chainIndex2coinPlatform = map[int64]string{
	constant.BtcChainIndex:      "bitcoin",
	constant.EthChainIndex:      "ethereum",
	constant.BscChainIndex:      "binance-smart-chain",
	constant.PolChainIndex:      "polygon-pos",
	constant.ArbChainIndex:      "arbitrum-one",
	constant.OptimismChainIndex: "optimistic-ethereum",
	constant.BaseChainIndex:     "base",
	constant.SolChainIndex:      "solana",
	constant.TronChainIndex:     "tron",
}

func (cc *CoingeckoClient) ListCoinIDOrderBy(limit int64, orderBy string) (coinIDs []string, err error) {
	step := int64(requestMarketDataLimit)
	if limit <= step {
		return cc.listCoinIDOrderBy(1, int(limit), orderBy)
	}
	coinIDs = make([]string, 0, limit)
	totalPage := limit / step
	lastPageSize := limit % step
	for page := int64(1); page <= totalPage; page++ {
		d, err := cc.listCoinIDOrderBy(int(page), int(step), orderBy)
		if err != nil {
			return nil, err
		}
		coinIDs = append(coinIDs, d...)
	}

	if lastPageSize > 0 {
		d, err := cc.listCoinIDOrderBy(int(totalPage+1), int(step), orderBy)
		if err != nil {
			return nil, err
		}
		if len(d) < int(lastPageSize) {
			return nil, fmt.Errorf("last page length is not expect: %d: %d", len(d), lastPageSize)
		}
		coinIDs = append(coinIDs, d[:lastPageSize]...)
	}

	if len(coinIDs) != int(limit) {
		err = fmt.Errorf("resp data length is not equal to ids length: %d != %d", len(coinIDs), limit)
	}
	return
}

const (
	OrderByMarketCapDes = "market_cap_desc"
	OrderByVolumeDesc   = "volume_desc"
)

func (cc *CoingeckoClient) listCoinIDOrderBy(page, perPage int, orderBy string) (coinIDs []string, err error) {
	cli := utils.NewHttpClient(cc.reqUrl("/coins/markets"), "GET")
	cli.SetDefaultHeaders()
	cli.SetHeader("x-cg-pro-api-key", cc.apiKey)

	cli.SetParam("vs_currency", "usd")
	cli.SetParam("per_page", strconv.Itoa(perPage)) // NOTE: per_page is 1-250
	cli.SetParam("page", strconv.Itoa(page))
	cli.SetParam("order", orderBy)

	rspBody, err := cli.Send()
	if err != nil {
		return nil, err
	}

	data := []struct {
		ID string `json:"id"`
	}{}
	err = json.Unmarshal(rspBody, &data)
	if err != nil {
		err = fmt.Errorf("parse json fail: %w: %s", err, string(rspBody))
		return
	}
	coinIDs = make([]string, 0, len(data))
	for _, v := range data {
		coinIDs = append(coinIDs, v.ID)
	}
	return
}

func (cc *CoingeckoClient) ListCoinID() (ids []*CoinID, err error) {
	cli := utils.NewHttpClient(cc.reqUrl("/coins/list"), "GET")
	cli.SetDefaultHeaders()
	cli.SetHeader("x-cg-pro-api-key", cc.apiKey)
	cli.SetParam("include_platform", "true")
	rspBody, err := cli.Send()
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(rspBody, &ids)
	if err != nil {
		err = fmt.Errorf("parse json fail: %w: %s", err, string(rspBody))
	}
	return
}

func (cc *CoingeckoClient) ListCoinPriceByIDs(currency string, ids []string) (prices []*CoinPrice, err error) {
	sb := strings.Builder{}
	for i, id := range ids {
		if i > 0 {
			sb.WriteRune(',')
		}
		sb.WriteString(id)
	}

	cli := utils.NewHttpClient(cc.reqUrl("/simple/price"), "GET")
	cli.SetDefaultHeaders()
	cli.SetHeader("x-cg-pro-api-key", cc.apiKey)
	cli.SetParam("vs_currencies", "usd")
	cli.SetParam("include_last_updated_at", "true")
	cli.SetParam("ids", sb.String())
	rspBody, err := cli.Send()
	if err != nil {
		return nil, err
	}
	data := map[string]interface{}{}
	err = json.Unmarshal(rspBody, &data)
	if err != nil {
		err = fmt.Errorf("parse json fail: %w: %s", err, string(rspBody))
		return
	}
	prices = make([]*CoinPrice, 0, len(data))
	for _, id := range ids {
		d, ok := data[id]
		if !ok {
			err = fmt.Errorf("resp data.%s is null", id)
			return
		}
		priceData, ok := d.(map[string]interface{})
		if !ok {
			err = fmt.Errorf("resp data.%s is not object", id)
			return
		}
		price, ok := priceData[currency].(float64)
		if !ok {
			err = fmt.Errorf("resp data.%s.%s is null", id, currency)
			return
		}
		lastPriceUpdatedAt, ok := priceData["last_updated_at"].(float64)
		if !ok {
			err = fmt.Errorf("resp data.%s.last_updated_at is null", id)
			return
		}
		prices = append(prices, &CoinPrice{
			ID:            id,
			LastUpdatedAt: int64(lastPriceUpdatedAt),
			Currency:      currency,
			Price:         price,
		})
	}
	return
}

const requestMarketDataLimit = 250

func (cc *CoingeckoClient) ListCoinMarketDataIn24hByIDs(currency string, ids []string) (data []*CoinMarketData, err error) {
	step := requestMarketDataLimit
	if len(ids) <= step {
		return cc.listCoinMarketDataIn24hByIDs(currency, ids, 1, len(ids))
	}
	data = make([]*CoinMarketData, 0, len(ids))
	for i := 0; i < len(ids); i += step {
		var tmpIds []string
		if i+step > len(ids) {
			tmpIds = ids[i:]
		} else {
			tmpIds = ids[i : i+step]
		}
		d, err := cc.listCoinMarketDataIn24hByIDs(currency, tmpIds, 1, step)
		if err != nil {
			return nil, err
		}
		data = append(data, d...)
	}
	// if len(data) != len(ids) {
	// 	err = fmt.Errorf("resp data length is not equal to ids length: %d != %d", len(data), len(ids))
	// }
	return
}

func (cc *CoingeckoClient) listCoinMarketDataIn24hByIDs(currency string, ids []string, page, perPage int) (data []*CoinMarketData, err error) {
	sb := strings.Builder{}
	for i, id := range ids {
		if i > 0 {
			sb.WriteRune(',')
		}
		sb.WriteString(id)
	}

	cli := utils.NewHttpClient(cc.reqUrl("/coins/markets"), "GET")
	cli.SetDefaultHeaders()
	cli.SetHeader("x-cg-pro-api-key", cc.apiKey)

	cli.SetParam("vs_currency", currency)
	cli.SetParam("ids", sb.String())
	cli.SetParam("price_change_percentage", "24h")
	cli.SetParam("per_page", strconv.Itoa(perPage)) // NOTE: per_page is 1-250
	cli.SetParam("page", strconv.Itoa(page))

	rspBody, err := cli.Send()
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(rspBody, &data)
	if err != nil {
		err = fmt.Errorf("parse json fail: %w: %s", err, string(rspBody))
		return
	}
	for _, d := range data {
		d.Currency = currency // NOTE: redundant data
		lua, err := time.ParseInLocation("2006-01-02T15:04:05.999Z", d.LastUpdated, time.UTC)
		if err == nil {
			d.LastUpdatedAt = lua.Unix()
		}
	}
	return
}

func (cc *CoingeckoClient) ListCoinOHLCIn24hByID(currency, id string) (ohlcs []*CoinOHLC, err error) {
	cli := utils.NewHttpClient(cc.reqUrl(fmt.Sprintf("/coins/%s/ohlc", id)), "GET")
	cli.SetDefaultHeaders()
	cli.SetHeader("x-cg-pro-api-key", cc.apiKey)

	cli.SetParam("vs_currency", currency)
	cli.SetParam("id", id)
	cli.SetParam("days", "1")
	interval := "hourly"
	cli.SetParam("interval", interval)

	rspBody, err := cli.Send()
	if err != nil {
		return nil, err
	}

	data := [][]float64{}
	err = json.Unmarshal(rspBody, &data)
	if err != nil {
		err = fmt.Errorf("parse json fail: %w: %s", err, string(rspBody))
		return
	}

	ohlcs = make([]*CoinOHLC, 0, len(data))
	for _, d := range data {
		if len(d) != 5 {
			err = fmt.Errorf("ohlc miss field value: %v", d)
			return
		}
		ohlcs = append(ohlcs, &CoinOHLC{
			ID:            id,
			Currency:      currency,
			Interval:      interval,
			LastUpdatedAt: int64(d[0]) / 1000,
			Open:          d[1],
			High:          d[2],
			Low:           d[3],
			Close:         d[4],
		})
	}
	return
}

func (cc *CoingeckoClient) ListCoinOHLCInRangeHByID(currency, id string, from, to int64) (ohlcs []*CoinOHLC, err error) {
	cli := utils.NewHttpClient(cc.reqUrl(fmt.Sprintf("/coins/%s/ohlc/range", id)), "GET")
	cli.SetDefaultHeaders()
	cli.SetHeader("x-cg-pro-api-key", cc.apiKey)

	cli.SetParam("vs_currency", currency)
	cli.SetParam("id", id)
	interval := "hourly"
	cli.SetParam("interval", interval)
	cli.SetParam("from", strconv.Itoa(int(from)))
	cli.SetParam("to", strconv.Itoa(int(to)))

	rspBody, err := cli.Send()
	if err != nil {
		return nil, err
	}

	data := [][]float64{}
	err = json.Unmarshal(rspBody, &data)
	if err != nil {
		err = fmt.Errorf("parse json fail: %w: %s", err, string(rspBody))
		return
	}

	ohlcs = make([]*CoinOHLC, 0, len(data))
	for _, d := range data {
		if len(d) != 5 {
			err = fmt.Errorf("ohlc miss field value: %v", d)
			return
		}
		ohlcs = append(ohlcs, &CoinOHLC{
			ID:            id,
			Currency:      currency,
			Interval:      interval,
			LastUpdatedAt: int64(d[0]) / 1000,
			Open:          d[1],
			High:          d[2],
			Low:           d[3],
			Close:         d[4],
		})
	}
	return
}

func (cc *CoingeckoClient) ListCoinLogosByIDs(ids []string) (data []*CoinLogo, err error) {
	step := requestMarketDataLimit
	if len(ids) <= step {
		return cc.listCoinLogosByIDs(ids, 1, len(ids))
	}
	data = make([]*CoinLogo, 0, len(ids))
	for i := 0; i < len(ids); i += step {
		var tmpIds []string
		if i+step > len(ids) {
			tmpIds = ids[i:]
		} else {
			tmpIds = ids[i : i+step]
		}
		d, err := cc.listCoinLogosByIDs(tmpIds, 1, step)
		if err != nil {
			return nil, err
		}
		data = append(data, d...)
	}
	// if len(data) != len(ids) {
	// 	err = fmt.Errorf("resp data length is not equal to ids length: %d != %d", len(data), len(ids))
	// }
	return
}

func (cc *CoingeckoClient) listCoinLogosByIDs(ids []string, page, perPage int) (data []*CoinLogo, err error) {
	currency := "usd"
	sb := strings.Builder{}
	for i, id := range ids {
		if i > 0 {
			sb.WriteRune(',')
		}
		sb.WriteString(id)
	}

	cli := utils.NewHttpClient(cc.reqUrl("/coins/markets"), "GET")
	cli.SetDefaultHeaders()
	cli.SetHeader("x-cg-pro-api-key", cc.apiKey)

	cli.SetParam("vs_currency", currency)
	cli.SetParam("ids", sb.String())
	cli.SetParam("price_change_percentage", "24h")
	cli.SetParam("per_page", strconv.Itoa(perPage)) // NOTE: per_page is 1-250
	cli.SetParam("page", strconv.Itoa(page))

	rspBody, err := cli.Send()
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(rspBody, &data)
	if err != nil {
		err = fmt.Errorf("parse json fail: %w: %s", err, string(rspBody))
		return
	}
	return
}

func (cc *CoingeckoClient) GetCoinIDByTokenAddress(chainIndex int64, address string) (coinID string, err error) {
	if address == "" {
		err = errors.New("address is empty")
		return
	}
	platformID, ok := chainIndex2coinPlatform[chainIndex]
	if !ok {
		err = fmt.Errorf("GetCoinIDByTokenAddress: unsupported chain index: %d", chainIndex)
	}
	cli := utils.NewHttpClient(cc.reqUrl(fmt.Sprintf("/coins/%s/contract/%s", platformID, address)), "GET")
	cli.SetDefaultHeaders()
	cli.SetHeader("x-cg-pro-api-key", cc.apiKey)
	body, err := cli.Send()
	if err != nil {
		return "", err
	}
	//fmt.Println(string(body))
	var data struct {
		ID string `json:"id"`
	}
	if err = json.Unmarshal(body, &data); err != nil {
		return "", err
	}
	return data.ID, nil
}
