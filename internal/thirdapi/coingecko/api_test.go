package coingecko

import (
	"byd_wallet/common/constant"
	"encoding/json"
	"fmt"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

type CoingeckoTestConfig struct {
	APIUrl       string `json:"api_url"`
	APIKey       string `json:"api_key"`
	IsLocalDebug bool   `json:"is_local_debug"`
	Socks5Proxy  string `json:"socks_5_proxy"`
}

var cfg CoingeckoTestConfig

func init() {
	data, err := os.ReadFile("../../../tmp/CoingeckoTestConfig.json")
	if err != nil {
		fmt.Println("warn: read test config json fail:" + err.<PERSON>rror())
		return
	}
	err = json.Unmarshal(data, &cfg)
	if err != nil {
		panic("parse test config json fail:" + err.Error())
	}

	if cfg.IsLocalDebug {
		os.Setenv("HTTPS_PROXY", cfg.Socks5Proxy)
		os.Setenv("HTTP_PROXY", cfg.Socks5Proxy)
	}
}

func TestListCoinIDSortByMarketDesc(t *testing.T) {
	if !cfg.IsLocalDebug {
		t.Skip("only local debug")
	}
	s := assert.New(t)
	cc := NewCoingeckoClient(cfg.APIUrl, cfg.APIKey)
	coinIDs, err := cc.ListCoinIDOrderBy(5, OrderByMarketCapDes)
	if !s.NoError(err) {
		return
	}
	s.Len(coinIDs, 5)
	t.Log("coinIDs", coinIDs)

	coinIDs, err = cc.ListCoinIDOrderBy(252, OrderByMarketCapDes)
	if !s.NoError(err) {
		return
	}
	s.Len(coinIDs, 252)
}

func TestListCoinID(t *testing.T) {
	if !cfg.IsLocalDebug {
		t.Skip("only local debug")
	}

	s := assert.New(t)
	cc := NewCoingeckoClient(cfg.APIUrl, cfg.APIKey)
	ids, err := cc.ListCoinID()
	if !s.NoError(err) {
		return
	}
	if !s.True(len(ids) > 0) {
		return
	}
	id := ids[0]
	s.NotEmpty(id.ID)
	s.NotEmpty(id.Name)
	s.NotEmpty(id.Symbol)
	s.NotEmpty(id.Platforms)
	t.Log(id)
}

func TestListCoinPriceByIDs(t *testing.T) {
	if !cfg.IsLocalDebug {
		t.Skip("only local debug")
	}

	s := assert.New(t)
	cc := NewCoingeckoClient(cfg.APIUrl, cfg.APIKey)

	currency := "usd"
	ids := []string{"bitcoin", "ethereum"}

	prices, err := cc.ListCoinPriceByIDs(currency, ids)
	if !s.NoError(err) {
		return
	}
	s.Len(prices, 2)
	for _, p := range prices {
		s.Contains(ids, p.ID)
		s.Equal(currency, p.Currency)
		s.True(p.Price > 0)
		s.True(p.LastUpdatedAt > 0)
	}
	rs, _ := json.Marshal(prices)
	t.Log(string(rs))
}

func TestListCoinMarketDataIn24hByIDs_NoPaging(t *testing.T) {
	if !cfg.IsLocalDebug {
		t.Skip("only local debug")
	}

	s := assert.New(t)
	cc := NewCoingeckoClient(cfg.APIUrl, cfg.APIKey)

	currency := "usd"
	ids := []string{"bitcoin", "ethereum"}

	list, err := cc.ListCoinMarketDataIn24hByIDs(currency, ids)
	if !s.NoError(err) {
		return
	}
	s.Len(list, 2)
	for _, data := range list {
		s.Contains(ids, data.ID)
		s.Equal(currency, data.Currency)
		s.True(data.CurrentPrice > 0)
		s.True(data.CirculatingSupply > 0)
		s.NotEmpty(data.LastUpdated)
		s.True(data.PriceChangePercentage24h != 0)
		s.True(data.TotalVolume > 0)
		s.True(data.LastUpdatedAt > 0)
	}
	rs, _ := json.Marshal(list)
	t.Log(string(rs))
}

func TestListCoinMarketDataIn24hByIDs_Paging(t *testing.T) {
	if !cfg.IsLocalDebug {
		t.Skip("only local debug")
	}

	s := assert.New(t)

	data, err := os.ReadFile("../../../tmp/ids.json")
	if err != nil {
		panic("read test config fail:" + err.Error())
	}
	ids := make([]string, 0)
	err = json.Unmarshal(data, &ids)
	if err != nil {
		panic("parse test config json fail:" + err.Error())
	}

	if !s.Len(ids, 500) {
		return
	}

	cc := NewCoingeckoClient(cfg.APIUrl, cfg.APIKey)

	currency := "usd"

	ids1 := ids[:254]
	list, err := cc.ListCoinMarketDataIn24hByIDs(currency, ids1)
	if !s.NoError(err) {
		return
	}
	if !s.Len(list, len(ids1)) {
		return
	}

	checkIDs := make(map[string]struct{}, len(ids1))
	for _, data := range list {
		checkIDs[data.ID] = struct{}{}
		s.Contains(ids, data.ID)
		s.Equal(currency, data.Currency)
		s.True(data.CurrentPrice > 0)
		s.True(data.CirculatingSupply > 0)
		s.NotEmpty(data.LastUpdated)
		// maybe stable coin
		// s.True(data.PriceChangePercentage24h != 0, data)
		// s.True(data.TotalVolume > 0)
		s.True(data.LastUpdatedAt > 0)
	}
	s.Len(checkIDs, len(ids1))
}

func TestListCoinOHLCIn24hByID(t *testing.T) {
	if !cfg.IsLocalDebug {
		t.Skip("only local debug")
	}

	s := assert.New(t)
	cc := NewCoingeckoClient(cfg.APIUrl, cfg.APIKey)
	currency := "usd"
	id := "bitcoin"
	ohlcs, err := cc.ListCoinOHLCIn24hByID(currency, id)
	if !s.NoError(err) {
		return
	}

	s.Len(ohlcs, 24)
	for _, data := range ohlcs {
		s.Equal(id, data.ID)
		s.Equal(currency, data.Currency)
		s.NotEmpty(data.Interval)
		s.True(data.LastUpdatedAt > 0)
		s.True(data.Open > 0)
		s.True(data.High > 0)
		s.True(data.Low > 0)
		s.True(data.Close > 0)
	}
	rs, _ := json.Marshal(ohlcs)
	t.Log(string(rs))
}

func TestListCoinOHLCInRangeHByID(t *testing.T) {
	if !cfg.IsLocalDebug {
		t.Skip("only local debug")
	}

	s := assert.New(t)
	cc := NewCoingeckoClient(cfg.APIUrl, cfg.APIKey)
	currency := "usd"
	id := "bitcoin"
	from := int64(1747119600)
	to := int64(1747123200)
	ohlcs, err := cc.ListCoinOHLCInRangeHByID(currency, id, from, to)
	if !s.NoError(err) {
		return
	}

	s.Len(ohlcs, 2)
	for _, data := range ohlcs {
		s.Equal(id, data.ID)
		s.Equal(currency, data.Currency)
		s.NotEmpty(data.Interval)
		s.True(data.LastUpdatedAt > 0)
		s.True(data.Open > 0)
		s.True(data.High > 0)
		s.True(data.Low > 0)
		s.True(data.Close > 0)
	}
	rs, _ := json.Marshal(ohlcs)
	t.Log(string(rs))
}

func TestListCoinLogosByIDs_NoPaging(t *testing.T) {
	if !cfg.IsLocalDebug {
		t.Skip("only local debug")
	}

	s := assert.New(t)
	cc := NewCoingeckoClient(cfg.APIUrl, cfg.APIKey)
	ids := []string{"bitcoin", "ethereum"}

	coinLogos, err := cc.ListCoinLogosByIDs(ids)
	if !s.NoError(err) {
		return
	}
	s.Len(coinLogos, 2)
	for _, logo := range coinLogos {
		s.Contains(ids, logo.ID)
		s.NotEmpty(logo.Image)
	}

	rs, _ := json.Marshal(coinLogos)
	t.Log(string(rs))
}

func TestListCoinLogosByIDs_Paging(t *testing.T) {
	if !cfg.IsLocalDebug {
		t.Skip("only local debug")
	}

	s := assert.New(t)

	data, err := os.ReadFile("../../../tmp/ids.json")
	if err != nil {
		panic("read test config fail:" + err.Error())
	}
	ids := make([]string, 0)
	err = json.Unmarshal(data, &ids)
	if err != nil {
		panic("parse test config json fail:" + err.Error())
	}

	if !s.Len(ids, 500) {
		return
	}

	cc := NewCoingeckoClient(cfg.APIUrl, cfg.APIKey)
	ids1 := ids[:268]
	coinLogos, err := cc.ListCoinLogosByIDs(ids1)
	if !s.NoError(err) {
		return
	}
	if !s.Len(coinLogos, len(ids1)) {
		return
	}
	checkIDs := make(map[string]struct{}, len(ids1))
	for _, logo := range coinLogos {
		checkIDs[logo.ID] = struct{}{}
		s.Contains(ids, logo.ID)
		s.NotEmpty(logo.Image)
	}
	s.Len(checkIDs, len(ids1))
}

func TestCoingeckoClient_GetCoinIDByTokenAddress(t *testing.T) {
	if !cfg.IsLocalDebug {
		t.Skip("only local debug")
	}

	s := assert.New(t)
	cc := NewCoingeckoClient(cfg.APIUrl, cfg.APIKey)
	coinID, err := cc.GetCoinIDByTokenAddress(constant.SolChainIndex, "AUrMpCDYYcPuHhyNX8gEEqbmDPFUpBpHrNW3vPeCFn5Z")
	s.NoError(err)
	s.Equal("tether", coinID)
}
