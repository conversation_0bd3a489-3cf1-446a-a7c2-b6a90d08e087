package spl_token

type Token struct {
	ChainID    int      `json:"chainId"`
	Address    string   `json:"address"`
	Symbol     string   `json:"symbol"`
	Name       string   `json:"name"`
	Decimals   int64    `json:"decimals"`
	LogoURI    string   `json:"logoURI"`
	Tags       []string `json:"tags"`
	Extensions struct {
		Facebook string `json:"facebook"`
		Twitter  string `json:"twitter"`
		Website  string `json:"website"`
	} `json:"extensions"`
}

type TokenList struct {
	Tokens []Token `json:"tokens"`
}
