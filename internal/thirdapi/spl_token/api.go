package spl_token

import (
	"byd_wallet/utils"
	"encoding/json"
)

var url = "https://raw.githubusercontent.com/solana-labs/token-list/main/src/tokens/solana.tokenlist.json"

func GetSolTokenList() ([]Token, error) {
	cli := utils.NewHttpClient(url, "GET")
	cli.SetDefaultHeaders()
	rspBody, err := cli.Send()
	if err != nil {
		return nil, err
	}
	var tokenList TokenList
	err = json.Unmarshal(rspBody, &tokenList)
	if err != nil {
		return nil, err
	}
	return tokenList.Tokens, nil
}
