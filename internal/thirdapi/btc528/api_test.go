package btc528

import (
	"encoding/json"
	"fmt"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

type Btc528TestConfig struct {
	APIUrl       string `json:"api_url"`
	Sign         string `json:"sign"`
	IsLocalDebug bool   `json:"is_local_debug"`
}

var cfg Btc528TestConfig

func init() {
	data, err := os.ReadFile("../../../tmp/btc528.json")
	if err != nil {
		fmt.Println("warn: read test config json fail:" + err.Error())
		return
	}
	err = json.Unmarshal(data, &cfg)
	if err != nil {
		panic("parse test config json fail:" + err.Error())
	}
}

func TestGetCurrencyUSDRate(t *testing.T) {
	if !cfg.IsLocalDebug {
		t.Skip("only local debug")
	}

	s := assert.New(t)

	cc := NewBtc528Client(cfg.APIUrl, cfg.Sign)
	for _, c := range []string{"usd", "cny", "jpy"} {
		rate, err := cc.GetCurrencyUSDRate(c)
		if !s.NoError(err) {
			return
		}
		r, _ := json.Marshal(rate)
		t.Log(c, "===>", string(r))
	}

}
