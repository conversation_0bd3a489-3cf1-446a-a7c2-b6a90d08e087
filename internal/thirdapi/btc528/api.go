package btc528

import (
	"byd_wallet/utils"
	"encoding/json"
	"fmt"
)

type Btc528Client struct {
	apiUrl string
	sign   string
}

func NewBtc528Client(apiUrl, sign string) *Btc528Client {
	return &Btc528Client{
		apiUrl: apiUrl,
		sign:   sign,
	}
}

type CurrencyRate struct {
	Code       string  `json:"code"`
	Title      string  `json:"title"`
	CodeSina   string  `json:"codeSina"`
	CreateTime int64   `json:"createTime"`
	UpdateTime int64   `json:"updateTime"`
	DataStatus int     `json:"dataStatus"`
	Rate       float64 `json:"rate"`
}

func (cc *Btc528Client) reqUrl(router string) string {
	return cc.apiUrl + router
}

func (cc *Btc528Client) GetCurrencyUSDRate(currency string) (*CurrencyRate, error) {
	cli := utils.NewHttpClient(cc.reqUrl("/xhj-gather-app/open/coin/rate/detailByCode"), "GET")
	cli.SetDefaultHeaders()
	cli.<PERSON>("sign", cc.sign)
	cli.SetParam("code", currency)

	rspBody, err := cli.Send()
	if err != nil {
		return nil, err
	}
	resp := struct {
		Data    *CurrencyRate `json:"data"`
		Code    int64         `json:"code"`
		Message string        `json:"message"`
	}{}
	err = json.Unmarshal(rspBody, &resp)
	if err != nil {
		return nil, fmt.Errorf("parse json fail: %w: %s", err, string(rspBody))
	}
	if resp.Code != 200 {
		return nil, fmt.Errorf("resp code not 200: %s", string(rspBody))
	}
	return resp.Data, nil
}
