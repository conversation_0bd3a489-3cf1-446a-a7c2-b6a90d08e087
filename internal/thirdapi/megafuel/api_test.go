package megafuel

import (
	"context"
	"fmt"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/node-real/megafuel-go-sdk/pkg/paymasterclient"
	"math/big"
	"testing"
)

var tokenContractAddress = common.HexToAddress("******************************************")
var recipientAddress = common.HexToAddress("******************************************")
var fromAddress = common.HexToAddress("******************************************")

func TestPaymaster_SendTransaction(t *testing.T) {
	parmaster, err := NewClient("https://bsc-megafuel-testnet.nodereal.io")
	if err != nil {
		t.Error(err)
	}
	gasLimit := uint64(300000)
	raw := "0xa9059cbb000000000000000000000000796fa99985003bb3868e15bb5a9e5c9be19e4f210000000000000000000000000000000000000000000000000de0b6b3a7640000"
	data := hexutil.MustDecode(raw)
	sponsorableTx := paymasterclient.TransactionArgs{
		To:    &tokenContractAddress,
		From:  fromAddress,
		Value: (*hexutil.Big)(big.NewInt(0)),
		Gas:   (*hexutil.Uint64)(&gasLimit),
		Data:  (*hexutil.Bytes)(&data),
	}
	sponsorableInfo, err := parmaster.paymasterClient.IsSponsorable(context.Background(), sponsorableTx)
	if err != nil {
		t.Error(err)
	}
	fmt.Println("sponsorableTx = ", sponsorableTx)
	fmt.Println("sponsorableInfo = ", sponsorableInfo)
	rawTx := "0xf8a60780830493e094337610d27c682e347c9cd60bd4b3b107c9d34ddd80b844a9059cbb000000000000000000000000796fa99985003bb3868e15bb5a9e5c9be19e4f210000000000000000000000000000000000000000000000000de0b6b3a764000081e5a0dcb3a8cb59c05501d133cddc68c0c6a12b5f4a7f6b5a99679e91c32fbc120afea02f09afe4fd9af6af8db6d8ac6e6b1453bc839abcde16399e54cd4cc21fb39091"
	txn, err := parmaster.SendTransaction(sponsorableTx, rawTx)
	if err != nil {
		t.Error(err)
	}
	t.Log(txn)
}
