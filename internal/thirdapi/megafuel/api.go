package megafuel

import (
	"byd_wallet/utils"
	"context"
	"fmt"

	"github.com/node-real/megafuel-go-sdk/pkg/paymasterclient"
)

type Paymaster struct {
	paymasterClient paymasterclient.Client
}

type Config struct {
	ApiUrl string `json:"api_url"`
}

func NewClient(apiUrl string) (*Paymaster, error) {
	paymasterClient, err := paymasterclient.New(context.Background(), apiUrl)
	if err != nil {
		return nil, err
	}
	return &Paymaster{
		paymasterClient: paymasterClient,
	}, nil
}

func (p *Paymaster) SendTransaction(transactionArgs paymasterclient.TransactionArgs, rawTx string) (string, error) {
	// Check if the transaction is sponsorable
	sponsorableInfo, err := p.paymasterClient.IsSponsorable(context.Background(), transactionArgs)
	if err != nil {
		return "", err
	}

	fmt.Printf("Sponsorable Information:\n%+v\n", sponsorableInfo)

	if !sponsorableInfo.Sponsorable {
		return "", fmt.Errorf("not sponsorable")
	}
	txInput, err := utils.GetRawTxBytes(rawTx)
	if err != nil {
		return "", err
	}
	txn, err := p.paymasterClient.SendRawTransaction(context.Background(), txInput, &paymasterclient.TransactionOptions{UserAgent: "BossWallet/v1.0.0"})
	if err != nil {
		return "", err
	}
	return txn.Hex(), nil

}
