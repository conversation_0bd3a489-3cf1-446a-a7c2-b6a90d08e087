package utils

import (
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
)

func GetTxSender(tx *types.Transaction) (common.Address, error) {
	chainId := tx.ChainId()
	if chainId == nil || chainId.Sign() <= 0 {
		return types.Sender(types.HomesteadSigner{}, tx)
	}

	signer := types.NewLondonSigner(chainId)
	from, err := types.Sender(signer, tx)
	if err == nil {
		return from, nil
	}

	return types.Sender(types.LatestSignerForChainID(chainId), tx)
}
