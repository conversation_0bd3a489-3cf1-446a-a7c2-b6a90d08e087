package utils

import (
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"github.com/btcsuite/btcd/btcec/v2"
	"github.com/btcsuite/btcd/chaincfg"
	"github.com/btcsuite/btcd/txscript"
	"github.com/btcsuite/btcutil"
	"testing"
)

func TestVerifySignature2(t *testing.T) {
	privateKey, err := btcec.NewPrivateKey()
	if err != nil {
		t.Error("error creating private key", err)
		return
	}
	publicKey := privateKey.PubKey()
	addr, err := GenerateBTcAddress(publicKey)
	fmt.Println("addr:", addr)
	message := "Hello, Bitcoin!"
	signBte, err := BtcSignMessage(privateKey, message)
	if err != nil {
		t.Error("error signing message", err)
	}
	//ok, err := BtcVerifySignature(publicKey, message, signBte)
	signStr := base64.StdEncoding.EncodeToString(signBte)
	ok, err := BtcVerifySignature(message, signStr, addr)
	if err != nil {
		t.Error("error verifying signature", err)
	}
	if !ok {
		t.Log("verify signature failed")
	}

	t.Log("verify signature ok")
}

func TestBtcVerifySignature(t *testing.T) {
	//privateKey, err := btcec.NewPrivateKey()
	//if err != nil {
	//	t.Error("error creating private key", err)
	//	return
	//}
	// 输入：hex 格式私钥（32 字节）
	//privHex := "a891d6c92fc05b167245b7442067a3f867747b6ee5941ceacdd600f6a1edb25a"
	privHex := "e1fc56f8bf8781e722229d096563195f3d766ff114d3010be79d318a8599f754"

	// 1. 解码 hex -> []byte
	privKeyBytes, err := hex.DecodeString(privHex)
	if err != nil {
		t.Fatalf("Failed to decode private key: %v", err)
	}

	// 2. 构造私钥对象
	privateKey, pubkey := btcec.PrivKeyFromBytes(privKeyBytes)
	fmt.Println("pubkey Key (Hex):", pubkey)

	// 2. 打印 Hex 格式私钥
	privateKeyHex := fmt.Sprintf("%x", privateKey.Serialize())

	fmt.Println("Private Key (Hex):", privateKeyHex)
	publicKey := privateKey.PubKey()
	// 公钥压缩形式
	pubKeyBytes := publicKey.SerializeCompressed()

	// 地址网络参数
	network := &chaincfg.MainNetParams

	// ------------------------------
	// P2PKH (Legacy): 1...
	pkHash := btcutil.Hash160(pubKeyBytes)
	addrP2PKH, err := btcutil.NewAddressPubKeyHash(pkHash, network)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println("P2PKH address (1...):", addrP2PKH.EncodeAddress())

	// ------------------------------
	// P2SH-P2WPKH (Nested SegWit): 3...
	witnessProg := pkHash
	p2wpkhAddr, err := btcutil.NewAddressWitnessPubKeyHash(witnessProg, network)
	if err != nil {
		t.Fatal(err)
	}
	script, err := txscript.PayToAddrScript(p2wpkhAddr)
	if err != nil {
		t.Fatal(err)
	}
	addrP2SH, err := btcutil.NewAddressScriptHash(script, network)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println("P2SH-P2WPKH address (3...):", addrP2SH.EncodeAddress())

	// ------------------------------
	// P2WPKH (Native SegWit): bc1q...
	fmt.Println("P2WPKH address (bc1q...):", p2wpkhAddr.EncodeAddress())

	// ------------------------------
	// P2TR (Taproot): bc1p...
	// Taproot 需要 x-only 公钥（32字节）
	// version 1 + xonly pubkey = witness program
	//taprootKey := schnorr.SerializePubKey(publicKey)
	//witnessProg1 := append([]byte{0x01}, taprootKey...)

	// Bech32m 编码
	//hrp := "bc"
	//addr, err := bech32.Encode(hrp, witnessProg1)
	//if err != nil {
	//	t.Fatal(err)
	//}
	//
	//fmt.Println("Taproot (P2TR) Address:", addr)
	//taprootKey := schnorr.SerializePubKey(publicKey) // 32 bytes x-only format
	//addrTaproot, err := btcutil.NewAddressTaproot(taprootKey, network)
	//if err != nil {
	//	t.Fatal(err)
	//}
	//fmt.Println("P2TR address (bc1p...):", addrTaproot.EncodeAddress())
	//
	//addr, err := GenerateBTcAddress(publicKey)
	//fmt.Println("addr:", addr)
	fmt.Println("SerializeCompressed:", publicKey.SerializeCompressed())
	fmt.Println("SerializeUncompressed:", publicKey.SerializeUncompressed())

	message := "[{\"address\":\"**********************************\",\"chainIndex\":0}]"
	signBte, err := BtcSignMessage(privateKey, message)
	if err != nil {
		t.Error("error signing message", err)
	}
	//ok, err := BtcVerifySignature(publicKey, message, signBte)
	signStr := base64.StdEncoding.EncodeToString(signBte)
	fmt.Println("signStr:", signStr)
	ok, err := BtcVerifySignature(message, signStr, addrP2PKH.EncodeAddress())
	if err != nil {
		t.Error("error verifying signature", err)
	}
	if !ok {
		t.Log("verify signature failed")
	}

	t.Log("verify signature ok")
}

func TestBtcVerifySignature1(t *testing.T) {
	message := "[{\"address\":\"**********************************\",\"chainIndex\":0}]"
	//ok, err := BtcVerifySignature(publicKey, message, sinBte)
	signStr := `IKDD9LqR5\/d2qxCzCVI7t3leF0pX5v\/IBDOVLP8b2cspFIgsgwlZlT1W8h7UwrOC9IHPUNDAuRpo7K2An4w8mW0=`
	fmt.Println("signStr:", signStr)
	ok, err := BtcVerifySignature(message, signStr, "**********************************")
	if err != nil {
		t.Error("error verifying signature", err)
	}
	if !ok {
		t.Log("verify signature failed")
	}

	t.Log("verify signature ok")
}

func TestBtcVerifySignatureMessge(t *testing.T) {
	//privHex := "a891d6c92fc05b167245b7442067a3f867747b6ee5941ceacdd600f6a1edb25a"
	privHex := "e1fc56f8bf8781e722229d096563195f3d766ff114d3010be79d318a8599f754"

	// 1. 解码 hex -> []byte
	privKeyBytes, err := hex.DecodeString(privHex)
	if err != nil {
		t.Fatalf("Failed to decode private key: %v", err)
	}

	// 2. 构造私钥对象
	privateKey, pubkey := btcec.PrivKeyFromBytes(privKeyBytes)
	fmt.Println("pubkey Key (Hex):", pubkey)

	// 2. 打印 Hex 格式私钥
	privateKeyHex := fmt.Sprintf("%x", privateKey.Serialize())

	fmt.Println("Private Key (Hex):", privateKeyHex)
	publicKey := privateKey.PubKey()
	// 公钥压缩形式
	pubKeyBytes := publicKey.SerializeCompressed()

	// 地址网络参数
	network := &chaincfg.MainNetParams

	// ------------------------------
	// P2PKH (Legacy): 1...
	pkHash := btcutil.Hash160(pubKeyBytes)
	addrP2PKH, err := btcutil.NewAddressPubKeyHash(pkHash, network)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println("P2PKH address (1...):", addrP2PKH.EncodeAddress())
	compressedPubKeyHex := hex.EncodeToString(pubKeyBytes)
	fmt.Println("compressedPubKeyHex = ", compressedPubKeyHex)
	// ------------------------------
	// P2SH-P2WPKH (Nested SegWit): 3...
	witnessProg := pkHash
	p2wpkhAddr, err := btcutil.NewAddressWitnessPubKeyHash(witnessProg, network)
	if err != nil {
		t.Fatal(err)
	}
	script, err := txscript.PayToAddrScript(p2wpkhAddr)
	if err != nil {
		t.Fatal(err)
	}
	addrP2SH, err := btcutil.NewAddressScriptHash(script, network)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println("P2SH-P2WPKH address (3...):", addrP2SH.EncodeAddress())

	// ------------------------------
	// P2WPKH (Native SegWit): bc1q...
	fmt.Println("P2WPKH address (bc1q...):", p2wpkhAddr.EncodeAddress())

	message := "[{\"address\":\"**********************************\",\"chainIndex\":0}]"
	signBte, err := BtcSignMessage(privateKey, message)
	if err != nil {
		t.Error("error signing message", err)
	}
	//ok, err := BtcVerifySignature(publicKey, message, signBte)
	signStr := base64.StdEncoding.EncodeToString(signBte)
	fmt.Println("signStr:", signStr)
	ok, err := BtcVerifySignature(message, signStr, compressedPubKeyHex)
	if err != nil {
		t.Error("error verifying signature", err)
	}
	if !ok {
		t.Log("verify signature failed")
	}

	t.Log("verify signature ok")
}

func TestBtcPubKeyToSegWitAddress(t *testing.T) {
	compressedPubKey := "02a325bc05949b6eb0676929d49b0f56fba34bb885653e74ad4741b23cf0de82d6"
	segWitAddr, err := PubKeyToSegWitAddress(compressedPubKey)
	if err != nil {
		panic(err)
	}
	fmt.Println("SegWit (Bech32) Address:", segWitAddr)
}
