package utils

import (
	"crypto/ecdsa"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"strings"

	"github.com/btcsuite/btcutil/base58"
	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/ethereum/go-ethereum/crypto"
	"golang.org/x/crypto/sha3"
)

var PrefixStr = "\x19TRON Signed Message:\n%d%s"
var PrefixStrV1 = "\x19TRON Signed Message:\n32%s"

// TronAddressFromPubKey converts an uncompressed public key to TRON address
func TronAddressFromPubKey(pub *ecdsa.PublicKey) string {
	pubBytes := crypto.FromECDSAPub(pub)[1:] // remove 0x04 prefix
	hash := sha3.NewLegacyKeccak256()
	hash.Write(pubBytes)
	address := hash.Sum(nil)[12:]
	return "T" + hex.EncodeToString(address)
}

//// TronMessageHash returns the Keccak256 hash of the TRON-prefixed message
//func TronMessageHash(message string) []byte {
//	prefix := fmt.Sprintf("\x19TRON Signed Message:\n%d%s", len(message), message)
//	return crypto.Keccak256([]byte(prefix))
//}
//
//// TronSignMessage signs the message using the given private key (TRON format)
//func TronSignMessage(privateKey *ecdsa.PrivateKey, message string) (string, error) {
//	hash := TronMessageHash(message)
//	signature, err := crypto.Sign(hash, privateKey)
//	if err != nil {
//		return "", err
//	}
//	// Fix V: Ethereum uses [0,1], TRON clients use [27,28]
//	signature[64] += 27
//	return hex.EncodeToString(signature), nil
//}

// TRON签名哈希（带正确前缀）
func TronMessageHash(message string) []byte {
	prefix := fmt.Sprintf(PrefixStr, len(message), message)
	return crypto.Keccak256([]byte(prefix))
}
func TronMessageHashV1(message string) []byte {
	prefix := fmt.Sprintf(PrefixStrV1, message)
	hashData := crypto.Keccak256Hash([]byte(prefix))
	return hashData.Bytes()
}

func TronSignMessageV1(privateKey *ecdsa.PrivateKey, message string) (string, error) {
	hash := TronMessageHashV1(message)
	sign, err := crypto.Sign(hash, privateKey)
	if err != nil {
		return "", err
	}

	// https://stackoverflow.com/a/69771013
	sign[64] += 27
	return hexutil.Encode(sign), nil
}

// TRON签名函数
func TronSignMessage(privateKey *ecdsa.PrivateKey, message string) (string, error) {
	hash := TronMessageHash(message)
	sig, err := crypto.Sign(hash, privateKey)
	if err != nil {
		return "", err
	}
	sig[64] += 27 // Ethereum [0,1] → TRON [27,28]
	return hex.EncodeToString(sig), nil
}

// TronVerifySignature verifies the signature against the message and expected address
func TronVerifySignature(message string, signatureHex string, expectedAddress string) (bool, error) {
	hash := TronMessageHash(message)
	sig, err := hex.DecodeString(strings.TrimPrefix(signatureHex, "0x"))
	if err != nil {
		return false, fmt.Errorf("decode sig: %v", err)
	}
	if len(sig) != 65 {
		return false, fmt.Errorf("signature length must be 65 bytes")
	}
	// Fix V
	if sig[64] >= 27 {
		sig[64] -= 27
	}

	pubKey, err := crypto.SigToPub(hash, sig)
	if err != nil {
		return false, fmt.Errorf("recover pubkey: %v", err)
	}
	addr1 := TronHexAddressFromPubKey(pubKey)
	addr := Base58CheckEncodeTRON(addr1)
	fmt.Println(addr)
	isValid := strings.EqualFold(addr, expectedAddress)
	return isValid, nil
}

func TronVerifySignatureV1(message string, signatureHex string, expectedAddress string) (bool, error) {
	hash := TronMessageHashV1(message)
	sig, err := hex.DecodeString(strings.TrimPrefix(signatureHex, "0x"))
	if err != nil {
		return false, fmt.Errorf("decode sig: %v", err)
	}
	if len(sig) != 65 {
		return false, fmt.Errorf("signature length must be 65 bytes")
	}
	// Fix V
	if sig[64] >= 27 {
		sig[64] -= 27
	}

	pubKey, err := crypto.SigToPub(hash, sig)
	if err != nil {
		return false, fmt.Errorf("recover pubkey: %v", err)
	}
	addr1 := TronHexAddressFromPubKey(pubKey)
	addr := Base58CheckEncodeTRON(addr1)
	fmt.Println(addr)
	isValid := strings.EqualFold(addr, expectedAddress)
	return isValid, nil
}

// 从公钥生成 TRON 地址（hex 格式，以 41 开头）
func TronHexAddressFromPubKey(pub *ecdsa.PublicKey) []byte {
	pubBytes := crypto.FromECDSAPub(pub)[1:] // 去掉 0x04 前缀
	hash := sha3.NewLegacyKeccak256()
	hash.Write(pubBytes)
	addr := hash.Sum(nil)[12:]           // 最后 20 字节
	return append([]byte{0x41}, addr...) // 0x41 前缀
}

// 计算 Base58Check 编码
func Base58CheckEncode(input []byte) string {
	// 计算 double SHA256 校验和（前4字节）
	hash := sha3.NewLegacyKeccak256()
	hash.Write(input)
	checksum := hash.Sum(nil)

	hash2 := sha3.NewLegacyKeccak256()
	hash2.Write(checksum)
	checksum = hash2.Sum(nil)

	full := append(input, checksum[:4]...)
	return base58.Encode(full)
}

func Base58CheckEncodeTRON(input []byte) string {
	// 计算 double SHA256 校验和（前4字节）
	first := sha256.Sum256(input)
	second := sha256.Sum256(first[:])
	checksum := second[:4]

	full := append(input, checksum...)
	return base58.Encode(full)
}
