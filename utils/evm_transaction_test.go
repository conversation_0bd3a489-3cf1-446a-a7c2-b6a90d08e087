package utils

import (
	"encoding/hex"
	"testing"
)

func TestRlpDecodeBytes(t *testing.T) {
	rawTx := "0xf8a60780830493e094337610d27c682e347c9cd60bd4b3b107c9d34ddd80b844a9059cbb000000000000000000000000796fa99985003bb3868e15bb5a9e5c9be19e4f210000000000000000000000000000000000000000000000000de0b6b3a764000081e5a0dcb3a8cb59c05501d133cddc68c0c6a12b5f4a7f6b5a99679e91c32fbc120afea02f09afe4fd9af6af8db6d8ac6e6b1453bc839abcde16399e54cd4cc21fb39091"
	tx, err := RlpDecodeBytes(rawTx)
	if err != nil {
		t.<PERSON>rror(err.<PERSON>rror())
	}
	t.Log("tx = ", tx)
	if tx == nil {
		t.Fatal()
	}
	t.Log("ChainId = ", tx.ChainId())
	t.Log("Value = ", tx.Value())
	t.Log("Data = ", hex.EncodeToString(tx.Data()))
	t.Log("Gas = ", tx.Gas())
	t.Log("GasPrice = ", tx.GasPrice())
	t.Log("To = ", tx.To())
	t.Logf("Nonce: %d", tx.Nonce())
	from, err := GetTxSender(tx)
	if err != nil {
		t.Fatal("GetSenderFromTransaction err ", err.<PERSON><PERSON><PERSON>())
	}
	t.Log("from", from)

}
