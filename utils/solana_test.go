package utils

import (
	"crypto/ed25519"
	"fmt"
	"github.com/gagliardetto/solana-go"
	"github.com/mr-tron/base58"
	"testing"
)

func TestVerifySignatureWithAddress(t *testing.T) {
	// 1. 生成 Solana 密钥对
	privateKey, err := solana.NewRandomPrivateKey()
	if err != nil {
		panic(err)
	}
	publicKey := privateKey.PublicKey()
	address := publicKey.String() // 获取地址（Base58 编码）

	fmt.Println("Private Key:", privateKey)
	fmt.Println("Public Key (Address):", address)

	// 2. 签名消息
	message := "Hello, Solana!"
	//message := []byte("Hello, Solana!")
	privateKeyBytes := privateKey[:] // 或者 privateKey.Bytes()

	// 转换为 ed25519.PrivateKey 类型
	ed25519PrivateKey := ed25519.PrivateKey(privateKeyBytes)

	// 使用标准库签名
	signature := ed25519.Sign(ed25519PrivateKey, []byte(message))

	fmt.Println("Signature:", solana.SignatureFromBytes(signature))
	signatureBase58 := base58.Encode(signature)
	fmt.Println("Base58 Signature:", signatureBase58)
	// 3. 通过地址验证签名
	valid, err := SolVerifySignature(message, signatureBase58, address)
	if err != nil {
		panic(err)
	}

	fmt.Println("Signature Valid:", valid) // 输出 true
}
