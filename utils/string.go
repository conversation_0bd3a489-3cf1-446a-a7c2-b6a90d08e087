package utils

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"math/big"
	"strings"
)

const charset = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ" // 可用字符集
func GenerateRandomString(minLen, maxLen int) (string, error) {
	// 计算随机长度（minLen ≤ length ≤ maxLen）
	lengthRange := big.NewInt(int64(maxLen - minLen + 1))
	lengthOffset, err := rand.Int(rand.Reader, lengthRange)
	if err != nil {
		return "", err
	}
	length := minLen + int(lengthOffset.Int64())

	// 生成随机字符串
	result := make([]byte, length)
	charsetLen := big.NewInt(int64(len(charset)))

	for i := 0; i < length; i++ {
		index, err := rand.Int(rand.Reader, charsetLen)
		if err != nil {
			return "", err
		}
		result[i] = charset[index.Int64()]
	}

	return string(result), nil
}

func ParseAddresses(csv string) []string {
	parts := strings.Split(csv, ",")
	for i := range parts {
		parts[i] = strings.TrimSpace(parts[i])
	}
	return parts
}

func GetRawTxBytes(rawTxHex string) ([]byte, error) {
	// 如果以 0x 或 0X 开头，去掉前缀
	rawTxHex = StripHexPrefix(rawTxHex)

	// 解码 hex -> []byte
	rawTxBytes, err := hex.DecodeString(rawTxHex)
	if err != nil {
		return nil, fmt.Errorf("decode hex error: %w", err)
	}
	return rawTxBytes, nil
}

func StripHexPrefix(hexStr string) string {
	if strings.HasPrefix(strings.ToLower(hexStr), "0x") {
		return hexStr[2:]
	}
	return hexStr
}
