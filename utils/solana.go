package utils

import (
	"crypto/ed25519"
	"errors"
	"fmt"
	bin "github.com/gagliardetto/binary"
	"github.com/gagliardetto/solana-go"
	"github.com/gagliardetto/solana-go/programs/token"
	"github.com/mr-tron/base58"
)

func GetSolTokenAccountOwner(data []byte, base58Addr string) (string, error) {
	if len(data) == 0 {
		return base58Addr, nil
	}
	acc, err := GetSolTokenAccount(data)
	if err != nil {
		return base58Addr, err
	}
	return acc.Owner.String(), nil
}

func GetSolTokenAccount(data []byte) (token.Account, error) {
	var tokenAccount token.Account
	if err := bin.NewBinDecoder(data).Decode(&tokenAccount); err != nil {
		return token.Account{}, fmt.Errorf("token account decoding failed: %w", err)
	}
	return tokenAccount, nil
}

func SolVerifySignature(message string, signatureStr string, addressStr string) (bool, error) {
	// 1. 将 Solana 地址字符串解析为 PublicKey
	publicKey, err := solana.PublicKeyFromBase58(addressStr)
	if err != nil {
		return false, fmt.Errorf("invalid Solana address: %v", err)
	}
	signature, err := base58.Decode(signatureStr)
	if err != nil {
		return false, fmt.Errorf("invalid signature: %v", err)
	}
	// 2. 检查签名长度（ed25519 签名必须是64字节）
	if len(signature) != ed25519.SignatureSize {
		return false, fmt.Errorf("invalid signature length")
	}
	// 将 solana.PublicKey 转换为 []byte 再转为 ed25519.PublicKey
	pubKeyBytes := publicKey[:] // 或者 publicKey.Bytes()
	ed25519PubKey := ed25519.PublicKey(pubKeyBytes)

	// 验证签名
	valid := ed25519.Verify(ed25519PubKey, []byte(message), signature)
	return valid, nil
}

func DecodeRawTx(rawTx string) (transaction *solana.Transaction, err error) {
	if rawTx == "" {
		return &solana.Transaction{}, errors.New("raw tx is not empty")
	}
	var msg solana.Message
	err = msg.UnmarshalBase64(rawTx)
	if err != nil {
		return &solana.Transaction{}, err
	}
	transaction = &solana.Transaction{}
	transaction.Message = msg
	return
}
