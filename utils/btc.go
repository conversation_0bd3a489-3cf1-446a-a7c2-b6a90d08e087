package utils

import (
	"crypto/sha256"
	"encoding/base64"
	"encoding/binary"
	"encoding/hex"
	"errors"
	"golang.org/x/crypto/ripemd160"

	"github.com/btcsuite/btcd/btcec/v2"
	"github.com/btcsuite/btcd/btcec/v2/ecdsa"
	"github.com/btcsuite/btcd/chaincfg"
	"github.com/btcsuite/btcutil"
)

const bitcoinMessageHeader = "\x18Bitcoin Signed Message:\n"

// BtcSignMessage 签名比特币标准消息
func BtcSignMessage(privateKey *btcec.PrivateKey, message string) ([]byte, error) {
	messageHash := hashBitcoinMessage(message)
	return ecdsa.SignCompact(privateKey, messageHash[:], true), nil
}

// BtcVerifySignature 验证比特币标准签名
func BtcVerifySignature(message, sigBase64, compressedPubKeyHex string) (bool, error) {
	signature, err := base64.StdEncoding.DecodeString(sigBase64)
	if err != nil {
		return false, err
	}
	if len(signature) != 65 {
		return false, errors.New("signature must be 65 bytes")
	}

	messageHash := hashBitcoinMessage(message)
	pubKey, _, err := ecdsa.RecoverCompact(signature, messageHash[:])
	if err != nil {
		return false, err
	}
	// 1. 解码十六进制公钥
	pubKeyBytes, err := hex.DecodeString(RemoveHexPrefix(compressedPubKeyHex))
	if err != nil {
		return false, err
	}
	// 2. 解析公钥
	pubKey1, err := btcec.ParsePubKey(pubKeyBytes)
	if err != nil {
		return false, err
	}

	if !pubKey.IsEqual(pubKey1) {
		return false, nil
	}
	return true, nil
}

// hashBitcoinMessage 构造比特币消息并双重SHA256
func hashBitcoinMessage(message string) [32]byte {
	msgBytes := []byte(message)
	varInt := encodeVarInt(len(msgBytes))

	prefixed := append([]byte(bitcoinMessageHeader), append(varInt, msgBytes...)...)
	return doubleSHA256(prefixed)
}

// encodeVarInt 编码比特币的 VarInt 格式（这里只处理常用范围）
func encodeVarInt(n int) []byte {
	if n < 0xfd {
		return []byte{byte(n)}
	}
	buf := make([]byte, binary.MaxVarintLen64)
	size := binary.PutUvarint(buf, uint64(n))
	return buf[:size]
}

// doubleSHA256 双重哈希
func doubleSHA256(data []byte) [32]byte {
	first := sha256.Sum256(data)
	return sha256.Sum256(first[:])
}

// publicKeyToAddress 公钥转P2PKH地址
func publicKeyToAddress(pubKey *btcec.PublicKey) (string, error) {
	pkHash := btcutil.Hash160(pubKey.SerializeCompressed())
	addr, err := btcutil.NewAddressPubKeyHash(pkHash, &chaincfg.MainNetParams)
	if err != nil {
		return "", err
	}
	return addr.EncodeAddress(), nil
}

// GenerateBTcAddress 公钥生成BTC地址
func GenerateBTcAddress(pubKey *btcec.PublicKey) (string, error) {
	return publicKeyToAddress(pubKey)
}

// PubKeyToSegWitAddress 将压缩公钥转换为 SegWit (Bech32) 地址
func PubKeyToSegWitAddress(compressedPubKeyHex string) (string, error) {
	// 1. 解码十六进制公钥
	pubKeyBytes, err := hex.DecodeString(RemoveHexPrefix(compressedPubKeyHex))
	if err != nil {
		return "", err
	}

	// 2. 解析公钥
	pubKey, err := btcec.ParsePubKey(pubKeyBytes)
	if err != nil {
		return "", err
	}
	return BtcPublicToSegWitAddress(pubKey.SerializeCompressed())
}
func BtcPublicToSegWitAddress(pubKey []byte) (string, error) {
	sha256Hash := sha256.Sum256(pubKey)
	hasher := ripemd160.New()
	hasher.Write(sha256Hash[:])
	witnessProgram := hasher.Sum(nil)

	// 4. 生成 SegWit v0 (P2WPKH) 地址
	segWitAddr, err := btcutil.NewAddressWitnessPubKeyHash(witnessProgram, &chaincfg.MainNetParams)
	if err != nil {
		return "", err
	}

	// 5. 返回 Bech32 格式地址
	return segWitAddr.EncodeAddress(), nil
}
