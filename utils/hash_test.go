package utils

import "testing"

func TestVerifySignature(t *testing.T) {
	msg := "123456"
	sign := "0xc28bc4c7f41c5e8ffa0124608656282ea1600cc895662020dc5f963a60ee1aba6a5734dcda6829248810f6d1d02f03d7119f0d874fd2ec81fa5c24f2d3d4eabc1c"
	addr := "0x2A08E2c9A48147b37560f8738B8450dD5F678698"
	ok, err := VerifySignature(msg, sign, addr)
	if err != nil {
		t.<PERSON>rror(err)
	}
	t.Log(ok)
}

func TestVerifySignatureRegister(t *testing.T) {
	msg := "[{\"chainIndex\":60,\"address\":\"0x2A08E2c9A48147b37560f8738B8450dD5F678698\"}]"

	sign := "0xaf193afccee58088fae8933a28b0ca881c3248dd7caeb72372d8567f420b95247ba6a532749173ac9e639cf10f80d5cb073ced7d8c41caa42524455815a7d32f1b"
	addr := "0x2A08E2c9A48147b37560f8738B8450dD5F678698"
	ok, err := VerifySignature(msg, sign, addr)
	if err != nil {
		t.Error(err)
	}
	t.<PERSON>g(ok)
}

func TestMD5Hash(t *testing.T) {
	msg := "123456444444213444"
	t.Log(MD5Hash(msg))
}
