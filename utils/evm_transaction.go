package utils

import (
	"errors"
	"fmt"
	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/rlp"
)

func RlpDecodeBytes(rawTx string) (*types.Transaction, error) {
	if rawTx == "" {
		return nil, errors.New("empty raw transaction string")
	}
	txData, err := hexutil.Decode(rawTx)
	if err != nil {
		return nil, fmt.Errorf("failed to decode hex string: %v", err)
	}
	// 解码RLP数据
	var tx types.Transaction
	if err := rlp.DecodeBytes(txData, &tx); err != nil {
		return nil, fmt.Errorf("rlp decoding failed: %v", err)
	}

	return &tx, nil
}
