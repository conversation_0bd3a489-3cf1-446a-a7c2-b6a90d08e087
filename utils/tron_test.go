package utils

import (
	"crypto/ecdsa"
	"encoding/hex"
	"fmt"
	"testing"

	"github.com/ethereum/go-ethereum/crypto"
)

func TestTronVerifySignature(t *testing.T) {
	//privateKeyHex := "c55967c591d53c80b3ebe19c6f87a70e6067f37eb8d8fd049800f5d3ca979263"
	privateKeyHex := "aec10fde2b926fb261ca03d54ca031010a7c6a16b67589916b5efe24b2186d10"

	// 将十六进制字符串转为 ECDSA 私钥
	privKeyBytes, err := hex.DecodeString(privateKeyHex)
	if err != nil {
		t.Fatalf("Invalid private key hex: %v", err)
	}
	privateKey, err := crypto.ToECDSA(privKeyBytes)
	if err != nil {
		t.Fatalf("Failed to parse private key: %v", err)
	}
	// 1. 创建私钥
	//privateKey, err := crypto.GenerateKey()
	//if err != nil {
	//	t.Fatal("Failed to generate key:", err)
	//}
	publicKey := privateKey.Public().(*ecdsa.PublicKey)
	tronAddressByte := TronHexAddressFromPubKey(publicKey)
	tronAddress := Base58CheckEncodeTRON(tronAddressByte)
	fmt.Println("TRON Address:", tronAddress)
	//privateKeyBytes := crypto.FromECDSA(privateKey)
	//privateKeyHex := hex.EncodeToString(privateKeyBytes)
	fmt.Println("Private Key (hex):", privateKeyHex)

	// 2. 签名消息
	message := "[{\"address\":\"TWeoEXnt48JpZV43dWP1EgU9jKs7ZNqLW2\",\"chainIndex\":195}]"
	fmt.Printf("Hash to sign (hex): %x\n", TronMessageHash(message))
	fmt.Printf("sign v1 =(hex): %x\n ", TronMessageHashV1(message))
	signatureHex, err := TronSignMessageV1(privateKey, message)
	if err != nil {
		t.Fatal("Failed to sign message:", err)
	}
	fmt.Println("Signature:", signatureHex)
	// 3. 验证签名
	valid, err := TronVerifySignatureV1(message, signatureHex, tronAddress)
	if err != nil {
		t.Fatal("Verification failed:", err)
	}

	if valid {
		fmt.Println("✅ Signature is valid.")
	} else {
		fmt.Println("❌ Signature is invalid.")
	}
}

func TestTroVerifySignature(t *testing.T) {
	// ✅ 示例：已有私钥（64 位 hex）
	privateKeyHex := "c55967c591d53c80b3ebe19c6f87a70e6067f37eb8d8fd049800f5d3ca979263"

	// 将十六进制字符串转为 ECDSA 私钥
	privKeyBytes, err := hex.DecodeString(privateKeyHex)
	if err != nil {
		t.Fatalf("Invalid private key hex: %v", err)
	}
	privateKey, err := crypto.ToECDSA(privKeyBytes)
	if err != nil {
		t.Fatalf("Failed to parse private key: %v", err)
	}

	publicKey := privateKey.Public().(*ecdsa.PublicKey)
	tronAddress1 := TronAddressFromPubKey(publicKey)

	fmt.Println("TRON Address1:", tronAddress1)
	message := "[{\"address\":\"TWeoEXnt48JpZV43dWP1EgU9jKs7ZNqLW2\",\"chainIndex\":195}]"
	signatureHex := "404b4db6e2568d85a93014052dd8141ea194130067c7a99a4bc4ac156075e4a522a3af01d15450c3870d41399a56981682700f6e679a805d90d8e97a646aff001b"
	tronAddress := "TWeoEXnt48JpZV43dWP1EgU9jKs7ZNqLW2"
	valid, err := TronVerifySignature(message, signatureHex, tronAddress)
	if err != nil {
		t.Fatal("Verification failed:", err)
	}
	t.Log("Signature:", valid)
}
