package utils

import (
	"crypto/ecdsa"
	"errors"
	"fmt"
	"strings"

	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/ethereum/go-ethereum/crypto"
)

func GetAddressByPrivateKey(key string) (common.Address, *ecdsa.PrivateKey, error) {
	privateKey, err := crypto.HexToECDSA(strings.TrimPrefix(key, "0x"))
	if err != nil {
		return common.Address{}, nil, fmt.Errorf("invalid private key: %w", err)
	}

	publicKeyECDSA, ok := privateKey.Public().(*ecdsa.PublicKey)
	if !ok {
		return common.Address{}, nil, errors.New("failed to get ECDSA public key from private key")
	}

	return crypto.PubkeyToAddress(*publicKeyECDSA), privateKey, nil
}

// Get<PERSON>bi parses a JSON-encoded ABI definition and returns an ABI object.
func GetAbi(tokenAbi string) (abi.ABI, error) {
	contractAbi, err := abi.JSON(strings.NewReader(tokenAbi))
	if err != nil {
		return abi.ABI{}, fmt.Errorf("failed to parse ABI: %w", err)
	}
	return contractAbi, nil
}

// GenerateAddress generates a new Ethereum key pair and returns the address
// and hex-encoded private key.
func GenerateAddress() (string, string, error) {
	privateKey, err := crypto.GenerateKey()
	if err != nil {
		return "", "", fmt.Errorf("failed to generate key: %w", err)
	}
	privateKeyHex := hexutil.Encode(crypto.FromECDSA(privateKey))
	privateKeyHex = strings.TrimPrefix(privateKeyHex, "0x")
	publicKeyECDSA, ok := privateKey.Public().(*ecdsa.PublicKey)
	if !ok {
		return "", "", errors.New("failed to get ECDSA public key")
	}
	address := strings.ToLower(crypto.PubkeyToAddress(*publicKeyECDSA).Hex())
	return address, privateKeyHex, nil
}
