package utils

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"strings"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/crypto"
	"golang.org/x/crypto/bcrypt"
)

// BcryptHash 使用 bcrypt 对密码进行加密
func BcryptHash(password string) string {
	bytes, _ := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	return string(bytes)
}

// BcryptCheck 对比明文密码和数据库的哈希值
func BcryptCheck(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

//@function: MD5V
//@description: md5加密
//@param: str []byte
//@return: string

func MD5V(str []byte, b ...byte) string {
	h := md5.New()
	h.Write(str)
	return hex.EncodeToString(h.Sum(b))
}

// 校验签名是否匹配地址
func VerifySignature(message, sigHex string, expectedAddress string) (bool, error) {
	sigHex = strings.TrimPrefix(sigHex, "0x")
	// 1. 以太坊标准签名消息格式
	prefixedMsg := fmt.Sprintf("\x19Ethereum Signed Message:\n%d%s", len(message), message)

	// 2. Hash 处理
	hash := crypto.Keccak256Hash([]byte(prefixedMsg))

	// 3. 解码签名
	sig := common.FromHex(sigHex)
	if len(sig) != 65 {
		return false, fmt.Errorf("invalid signature length: %d", len(sig))
	}
	// 4. v 值修正（如果小于27则加27）
	if sig[64] == 27 || sig[64] == 28 {
		sig[64] -= 27
	}

	// 5. 从签名中恢复公钥
	pubKey, err := crypto.SigToPub(hash.Bytes(), sig)
	if err != nil {
		return false, fmt.Errorf("failed to recover public key: %w", err)
	}

	// 6. 公钥转地址
	recoveredAddr := crypto.PubkeyToAddress(*pubKey).Hex()

	// 7. 校验地址是否一致
	return strings.EqualFold(recoveredAddr, expectedAddress), nil
}

func MD5Hash(msg string) string {
	h := md5.New()
	h.Write([]byte(msg))
	return hex.EncodeToString(h.Sum(nil))
}
