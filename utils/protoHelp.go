/*
 * Copyright © 2023 Xbit Protocol
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package utils

import (
	"encoding/hex"
	"encoding/json"
	"errors"
	"github.com/ethereum/go-ethereum/crypto"
)

func ForceJsonMarshal(s interface{}) ([]byte, error) {
	if s == nil {
		return nil, errors.New("nil pointer")
	}
	var (
		data []byte
		err  error
	)
	data, err = json.Marshal(s)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func ForceJsonUnmarshal[T any](s []byte, data T) (T, error) {
	if len(s) == 0 {
		return data, nil
	}
	if string(s) == "null" || string(s) == "<null>" {
		return data, nil
	}
	err := json.Unmarshal(s, &data)
	if err != nil {
		return data, err
	}
	return data, nil
}

func PackedBytesToHash(packed []byte) (string, error) {
	// Calculate keccak256
	hash := crypto.Keccak256(packed)
	var result [32]byte
	copy(result[:], hash)
	return hex.EncodeToString(result[:]), nil
}
