package utils

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"errors"
	"io"
)

func EncryptWithAESGCM(plainText []byte, key []byte) (string, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}
	aesGCM, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}
	nonce := make([]byte, aesGCM.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", err
	}

	cipherText := aesGCM.Seal(nonce, nonce, plainText, nil)
	return base64.StdEncoding.EncodeToString(cipherText), nil
}

func DecryptWithAESGCM(encoded string, keyStr string) ([]byte, error) {
	key, err := decodeAESKeyBase64(keyStr)
	if err != nil {
		return nil, err
	}
	cipherText, err := base64.StdEncoding.DecodeString(encoded)
	if err != nil {
		return nil, err
	}

	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	aesGCM, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}
	nonceSize := aesGCM.NonceSize()
	if len(cipherText) < nonceSize {
		return nil, errors.New("ciphertext too short")
	}
	nonce, cipherData := cipherText[:nonceSize], cipherText[nonceSize:]
	return aesGCM.Open(nil, nonce, cipherData, nil)
}

func generateAESKey(n int) ([]byte, int, error) {
	b := make([]byte, n) // AES-256 -> 32 字节
	if _, err := rand.Read(b); err != nil {
		return b, n, err
	}
	return b, n, nil
}

func generateAESKeyBase64(n int) (string, error) {
	key, _, err := generateAESKey(n)
	if err != nil {
		return "", err
	}
	return base64.StdEncoding.EncodeToString(key), nil
}

func decodeAESKeyBase64(keyStr string) ([]byte, error) {
	return base64.StdEncoding.DecodeString(keyStr)
}
