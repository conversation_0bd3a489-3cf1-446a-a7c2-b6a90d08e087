package model

import (
	"errors"
	"fmt"
	"testing"
	"time"
)

func TestError(t *testing.T) {
	var err error
	defer func() {
		if err != nil {
			fmt.Println("error:", err)
		}
	}()
	i, err := testErr()
	fmt.Println(i)
}

func testErr() (int, error) {
	return 1, errors.New("test error")
}

func TestDapp_S3ObjectKey(t *testing.T) {
	tests := []struct {
		name string
		dapp Dapp
		want string
	}{
		{
			name: "empty logo",
			dapp: Dapp{Logo: ""},
			want: "",
		},
		{
			name: "valid logo URL",
			dapp: Dapp{Logo: "https://example.com/path/to/image.png"},
			want: "path/to/image.png",
		},
		{
			name: "logo URL with S3 bucket-like prefix",
			dapp: Dapp{Logo: "https://s3.amazonaws.com/bucket-name/path/to/image.jpg"},
			want: "bucket-name/path/to/image.jpg",
		},
		{
			name: "invalid logo URL",
			dapp: Dapp{Logo: "://invalid-url"},
			want: "",
		},
		{
			name: "logo URL without path",
			dapp: Dapp{Logo: "https://example.com"},
			want: "",
		},
		{
			name: "logo URL with only slash path",
			dapp: Dapp{Logo: "https://example.com/"},
			want: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.dapp.S3ObjectKey(); got != tt.want {
				t.Errorf("S3ObjectKey() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDapp_Copy(t *testing.T) {
	tests := []struct {
		name string
		dapp Dapp
	}{
		{
			name: "basic copy",
			dapp: Dapp{
				BaseModelNoDeleted: BaseModelNoDeleted{
					ID:        123,
					CreatedAt: time.Now(),
					UpdatedAt: time.Now(),
				},
				Logo: "https://example.com/logo.png",
				Link: "https://example.com",
				Tags: "tag1,tag2",
				Hot:  true,
				Show: true,
				DappI18Ns: []*DappI18N{
					{
						Name:     "Test Dapp",
						Summary:  "Test Summary",
						Language: "en",
					},
				},
			},
		},
		{
			name: "empty dapp",
			dapp: Dapp{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 使用当前的 Copy 方法
			copied := tt.dapp.Copy()

			// 验证基本字段是否正确复制
			if copied.Logo != tt.dapp.Logo {
				t.Errorf("Copy() Logo = %v, want %v", copied.Logo, tt.dapp.Logo)
			}
			if copied.Link != tt.dapp.Link {
				t.Errorf("Copy() Link = %v, want %v", copied.Link, tt.dapp.Link)
			}
			if copied.Tags != tt.dapp.Tags {
				t.Errorf("Copy() Tags = %v, want %v", copied.Tags, tt.dapp.Tags)
			}
			if copied.Hot != tt.dapp.Hot {
				t.Errorf("Copy() Hot = %v, want %v", copied.Hot, tt.dapp.Hot)
			}
			if copied.Show != tt.dapp.Show {
				t.Errorf("Copy() Show = %v, want %v", copied.Show, tt.dapp.Show)
			}

			// 验证 BaseModelNoDeleted 字段是否被复制（不应该被复制）
			if copied.ID == tt.dapp.ID && tt.dapp.ID != 0 {
				t.Errorf("Copy() ID = %v, should not be copied", copied.ID)
			}
			if !copied.CreatedAt.IsZero() && !tt.dapp.CreatedAt.IsZero() {
				t.Errorf("Copy() CreatedAt = %v, should not be copied", copied.CreatedAt)
			}
			if !copied.UpdatedAt.IsZero() && !tt.dapp.UpdatedAt.IsZero() {
				t.Errorf("Copy() UpdatedAt = %v, should not be copied", copied.UpdatedAt)
			}

			// 验证关联关系是否被复制（应该是深拷贝）
			if len(copied.DappI18Ns) != len(tt.dapp.DappI18Ns) {
				t.Errorf("Copy() DappI18Ns length = %v, want %v", len(copied.DappI18Ns), len(tt.dapp.DappI18Ns))
			}

			// 验证是否是深拷贝（修改复制后的对象不应影响原对象）
			if len(tt.dapp.DappI18Ns) > 0 && len(copied.DappI18Ns) > 0 {
				originalName := tt.dapp.DappI18Ns[0].Name
				copied.DappI18Ns[0].Name = "Modified Name"
				if tt.dapp.DappI18Ns[0].Name != originalName {
					t.Errorf("Copy() did not create a deep copy, original object was modified")
				}
			}
		})
	}
}
