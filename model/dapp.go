package model

import (
	"net/url"
	"strings"

	"gorm.io/gorm"

	"github.com/jinzhu/copier"
)

type Dapp struct {
	BaseModelNoDeleted
	Logo string `gorm:"comment:logo"`
	Link string `gorm:"comment:链接"`
	Tags string `gorm:"comment:标签"`
	Hot  bool   `gorm:"comment:是否热门"`
	Show bool   `gorm:"comment:是否展示"`

	// 关联关系定义
	DappI18Ns              []*DappI18N              `gorm:"foreignKey:DappID"`
	DappBlockchainNetworks []*DappBlockchainNetwork `gorm:"foreignKey:DappID"`
	DappCategoryRels       []*DappCategoryRel       `gorm:"foreignKey:DappID"`
	DappTopicRels          []*DappTopicRel          `gorm:"foreignKey:DappID"`
}

// BeforeSave 执行Save前，删除关联数据
func (d Dapp) BeforeSave(tx *gorm.DB) error {
	return d.BeforeDelete(tx)
}

func (d Dapp) BeforeDelete(tx *gorm.DB) error {
	if err := tx.Delete(&DappI18N{}, "dapp_id=?", d.ID).Error; err != nil {
		return err
	}
	if err := tx.Delete(&DappBlockchainNetwork{}, "dapp_id=?", d.ID).Error; err != nil {
		return err
	}
	return nil
}

func (Dapp) TableName() string {
	return "dapp"
}

func (d Dapp) BlockchainNetworks() []*BlockchainNetwork {
	var out []*BlockchainNetwork
	for _, dappBlockchainNetwork := range d.DappBlockchainNetworks {
		out = append(out, dappBlockchainNetwork.BlockchainNetwork)
	}
	return out
}

func (d Dapp) S3ObjectKey() string {
	if d.Logo == "" {
		return ""
	}
	u, err := url.Parse(d.Logo)
	if err != nil {
		return ""
	}
	return strings.TrimPrefix(u.Path, "/")
}

// Copy 复制一个dapp
// BaseModelNoDeleted 字段不会被复制
func (d Dapp) Copy() *Dapp {
	var out Dapp
	_ = copier.CopyWithOption(&out, &d, copier.Option{
		DeepCopy: true,
	})
	out.BaseModelNoDeleted = BaseModelNoDeleted{}
	return &out
}

// DappBlockchainNetwork dapp支持的区块链网络
type DappBlockchainNetwork struct {
	BaseModelNoDeleted
	DappID              uint   `gorm:"index:,unique,composite:dapp_network;comment:dapp 主键ID"`
	BlockchainNetworkID uint   `gorm:"index:,unique,composite:dapp_network;comment:区块链网络主键ID"`
	Address             string `gorm:"type:citext;index:,unique,composite:dapp_network;comment:被授权的合约地址"`

	// 关联关系定义
	Dapp              *Dapp              `gorm:"foreignKey:DappID;references:ID"`
	BlockchainNetwork *BlockchainNetwork `gorm:"foreignKey:BlockchainNetworkID;references:ID"`
}

func (DappBlockchainNetwork) TableName() string {
	return "dapp_blockchain_network"
}

// DappI18N dapp多语言
type DappI18N struct {
	BaseModelNoDeleted
	DappID   uint   `gorm:"index;comment:dapp 主键ID"`
	Name     string `gorm:"index;comment:dapp名称"`
	Summary  string `gorm:"comment:简介"`
	Language string `gorm:"index;comment:语言"`

	// 关联关系定义
	Dapp *Dapp `gorm:"foreignKey:DappID;references:ID"`
}

func (DappI18N) TableName() string {
	return "dapp_i18n"
}

// DappCategory dapp分类
type DappCategory struct {
	BaseModelNoDeleted
	Show bool `gorm:"comment:是否展示"`

	// 关联关系定义
	DappCategoryI18Ns []*DappCategoryI18N `gorm:"foreignKey:DappCategoryID"`
	DappCategoryRels  []*DappCategoryRel  `gorm:"foreignKey:DappCategoryID"`
}

// BeforeSave 执行Save前，删除关联数据
func (d DappCategory) BeforeSave(tx *gorm.DB) error {
	return tx.Delete(&DappCategoryI18N{}, "dapp_category_id=?", d.ID).Error
}

func (d DappCategory) BeforeDelete(tx *gorm.DB) error {
	if err := tx.Delete(&DappCategoryI18N{}, "dapp_category_id=?", d.ID).Error; err != nil {
		return err
	}
	if err := tx.Delete(&DappCategoryRel{}, "dapp_category_id=?", d.ID).Error; err != nil {
		return err
	}
	return nil
}

func (DappCategory) TableName() string {
	return "dapp_category"
}

// DappCategoryI18N dapp分类多语言
type DappCategoryI18N struct {
	BaseModelNoDeleted
	DappCategoryID uint   `gorm:"index;comment:dapp类别主键ID"`
	Name           string `gorm:"comment:分类名称"`
	Summary        string `gorm:"comment:简介"`
	Language       string `gorm:"comment:语言"`

	// 关联关系定义
	DappCategory *DappCategory `gorm:"foreignKey:DappCategoryID;references:ID"`
}

func (DappCategoryI18N) TableName() string {
	return "dapp_category_i18n"
}

// DappCategoryRel 类别下的dapp
type DappCategoryRel struct {
	BaseModelNoDeleted
	DappCategoryID uint `gorm:"index:,unique,composite:dapp_category_rel;comment:dapp类别主键ID"`
	DappID         uint `gorm:"index:,unique,composite:dapp_category_rel;comment:dapp 主键ID"`
	SortOrder      int  `gorm:"index:,unique,composite:dapp_category_rel;comment:排序序号"`

	// 关联关系定义
	DappCategory *DappCategory `gorm:"foreignKey:DappCategoryID;references:ID"`
	Dapp         *Dapp         `gorm:"foreignKey:DappID;references:ID"`
}

func (DappCategoryRel) TableName() string {
	return "dapp_category_rel"
}

// DappTopic dapp专题
type DappTopic struct {
	BaseModelNoDeleted
	Show          bool   `gorm:"comment:是否展示"`
	BackgroundUrl string `gorm:"comment:背景图"`

	// 关联关系定义
	DappTopicI18Ns []*DappTopicI18N `gorm:"foreignKey:DappTopicID"`
	DappTopicRels  []*DappTopicRel  `gorm:"foreignKey:DappTopicID"`
}

func (DappTopic) TableName() string {
	return "dapp_topic"
}

func (d DappTopic) BeforeDelete(tx *gorm.DB) error {
	if err := tx.Delete(&DappTopicI18N{}, "dapp_topic_id=?", d.ID).Error; err != nil {
		return err
	}
	if err := tx.Delete(DappTopicRel{}, "dapp_topic_id=?", d.ID).Error; err != nil {
		return err
	}
	return nil
}

// DappTopicI18N dapp专题多语言
type DappTopicI18N struct {
	BaseModelNoDeleted
	DappTopicID uint   `gorm:"index;comment:dapp专题主键ID"`
	Name        string `gorm:"comment:专题名称"`
	Summary     string `gorm:"comment:简介"`
	Language    string `gorm:"comment:语言"`
	Title       string `gorm:"comment:标题"`
	TopTitle    string `gorm:"comment:上小标题"`
	BottomTitle string `gorm:"comment:下小标题"`

	// 关联关系定义
	DappTopic *DappTopic `gorm:"foreignKey:DappTopicID;references:ID"`
}

func (DappTopicI18N) TableName() string {
	return "dapp_topic_i18n"
}

// DappTopicRel 专题下的dapp
type DappTopicRel struct {
	BaseModelNoDeleted
	DappTopicID uint `gorm:"index;comment:dapp专题主键ID"`
	DappID      uint `gorm:"index;comment:dapp 主键ID"`
	SortOrder   int  `gorm:"comment:排序序号"`

	// 关联关系定义
	DappTopic *DappTopic `gorm:"foreignKey:DappTopicID;references:ID"`
	Dapp      *Dapp      `gorm:"foreignKey:DappID;references:ID"`
}

func (DappTopicRel) TableName() string {
	return "dapp_topic_rel"
}

// DappNavigation dapp导航栏
type DappNavigation struct {
	BaseModelNoDeleted
	SortOrder      int  `gorm:"comment:排序序号"`
	DappCategoryID uint `gorm:"index;comment:dapp类别主键ID"`
	Show           bool `gorm:"comment:是否展示"`

	// 关联关系定义
	DappCategory *DappCategory `gorm:"foreignKey:DappCategoryID;references:ID"`
}

func (DappNavigation) TableName() string {
	return "dapp_navigation"
}

// DappIndex dapp首页管理
type DappIndex struct {
	BaseModelNoDeleted
	OwnerType string `gorm:"comment:dapp_topic/dapp_category"`
	OwnerID   uint   `gorm:"index;comment:表示DappCategoryID/DappTopicID"`
	SortOrder int    `gorm:"comment:排序序号"`
}

func (DappIndex) TableName() string {
	return "dapp_index"
}
