package model

import (
	"gorm.io/gorm"
)

type RPCEndpoint struct {
	gorm.Model
	ChainIndex int64  `gorm:"index;comment:所属链 chainIndex， 唯一id"`
	URL        string `gorm:"type:text;comment:RPC 节点地址"`
	UrlType    string `gorm:"default:'http';comment:节点类型"`
	IsPrimary  bool   `gorm:"default:false;comment:是否主用节点"`
	IsActive   bool   `gorm:"default:true;comment:是否启用"`
	IsArchived bool   `gorm:"default:false;comment:是否废弃"`
	Region     string `gorm:"type:varchar(50);comment:区域标识，如 asia、us-east"`
}

func (RPCEndpoint) TableName() string {
	return "rpc_endpoint"
}

var RPCEndpointStub = &RPCEndpoint{}
