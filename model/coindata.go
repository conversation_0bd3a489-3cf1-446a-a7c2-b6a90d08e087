package model

import (
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

const coinDataCachePrefix = "coindata:"

type CoinInfo struct {
	gorm.Model
	ChainIndex int64  `gorm:"index:,unique,composite:ci_addr;comment:链ID"`
	Address    string `gorm:"index:,unique,composite:ci_addr;type:varchar(255);comment:合约地址"`

	CoinID string `gorm:"index;type:varchar(200);comment:币种ID"`
	Name   string `gorm:"type:varchar(100);comment:币种名称"`
	Symbol string `gorm:"index;type:varchar(100);comment:币种符号"`
}

var CoinInfoStub = &CoinInfo{}

func (CoinInfo) TableName() string {
	return "coin_infos"
}

type CoinInfos []*CoinInfo

func (cis CoinInfos) UniqueCoinIDs() []string {
	ids := make([]string, 0, len(cis))
	check := make(map[string]struct{})
	for _, v := range cis {
		_, ok := check[v.CoinID]
		if !ok {
			ids = append(ids, v.CoinID)
			check[v.CoinID] = struct{}{}
		}
	}
	return ids
}

type CoinMarketData struct {
	CoinID                   string          // 币种ID // CoinInfo.CoinID字段
	Price                    decimal.Decimal // 当前价格
	Currency                 string          // 价格和成交额对应的法币
	CirculatingSupply        decimal.Decimal // 流动性
	LastUpdatedAtStr         string          // 数据更新时间(原始值)
	LastUpdatedAt            int64           // 数据更新时间戳(单位秒)
	PriceChangePercentage24h decimal.Decimal // 24小时价格涨跌幅(%)
	TradingVolume24h         decimal.Decimal // 24小时成交额
}

const (
	CoinMarketDataPopularCacheKey   = coinDataCachePrefix + "cmdatap"
	CoinMarketDataNoPopularCacheKey = coinDataCachePrefix + "cmdatanp" // 非定时器获取的market data
)

type CoinOHLC struct {
	CoinID        string          // 币种ID // CoinInfo.CoinID字段
	Currency      string          // 价格对应的法币
	Interval      string          // 级别(小时hourly,日daily,...)
	LastUpdatedAt int64           // 数据更新时间戳(单位秒)
	Open          decimal.Decimal // 开盘价
	High          decimal.Decimal // 最高价
	Low           decimal.Decimal // 最低价
	Close         decimal.Decimal // 收盘价
}

const CoinOHLCsCacheKey = coinDataCachePrefix + "coinohlcs"

type CoinLogo struct {
	gorm.Model
	CoinID string `gorm:"uniqueIndex;type:varchar(200);comment:币种ID关联coin_infos表"`
	Image  string `gorm:"type:text;comment:币种logo"`
}

var CoinLogoStub = &CoinLogo{}

func (CoinLogo) TableName() string {
	return "coin_logos"
}

const CurrencyRatesCacheKey = coinDataCachePrefix + "currencyrates"

const CoinIDRankCacheKey = coinDataCachePrefix + "coinidrank"
