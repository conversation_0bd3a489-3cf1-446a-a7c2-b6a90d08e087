package model

import "time"

// MissingBlock 区块交易数据同步失败时记录
type MissingBlock struct {
	ID          uint `gorm:"primarykey"`
	CreatedAt   time.Time
	ChainIndex  int64  `gorm:"index:,unique,composite:missing_block"`
	BlockHeight int64  `gorm:"index:,unique,composite:missing_block;comment:区块高度"`
	Msg         string `gorm:"comment:错误信息"`
}

func (MissingBlock) TableName() string {
	return "missing_block"
}
