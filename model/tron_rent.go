package model

import (
	"github.com/shopspring/decimal"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type TronRentStatus string

const (
	TronRentStatusSuccess TronRentStatus = "success"
	TronRentStatusFailed  TronRentStatus = "failed"
	TronRentStatusPending TronRentStatus = "pending"
)

type TronRentRecord struct {
	gorm.Model
	OrderId             string          `gorm:"uniqueIndex;comment:订单id"`
	Channel             TronRentChannel `gorm:"comment:API渠道"`
	ChannelOrderId      string          `gorm:"comment:渠道订单id"`
	PlatformAddr        string          `gorm:"comment:转trx的目标地址"`
	FromAddress         string          `gorm:"comment:钱包地址"`
	PledgeAddress       string          `gorm:"comment:接收能量地址"`
	PledgeHour          int             `gorm:"comment:租用时长，单位（小时)"`
	PledgeMinute        int             `gorm:"comment:租用时长，单位（分钟）"`
	TradeType           string          `gorm:"comment:交易类型"`
	Source              string          `gorm:"comment:渠道来源"`
	OrderType           string          `gorm:"comment:订单类型"`
	OrderPrice          decimal.Decimal `gorm:"type:decimal(100,0);comment:订单价格(sun)"`
	ChannelPrice        decimal.Decimal `gorm:"type:decimal(100,0);comment:渠道价格(sun)"`
	ActualChannelPrice  decimal.Decimal `gorm:"type:decimal(100,0);comment:购买时渠道价格(sun)"`
	CustomPrice         decimal.Decimal `gorm:"type:decimal(100,0);comment:自定义价格(sun)"`
	PledgeNum           int             `gorm:"comment:能量数量"`
	PledgeTrxNum        string          `gorm:"comment:trx数量"`
	Transaction         datatypes.JSON  `gorm:"comment:待签名的交易数据"`
	TxHash              string          `gorm:"index;comment:转trx交易hash"`
	Status              TronRentStatus  `gorm:"index;comment:状态"`
	ErrMsg              string          `gorm:"comment:错误信息"`
	SignedData          string          `gorm:"type:text;comment:签名数据"`
	UploadHashReplyData datatypes.JSON  `gorm:"comment:上传hash相应数据"`
}

func (t TronRentRecord) TableName() string {
	return "tron_rent_record"
}

var TronRentRecordStub = &TronRentRecord{}

type TronRentChannel string

const (
	TronRentChannelTronify  TronRentChannel = "tronify"
	TronRentChannelWeidubot TronRentChannel = "weidubot"
)

type TronRentConfig struct {
	gorm.Model
	Channel        TronRentChannel `gorm:"uniqueIndex;comment:API渠道"`
	TradeType      string          `gorm:"comment:交易类型"`
	SourceFlag     string          `gorm:"comment:渠道来源"`
	OrderType      string          `gorm:"comment:订单类型"`
	PledgeHour     int             `gorm:"comment:租用时长，单位（小时)"`
	PledgeMinute   int             `gorm:"comment:租用市场，单位（分钟）"`
	CustomPrice    decimal.Decimal `gorm:"type:decimal(100,0);comment:自定义价格(sun)"`
	ReceiveTrxAddr string          `gorm:"comment:接收TRX地址"`
	Enable         bool            `gorm:"comment:是否启用"`
}

func (t TronRentConfig) TableName() string {
	return "tron_rent_config"
}

var TronRentConfigStub = &TronRentConfig{}
