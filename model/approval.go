package model

import "gorm.io/gorm"

// Approval 授权记录
type Approval struct {
	gorm.Model
	ChainIndex     int64  `gorm:"index:,unique,composite:chain_addresses;comment:BlockchainNetwork表的ChainIndex"`
	OwnerAddress   string `gorm:"type:citext;index:,unique,composite:chain_addresses;comment:授权地址(用户钱包地址)"`
	SpenderAddress string `gorm:"type:citext;index:,unique,composite:chain_addresses;comment:被授权的合约地址"`
	TokenAddress   string `gorm:"type:citext;index:,unique,composite:chain_addresses;comment:token合约地址"`
	Value          string `gorm:"type:decimal(100,0);comment:授权token数量"`
}

func (Approval) TableName() string {
	return "approval"
}

func (a Approval) Approved() bool {
	return a.Value != "" && a.Value != "0"
}
