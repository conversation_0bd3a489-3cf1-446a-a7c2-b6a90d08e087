package model

import (
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type CrossChainRelation struct {
	gorm.Model
	RelationType  string         `gorm:"index;comment:关系类型，如地址映射、token 映射、交易映射"` // 'address','token','chain'
	SourceChainID int64          `gorm:"index;comment:源链ID"`
	SourceValue   string         `gorm:"type:varchar(255);index;comment:源链上的值（地址/合约/交易哈希）"`
	DestChainID   int64          `gorm:"index;comment:目标链ID"`
	DestValue     string         `gorm:"type:varchar(255);index;comment:目标链上的值（地址/合约/交易哈希）"`
	RelationInfo  datatypes.JSON `gorm:"type:json;comment:额外扩展信息，如桥协议、方向等"`
}

func (CrossChainRelation) TableName() string {
	return "cross_chain_relation"
}
