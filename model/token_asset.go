package model

import (
	"strings"
	"time"

	"gorm.io/gorm"
)

type TokenAsset struct {
	gorm.Model
	ChainIndex      int64  `gorm:"index:,unique,composite:index_addr;comment:所属链的ID"`
	ChainId         string `gorm:"comment:链标记，chainId如eth主网：1"`
	Address         string `gorm:"type:citext;index:,unique,composite:index_addr;comment:Token合约地址，Native币为空字符串"`
	Symbol          string `gorm:"type:citext;index;comment:Token符号，如 ETH、USDT"`
	Name            string `gorm:"type:citext;comment:Token全称"`
	Decimals        int64  `gorm:"comment:精度位数"`
	LogoUrl         string `gorm:"type:text;comment:token图标地址"`
	TokenType       string `gorm:"comment:Token类型"` // values: common/constant/token_type.go
	TotalSupply     string `gorm:"comment:发行量"`
	IsDisplay       bool   `gorm:"default:true;comment:是否展示"`
	CoinID          string `gorm:"index;type:varchar(200);comment:币种ID,coin_infos表关联"`
	TokenDeployedAt int64  `gorm:"comment:部署时间"`
}

func ConvValidSymbol(symbol string) string {
	return strings.ToUpper(strings.ReplaceAll(symbol, "-", ""))
}

func (TokenAsset) TableName() string {
	return "token_assets"
}

var TokenAssetStub = &TokenAsset{}

type TokenAssetRank struct {
	gorm.Model
	TokenAssetID uint `gorm:"uniqueIndex;comment:token_assets表主键"`
	Rank         int  `gorm:"comment:排名"`
}

func (TokenAssetRank) TableName() string {
	return "token_asset_ranks"
}

var TokenAssetRankStub = &TokenAssetRank{}

// TokenAssetWithRank only read permission
type TokenAssetWithRank struct {
	TokenAsset
	Rank int
}

type TokenAssetStar struct {
	ID        uint `gorm:"primarykey"`
	CreatedAt time.Time
	UpdatedAt time.Time
	// DeletedAt DeletedAt `gorm:"index"`
	// If your model includes a gorm.DeletedAt field (which is included in gorm.Model),
	// it will get soft delete ability automatically!
	TokenAssetID uint  `gorm:"uniqueIndex;comment:token_assets表主键"`
	SortOrder    int64 `gorm:"comment:排序"`
}

func (TokenAssetStar) TableName() string {
	return "token_asset_stars"
}

var TokenAssetStarStub = &TokenAssetStar{}

// TokenAssetWithStar only read permission
type TokenAssetWithStar struct {
	TokenAsset
	StarID        uint      // TokenAssetStar.ID
	StarCreatedAt time.Time // TokenAssetStar.CreatedAt
	SortOrder     int64
}
