package model

import (
	"fmt"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type GasPool struct {
	gorm.Model
	UserID  uint            `gorm:"uniqueIndex;comment:用户ID"`
	Balance decimal.Decimal `gorm:"type:decimal(100,0);comment:余额(不带精度)"`
}

func (GasPool) TableName() string {
	return "gas_pools"
}

var GasPoolStub = &GasPool{}

type GasPoolFlowType string

const (
	GasPoolFlowTypeDeposit GasPoolFlowType = "deposit"
	GasPoolFlowTypeReduce  GasPoolFlowType = "reduce"
	GasPoolFlowTypeRefund  GasPoolFlowType = "refund"
)

type GasPoolFlowDirection string

const (
	GasPoolFlowDirectionIn  GasPoolFlowDirection = "in"
	GasPoolFlowDirectionOut GasPoolFlowDirection = "out"
)

func GasPoolFlowSourceSponsorTx(chainIndex int64, txHash string) string {
	return fmt.Sprintf("stx_%d_%s", chainIndex, txHash)
}

type GasPoolFlow struct {
	gorm.Model
	UserID        uint                 `gorm:"index;comment:用户ID"`
	GasPoolID     uint                 `gorm:"index;comment:gasPoolID"`
	FlowType      GasPoolFlowType      `gorm:"type:varchar(50);comment:流水类型"`
	FlowDirection GasPoolFlowDirection `gorm:"type:varchar(10);comment:流水方向"`
	Amount        decimal.Decimal      `gorm:"type:decimal(100,0);comment:金额"`
	OldBalance    decimal.Decimal      `gorm:"type:decimal(100,0);comment:原有余额(不带精度)"`
	Balance       decimal.Decimal      `gorm:"type:decimal(100,0);comment:当前余额(不带精度)"`
	ChainIndex    int64                `gorm:"comment:链索引"`
	TxHash        string               `gorm:"comment:余额变动关联交易hash"`
	TokenAddress  string               `gorm:"comment:充值币种地址"`
	ReduceFlowID  uint                 `gorm:"index;comment:扣款流水ID,退款时赋值"`
}

func (GasPoolFlow) TableName() string {
	return "gas_pool_flows"
}

var GasPoolFlowStub = &GasPoolFlow{}

type GasPoolTxType string

const (
	GasPoolTxTypeDeposit             GasPoolTxType = "deposit"
	GasPoolTxTypeDepositPreReduceGas GasPoolTxType = "deposit_pre_reduce_gas"
	GasPoolTxTypeTransfer            GasPoolTxType = "transfer"
	GasPoolTxTypeSwap                GasPoolTxType = "swap"
	GasPoolTxTypeDapp                GasPoolTxType = "dapp"
)

func ReduceGasPoolTxTypes() []GasPoolTxType {
	return []GasPoolTxType{
		GasPoolTxTypeDepositPreReduceGas,
		GasPoolTxTypeTransfer,
		GasPoolTxTypeSwap,
		GasPoolTxTypeDapp,
	}
}

func (txType GasPoolTxType) Valid() error {
	switch txType {
	case GasPoolTxTypeDeposit,
		GasPoolTxTypeDepositPreReduceGas,
		GasPoolTxTypeTransfer,
		GasPoolTxTypeSwap,
		GasPoolTxTypeDapp:
		return nil
	default:
		return fmt.Errorf("unknown tx type: %s", txType)
	}
}

func (txType GasPoolTxType) IsUseGasPool() bool {
	return txType != GasPoolTxTypeDeposit
}

func (txType GasPoolTxType) IsPreReduceGasPool() bool {
	return txType == GasPoolTxTypeDepositPreReduceGas
}

func (txType GasPoolTxType) IsDepositGasPool() bool {
	return txType == GasPoolTxTypeDeposit ||
		txType == GasPoolTxTypeDepositPreReduceGas

}

type GasPoolTxStatus string

const (
	GasPoolTxStatusInit    GasPoolTxStatus = "init"
	GasPoolTxStatusWaitGas GasPoolTxStatus = "wait_gas"
	GasPoolTxStatusPending GasPoolTxStatus = "pending"
	GasPoolTxStatusSuccess GasPoolTxStatus = "success"
	GasPoolTxStatusFail    GasPoolTxStatus = "fail"
)

type GasPoolSponsorTx struct {
	gorm.Model
	ChainIndex        int64           `gorm:"index:,unique,composite:index_hash;comment:链ID"`
	TxHash            string          `gorm:"type:varchar(100);index:,unique,composite:index_hash;comment:交易Hash"`
	UserID            uint            `gorm:"index;comment:用户ID"`
	RawTxHex          string          `gorm:"type:text;comment:原始交易数据hex"`
	From              string          `gorm:"index;comment:发送方"`
	To                string          `gorm:"index;comment:接收方"`
	Contract          string          `gorm:"comment:合约地址"`
	Value             decimal.Decimal `gorm:"type:decimal(100,0);comment:金额"`
	ValueUSDT         decimal.Decimal `gorm:"type:decimal(100,0);comment:金额USDT"`
	Gas               decimal.Decimal `gorm:"type:decimal(100,0);comment:估算gas"`
	GasUSDT           decimal.Decimal `gorm:"type:decimal(100,0);comment:估算gasUSDT"`
	Price             decimal.Decimal `gorm:"type:decimal(20,8);comment:交易所价格"`
	PriceTimeUnix     int64           `gorm:"comment:交易所价格更新时间unix"`
	Bandwidth         decimal.Decimal `gorm:"type:decimal(100,0);comment:消耗带宽"`
	BandwidthPrice    decimal.Decimal `gorm:"type:decimal(100,0);comment:消耗带宽单价(sun)"`
	Energy            decimal.Decimal `gorm:"type:decimal(100,0);comment:消耗能量"`
	EnergyPrice       decimal.Decimal `gorm:"type:decimal(100,0);comment:消耗能量单价(sun)"`
	ActualEnergyPrice decimal.Decimal `gorm:"type:decimal(20,8);comment:实际消耗能量单价(sun)"`
	ActivateFee       decimal.Decimal `gorm:"type:decimal(100,0);comment:激活费用(sun)"`
	NativeTxHash      string          `gorm:"comment:转账用户矿币交易哈希"`                        // tron: bandwidth trx + activate account fee trx
	NativeTxFee       decimal.Decimal `gorm:"type:decimal(100,0);comment:转账用户矿币费用(sun)"` // tron: bandwidth trx + activate account fee trx + transfer fee
	RentEnergyOrderID string          `gorm:"comment:租赁能量订单ID"`
	RentEnergyFee     decimal.Decimal `gorm:"type:decimal(100,0);comment:租赁能量TRX费用(sun)"`
	ActualGas         decimal.Decimal `gorm:"type:decimal(100,0);comment:实际消耗gas"`
	ActualGasUSDT     decimal.Decimal `gorm:"type:decimal(100,0);comment:实际消耗gasUSDT"`
	TxType            GasPoolTxType   `gorm:"index;type:varchar(50);comment:交易类型"`
	ReduceFlowID      uint            `gorm:"index;comment:扣款流水ID"`
	RefundFlowID      uint            `gorm:"index;comment:退款流水ID"`
	DepositFlowID     uint            `gorm:"index;comment:充值流水ID"`
	Status            GasPoolTxStatus `gorm:"type:varchar(50);comment:交易状态"`
	Reason            string          `gorm:"type:text;comment:失败原因"`
}

func (GasPoolSponsorTx) TableName() string {
	return "gas_pool_sponsor_txs"
}

var GasPoolSponsorTxStub = &GasPoolSponsorTx{}

type GasPoolDepositToken struct {
	gorm.Model
	TokenAssetID     uint            `gorm:"uniqueIndex;comment:token_assets表ID"`
	MinDepositAmount decimal.Decimal `gorm:"type:decimal(100,0);comment:最小充值金额(不带精度)"`
	Enable           bool            `gorm:"default:1;comment:是否启用"`
}

type GasPoolDepositTokenView struct {
	TokenAsset
	MinDepositAmount decimal.Decimal `gorm:"type:decimal(100,0);comment:最小充值金额(不带精度)"`
	Enable           bool            `gorm:"default:1;comment:是否启用"`
	DepositAddress   string          `gorm:"comment:充值地址"`
}

func (GasPoolDepositToken) TableName() string {
	return "gas_pool_deposit_tokens"
}

var GasPoolDepositTokenStub = &GasPoolDepositToken{}

type GasPoolDepositAddress struct {
	gorm.Model
	ChainIndex int64  `gorm:"uniqueIndex;comment:链ID"`
	Address    string `gorm:"uniqueIndex;type:citext;comment:充值地址"`
}

func (GasPoolDepositAddress) TableName() string {
	return "gas_pool_deposit_addresses"
}

var GasPoolDepositAddressStub = &GasPoolDepositAddress{}
