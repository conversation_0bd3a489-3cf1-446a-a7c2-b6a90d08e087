package model

import (
	"gorm.io/gorm"
	"slices"
	"time"
)

const (
	SwapChannelNameMetapath = "metapath"
)

type SwapChannel struct {
	gorm.Model
	Name   string `gorm:"uniqueIndex;comment:渠道名称"`
	Enable bool   `gorm:"comment:是否可用"`
}

func (SwapChannel) TableName() string {
	return "swap_channel"
}

// SwappableBlockchainNetwork 兑换支持的区块网络
type SwappableBlockchainNetwork struct {
	gorm.Model
	SwapChannelID       uint               `gorm:"index:,unique,composite:channel_network;comment:兑换商ID"`
	SwapChannel         *SwapChannel       `gorm:"foreignKey:SwapChannelID;references:ID"`
	BlockchainNetworkID uint               `gorm:"index:,unique,composite:channel_network;comment:BlockchainNetwork主键ID"`
	BlockchainNetwork   *BlockchainNetwork `gorm:"foreignKey:BlockchainNetworkID;references:ID"`
	SortOrder           int                `gorm:"comment:排序序号"`
}

func (SwappableBlockchainNetwork) TableName() string {
	return "swappable_blockchain_network"
}

type SwappableToken struct {
	gorm.Model
	TokenAssetID        uint               `gorm:"index:,unique,composite:channel_token;comment:token_assets表主键"`
	BlockchainNetworkID uint               `gorm:"index;comment:BlockchainNetwork主键ID"`
	BlockchainNetwork   *BlockchainNetwork `gorm:"foreignKey:BlockchainNetworkID;references:ID"`
	ChainIndex          int64              `gorm:"index:,composite:chain_address;comment:链索引"`
	Address             string             `gorm:"type:citext;index:,composite:chain_address;comment:token地址"`
	TokenAsset          *TokenAsset        `gorm:"foreignKey:TokenAssetID;references:ID"`
	Enable              bool               `gorm:"comment:是否可用"`
	Display             bool               `gorm:"comment:是否展示"`

	Channels []*SwappableTokenChannel `gorm:"foreignKey:SwappableTokenID"`
}

func (SwappableToken) TableName() string {
	return "swappable_token"
}

type SwappableTokenChannel struct {
	gorm.Model
	SwappableTokenID uint         `gorm:"index;comment:SwappableTokenID"`
	SwapChannelID    uint         `gorm:"index;comment:兑换商ID"`
	SwapChannel      *SwapChannel `gorm:"foreignKey:SwapChannelID;references:ID"`
}

func (SwappableTokenChannel) TableName() string {
	return "swappable_token_channel"
}

type SwappableHotToken struct {
	gorm.Model
	SwappableTokenID uint            `gorm:"index:,composite:chain_token;comment:可兑换tokenID"`
	SwappableToken   *SwappableToken `gorm:"foreignKey:SwappableTokenID;references:ID"`
	ChainIndex       int64           `gorm:"index:,composite:chain_token;comment:链索引"`
	SortOrder        int             `gorm:"comment:排序序号"`
	IsAll            bool            `gorm:"comment:是否是全部"`
}

func (SwappableHotToken) TableName() string {
	return "swappable_hot_token"
}

type SwapRecord struct {
	gorm.Model
	SwapChannelID    uint          `gorm:"index:,unique,composite:channel_hash;comment:兑换商ID"`
	SwapChannel      *SwapChannel  `gorm:"foreignKey:SwapChannelID;references:ID"`
	SwappedAt        time.Time     `gorm:"comment:兑换时间(unix)"`
	Status           SwapStatus    `gorm:"comment:兑换状态"`
	FromTokenAssetID uint          `gorm:"comment:源TokenAssetID"`
	FromTokenAsset   *TokenAsset   `gorm:"foreignKey:FromTokenAssetID;references:ID"`
	ToTokenAssetID   uint          `gorm:"comment:目标TokenAssetID"`
	ToTokenAsset     *TokenAsset   `gorm:"foreignKey:ToTokenAssetID;references:ID"`
	FromTokenAmount  string        `gorm:"comment:源Token数量"`
	ToTokenAmount    string        `gorm:"comment:目标Token数量"`
	FromAddress      string        `gorm:"index;comment:发送的钱包地址"`
	ToAddress        string        `gorm:"index;comment:接收的钱包地址"`
	GasFee           string        `gorm:"comment:用户兑换消耗的GasFee"`
	FeeRate          string        `gorm:"comment:兑换手续费率"`
	Hash             string        `gorm:"index:,unique,composite:channel_hash;comment:用户发起的交易hash"`
	ApprovalHash     string        `gorm:"comment:授权hash"`
	BlockNumber      int64         `gorm:"comment:区块高度"`
	Dex              string        `gorm:"comment:兑换平台名称"`
	DexLogo          string        `gorm:"comment:兑换平台logo url"`
	SwapPrice        string        `gorm:"comment:兑换价格"`
	Details          []*SwapDetail `gorm:"foreignKey:SwapRecordID"`
	FinishedAt       *time.Time    `gorm:"comment:结束时间"`
	EstimatedTime    string        `gorm:"comment:预估时间，1(1-3分钟)，2(1-10分钟)，3(5-30分钟)"`
}

func (SwapRecord) TableName() string {
	return "swap_record"
}

func (s SwapRecord) IsFinalStatus() bool {
	return slices.Contains(FinalStatus, s.Status)
}

type SwapDetail struct {
	gorm.Model
	SwapRecordID uint   `gorm:"index:,unique,composite:id_hash;comment:兑换记录ID"`
	ChainIndex   int64  `gorm:"index;comment:链索引"`
	ExplorerURL  string `gorm:"comment:区块浏览器url"`
	Hash         string `gorm:"index:,unique,composite:id_hash;comment:交易hash"`
	Status       string `gorm:"comment:交易状态"`
	Collected    bool   `gorm:"comment:是否已收录"`
}

func (SwapDetail) TableName() string {
	return "swap_detail"
}

// SwapStatus 兑换状态
type SwapStatus string

func (s SwapStatus) AppStatus() string {
	switch s {
	case SwapStatusPending, SwapStatusWaitKyc, SwapStatusWaitRefund, SwapStatusWaitForInformation, SwapStatusWaitDepositSendFail:
		return string(SwapStatusPending)
	case SwapStatusSuccess:
		return string(SwapStatusSuccess)
	case SwapStatusFail, SwapStatusRefundComplete, SwapStatusTimeout:
		return string(SwapStatusFail)
	default:
		return string(SwapStatusPending)
	}
}

const (
	SwapStatusPending             SwapStatus = "pending"                // 处理中
	SwapStatusSuccess             SwapStatus = "success"                // 兑换成功
	SwapStatusFail                SwapStatus = "fail"                   // 兑换失败
	SwapStatusWaitKyc             SwapStatus = "wait_kyc"               // 等待KYC验证
	SwapStatusWaitRefund          SwapStatus = "wait_refund"            // 等待退款
	SwapStatusRefundComplete      SwapStatus = "refund_complete"        // 退款完成
	SwapStatusWaitDepositSendFail SwapStatus = "wait_deposit_send_fail" // 充币失败
	SwapStatusTimeout             SwapStatus = "timeout"                // 超时（未在指定时间内充币）
	SwapStatusWaitForInformation  SwapStatus = "wait_for_information"   // 异常订单，等待处理
)

var FinalStatus = []SwapStatus{
	SwapStatusSuccess,
	SwapStatusFail,
	SwapStatusRefundComplete,
	SwapStatusTimeout,
}

type SwapConfig struct {
	gorm.Model
	SwapChannelID        uint         `gorm:"uniqueIndex;comment:兑换商ID"`
	SwapChannel          *SwapChannel `gorm:"foreignKey:SwapChannelID;references:ID"`
	CronSpec             string       `gorm:"comment:cron表达式"`
	RecordMaxConcurrency int          `gorm:"comment:查询订单记录最大并发数"`
}

func (SwapConfig) TableName() string {
	return "swap_config"
}
