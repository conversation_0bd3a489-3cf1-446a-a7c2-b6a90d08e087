package model

import (
	"fmt"
	"gorm.io/gorm"
	"strings"
)

type BlockchainNetwork struct {
	gorm.Model
	Name           string `gorm:"type:varchar(50);uniqueIndex;comment:链名称，如 Ethereum、Solana"` // name
	ChainName      string `gorm:"type:varchar(50);comment:链名称，如 Ethereum、Solana"`             // chain_name
	Symbol         string `gorm:"type:varchar(20);index;comment:链简称，如 eth、sol"`
	Handle         string `gorm:"type:varchar(20);comment:链名称，如 solana,ethereum"`
	ChainType      string `gorm:"type:varchar(20);comment:链类型，如 evm、solana、cosmos"`
	ChainIndex     int64  `gorm:"index:,unique,composite:chain_idx_id;comment:bip44 chainIndex，如 0:btc"`
	ChainID        string `gorm:"index:,unique,composite:chain_idx_id;type:varchar(50);comment:链ID，EVM 链使用 chainId"`
	GasTokenSymbol string `gorm:"type:varchar(20);comment:支付 gas 的币种符号，如 ETH、SOL"`
	Decimals       int64  `gorm:"comment:精度，decimals"`
	SortOrder      int64  `gorm:"comment:排序，sort_order"`
	BlockchainURL  string `gorm:"type:text;comment:链的图标"`
	TokenURL       string `gorm:"type:text;comment:代币图标"`
	ExplorerURL    string `gorm:"type:text;comment:区块浏览器地址，可用于拼接交易链接"`
	IsEvm          bool   `gorm:"default:false;comment:是否是evm"`
	IsDisplay      bool   `gorm:"default:true;comment:是否展示"`
	CurrentBlock   int64  `gorm:"comment:当前同步的区块高度（EVM）或 slot（Solana）"`
	IsSyncing      bool   `gorm:"default:false;comment:是否正在同步"`
	MaxConcurrency int    `gorm:"comment:同步最大并发数"`
	CronSpec       string `gorm:"type:text;comment:定时任务spec配置"`
}

func (BlockchainNetwork) TableName() string {
	return "blockchain_network"
}

func (b BlockchainNetwork) TxTableName() string {
	return fmt.Sprintf("transactions_%s", strings.ToLower(b.Name))
}

var BlockchainNetworkStub = &BlockchainNetwork{}
