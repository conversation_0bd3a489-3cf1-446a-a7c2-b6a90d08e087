package model

import (
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type DepositTokens struct {
	gorm.Model
	ChainIndex int64           `gorm:"comment:chain_index "`
	TokenId    int64           `gorm:"comment:token_asset id"`
	Min        decimal.Decimal `gorm:"type:decimal(20,6);default:0.0000;comment:最小充值"`
	Max        decimal.Decimal `gorm:"type:decimal(20,6);default:0.0000;comment:最大充值"`
	IsEnabled  bool            `gorm:"default:true;comment:是否启用充值"`
	Contract   string          `gorm:"comment:合约地址（如适用）"`
}

func (DepositTokens) TableName() string {
	return "deposit_tokens"
}
